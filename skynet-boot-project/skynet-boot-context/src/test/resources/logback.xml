<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true" scanPeriod="30 seconds">

    <contextName>logback-mdc-ttl</contextName>
    <property scope="context" name="CHARSET" value="utf-8"/>
    <property scope="context" name="APP_PATTERN"
              value='%magenta(%d{yyyy-MM-dd HH:mm:ss.SSS}  %highlight(%-5level) %magenta([%-15.15(%thread)]) [%X{traceId}] %cyan( %-50.50(%logger{50} )) : %msg%n)'/>

    <!--    <conversionRule conversionWord="emsg"-->
    <!--                    converterClass="skynet.boot.logging.converter.SkynetLoggingMsgConverter4Ellipsis"/>-->

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <charset>${CHARSET}</charset>
            <pattern>${APP_PATTERN}</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>

    <!--    <logger name="skynet.logging.SkynetEllipsesLoggerTest" level="DEBUG">-->
    <!--        <appender-ref ref="STDOUT"/>-->
    <!--    </logger>-->


</configuration>