server.port=7878
######################################################
logging.level.root=INFO
logging.level.org.springframework=WARN
logging.level.com.iflytek=INFO
######################################################
springfox.documentation.enabled=false
######################################################
management.endpoints.web.exposure.include=*
management.endpoints.enabled-by-default=true
management.health.defaults.enabled=false
management.metrics.distribution.percentilesHistogram.all=false
######################################################
skynet.zookeeper.enabled=true
skynet.zookeeper.server_list=*************:2181
######################################################
spring.application.name=skynet-context-v40
######################################################
spring.cloud.zookeeper.enabled=false
spring.cloud.zookeeper.connect-string=*************:2181
spring.cloud.zookeeper.discovery.root=/test/discovery
spring.cloud.zookeeper.discovery.instance-host=${skynet.ipAddress}
spring.cloud.zookeeper.discovery.enabled=true
spring.cloud.zookeeper.discovery.register=true
######################################################
skynet.tlb.enabled=true
skynet.tlb.endpoints=**************:33001
######################################################
skynet.logging.enabled=true
skynet.logging.ellipses.enabled=true
skynet.logging.ellipses.array-limit-len=100
skynet.logging.ellipses.string-limit-len=500
skynet.logging.debug.enabled=false
skynet.logging.debug.head-key=x-skynet-debugging
skynet.logging.debug.expression=$.user=="lyhu"
skynet.logging.debug.dubbo.enabled=false
# ---------------------------------------------------------#
skynet.logging.debug.springmvc.enabled=false
skynet.logging.debug.springmvc.uri-pattern=/**/api/**
# ---------------------------------------------------------#
skynet.logging.debug.enable.root=true
skynet.logging.debug.enable.com.iflytek=true
skynet.logging.debug.enable.skynet=false
skynet.logging.debug.enable.skynet.boot=true
# ---------------------------------------------------------#

