def execute(input) {
     return [
            "data": input.mergeList.collect { mergeItem ->
                println "mergeItem: ${mergeItem}"
                [
                        query: mergeItem.query,
                        type: mergeItem.type,
                        extra: mergeItem.extra,
                        docs: mergeItem.docs.collect { doc ->
                            [
                                    id: doc.id,
                                    title: doc.title,
                                    content: doc.content.take(512),
                            ]
                        }
                ]}
    ]
}

//def execute(input) {
//    return [
//            "data": input.mergeList.collect { mergeItem ->
//                [
//                        query: mergeItem.query,
//                        type: mergeItem.type,
//                        extra: mergeItem.extra,
//                        docs: mergeItem.docs.collect { doc ->
//                            [
//                                    id: doc.id,
//                                    title: doc.title,
//                                    content: doc.content.take(512),
//                            ]
//                        }
//                ]}
//    ]
//}