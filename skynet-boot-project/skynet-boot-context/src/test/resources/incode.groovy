// Groovy 示例代码
def execute(input) {

    println("-1-----")
    println(input)
    println("-2-----")
    def apiResponse = new groovy.json.JsonSlurper().parseText(input.sceneDict)
    println(apiResponse)
    println("-3-----")
    if(apiResponse.header.code != 0 ) {
        // 业务流程兜底
        return [
                "code": -1
        ]
    }
    println("-4-----")
    println(apiResponse.payload)
    println("-5-----")
    println(apiResponse.payload.data)
    println("-6-----")
    // 遍历 payload.data，查找与 input.scene 匹配的元素
    def matchingElement = apiResponse.payload.data.find { it.name == input.scene[0] }

    println(matchingElement)


    if (!matchingElement) {
        return [
                "code": -1
        ]
    }

    // 转换 matchingElement.code 为整数类型
    def code
    try {
        code = matchingElement.code.toInteger()
    } catch (Exception e) {
        return [
                "code": -1
        ]
    }
    println("-7-----")
    // 返回整数 code 值
    return [
            "code": code
    ]
}