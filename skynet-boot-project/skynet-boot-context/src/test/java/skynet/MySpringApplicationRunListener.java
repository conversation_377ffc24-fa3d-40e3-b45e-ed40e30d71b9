//package skynet;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.SpringApplication;
//import org.springframework.boot.SpringApplicationRunListener;
//import org.springframework.context.ConfigurableApplicationContext;
//import org.springframework.core.env.ConfigurableEnvironment;
//
//@Slf4j
//public class MySpringApplicationRunListener implements SpringApplicationRunListener {
//
//    final SpringApplication app;
//
//    public MySpringApplicationRunListener(SpringApplication app, String[] args) {
//        System.err.println("init MySpringApplicationRunListener");
//        this.app = app;
//    }
//
//    @Override
//    public void starting() {
//
//        log.debug("starting");
//    }
//
//    @Override
//    public void environmentPrepared(ConfigurableEnvironment environment) {
//
//        // String prop = environment.getProperty("skynet.actionName");
//        //
//        // prop = environment.getProperty("skynet.actionId");
//        //
//        // prop = environment.getProperty("logging.path");
//
//        log.debug("environmentPrepared");
//
//    }
//
//    @Override
//    public void contextPrepared(ConfigurableApplicationContext context) {
//        log.debug("contextPrepared");
//
//    }
//
//    @Override
//    public void contextLoaded(ConfigurableApplicationContext context) {
//        log.debug("contextLoaded");
//
//    }
//
/// /	@Override
/// /	public void finished(ConfigurableApplicationContext context, Throwable exception) {
/// /		if (context.containsBean(SkynetProperties.BEAN_NAME)) {
/// /			SkynetProperties skynetProperties = context.getBean(SkynetProperties.class);
/// /			log.debug(skynetProperties.getPort() + "");
/// /			log.debug("finished");
/// /		}
/// /		log.debug("finished");
/// /	}
//}
