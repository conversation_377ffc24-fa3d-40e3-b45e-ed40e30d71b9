package skynet.boot;

import java.util.concurrent.atomic.AtomicInteger;

public class ThreadLocalTest {
    public static void main(String[] args) throws InterruptedException {
        final InheritableThreadLocal<AtomicInteger> context = new InheritableThreadLocal<>();
        context.set(new AtomicInteger(1));

        Thread t1 = new Thread() {
            @Override
            public void run() {
                System.out.println("thread name: " + Thread.currentThread().getName() + " threadlocal value:" + context.get());
                sleepMs(2000);
                AtomicInteger atomicInteger = context.get();
                if (atomicInteger != null) {
                    atomicInteger.incrementAndGet();
                }
            }

            private void sleepMs(long temp) {
                try {
                    Thread.sleep(temp);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        };
        t1.start();
        System.out.println("thread name: " + Thread.currentThread().getName() + " threadlocal value:" + context.get());
        t1.join();
        Thread.sleep(2000);
        System.out.println("thread name: " + Thread.currentThread().getName() + " threadlocal value:" + context.get());
        System.out.println("主线程退出");
    }
}
