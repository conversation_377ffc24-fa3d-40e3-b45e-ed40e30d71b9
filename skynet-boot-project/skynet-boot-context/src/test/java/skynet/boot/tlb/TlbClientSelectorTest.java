package skynet.boot.tlb;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import skynet.boot.annotation.EnableSkynetTlbDiscoveryClient;
import skynet.boot.tlb.domain.ServerIPEndpoint;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
@ContextConfiguration
@EnableSkynetTlbDiscoveryClient
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TlbClientSelectorTest {


    private Integer threadNum = 1;

    private Integer requestNum = 5;

    @Autowired
    private TlbClientSelector tlbClientSelector;

    @Test
    public void test() throws Exception {


        ExecutorService pool = new ThreadPoolExecutor(Math.min(threadNum, requestNum), Math.min(threadNum, requestNum), 60, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1024),
                new ThreadFactoryBuilder().setNameFormat("mock-pool-%d").build(),
                new ThreadPoolExecutor.AbortPolicy());

        //   List<String> serviceIdList = Arrays.asList("skyline-gateway", "skyline-resource", "skyline-router","skyline-console");
        List<String> serviceIdList = Collections.singletonList("skybox-gateway");

        CountDownLatch latch = new CountDownLatch(requestNum);
        for (int i = 0; i < requestNum; i++) {
            pool.submit(() -> {

                // 随机选一个mock数据，读取内容并发送请求
                int idx = RandomUtils.nextInt(0, serviceIdList.size() - 1);
//                List<TlbServiceInstance> objList = tlbClientSelector.getBestServerInstances(serviceIdList.get(idx), "", "", 1);
//                System.out.println(objList);
                ServerIPEndpoint serverIPEndpoint = tlbClientSelector.getBestServer(serviceIdList.get(idx));
                System.out.println(serverIPEndpoint);
//
//                List<TlbServiceInstance> lbServiceInstances = tlbClientSelector.getServiceInstances(serviceIdList.get(idx));
//                System.out.println(lbServiceInstances);
                latch.countDown();
            });
        }
        latch.await();

        System.out.println("--end----");
    }
}