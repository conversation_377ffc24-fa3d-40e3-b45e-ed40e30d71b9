//package skynet.boot.logging;
//
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.slf4j.MDC;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import skynet.SkynetBootTestApp;
//import skynet.boot.SkynetConsts;
//import skynet.boot.logging.config.SkynetLoggingProperties;
//
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SkynetBootTestApp.class)
//class SkynetEllipsesLoggerTest {
//
//    @Autowired
//    private SkynetLoggingProperties properties;
//
//    private final JsonableTest.TestObject testObject;
//
//    public SkynetEllipsesLoggerTest() {
//        JsonableTest.TestObject testObject = new JsonableTest.TestObject();
//        testObject.setArrayStringList(new String[200]);
//        testObject.setArrayIntList(new Integer[200]);
//
//        for (int index = 0; index < testObject.getArrayStringList().length; index++) {
//            testObject.getArrayStringList()[index] = String.valueOf(index);
//            testObject.getArrayIntList()[index] = index;
//            testObject.getStringList().add(String.valueOf(index));
//            testObject.getIntList().add(index);
//            testObject.setString(String.format("%s_%s", testObject.getString(), index));
//        }
//        this.testObject = testObject;
//    }
//
//    @Test
//    public void test1() {
//
//        MDC.put(SkynetConsts.MDC_CURRENT_TRACE_ID_KEY, "123");
//
/// /        log.info(this.testObject.toString());
//        log.info("only String={}", testObject.getString());
//        log.info("testObject={}", this.testObject);
//        log.info("testObject String={}", this.testObject.toString());
//        log.info("index={}; testObject={},objectString={}", 100, this.testObject, this.testObject.toString());
////        log.info("message");
//    }
//
//}
