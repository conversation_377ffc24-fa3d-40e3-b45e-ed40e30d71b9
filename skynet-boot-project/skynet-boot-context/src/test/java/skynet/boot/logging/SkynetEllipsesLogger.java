//package skynet.logging;
//
//import ch.qos.logback.classic.LoggerContext;
//import com.alibaba.fastjson2.JSON;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import skynet.boot.common.domain.Jsonable;
//
//import java.util.function.BooleanSupplier;
//
//
/// **
// * 输出日志，过滤大对象，主要是长字符串和数组，默认 超过100
// * <p>
// * 此类通过  SkynetLoggingEllipsesMessageConverter extends MessageConverter  实现了，后续不再使用，
// * <p>
// * 保留目的主要是为了 后续的代码参考
// *
// * <AUTHOR> 2023年08月30日
// * <p>
// * @since 4.0.13
// */
//@Slf4j
//public class SkynetEllipsesLogger {
//
//    private static final LoggerContext LOGGER_CONTEXT;
//
//    static {
//        LOGGER_CONTEXT = (LoggerContext) LoggerFactory.getILoggerFactory();
//    }
//
//    public static void trace(String format, Object... arguments) {
//        LogContextParam logContextParam = getLogContextParam();
//        logIfEnabled(logContextParam.getLogger()::isTraceEnabled, logContextParam.getLogger()::trace, logContextParam, format, arguments);
//    }
//
//    public static void info(String format, Object... arguments) {
//        LogContextParam logContextParam = getLogContextParam();
//        logIfEnabled(logContextParam.getLogger()::isInfoEnabled, logContextParam.getLogger()::info, logContextParam, format, arguments);
//    }
//
//    public static void debug(String format, Object... arguments) {
//        LogContextParam logContextParam = getLogContextParam();
//        logIfEnabled(logContextParam.getLogger()::isDebugEnabled, logContextParam.getLogger()::debug, logContextParam, format, arguments);
//    }
//
//    public static void warn(String format, Object... arguments) {
//        LogContextParam logContextParam = getLogContextParam();
//        logIfEnabled(logContextParam.getLogger()::isWarnEnabled, logContextParam.getLogger()::warn, logContextParam, format, arguments);
//    }
//
//    public static void error(String format, Object... arguments) {
//        LogContextParam logContextParam = getLogContextParam();
//        logIfEnabled(logContextParam.getLogger()::isErrorEnabled, logContextParam.getLogger()::error, logContextParam, format, arguments);
//    }
//
//    private static void logIfEnabled(BooleanSupplier isEnabled, LogMethod logMethod, LogContextParam logContextParam, String format, Object... arguments) {
//        if (isEnabled.getAsBoolean()) {
//            StackTraceElement caller = logContextParam.getCaller();
//            logMethod.log("[{}]" + format, caller, buildArguments(arguments));
//        }
//    }
//
//    private static LogContextParam getLogContextParam() {
//        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
//        //注意：此处de必须是第三层
//        StackTraceElement caller = stackTrace[3];
//        return new LogContextParam().setLogger(LOGGER_CONTEXT.getLogger(caller.getClassName())).setCaller(caller);
//    }
//
//    private static Object buildArguments(Object... arguments) {
//        if (arguments == null || arguments.length == 0) {
//            return arguments;
//        }
//
//        Object[] temps = new Object[arguments.length];
//        for (int index = 0; index < arguments.length; index++) {
//            //TODO: 非对象中含有字符串
//            temps[index] = JSON.toJSONString(arguments[index], Jsonable.DEFAULT_LARGE_OBJECT_ELLIPSES_FILTER);
//        }
//        return temps;
//    }
//
//    @Getter
//    @Setter
//    @Accessors(chain = true)
//    static class LogContextParam {
//        Logger logger;
//        StackTraceElement caller;
//    }
//
//
//    @FunctionalInterface
//    private interface LogMethod {
//        void log(String format, Object... arguments);
//    }
//}
//
//
////    StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
////    StackTraceElement caller = stackTrace[2]; // Index 2 corresponds to the caller of getInfo()
////
////    String className = caller.getClassName();
////    String methodName = caller.getMethodName();
////    int lineNumber = caller.getLineNumber();