package skynet.boot.discovery.tlb;


import skynet.boot.tlb.client.TlbClient;
import skynet.boot.tlb.client.data.TlbServiceInstance;

import java.time.Duration;
import java.util.Collections;

/**
 * 向LB 汇报服务信息
 */
public class ReportServiceInfoTest {

    public static void main(String[] args) throws Exception {
        try_reportServiceInfo();
    }

    public static void try_reportServiceInfo() throws Exception {

        // 1. 用 url 新建一个 TlbClient 对象.
        String url = "172.31.98.237:3301";
        url = "172.31.234.57:3301";


        // tuling-tlb-sdk 定时上报时间间隔由 TlbClient 的2个参数构造方法的第二个参数指定
        // 如下，第二个参数含义就是定时上报时间间隔，不传默认是 2000 ms
        // TlbClient tlbClient = new TlbClient(url, 2000);
        // 第二个参数值 <= 0 不启动定时上报功能

        TlbClient tlbClient = new TlbClient(Collections.singletonList(url), Duration.ofSeconds(5));

        // 2. 根据自己服务真实情况设置 DetailServiceInfo 对象属性,然后上报.
        try {
            for (int i = 0; i < 200; ++i) { // 上报可循环.
                TlbServiceInstance tlbServiceInstance = new TlbServiceInstance();
                tlbServiceInstance.setCpu(i);
                tlbServiceInstance.setMaxLic(20);
                tlbServiceInstance.setMemory(i);
                tlbServiceInstance.setServiceIP("************");
                tlbServiceInstance.setServiceName("test_reportServiceInfo");
                tlbServiceInstance.setUsedLic(0);
                tlbServiceInstance.setServicePort(9999);
                tlbClient.reportServiceInfo(tlbServiceInstance);
                // tlbClient.removeUid("1234");
                tlbClient.addBizId("1234,5678");
                tlbClient.removeBizId("5678");
                tlbClient.addBizId("9090");
                System.out.println("一次上报动作结束 times:" + i);
                Thread.sleep(2000);
            }
        } catch (Exception e) {
            System.out.println("report error...");
        }

        // 4. 其他业务逻辑...
        int sleepTime = 300000000;
        try {
            Thread.sleep(sleepTime);
        } catch (Exception e) {
            System.out.println("exception...");
        }

        // 5. 使用 tlbClient 对象结束调用 close 方法.
        try {
            tlbClient.clearBizId();
            tlbClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
