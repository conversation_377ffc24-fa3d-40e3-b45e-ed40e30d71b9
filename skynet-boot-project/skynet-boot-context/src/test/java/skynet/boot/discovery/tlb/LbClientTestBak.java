package skynet.boot.discovery.tlb;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.tlb.client.TlbClient;
import skynet.boot.tlb.client.data.TlbClientInitParam;
import skynet.boot.tlb.client.data.TlbServiceInstance;
import skynet.boot.tlb.client.data.TlbServiceInstanceResponse;
import skynet.boot.tlb.domain.BestServerParam;
import skynet.boot.tlb.domain.BestServerResult;
import skynet.boot.tlb.domain.ServerIPEndpoint;
import skynet.boot.tlb.exception.TlbException;

import java.io.File;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * tlb 客户端测试用例，汇报服务，设置bizId，获取服务
 */
@Slf4j
public class LbClientTestBak {
    /**
     * tlb url,多个用逗号分隔
     */
    private static final List<String> lbEndpoints = Collections.singletonList("*************:33000");

    /**
     * 循环次数
     */
    private static final int loopNum = 20000;

    /**
     * 线程 sleep 时间
     */
    private static final long threadSleepTime = 3000;

    // iat_8k_chinese
    private static final String SERVICE_NAME = "test_reportServiceInfo";

    private static TlbClient buildLbClient() throws TlbException {
        String rootKey = String.format("%s%s%s", "yVqsOYC8cgi5XiwjgjmGKyYhctLKZ6o18TdhKDwV084vVlE5mSSj05Fh0ITkK3YSeXWY7hae6kHH9VxGp\n",
                "JbYPBxxLn8fR0gC5aLhmURiyM7mY7wfQHa7RwX6MoryljX2vY78C3kbzqZfj87MMFaJSdEWIGNX5m4WIFggJtUX\n",
                "ONjOgjyLSJuLIDIvTzsOhDltVOwJ47l2Jp6zV9j8CzVGoJ98BKDwkmIFxS1wU3w8SzzQYnIcHqyQD7dn4pERPrpx");

        TlbClientInitParam cip = new TlbClientInitParam();
        cip.setRpcTimeout(Duration.ofSeconds(10));
        cip.setAuthority("iflytek.com");
        cip.setEncryptedSecret("8d5aa658adac610021a7e90d-8b203b7197b26f67614b17da45933300a64543c4f5a1d8184593c73f3d1c13e9efdb76be06d6054ba36eb6011c3764a0");
        cip.setRootSecret(rootKey);
        cip.setSalt("b25fd269f0a7a5e2a835a2f4bd91e1d26895197648fb1129327da2936179ddd6");
        cip.setTrustCertCollectionFile(new File("/home/<USER>/winD/trash/2021-09-27/server.pem"));
        cip.setEnableCallCredentials(true);
        return new TlbClient(lbEndpoints, Duration.ofHours(10), Duration.ofHours(10)/*, cip*/);
    }

    /**
     * 测试 getBestServer 接口
     *
     * @throws TlbException
     */
    private static void testGetBestServer() throws TlbException {
        TlbClient tlbClient = buildLbClient();

        BestServerParam bestServerParam = new BestServerParam();
        bestServerParam.setServiceName(SERVICE_NAME); // 要获取的服务名称
        bestServerParam.setNumber(10); // 要获取的服务地址数量
        /*Map<String, String> bizIdInfo = new HashMap<>();
        bizIdInfo.put("SELECT_SERVER_FULL", "false");*/
        //查询bizId绑定的服务地址
        bestServerParam.setBizId("test022");
        bestServerParam.setReqType("");
        // bestServerParam.setServiceInfo(bizIdInfo);
        List<Long> elapsedTimeList = new ArrayList<>();

        BestServerResult bestServerResult;
        for (int i = 0; i < loopNum; ++i) {
            try {
                long start = System.currentTimeMillis();
                bestServerResult = tlbClient.getBestServer(bestServerParam);
                elapsedTimeList.add(System.currentTimeMillis() - start);
                printBestServerResult(bestServerResult, "main thread. ");
            } catch (Exception e) {
                log.error(String.format("测试 testGetBestServer 错误,第 %s 次执行", i), e);
            }
        }

        double avg = 0L;
        for (Long oneElapsedTime : elapsedTimeList) {
            avg = (oneElapsedTime + avg);
        }

        log.info("total time: {}", avg);
        log.info("list size: {}", elapsedTimeList.size());
        log.info("average elapsed time: {}", avg / elapsedTimeList.size());
        try {
            Thread.sleep(5 * 1000);
            tlbClient.close();
        } catch (Exception e) {
            log.error("testGetBestServer 测试 关闭 tlbClient 异常");
        }
    }

    /**
     * 测试 getServerInfo 接口
     *
     * @throws TlbException
     */
    private static void testGetServerInfo() throws TlbException {
        TlbClient tlbClient = buildLbClient();
        TlbServiceInstanceResponse tlbServiceInstanceResponse;

        for (int i = 0; i < loopNum; ++i) {
            try {
                tlbServiceInstanceResponse = tlbClient.getServiceInfo("");
                printServiceInfoResult(tlbServiceInstanceResponse, "main thread ");
            } catch (Exception e) {
                log.error(String.format("测试 testGetServerInfo 错误,第 %s 次执行", i), e);
            }
            try {
                Thread.sleep(threadSleepTime);
            } catch (Exception e) {
                log.error("testGetServerInfo 测试 Thread.sleep exception ", e);
            }
        }

        try {
            tlbClient.close();
        } catch (Exception e) {
            log.error("testGetServerInfo 测试 关闭 tlbClient 异常");
        }
    }

    /**
     * 测试 汇报 接口
     *
     * @throws TlbException
     */
    private static void testReport() throws TlbException {
        // 多个 tlb url 用英文分号分隔
        TlbClient tlbClient = buildLbClient();

        for (int i = 0; i < loopNum; ++i) {
            try {
                TlbServiceInstance tlbServiceInstance = new TlbServiceInstance();
                tlbServiceInstance.setCpu(i);
                tlbServiceInstance.setMaxLic(20);
                tlbServiceInstance.setMemory(i);
                tlbServiceInstance.setServiceIP("************");
                tlbServiceInstance.setServiceName(SERVICE_NAME);
                tlbServiceInstance.setUsedLic(0);
                tlbServiceInstance.setServicePort(9999);

                tlbClient.reportServiceInfo(tlbServiceInstance);
                //给服务设置bizId，获取地址时可根据bizId查询。
                tlbClient.addBizId("75");
                log.info("testReport 测试上报第 {} 次 结束", i);
            } catch (Exception e) {
                log.error(String.format("测试第 %s 次上报错误", i), e);
            }
            try {
                Thread.sleep(threadSleepTime);
            } catch (Exception e) {
                log.error("testReport 测试 Thread.sleep exception ", e);
            }
        }

        try {
            //调用close后将与LB服务端失去心跳，服务将被剔除下线
            tlbClient.close();
        } catch (Exception e) {
            log.error("testReport 测试 关闭 tlbClient 异常");
        }
    }

    private static void testAddBizId() throws TlbException {
        // 多个 tlb url 用英文分号分隔
        TlbClient tlbClient = buildLbClient();

        try {
            TlbServiceInstance tlbServiceInstance = new TlbServiceInstance();
            tlbServiceInstance.setCpu(50);
            tlbServiceInstance.setMaxLic(20);
            tlbServiceInstance.setMemory(45);
            tlbServiceInstance.setServiceIP("************");
            tlbServiceInstance.setServiceName(SERVICE_NAME);
            tlbServiceInstance.setUsedLic(0);
            tlbServiceInstance.setServicePort(9999);
            tlbClient.reportServiceInfo(tlbServiceInstance);
            // 上报可循环. bizId用于个性化资源
            for (int i = 0; i < 200; ++i) {
                tlbClient.addBizId("1234,5678");
                System.out.println("一次上报动作结束 times:" + i);
                Thread.sleep(2000);
            }
        } catch (Exception e) {
            System.out.println("report error...");
        }

        // 4. 其他业务逻辑...
        int sleepTime = 300000000;
        try {
            Thread.sleep(sleepTime);
        } catch (Exception e) {
            System.out.println("exception...");
        }

        // 5. 使用 tlbClient 对象结束调用 close 方法.
        try {
            tlbClient.clearBizId();
            tlbClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void printServiceInfoResult(TlbServiceInstanceResponse tlbServiceInstanceResponse, String threadInfo) {
        String detailServiceInfo = "";
        Map<String, List<TlbServiceInstance>> detailServiceInfoList = tlbServiceInstanceResponse.getServiceInstanceMap();
        detailServiceInfo = JSON.toJSONString(detailServiceInfoList);
        String serviceInfoResultContent = String.format("%s errorInfo=%s,errorCode=%s,detailServiceInfo=%s", threadInfo, tlbServiceInstanceResponse.getErrorInfo(), tlbServiceInstanceResponse.getErrorCode(),
                detailServiceInfo);
        log.info(serviceInfoResultContent);
    }

    private static void printBestServerResult(BestServerResult bestServerResult, String threadInfo) {
        List<ServerIPEndpoint> addressList = bestServerResult.getAddressList();
        if (null == addressList || addressList.size() == 0) {
            log.warn(threadInfo + " value is null, no print");
            return;
        }
        String addressListContent = "";
        for (int i = 0; i < addressList.size(); i++) {
            addressListContent = ",ServerIp=" + addressList.get(i).getIp() + ",ServerPost=" + addressList.get(i).getPort();
        }
        String bestServerResultContent = threadInfo + "errorInfo=" + bestServerResult.getErrorInfo() + ",errorCode=" + bestServerResult.getErrorCode() + addressListContent;
        System.out.println(bestServerResultContent);
    }

    public static void threadTestReport() {
        Thread testReportThread = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    testReport();
                } catch (TlbException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        });
        testReportThread.setName("test-report-thread");
        testReportThread.start();
    }

    public static void threadGetServerInfo() {
        Thread threadGetServerInfo = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    testGetServerInfo();
                } catch (TlbException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        });
        threadGetServerInfo.setName("test-getServerInfo-thread");
        threadGetServerInfo.start();
    }

    public static void threadGetBestServer() {
        Thread threadGetBestServer = new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    testGetBestServer();
                } catch (TlbException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        });
        threadGetBestServer.setName("test-getBestServer-thread");
        threadGetBestServer.start();
    }

    public static void main(String[] args) throws Exception {
        // testReport();
        testGetBestServer();
        //testGetServerInfo();


        /* 启动线程测试 */
        // threadTestReport();
        /*threadGetBestServer();*/
        // threadGetServerInfo();
    }

	/*public static void main(String[] args) throws Exception {

		//初始化, lbClient  在进程级复用
		TlbClient lbClient = new TlbClient("*************:3301;*************:3301");
		reportServerNode(lbClient);
		getServerNode(lbClient);

		lbClient.close();
	}

	//上报服务节点
	static void reportServerNode(TlbClient lbClient) throws Exception {
		DetailServiceInfo detailServiceInfo = new DetailServiceInfo();
		detailServiceInfo.setServiceName("iat_en_v10");

		//服务所在IP 和端口
		detailServiceInfo.setServiceIP("*********");
		detailServiceInfo.setServicePort(9999);
		//使用路数，一般在上报的时候获取 当时的在线数
		detailServiceInfo.setUsedLic(0);

		lbClient.reportServiceInfo(detailServiceInfo);

		//服务并发路数发生变化时
		lbClient.addBizId("uid_xxx");

		//移除 uid_xxx
		lbClient.removeBizId("uid_xxx");

	}

	//获取最优服务节点
	static void getServerNode(TlbClient lbClient) throws Exception {

		BestServerParam bestServerParam = new BestServerParam();
		bestServerParam.setServiceName("iat_en_v10");
		bestServerParam.setNumber(1);
		bestServerParam.setBizId("uid_xxxx");
		BestServerResult bestServerResult = lbClient.getBestServer(bestServerParam);

		if (bestServerResult.getErrorCode() == 0 && bestServerResult.getAddressList().size() > 0) {
			List<ServerIPEndpoint> addressList = bestServerResult.getAddressList();
			System.out.println("best server node:" + addressList.get(0));
		} else {
			//输出ERROR信息
			String errorInfo = bestServerResult.getErrorInfo();
		}
	}*/
}
