package skynet.boot.discovery.tlb;

import java.util.ArrayList;
import java.util.List;

public class MultiThreadLbClientTest {
    public static void main(String[] args) {
        int threadCount = 3;
        List<Thread> threadList = new ArrayList<>();

        for (int i = 0; i < threadCount; ++i) {
            LbClientMyRunnableThread oneThread = new LbClientMyRunnableThread("【 thread " + i + " 】... ");
            threadList.add(new Thread(oneThread));
        }

        for (int i = 0; i < threadCount; ++i) {
            threadList.get(i).start();
        }
    }
}
