package skynet.boot.discovery;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.junit.After;
import org.junit.Before;


@Slf4j
public class BaseTest {

    StopWatch stopWatch = new StopWatch();

    @Before
    public void setUp() throws Exception {
        stopWatch.start();

    }

    @After
    public void tearDown() throws Exception {
        stopWatch.stop();

        log.info("-- {} cost={} --", this.getClass().getSimpleName(), stopWatch);
    }
}