package skynet.boot.discovery.tlb;

import skynet.boot.discovery.tlb.client.OneLbClientTest;

public class LbClientMyRunnableThread implements Runnable {

    private final String threadInfo;

    public LbClientMyRunnableThread(String threadInfo_) {
        this.threadInfo = threadInfo_;
    }

    @Override
    public void run() {
        OneLbClientTest.try_getBestServer(threadInfo);
        OneLbClientTest.try_getServiceInfo(threadInfo);
    }
}
