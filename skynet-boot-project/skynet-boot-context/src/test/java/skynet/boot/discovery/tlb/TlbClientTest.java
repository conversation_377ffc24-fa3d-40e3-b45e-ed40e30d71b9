package skynet.boot.discovery.tlb;

import jakarta.annotation.Resource;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import skynet.boot.AppContext;
import skynet.boot.TestApplication;
import skynet.boot.tlb.TlbClientRegister;

import java.util.HashMap;
import java.util.Map;

/**
 * tlb 客户端测试用例，汇报服务，设置bizId，获取服务
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles("test")
public class TlbClientTest {

    @Resource
    private TlbClientRegister tlbClientRegister;

    boolean isInitialize;

    @Autowired
    private AppContext appContext;

    @Before
    public void isInit() {
        isInitialize = tlbClientRegister.isInitialize();
        Assert.assertFalse(isInitialize);
    }


    @Test
    public void initialize0() throws Exception {
        tlbClientRegister.initialize(111);
        Assert.assertTrue(tlbClientRegister.isInitialize());
    }

    @Test
    public void initialize1() throws Exception {
        tlbClientRegister.initialize("test1", appContext.getPort(), 100);
        Assert.assertTrue(tlbClientRegister.isInitialize());
    }

    @Test
    public void initialize2() throws Exception {
        tlbClientRegister.initialize("test2", appContext.getNodeName(), appContext.getPort(), 100);
        Assert.assertTrue(tlbClientRegister.isInitialize());
    }

    @Test
    public void initialize3() throws Exception {
        tlbClientRegister.initialize("test3", appContext.getNodeName(), appContext.getPort(), 100);
        Assert.assertTrue(tlbClientRegister.isInitialize());
    }

    @Test
    public void initialize4() throws Exception {
        tlbClientRegister.initialize("0307", "test4", appContext.getNodeName(), appContext.getPort(), 100);
        Assert.assertTrue(tlbClientRegister.isInitialize());
    }

    @Test
    public void addTag() throws Exception {
        initialize1();
        tlbClientRegister.addTag("test");
    }

    @Test
    public void moveTag() throws Exception {
        initialize1();
        tlbClientRegister.removeTag("test");

    }

    @Test
    public void addAndMoveTag() throws Exception {
        initialize0();
        tlbClientRegister.addTag("testTEST");
        tlbClientRegister.removeTag("testTEST");

    }

    @Test
    public void addBizId() throws Exception {
        initialize2();
        tlbClientRegister.addBizId("bizId1");
    }

    @Test
    public void removeBizId() throws Exception {
        initialize2();
        tlbClientRegister.removeBizId("bizId1");

    }

    @Test
    public void reportUsedLic() throws Exception {
        initialize3();
        tlbClientRegister.reportUsedLic(11);
    }

    @Test
    public void reportUsage() throws Exception {
        initialize1();
        tlbClientRegister.reportUsage();
        tlbClientRegister.reportUsage();

    }

    @Test
    public void reportRelease() throws Exception {
        initialize0();
        tlbClientRegister.reportUsage();
        tlbClientRegister.reportUsage();
        tlbClientRegister.reportRelease();
    }

    @Test
    public void reportMaxLic() throws Exception {
        initialize1();
        Thread.sleep(10000);
        tlbClientRegister.reportMaxLic(-1);
    }

    @Test
    public void reportMetaData() throws Exception {
        initialize4();
        Map<String, String> map = new HashMap<>();
        map.put("test", "tValue");
        tlbClientRegister.reportMetaData(map);

    }


    @After
    public void sleep() throws Exception {
        System.out.println("close thread sleep");
        Thread.sleep(10000);
    }

}
