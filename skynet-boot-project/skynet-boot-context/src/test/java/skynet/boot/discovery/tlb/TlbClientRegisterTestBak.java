package skynet.boot.discovery.tlb;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import skynet.boot.tlb.TlbClientRegister;

@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = AntWorkerApp.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class TlbClientRegisterTestBak {


    static {
        System.setProperty("spring.profiles.active", "test");
    }

    @Autowired
    private TlbClientRegister tlbClientRegister;

    /**
     * 具体的业务测试
     *
     * @throws Exception
     */
    @Test
    public void testInit() throws Exception {
        log.info("testInit");
        for (int i = 0; i < 1; ++i) {
            String targetActionName = String.format("LbServiceTest%s", i);
            int port = 1234 + i;
            int maxLic = 30;

            tlbClientRegister.initialize(targetActionName, port, maxLic);
            // lbService.reportUsedLic(usedLic);
            // lbService.addBizId(bizId);
            // lbService.removeBizId(bizId);
            // lbService.getBestServer(bestServerParam);

        }
        Thread.sleep(3000);

    }

    @Test
    public void testReportServiceInfo() throws Exception {
        testInit();
        for (int i = 0; i < 9; ++i) {
            tlbClientRegister.reportUsedLic(i);
            Thread.sleep(3000);
        }
        tlbClientRegister.close();
        Thread.sleep(5000);
    }

    @Test
    public void testAddBizId() throws Exception {
        testInit();
        for (int i = 0; i < 9; ++i) {
            tlbClientRegister.addBizId(String.valueOf(i));
            Thread.sleep(5000);
        }
        tlbClientRegister.close();
        Thread.sleep(3000);
    }

    @Test
    public void testRemoveBizId() throws Exception {
        testInit();
        for (int i = 0; i < 9; ++i) {
            // lbService.addBizId(String.valueOf(i));
            Thread.sleep(4000);
            tlbClientRegister.removeBizId(String.valueOf(i));
        }
        tlbClientRegister.close();
        Thread.sleep(3000);
    }
}
