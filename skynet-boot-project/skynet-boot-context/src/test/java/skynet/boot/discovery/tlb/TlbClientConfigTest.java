package skynet.boot.discovery.tlb;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import skynet.boot.tlb.service.TlbClientBuilder;

@Slf4j
public class TlbClientConfigTest {


    static {
        System.setProperty("spring.profiles.active", "test");
    }

    @Autowired
    private TlbClientBuilder tlbClientBuilder;

    /**
     * 具体的业务测试
     *
     * @throws Exception
     */
    @Test
    public void get() throws Exception {
        for (int i = 0; i < 10; i++) {
            log.info("Index= {}\t client = {}", i, tlbClientBuilder.newTlbClient() == null);
        }
    }
}