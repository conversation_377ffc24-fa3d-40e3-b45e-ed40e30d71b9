//package skynet.boot.discovery;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.testng.Assert;
//import skynet.boot.pandora.ogma.BaseTest;
//import skynet.boot.pandora.ogma.OgmaTestBoot;
//import skynet.boot.pandora.ogma.data.UrlRequestData;
//
//import java.net.URI;
//import java.net.URISyntaxException;
//import java.util.Collections;
//import java.util.List;
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = OgmaTestBoot.class)
//public class DefaultLoadBalancerTest extends BaseTest {
//
//
//    @Autowired(required = false)
//    public DefaultLoadBalancer defaultLoadBalancer;
//
//
//    @Test
//    public void choose() throws URISyntaxException {
//        Assert.assertNotNull(defaultLoadBalancer);
//        log.info("-----------------------------------");
//        List<String> urlList = UrlRequestData.getUriList();
//        for (String url : urlList) {
//            URI uri = defaultLoadBalancer.choose(url, Collections.EMPTY_MAP);
//            log.info("{} ==> {}", url, uri);
//        }
//        log.info("-----------------------------------");
//
//        //多个的URL，是否每次随机
//        urlList.clear();
//        for (int i = 0; i < 10; i++) {
//            urlList.add(String.format("grpc://127.0.0.1:90%s/api/v3/talk", i));
//        }
//
//        String urls = StringUtils.join(urlList, ";");
//        for (int i = 0; i < 50; i++) {
//            URI uri = defaultLoadBalancer.choose(urls, Collections.EMPTY_MAP);
//            log.info("{} ==> {}", i, uri);
//        }
//
//        log.info("-----------------------------------");
//
//    }
//}