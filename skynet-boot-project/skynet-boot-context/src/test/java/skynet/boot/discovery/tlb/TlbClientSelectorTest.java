package skynet.boot.discovery.tlb;

import io.jsonwebtoken.lang.Assert;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import skynet.boot.AppContext;
import skynet.boot.TestApplication;
import skynet.boot.tlb.TlbClientRegister;
import skynet.boot.tlb.TlbClientSelector;
import skynet.boot.tlb.client.data.TlbServiceInstance;
import skynet.boot.tlb.client.data.TlbServiceInstanceResponse;
import skynet.boot.tlb.domain.LbFeature;
import skynet.boot.tlb.domain.ServerIPEndpoint;
import skynet.boot.tlb.domain.Tag;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * tlb 客户端测试用例，汇报服务，设置bizId，获取服务
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TestApplication.class})
@ActiveProfiles("test")
public class TlbClientSelectorTest {

    @Autowired
    private TlbClientRegister tlbClientRegister;

    @Autowired
    private TlbClientSelector tlbClientSelector;

    @Autowired
    private AppContext appContext;

    private final String serviceName = "abc";


    @Before
    public void setUp() throws Exception {
        tlbClientRegister.initialize(1000);
        tlbClientRegister.initialize(serviceName, 8080, 1000);
    }


    @Test
    public void getBestServer() throws Exception {
        ServerIPEndpoint serverIPEndpoint = tlbClientSelector.getBestServer(serviceName);
        Assert.notNull(serverIPEndpoint);
        System.out.println(serverIPEndpoint);
    }

    @Test
    public void getBestServer2() throws Exception {
        Tag tag = new Tag();
        Set<String> set = new HashSet<>();
        set.add("ABC");
        ServerIPEndpoint serverIPEndpoint = tlbClientSelector.getBestServer(serviceName, tag);
        System.out.println(serverIPEndpoint);
    }

    @Test
    public void getBestServer3() throws Exception {
        Tag tag = new Tag();
        Set<String> set = new HashSet<>();
        set.add("ABC");
        List<ServerIPEndpoint> bestServers = tlbClientSelector.getBestServers("uocr", "ABC", "bizId", 1, LbFeature.DISABLED_SELECT_SERVER_DEFAULT);
        System.out.println(bestServers);
    }

    @Test
    public void getServiceInstances() throws Exception {
        List<TlbServiceInstance> list = tlbClientSelector.getServiceInstances("springboot@skynet-sample");
        System.out.println(list.toString());
    }


    @Test
    public void getServiceInfo() throws Exception {
        TlbServiceInstanceResponse tlbServiceInstanceResponse = tlbClientSelector.getServiceInfo(serviceName);
        System.out.println(tlbServiceInstanceResponse.toString());
    }


    @After
    public void sleep() throws Exception {
        System.out.println("close thread sleep");
        Thread.sleep(10000);
    }
}
