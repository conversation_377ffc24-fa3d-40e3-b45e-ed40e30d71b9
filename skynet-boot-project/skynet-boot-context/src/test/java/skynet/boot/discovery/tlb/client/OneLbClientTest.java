package skynet.boot.discovery.tlb.client;

import com.alibaba.fastjson2.JSON;
import skynet.boot.tlb.client.OneTlbClient;
import skynet.boot.tlb.client.data.ReportResult;
import skynet.boot.tlb.client.data.TlbClientInitParam;
import skynet.boot.tlb.client.data.TlbServiceInstance;
import skynet.boot.tlb.client.data.TlbServiceInstanceResponse;
import skynet.boot.tlb.domain.BestServerParam;
import skynet.boot.tlb.domain.BestServerResult;
import skynet.boot.tlb.domain.ServerIPEndpoint;

import java.util.List;
import java.util.Map;

public class OneLbClientTest {
    public static void main(String[] args) {
        try_getBestServer("【main function...】");
    }

    public static void try_getBestServer(String threadInfo) {
        // 1. 用 url 新建一个 OneTlbClient 对象.
        String url = "**************:32001";
//        String url = "*************:33000";


//        skynet.tlb.endpoints=**************:32001
//        skynet.tlb.http-endpoints=**************:32101,**************:32102

        OneTlbClient oneTlbClient = new OneTlbClient(url, new TlbClientInitParam());

        // 2. 用初始化一个 BestServerParam 对象,用于调用 getBestServer() 方法.
        BestServerParam bestServerParam = new BestServerParam();
        bestServerParam.setServiceName("skybox-gateway");// 要获取的服务名称
        bestServerParam.setNumber(1);// 要获取的服务地址数量
        //bestServerParam.setBizId("75");// UID用于个性化

        //oneTlbClient.reportServiceInfo();
        BestServerResult bestServerResult = null;
        for (int i = 0; i < 1000; ++i) {
            try {
                bestServerResult = oneTlbClient.getBestServer(bestServerParam);
                printBestServerResult(bestServerResult, threadInfo);

                TlbServiceInstanceResponse bestServerInstances = oneTlbClient.getBestServerInstances(bestServerParam);
                System.out.println("bestServerInstances={}" + bestServerInstances);

                System.out.println("getBestServer 执行一次 " + i);
            } catch (Exception e) {
                System.out.println("getBestServer() exception...");
            }
//            try {
//                Thread.sleep(2000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
        }

        // 3. 返回结果调用者自行处理,此处示例仅打印出内容.
        printBestServerResult(bestServerResult, threadInfo + "【last time】");

        // 4. 其他业务逻辑...
        int sleepTime = 9000;
        try {
            Thread.sleep(sleepTime);
        } catch (Exception e) {
            System.out.println("exception...");
        }

        // 5. 使用类结束后关闭.
        try {
            oneTlbClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void try_getServiceInfo(String threadInfo) {
        // 1. 用 url 新建一个 OneTlbClient 对象.
        // String url = "172.31.194.79:33001";
        String url = "192.168.83.206:33030";

        OneTlbClient oneTlbClient = new OneTlbClient(url, new TlbClientInitParam());

        // 2. 设定选择的服务名称,调用 getServiceInfo() 方法.
        String serviceName = "";
        TlbServiceInstanceResponse tlbServiceInstanceResponse = null;
        for (int i = 0; i < 60; ++i) {
            try {
                tlbServiceInstanceResponse = oneTlbClient.getServiceInfo(serviceName);
                printServiceInfoResult(tlbServiceInstanceResponse, threadInfo);
                System.out.println("getServiceInfo 一次 " + i);
                Thread.sleep(2000);
            } catch (Exception e) {
                System.out.println("getServiceInfo exception...");
            }
        }

        // 3. 返回结果调用者自行处理,此处示例仅打印出内容.
        printServiceInfoResult(tlbServiceInstanceResponse, threadInfo);

        // 4. 其他业务逻辑...
        int sleepTime = 9000;
        try {
            Thread.sleep(sleepTime);
        } catch (Exception e) {
            System.out.println("exception...");
        }

        // 5. 使用类结束后关闭.
        try {
            oneTlbClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void printBestServerResult(BestServerResult bestServerResult, String threadInfo) {
        List<ServerIPEndpoint> addressList = bestServerResult.getAddressList();
        if (null == addressList || addressList.size() == 0) {
            System.out.println(threadInfo + " value is null, no print");
            return;
        }
        String addressListContent = "";
        for (int i = 0; i < addressList.size(); i++) {
            addressListContent = ",ServerIp=" + addressList.get(i).getIp() + ",ServerPost=" + addressList.get(i).getPort();
        }
        /*
         * if (addressListContent.length() > 0) { addressListContent = addressListContent.substring(1, addressListContent.length() - 1); }
         */
        String bestServerResultContent = threadInfo + "errorInfo=" + bestServerResult.getErrorInfo() + ",errorCode=" + bestServerResult.getErrorCode() + addressListContent;

        System.out.println(bestServerResultContent);
    }

    public static void printServiceInfoResult(TlbServiceInstanceResponse tlbServiceInstanceResponse, String threadInfo) {
        String detailServiceInfo = "";

        Map<String, List<TlbServiceInstance>> detailServiceInfoList = tlbServiceInstanceResponse.getServiceInstanceMap();
        detailServiceInfo = JSON.toJSONString(detailServiceInfoList);

        String serviceInfoResultContent = String.format("%s errorInfo=%s,errorCode=%s,detailServiceInfo=%s", threadInfo, tlbServiceInstanceResponse.getErrorInfo(), tlbServiceInstanceResponse.getErrorCode(),
                detailServiceInfo);

        System.out.println(serviceInfoResultContent);
    }

    public static void printReportResult(ReportResult reportResult) {
        String content = String.format("errorCode=%s, errorInfo=%s", reportResult.getErrorCode(), reportResult.getErrorInfo());
        System.out.println(content);
    }
}
