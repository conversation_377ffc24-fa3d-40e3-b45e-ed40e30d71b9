//package skynet.boot.discovery;
//
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.testng.Assert;
//import skynet.boot.pandora.ogma.BaseTest;
//import skynet.boot.pandora.ogma.OgmaTestBoot;
//import skynet.boot.pandora.ogma.core.data.ServiceInstance;
//import skynet.boot.pandora.ogma.data.UrlRequestData;
//
//import java.util.HashMap;
//import java.util.Map;
//
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = OgmaTestBoot.class)
//public class ServiceChooser4lbTest extends BaseTest {
//
//    @Autowired(required = false)
//    public ServiceChooser4lb serviceChooser4lb;
//
//
//    @Test
//    public void getScheme() {
//        Assert.assertNotNull(serviceChooser4lb);
//        Assert.assertEquals("lb", serviceChooser4lb.getScheme());
//    }
//
//    @Test
//    public void choose() {
//
//        String serviceId = UrlRequestData.lbServiceId;
//        Map<String, String> context = new HashMap<>();
//        ServiceInstance serviceInstance = serviceChooser4lb.choose(serviceId, context);
//        log.debug("serviceInstance={}", serviceInstance);
//        Assert.assertNotNull(serviceInstance);
//        Assert.assertEquals(serviceInstance.getServiceId(), serviceId);
//
//        context.put(ServiceChooser.TLB_TAG_SELECTOR_KEY, "A");
//        serviceInstance = serviceChooser4lb.choose(serviceId, context);
//        log.debug("serviceInstance={}", serviceInstance);
//        Assert.assertNotNull(serviceInstance);
//        Assert.assertEquals(serviceInstance.getServiceId(), serviceId);
//    }
//}