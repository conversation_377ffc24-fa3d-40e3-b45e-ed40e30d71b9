package skynet.boot.common.disruptor.impl;

import skynet.boot.common.disruptor.DataEvent;
import skynet.boot.common.disruptor.DataEventHandler;
import skynet.boot.common.disruptor.DataPipeline;

import java.util.List;

public class MyDataPipeline extends DataPipeline<DataEvent<Integer>, Object> {

    private final List<DataEventHandler<DataEvent<Integer>, Object>> handlers;

    public MyDataPipeline(List<DataEventHandler<DataEvent<Integer>, Object>> handlers) {
        this.handlers = handlers;
    }


    @Override
    public int getBufferSize() {
        return 4096;
    }

    @Override
    protected void onInit(Object param) throws Exception {
    }

    @Override
    protected List<DataEventHandler<DataEvent<Integer>, Object>> getHandlers() {
        return handlers;
    }

    @Override
    protected void onClose() throws Exception {
        //
    }
}
