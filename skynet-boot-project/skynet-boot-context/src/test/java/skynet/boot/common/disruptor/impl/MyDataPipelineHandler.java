package skynet.boot.common.disruptor.impl;

import skynet.boot.common.disruptor.DataEvent;
import skynet.boot.common.disruptor.DataPipelineHandler;

public class MyDataPipelineHandler extends DataPipelineHandler<DataEvent<Integer>, Object> {
    @Override
    protected void onInit(Object param) throws Exception {
        // TODO Auto-generated method stub
    }

    @Override
    public void onEvent(DataEvent<DataEvent<Integer>> event, boolean endOfBatch) throws Exception {
        // 从管道中获取数据，进行处理
        System.out.println("run - " + event.getValue() + "\t" + System.currentTimeMillis());
        // Thread.sleep(1000);
    }

    @Override
    protected void onClose() throws Exception {
        // TODO Auto-generated method stub

    }
}