package skynet.boot.common.utils;

import org.junit.jupiter.api.*;
import skynet.boot.common.FileZipUtil;

import java.io.File;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FileZipUtilTest {

    private File tempFolder;
    private File testFile;
    private File zipFile;

    @BeforeAll
    public void setUp() throws Exception {
        // 创建临时文件夹和测试文件
        tempFolder = Files.createTempDirectory("tempFolder").toFile();
        testFile = new File(tempFolder, "test.txt");
        Files.write(testFile.toPath(), "This is a test file.".getBytes());

        // 创建用于压缩的文件
        zipFile = new File(tempFolder, "test.zip");
    }

    @AfterEach
    public void tearDown() throws Exception {
        // 每次测试后删除压缩文件和测试文件，保持测试环境清洁
        if (zipFile.exists()) {
            zipFile.delete();
        }
        if (testFile.exists()) {
            testFile.delete();
        }
    }

    @AfterAll
    public void cleanUp() throws Exception {
        // 最后删除临时文件夹
        if (tempFolder.exists()) {
            tempFolder.delete();
        }
    }

    /**
     * 压缩并解压文件，验证解压后的文件内容和数量。
     *
     * @throws Exception
     */
    @Test
    public void testZipAndUnzip() throws Exception {
        // 压缩文件
        FileZipUtil.zip(testFile.getAbsolutePath(), zipFile.getAbsolutePath());
        assertTrue(zipFile.exists());

        // 解压文件到新目录
        File unzipFolder = new File(tempFolder, "unzip");
        unzipFolder.mkdir();
        FileZipUtil.unZip(zipFile.getAbsolutePath(), unzipFolder.getAbsolutePath());

        File extractedFile = new File(unzipFolder, "test.txt");
        assertTrue(extractedFile.exists());

        // 检查解压文件内容
        String content = new String(Files.readAllBytes(extractedFile.toPath()));
        assertEquals("This is a test file.", content);
    }
}
