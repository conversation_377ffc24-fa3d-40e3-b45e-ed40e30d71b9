package skynet.boot.common.utils;

import com.google.common.collect.Range;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class RangeUtilTest {

    // --- RangeCheckerUtil Tests ---

    /**
     * 测试范围是否连续且不重叠
     */
    @Test
    public void testAreRangesContinuousAndNonOverlapping_ValidRanges() {
        List<Range<Double>> ranges = Arrays.asList(
                Range.closed(1.0, 2.0),
                Range.open(2.0, 3.0),
                Range.closedOpen(3.0, 4.0)
        );

        boolean result = RangeCheckerUtil.areRangesContinuousAndNonOverlapping(ranges);
        assertTrue(result);
    }

    /**
     * 测试范围是否连续且不重叠
     */
    @Test
    public void testAreRangesContinuousAndNonOverlapping_InvalidRanges() {
        List<Range<Double>> ranges = Arrays.asList(
                Range.closed(1.0, 2.0),
                Range.closed(2.0, 3.0),
                Range.closedOpen(3.0, 4.0)
        );

        boolean result = RangeCheckerUtil.areRangesContinuousAndNonOverlapping(ranges);
        assertFalse(result);
    }

    /**
     * 测试范围是否连续且不重叠
     *
     * @throws RuntimeException ranges cannot be null
     */
    @Test
    public void testAreRangesContinuousAndNonOverlapping_EmptyListThrowsException() {
        Exception exception = assertThrows(RuntimeException.class, () -> {
            RangeCheckerUtil.areRangesContinuousAndNonOverlapping(Collections.emptyList());
        });
        assertEquals("ranges cannot be null", exception.getMessage());
    }

    /**
     * 测试范围是否覆盖整个区间
     */
    @Test
    public void testDoRangesCoverWholeInterval_Valid() {
        List<Range<Double>> ranges = Arrays.asList(
                Range.closed(1.0, 2.0),
                Range.open(2.0, 3.0),
                Range.closed(3.0, 4.0)
        );
        Range<Double> targetRange = Range.closed(1.0, 4.0);

        boolean result = RangeCheckerUtil.doRangesCoverWholeInterval(ranges, targetRange);
        assertTrue(result);
    }

    /**
     * 测试范围是否覆盖整个区间
     */
    @Test
    public void testDoRangesCoverWholeInterval_Invalid() {
        List<Range<Double>> ranges = Arrays.asList(
                Range.closed(1.0, 2.0),
                Range.open(2.0, 3.0),
                Range.closed(3.0, 4.0)
        );
        Range<Double> targetRange = Range.closed(1.0, 5.0); // Target range extends beyond the available ranges

        boolean result = RangeCheckerUtil.doRangesCoverWholeInterval(ranges, targetRange);
        assertFalse(result);
    }

    // --- RangeConvertUtil Tests ---

    /**
     * 测试范围是否连续且不重叠
     */
    @Test
    public void testConvertToRange_ClosedRange() {
        RangeConvertUtil.CustomRange customRange = new RangeConvertUtil.CustomRange();
        customRange.setStart(1.0);
        customRange.setEnd(2.0);
        customRange.setType("closed");

        Range<Double> range = RangeConvertUtil.convertToRange(customRange);
        assertEquals(Range.closed(1.0, 2.0), range);
    }

    /**
     * 测试范围是否连续且不重叠
     */
    @Test
    public void testConvertToRange_OpenRange() {
        RangeConvertUtil.CustomRange customRange = new RangeConvertUtil.CustomRange();
        customRange.setStart(1.0);
        customRange.setEnd(2.0);
        customRange.setType("open");

        Range<Double> range = RangeConvertUtil.convertToRange(customRange);
        assertEquals(Range.open(1.0, 2.0), range);
    }

    /**
     * @throws IllegalArgumentException if type is invalid
     */
    @Test
    public void testConvertToRange_InvalidType() {
        RangeConvertUtil.CustomRange customRange = new RangeConvertUtil.CustomRange();
        customRange.setStart(1.0);
        customRange.setEnd(2.0);
        customRange.setType("invalid");

        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            RangeConvertUtil.convertToRange(customRange);
        });
        assertEquals("无效的类型: invalid", exception.getMessage());
    }

    /**
     * 将CustomRange转换为Range<Double>列表
     */
    @Test
    public void testToRangeArray() {
        RangeConvertUtil util = new RangeConvertUtil();
        RangeConvertUtil.CustomRange range1 = new RangeConvertUtil.CustomRange();
        range1.setStart(1.0);
        range1.setEnd(2.0);
        range1.setType("closed");

        RangeConvertUtil.CustomRange range2 = new RangeConvertUtil.CustomRange();
        range2.setStart(2.0);
        range2.setEnd(3.0);
        range2.setType("open");

        util.setCustomRanges(Arrays.asList(range1, range2));

        List<Range<Double>> ranges = util.toRangeArray();
        assertEquals(2, ranges.size());
        assertEquals(Range.closed(1.0, 2.0), ranges.get(0));
        assertEquals(Range.open(2.0, 3.0), ranges.get(1));
    }
}
