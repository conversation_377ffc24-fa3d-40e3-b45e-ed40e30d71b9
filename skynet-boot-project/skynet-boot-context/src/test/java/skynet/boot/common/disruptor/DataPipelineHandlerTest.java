package skynet.boot.common.disruptor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import skynet.boot.common.disruptor.impl.MyDataPipelineHandler;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

public class DataPipelineHandlerTest {

    private DataPipelineHandler<DataEvent<Integer>, Object> dataPipelineHandler;
    private CountDownLatch latch;

    @BeforeEach
    public void setUp() throws Exception {
        latch = new CountDownLatch(5); // 假设处理5个事件

        // 创建管道处理器
        dataPipelineHandler = new MyDataPipelineHandler() {

            @Override
            public void onEvent(DataEvent<DataEvent<Integer>> event, boolean endOfBatch) throws Exception {
                // 事件处理逻辑
                super.onEvent(event, endOfBatch);
                latch.countDown(); // 处理完成后减少计数
            }


        };

        // 初始化数据管道处理器
        dataPipelineHandler.init(null);
    }

    /**
     * 测试提交数据到管道，并等待所有事件处理完成。
     *
     * @throws InterruptedException
     */
    @Test
    public void testOnData() throws InterruptedException {
        // 向管道中提交5个数据
        for (int i = 0; i < 5; i++) {
            dataPipelineHandler.onData(new DataEvent<>(i));
        }

        // 等待所有事件被处理
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue(completed, "所有事件应该在超时时间内处理完毕");
        assertEquals(0, latch.getCount(), "Latch 计数应该为 0，表示所有事件都已处理");
    }

    /**
     * 清除管道中未处理的数据。
     *
     * @throws InterruptedException
     */
    @Test
    public void testClear() throws InterruptedException {
        // 初始化 CountDownLatch，计数为 5
        latch = new CountDownLatch(5);

        // 提交 5 个数据到管道
        for (int i = 0; i < 5; i++) {
            dataPipelineHandler.onData(new DataEvent<>(i));
        }

        // 立即调用 clear 方法，清除未处理的数据
        dataPipelineHandler.clear();

        // 等待一段时间，确保没有更多的事件被处理
        boolean completed = latch.await(2, TimeUnit.SECONDS);

        // 验证 latch 的计数是否正确，应该大于 0，因为未处理的事件被清除，不会调用 latch.countDown()
        assertFalse(completed, "应该有未处理的事件被清除，Latch 不应该归零");
        assertTrue(latch.getCount() > 0, "Latch 计数应该大于 0，表示有事件未被处理");

        // 验证待处理数量是否为 0，因为未处理的事件已被清除
        assertEquals(0, dataPipelineHandler.getTodoSize(), "调用 clear 后，待处理数量应该为 0");
    }

    /**
     * 关闭管道并等待处理完成。
     *
     * @throws Exception
     */
    @Test
    public void testCloseUntilComplete() throws Exception {
        // 向管道中提交5个数据
        for (int i = 0; i < 5; i++) {
            dataPipelineHandler.onData(new DataEvent<>(i));
        }

        // 关闭管道并等待处理完成
        dataPipelineHandler.closeUntilComplete(10, TimeUnit.SECONDS);

        // 确保所有事件被处理
        assertTrue(latch.await(10, TimeUnit.SECONDS), "所有事件应该处理完毕");
    }
}
