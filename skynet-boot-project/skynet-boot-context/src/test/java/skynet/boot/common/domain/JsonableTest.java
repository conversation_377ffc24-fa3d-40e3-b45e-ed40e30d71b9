package skynet.boot.common.domain;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LargeObjectEllipsesFilter 单元测试类
 */
public class JsonableTest {

    /**
     * 测试字符串超过过滤门槛时，是否被正确截取
     */
    @Test
    public void testLargeObjectEllipsesFilterForString() {
        // 创建超过过滤门槛的长字符串（600个字符）
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 600; i++) {
            longString.append("a");
        }

        // 过滤器应用
        Jsonable.LargeObjectEllipsesFilter filter = new Jsonable.LargeObjectEllipsesFilter();
        String result = (String) filter.apply(null, "test", longString.toString());

        // 验证结果是否包含省略号
        assertTrue(result.contains("...ellipses"), "字符串未被正确截取");
    }

    /**
     * 测试短字符串未超过过滤门槛时，是否被正确保留
     */
    @Test
    public void testLargeObjectEllipsesFilterForShortString() {
        // 创建不过过滤门槛的短字符串（小于500个字符）
        String shortString = "This is a short string";

        // 过滤器应用
        Jsonable.LargeObjectEllipsesFilter filter = new Jsonable.LargeObjectEllipsesFilter();
        String result = (String) filter.apply(null, "test", shortString);

        // 验证结果是否没有被截取
        assertEquals(shortString, result, "短字符串不应被截取");
    }

    /**
     * 测试数组超过过滤门槛时，是否被正确截取
     */
    @Test
    public void testLargeObjectEllipsesFilterForArray() {
        // 创建超过过滤门槛的数组（200个元素）
        Integer[] longArray = new Integer[200];
        for (int i = 0; i < longArray.length; i++) {
            longArray[i] = i;
        }

        // 过滤器应用
        Jsonable.LargeObjectEllipsesFilter filter = new Jsonable.LargeObjectEllipsesFilter();
        Object[] result = (Object[]) filter.apply(null, "test", longArray);

        // 验证结果是否截取为前10和后10元素
        assertEquals(20, result.length, "数组未被正确截取");
        assertArrayEquals(new Integer[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199}, result, "数组内容不正确");
    }

    /**
     * 测试短数组未超过过滤门槛时，是否被正确保留
     */
    @Test
    public void testLargeObjectEllipsesFilterForShortArray() {
        // 创建不过过滤门槛的短数组（小于100个元素）
        Integer[] shortArray = new Integer[50];
        for (int i = 0; i < shortArray.length; i++) {
            shortArray[i] = i;
        }

        // 过滤器应用
        Jsonable.LargeObjectEllipsesFilter filter = new Jsonable.LargeObjectEllipsesFilter();
        Object[] result = (Object[]) filter.apply(null, "test", shortArray);

        // 验证结果是否没有被截取
        assertArrayEquals(shortArray, result, "短数组不应被截取");
    }

    /**
     * 测试列表超过过滤门槛时，是否被正确截取
     */
    @Test
    public void testLargeObjectEllipsesFilterForList() {
        // 创建超过过滤门槛的列表（200个元素）
        List<Integer> longList = new ArrayList<>();
        for (int i = 0; i < 200; i++) {
            longList.add(i);
        }

        // 过滤器应用
        Jsonable.LargeObjectEllipsesFilter filter = new Jsonable.LargeObjectEllipsesFilter();
        List<?> result = (List<?>) filter.apply(null, "test", longList);

        // 验证结果是否截取为前10和后10元素
        assertEquals(20, result.size(), "列表未被正确截取");
        assertEquals(longList.subList(0, 10), result.subList(0, 10), "列表前10元素不正确");
        assertEquals(longList.subList(190, 200), result.subList(10, 20), "列表后10元素不正确");
    }

    /**
     * 测试短列表未超过过滤门槛时，是否被正确保留
     */
    @Test
    public void testLargeObjectEllipsesFilterForShortList() {
        // 创建不过过滤门槛的短列表（小于100个元素）
        List<Integer> shortList = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            shortList.add(i);
        }

        // 过滤器应用
        Jsonable.LargeObjectEllipsesFilter filter = new Jsonable.LargeObjectEllipsesFilter();
        List<?> result = (List<?>) filter.apply(null, "test", shortList);

        // 验证结果是否没有被截取
        assertEquals(shortList, result, "短列表不应被截取");
    }
}
