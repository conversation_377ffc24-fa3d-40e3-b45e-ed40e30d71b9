package skynet.boot.common.concurrent;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ManualResetEventTest {

    private ManualResetEvent manualResetEvent;

    @BeforeEach
    void setUp() {
        manualResetEvent = new ManualResetEvent(false); // 初始状态为阻塞
    }

    /**
     * 验证waitOne方法在初始状态为阻塞时，会阻塞等待
     *
     * @throws InterruptedException
     */
    @Test
    void testWaitOneAndSet() throws InterruptedException {
        Thread thread = new Thread(() -> {
            try {
                assertFalse(manualResetEvent.waitOne()); // 初始状态为阻塞
            } catch (InterruptedException e) {
                fail("Thread was interrupted");
            }
        });

        thread.start();

        Thread.sleep(100); // 确保线程进入等待状态
        manualResetEvent.set(); // 设置为放行

        thread.join();
        assertTrue(manualResetEvent.waitOne()); // 验证状态为放行
    }

    /**
     * 验证waitOne方法在初始状态为阻塞时，会阻塞等待直到超时
     *
     * @throws InterruptedException
     */
    @Test
    void testWaitOneWithTimeout() throws InterruptedException {
        long startTime = System.currentTimeMillis();
        boolean result = manualResetEvent.waitOne(200); // 超时200毫秒
        long elapsedTime = System.currentTimeMillis() - startTime;

        assertFalse(result); // 超时后应返回阻塞状态
        assertTrue(elapsedTime >= 200 && elapsedTime < 300); // 验证等待时间
    }

    /**
     * 验证waitOne方法在初始状态为阻塞时，会阻塞等待直到超时，然后设置为放行，立即返回放行状态
     *
     * @throws InterruptedException
     */
    @Test
    void testSetAndReset() throws InterruptedException {
        manualResetEvent.set(); // 设置为放行
        assertTrue(manualResetEvent.waitOne()); // 验证状态为放行

        manualResetEvent.reset(); // 重置为阻塞
        Thread thread = new Thread(() -> {
            try {
                assertFalse(manualResetEvent.waitOne(100)); // 100毫秒后仍应为阻塞状态
            } catch (InterruptedException e) {
                fail("Thread was interrupted");
            }
        });

        thread.start();
        thread.join();
    }

    /**
     * 验证多个线程等待时，任一线程放行后其他线程均放行
     *
     * @throws InterruptedException
     */
    @Test
    void testMultipleThreadsWaiting() throws InterruptedException {
        ManualResetEvent resetEvent = new ManualResetEvent(false);

        Runnable waitTask = () -> {
            try {
                assertFalse(resetEvent.waitOne());
            } catch (InterruptedException e) {
                fail("Thread was interrupted");
            }
        };

        Thread thread1 = new Thread(waitTask);
        Thread thread2 = new Thread(waitTask);

        thread1.start();
        thread2.start();

        Thread.sleep(100); // 确保所有线程进入等待状态
        resetEvent.set(); // 设置为放行

        thread1.join();
        thread2.join();

        assertTrue(resetEvent.waitOne()); // 验证状态为放行
    }

    /**
     * 验证在设置为放行后，waitOne方法立即返回放行状态
     *
     * @throws InterruptedException
     */
    @Test
    void testWaitOneImmediatelyWhenSet() throws InterruptedException {
        manualResetEvent.set(); // 设置为放行

        // 验证在设置为放行后，waitOne方法立即返回放行状态
        assertTrue(manualResetEvent.waitOne());
        assertTrue(manualResetEvent.waitOne(100)); // 超时等待同样立即返回放行状态
    }
}
