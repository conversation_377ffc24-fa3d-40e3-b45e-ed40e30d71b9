package skynet.boot.common.concurrent;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

class CompletableFutureServiceTest {

    private CompletableFutureService<Integer> completableFutureService;

    @BeforeEach
    void setUp() {
        completableFutureService = new CompletableFutureService<>(Executors.newFixedThreadPool(5));
    }

    /**
     * 测试异步执行任务并获取结果
     */
    @Test
    void testSupplyAsync() {
        completableFutureService.supplyAsync(() -> 10);
        completableFutureService.waitForCompletion();
        List<Integer> results = completableFutureService.getAll();
        assertEquals(1, results.size());
        assertEquals(10, results.get(0));
    }

    /**
     * 测试异步执行任务列表并获取结果
     */
    @Test
    void testSupplyAsyncList() {
        completableFutureService.supplyAsyncList(() -> Arrays.asList(1, 2, 3));
        completableFutureService.waitForCompletion();
        List<Integer> results = completableFutureService.getAll();
        assertEquals(3, results.size());
        assertTrue(results.containsAll(Arrays.asList(1, 2, 3)));
    }

    /**
     * 测试异步执行任务并获取结果
     */
    @Test
    void testRunAsync() {
        AtomicBoolean taskRan = new AtomicBoolean(false);
        completableFutureService.runAsync(() -> taskRan.set(true));
        completableFutureService.waitForCompletion();
        assertTrue(taskRan.get());
    }

    /**
     * 测试等待任务完成并成功获取结果
     */
    @Test
    void testWaitForCompletionWithTimeoutSuccess() {
        completableFutureService.supplyAsync(() -> 10);
        boolean completedInTime = completableFutureService.waitForCompletion(2, TimeUnit.SECONDS);
        assertTrue(completedInTime);
        List<Integer> results = completableFutureService.getAll();
        assertEquals(1, results.size());
    }

    /**
     * 测试等待任务完成并失败获取结果
     */
    @Test
    void testWaitForCompletionWithTimeoutFailure() {
        completableFutureService.supplyAsync(() -> {
            try {
                Thread.sleep(3000); // 模拟长时间任务
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return 20;
        });
        boolean completedInTime = completableFutureService.waitForCompletion(1, TimeUnit.SECONDS);
        assertFalse(completedInTime);
        List<Integer> results = completableFutureService.getAll();
        assertTrue(results.isEmpty()); // 超时后应为空
    }

    /**
     * 测试等待任务完成并成功获取结果
     */
    @Test
    void testGetAllWithoutTimeout() {
        List<Integer> expected = IntStream.range(0, 10).boxed().collect(Collectors.toList());
        expected.forEach(i -> completableFutureService.supplyAsync(() -> i));
        completableFutureService.waitForCompletion();
        List<Integer> results = completableFutureService.getAll();
        assertEquals(10, results.size());
        assertTrue(results.containsAll(expected));
    }

    /**
     * 测试等待任务完成并成功获取结果
     */
    @Test
    void testGetAllWithTimeout() {
        List<Integer> expected = IntStream.range(0, 5).boxed().collect(Collectors.toList());
        expected.forEach(i -> completableFutureService.supplyAsync(() -> i));

        completableFutureService.supplyAsync(() -> {
            try {
                Thread.sleep(3000); // 模拟长时间任务
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return 20;
        });

        List<Integer> results = completableFutureService.getAll(1, TimeUnit.SECONDS); // 设置较短的超时时间
        assertTrue(results.containsAll(expected)); // 应包含所有快速完成的任务
        assertEquals(5, results.size()); // 长时间任务不会在结果中
    }

    /**
     * 测试等待任务完成并成功获取结果
     */
    @Test
    void testWaitForCompletionWithDuration() {
        completableFutureService.supplyAsync(() -> 10);
        boolean completedInTime = completableFutureService.waitForCompletion(Duration.ofSeconds(2));
        assertTrue(completedInTime);
        List<Integer> results = completableFutureService.getAll();
        assertEquals(1, results.size());
        assertEquals(10, results.get(0));
    }
}
