package skynet.boot.common.disruptor.impl;

import skynet.boot.common.disruptor.DataEvent;
import skynet.boot.common.disruptor.DataEventHandler;

public class MyDataEventHandler4A implements DataEventHandler<DataEvent<Integer>, Object> {

    @Override
    public void init(Object obj) throws Exception {
    }

    @Override
    public void onEvent(DataEvent<DataEvent<Integer>> event, long sequence, boolean endOfBatch) throws Exception {
        // 从管道中获取数据，进行处理
    }

    @Override
    public void close() throws Exception {
    }
}
