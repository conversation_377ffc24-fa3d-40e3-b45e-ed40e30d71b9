package skynet.boot.common.utils;

import org.junit.jupiter.api.Test;

public class IdUtilTest {

    @Test
    void testGetSnowflakeNextId() throws Exception {
        for (int i = 0; i < 10; i++) {
            long nextId = IdUtil.getSnowflakeNextId();
            System.out.println(nextId);
            Thread.sleep(1000);
        }
    }

    @Test
    void testGetSnowflakeNextIdStr() throws Exception {
        for (int i = 0; i < 10; i++) {
            String nextId = IdUtil.getSnowflakeNextIdStr();
            System.out.println(nextId);
            Thread.sleep(1000);
        }
    }
}
