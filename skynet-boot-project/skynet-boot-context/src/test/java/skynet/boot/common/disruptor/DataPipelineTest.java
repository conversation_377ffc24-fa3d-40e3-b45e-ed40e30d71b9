package skynet.boot.common.disruptor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import skynet.boot.common.disruptor.impl.MyDataEventHandler4A;
import skynet.boot.common.disruptor.impl.MyDataEventHandler4B;
import skynet.boot.common.disruptor.impl.MyDataPipeline;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DataPipelineTest {

    private MyDataPipeline myDataPipeline;
    private CountDownLatch latch;

    @BeforeEach
    public void setUp() throws Exception {
        latch = new CountDownLatch(5); // 假设处理5个事件

        // 创建处理器
        List<DataEventHandler<DataEvent<Integer>, Object>> handlers = new ArrayList<>();
        handlers.add(new MyDataEventHandler4A() {
            @Override
            public void onEvent(DataEvent<DataEvent<Integer>> event, long sequence, boolean endOfBatch) throws Exception {
                super.onEvent(event, sequence, endOfBatch);
                latch.countDown(); // 处理完成后减少计数
            }
        });

        handlers.add(new MyDataEventHandler4B() {
            @Override
            public void onEvent(DataEvent<DataEvent<Integer>> event, long sequence, boolean endOfBatch) throws Exception {
                super.onEvent(event, sequence, endOfBatch);
                latch.countDown(); // 处理完成后减少计数
            }
        });

        // 初始化数据管道
        myDataPipeline = new MyDataPipeline(handlers);
        myDataPipeline.init(null);
    }

    /**
     * 测试提交数据到管道，并等待所有事件处理完成。
     *
     * @throws InterruptedException
     */
    @Test
    public void testOnData() throws InterruptedException {
        // 提交5个数据到管道
        for (int i = 0; i < 5; i++) {
            myDataPipeline.onData(new DataEvent<>(i));
        }

        // 等待所有事件被处理
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue(completed, "所有事件应该在超时时间内处理完毕");
        assertEquals(0, latch.getCount(), "Latch 计数应该为 0，表示所有事件都已处理");
    }

    /**
     * 测试关闭管道，并等待所有事件处理完成。
     *
     * @throws Exception
     */
    @Test
    public void testCloseUntilComplete() throws Exception {
        // 提交5个数据到管道
        for (int i = 0; i < 5; i++) {
            myDataPipeline.onData(new DataEvent<>(i));
        }

        // 关闭管道并等待处理完成
        myDataPipeline.closeUntilComplete(10, TimeUnit.SECONDS);

        // 确保所有事件被处理
        assertTrue(latch.await(10, TimeUnit.SECONDS), "所有事件应该处理完毕");
    }
}