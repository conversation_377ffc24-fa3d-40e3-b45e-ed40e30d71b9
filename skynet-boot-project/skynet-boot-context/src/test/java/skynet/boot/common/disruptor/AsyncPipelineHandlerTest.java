package skynet.boot.common.disruptor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import skynet.boot.common.AsyncPipelineHandler;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class AsyncPipelineHandlerTest {

    private AsyncPipelineHandler<String, Void> pipelineHandler;
    private CountDownLatch latch;

    @BeforeEach
    void setUp() throws Exception {
        latch = new CountDownLatch(2); // 用于测试等待两个事件被处理
        pipelineHandler = new AsyncPipelineHandler<>() {
            @Override
            protected void onEvent(String event) throws Exception {
                // 模拟处理事件的逻辑
                System.out.println("Processing event: " + event);
                latch.countDown(); // 每处理一个事件，计数器减少
            }

        };

        pipelineHandler.init(10, null); // 初始化队列大小为10
    }

    /**
     * 测试提交数据到管道，并等待处理完成
     *
     * @throws InterruptedException
     */
    @Test
    void testOnDataAndProcess() throws InterruptedException {
        // 提交数据，触发 onEvent
        pipelineHandler.onData("Test event 1");
        pipelineHandler.onData("Test event 2");

        // 等待数据处理完成，超时时间为5秒
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertTrue(processed, "Not all events were processed in time");

        // 验证管道中的待处理数据量
        assertEquals(0, pipelineHandler.getTodoSize());
    }

    /**
     * 测试清空管道中未处理的数据
     *
     * @throws InterruptedException
     */
    @Test
    void testClear() throws InterruptedException {
        // 提交数据
        pipelineHandler.onData("Test event 1");
        pipelineHandler.onData("Test event 2");

        // 清空管道
        pipelineHandler.clear();

        // 验证队列已清空
        assertEquals(0, pipelineHandler.getQueueSize());
    }

    /**
     * 测试关闭管道，停止处理数据
     *
     * @throws Exception
     */
    @Test
    void testClose() throws Exception {
        // 提交数据
        pipelineHandler.onData("Test event 1");
        pipelineHandler.onData("Test event 2");

        // 等待数据处理完成，超时时间为5秒
        latch.await(5, TimeUnit.SECONDS);

        // 关闭管道
        pipelineHandler.close();

        // 验证管道已关闭，没有待处理的数据
        assertEquals(0, pipelineHandler.getTodoSize());
    }
}