package skynet.boot.common.concurrent;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

class ParallelServiceTest {

    private ParallelService<Integer, Integer> parallelService;

    @BeforeEach
    void setUp() {
        parallelService = new ParallelService<>(5);  // 使用5个线程的线程池
    }

    @AfterEach
    void tearDown() throws Exception {
        parallelService.close();
    }

    /**
     * 测试提交任务并获取结果的场景。
     */
    @Test
    void testSubmitAndGetResult() {
        List<Integer> taskList = Arrays.asList(1, 2, 3, 4, 5);
        parallelService.submit(taskList, input -> input * input);  // 提交计算平方的任务

        List<Integer> result = parallelService.getResult();
        assertNotNull(result);
        assertEquals(5, result.size());  // 结果列表应有5个元素
        assertTrue(result.containsAll(Arrays.asList(1, 4, 9, 16, 25)));  // 验证结果是否正确
    }

    /**
     * 测试提交任务并获取结果，但设定超时时间。
     */
    @Test
    void testSubmitWithTimeout() {
        List<Integer> taskList = Arrays.asList(1, 2, 3, 4, 5);
        parallelService.submit(taskList, input -> {
            Thread.sleep(100);  // 模拟较长的任务执行时间
            return input * input;
        });

        List<Integer> result = parallelService.getResult(1, TimeUnit.SECONDS);
        assertNotNull(result);
        assertEquals(5, result.size());  // 结果列表应有5个元素，因时间充足，所有任务应完成
    }

    /**
     * 测试超时场景。
     */
    @Test
    void testTimeoutScenario() {
        List<Integer> taskList = Arrays.asList(1, 2, 3, 4, 5);

        // 使用 CountDownLatch 控制任务的执行时机
        CountDownLatch latch = new CountDownLatch(1);

        // 提交任务，但任务会等待 latch
        parallelService.submit(taskList, input -> {
            try {
                latch.await(); // 等待 latch 被释放
                Thread.sleep(5000); // 任务休眠 5 秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return input * input;
        });

        // 此时任务都在等待 latch，超时时间为 100 毫秒
        List<Integer> result = parallelService.getResult(1, TimeUnit.SECONDS);

        // 检查结果应为空，因为所有任务都应该超时
        assertNotNull(result);
        assertTrue(result.isEmpty(), "Result should be empty due to timeout but contains: " + result);

        // 释放 latch，允许任务继续执行（这步确保后续任务不会阻塞）
        latch.countDown();
    }

    /**
     * 测试异常处理场景。
     */
    @Test
    void testWithExceptionHandling() {
        List<Integer> taskList = Arrays.asList(1, 2, 3, 4, 5);
        parallelService.submit(taskList, input -> {
            if (input == 3) throw new RuntimeException("Test Exception");  // 模拟任务中出现异常
            return input * input;
        });

        List<Integer> result = parallelService.getResult();
        assertNotNull(result);
        assertEquals(4, result.size());  // 只有4个任务成功完成，因为其中一个任务抛出异常
        assertFalse(result.contains(9));  // 验证结果中不包含导致异常的输入的平方值
    }

    /**
     * 测试关闭方法。
     */
    @Test
    void testClose() {
        assertDoesNotThrow(() -> parallelService.close());  // 确保close方法不抛出异常
    }
}
