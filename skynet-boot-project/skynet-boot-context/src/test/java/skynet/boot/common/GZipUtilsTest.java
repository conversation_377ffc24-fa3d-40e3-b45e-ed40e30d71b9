package skynet.boot.common;

import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {TestApplication.class})
//@ActiveProfiles("test")
public class GZipUtilsTest {

    @Test
    void testCompressAndDecompressString() {
        String original = "Hello World";
        String compressed = GZipUtils.compressString(original);

        assertNotNull(compressed);
        assertFalse(compressed.isEmpty());

        String decompressed = GZipUtils.decompressString(compressed);
        assertEquals(original, decompressed);
    }

    @Test
    void testCompressAndDecompressByteArray() {
        byte[] original = "Test bytes".getBytes(StandardCharsets.UTF_8);
        byte[] compressed = GZipUtils.compress(original);

        assertNotNull(compressed);
        assertNotEquals(original.length, compressed.length);

        byte[] decompressed = GZipUtils.decompress(compressed);
        assertArrayEquals(original, decompressed);
    }

    @Test
    void testEmptyStringCompression() {
        String original = "";
        String compressed = GZipUtils.compressString(original);

        assertNotNull(compressed);
        assertEquals("", GZipUtils.decompressString(compressed));
    }

    @Test
    void testNullStringHandling() {
        assertThrows(IllegalArgumentException.class, () -> GZipUtils.compressString(null));
        assertThrows(IllegalArgumentException.class, () -> GZipUtils.decompressString(null));
    }

    @Test
    void testChineseCharacterCompression() {
        String original = "中国人";
        String compressed = GZipUtils.compressString(original);

        assertNotNull(compressed);
        assertFalse(compressed.isEmpty());

        String decompressed = GZipUtils.decompressString(compressed);
        assertEquals(original, decompressed);
    }

    @Test
    void testEmptyByteArrayCompression() {
        byte[] original = new byte[0];
        byte[] compressed = GZipUtils.compress(original);

        assertNotNull(compressed);
        assertArrayEquals(original, GZipUtils.decompress(compressed));
    }
}
