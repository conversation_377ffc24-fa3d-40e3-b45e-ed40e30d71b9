package skynet.boot.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.Test;
import skynet.boot.common.JsonUtils;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class JsonUtilsTest {

    /**
     * 测试Null转Json
     */
    @Test
    public void testToJSONStringWithNull() {
        assertEquals("", JsonUtils.toJSONString(null));
    }

    /**
     * 测试Json转对象类型
     *
     * @throws JsonProcessingException
     */
    @Test
    public void testToJavaObject() throws JsonProcessingException {
        String json = "{\"name\":\"John\",\"age\":30}";
        Map result = JsonUtils.toJavaObject(json, Map.class);
        assertNotNull(result);
        assertEquals("John", result.get("name"));
        assertEquals(30, result.get("age"));
    }

    /**
     * 测试Json转对象类型包含Null
     */
    @Test
    public void testToJavaObjectWithNull() {
        assertNull(JsonUtils.toJavaObject(null, Map.class));
    }

    /**
     * 测试Json转List<Map>类型
     *
     * @throws JsonProcessingException
     */
    @Test
    public void testToJavaObjectList() throws JsonProcessingException {
        String json = "[{\"name\":\"John\",\"age\":30}, {\"name\":\"Doe\",\"age\":25}]";
        List<Map> result = JsonUtils.toJavaObjectList(json, Map.class);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("John", result.get(0).get("name"));
        assertEquals(30, result.get(0).get("age"));
        assertEquals("Doe", result.get(1).get("name"));
        assertEquals(25, result.get(1).get("age"));
    }

    /**
     * 测试Json转Map类型
     */
    @Test
    public void testToMap() {
        String json = "{\"name\":\"John\",\"age\":30}";
        Map<String, Object> result = JsonUtils.toMap(json);

        assertNotNull(result);
        assertEquals("John", result.get("name"));
        assertEquals(30, result.get("age"));
    }

    /**
     * 测试Json转List类型
     */
    @Test
    public void testToList() {
        String json = "[\"apple\", \"banana\", \"orange\"]";
        List<Object> result = JsonUtils.toList(json);

        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("apple", result.get(0));
        assertEquals("banana", result.get(1));
        assertEquals("orange", result.get(2));
    }

    /**
     * 测试Long类型
     */
    @Test
    public void testGetLong() {
        Map<String, Object> map = Map.of("id", 123L);
        long id = JsonUtils.getLong(map, "id");

        assertEquals(123L, id);
    }

    /**
     * 测试Int类型
     */
    @Test
    public void testGetInt() {
        Map<String, Object> map = Map.of("age", 25);
        int age = JsonUtils.getInt(map, "age");

        assertEquals(25, age);
    }

}
