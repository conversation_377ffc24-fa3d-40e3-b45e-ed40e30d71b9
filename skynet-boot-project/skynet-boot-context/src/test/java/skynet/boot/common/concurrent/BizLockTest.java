package skynet.boot.common.concurrent;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.spy;

/**
 * Unit tests for the BizLock class.
 * Tests basic locking functionality, multiple locks, concurrent access, and wait count tracking.
 */
class BizLockTest {

    private BizLock bizLock;

    @BeforeEach
    void setUp() {
        bizLock = new BizLock();
    }

    /**
     * Tests basic lock and unlock functionality.
     */
    @Test
    void testLockAndUnlock() {
        String bizId = "testBizId";

        // Verify initial state has no locks
        assertEquals(0, bizLock.getOnlineLockSize());

        // Test locking
        bizLock.lock(bizId);
        assertEquals(1, bizLock.getOnlineLockSize());

        // Test unlocking
        bizLock.unlock(bizId);
        assertEquals(0, bizLock.getOnlineLockSize());
    }

    /**
     * Tests locking multiple different business IDs.
     */
    @Test
    void testMultipleLocks() {
        String bizId1 = "biz1";
        String bizId2 = "biz2";

        // Test locking different business IDs
        bizLock.lock(bizId1);
        bizLock.lock(bizId2);
        assertEquals(2, bizLock.getOnlineLockSize());

        // Test unlocking and verify lock count
        bizLock.unlock(bizId1);
        bizLock.unlock(bizId2);
        assertEquals(0, bizLock.getOnlineLockSize());
    }

    /**
     * Tests unlocking a business ID that hasn't been locked.
     */
    @Test
    void testUnlockWithoutLock() {
        String bizId = "testBizId";

        // Attempt to unlock a non-existent lock, should log a warning but not throw
        bizLock.unlock(bizId);
        assertEquals(0, bizLock.getOnlineLockSize());
    }

    /**
     * Tests concurrent locking and unlocking from multiple threads.
     *
     * @throws InterruptedException if thread operations are interrupted
     */
    @Test
    void testConcurrentLocking() throws InterruptedException {
        String bizId = "concurrentBizId";
        int threadCount = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        // Simulate multiple threads concurrently locking and unlocking the same business ID
        for (int i = 0; i < threadCount; i++) {
            executorService.execute(() -> {
                bizLock.lock(bizId);
                bizLock.unlock(bizId);
                latch.countDown();
            });
        }

        // Wait for all threads to complete
        latch.await();
        assertEquals(0, bizLock.getOnlineLockSize());
        executorService.shutdown();
    }

    /**
     * Tests the tryLock method with timeout.
     */
    @Test
    void testTryLock() {
        String bizId = "testBizId";

        // Test successful lock acquisition
        boolean acquired = bizLock.tryLock(bizId, 1, TimeUnit.SECONDS);
        assertTrue(acquired);
        assertEquals(1, bizLock.getOnlineLockSize());

        // Clean up
        bizLock.unlock(bizId);
    }

    /**
     * Tests the wait count tracking in BizLockItem.
     */
    @Test
    void testLockWaitCount() {
        String bizId = "testBizId";

        // Use spy to monitor BizLockItem behavior
        BizLock.BizLockItem spyLockItem = spy(new BizLock.BizLockItem(bizId));
        spyLockItem.lock();
        assertEquals(1, spyLockItem.getWaitCount());

        spyLockItem.lock();
        assertEquals(2, spyLockItem.getWaitCount());

        spyLockItem.unlock();
        assertEquals(1, spyLockItem.getWaitCount());

        spyLockItem.unlock();
        assertEquals(0, spyLockItem.getWaitCount());
    }

    /**
     * Tests that null business ID is rejected.
     */
    @Test
    void testNullBusinessId() {
        assertThrows(IllegalArgumentException.class, () -> bizLock.lock(null));
        assertThrows(IllegalArgumentException.class, () -> bizLock.unlock(null));
        assertThrows(IllegalArgumentException.class, () -> bizLock.tryLock(null, 1, TimeUnit.SECONDS));
    }
}
