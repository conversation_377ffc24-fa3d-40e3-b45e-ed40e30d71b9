package skynet.boot.common.pool;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ArrayBlockingQueueStoreTest {

    private ArrayBlockingQueueStore<String> store;
    private static final int POOL_SIZE = 3;

    @BeforeEach
    void setUp() {
        store = new ArrayBlockingQueueStore<>(POOL_SIZE);
    }

    /**
     * 测试 put 和 take 方法
     *
     * @throws InterruptedException
     */
    @Test
    void testPutAndTake() throws InterruptedException {
        // 测试 put 和 take 方法
        store.put("Object 1");
        store.put("Object 2");

        // 验证是否可以按顺序取出对象
        String obj1 = store.take();
        String obj2 = store.take();

        assertEquals("Object 1", obj1, "Should retrieve Object 1 first");
        assertEquals("Object 2", obj2, "Should retrieve Object 2 second");
    }

    /**
     * 验证 contains 方法
     *
     * @throws InterruptedException
     */
    @Test
    void testContains() throws InterruptedException {
        // 添加数据
        store.put("Object 1");
        store.put("Object 2");

        // 验证 contains 方法
        assertTrue(store.contains("Object 1"), "Store should contain 'Object 1'");
        assertTrue(store.contains("Object 2"), "Store should contain 'Object 2'");
        assertFalse(store.contains("Object 3"), "Store should not contain 'Object 3'");
    }

    /**
     * 测试队列为空时调用 take() 方法的行为
     *
     * @throws InterruptedException
     */
    @Test
    public void testTakeBlocksWhenQueueIsEmpty() throws InterruptedException {
        ArrayBlockingQueueStore<Integer> store = new ArrayBlockingQueueStore<>(1);

        // 创建一个线程，用来从空队列中调用 take()
        Thread takeThread = new Thread(() -> {
            try {
                store.take();  // 这个操作会阻塞
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        takeThread.start();

        // 等待一段时间确保 take() 操作已经执行并阻塞
        Thread.sleep(100);  // 100毫秒应该足够确保 take() 被调用

        // 验证线程仍然在阻塞（意味着队列是空的，take() 正在等待）
        assertTrue(takeThread.isAlive());

        // 现在放入一个元素，让 take() 不再阻塞
        store.put(1);

        // 等待 take() 完成
        takeThread.join(1000);  // 最多等待1秒
        assertFalse(takeThread.isAlive());  // 线程应该已经结束
    }

    /**
     * 测试 put 方法在队列已满时的行为
     *
     * @throws InterruptedException
     */
    @Test
    public void testPutBlocksWhenQueueIsFull() throws InterruptedException {
        ArrayBlockingQueueStore<Integer> store = new ArrayBlockingQueueStore<>(1);

        // 首先填满队列
        store.put(1);

        // 创建一个线程，在队列已满时尝试调用 put()
        Thread putThread = new Thread(() -> {
            try {
                store.put(2);  // 这个操作会阻塞
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        putThread.start();

        // 等待一段时间确保 put() 操作已经执行并阻塞
        Thread.sleep(100);  // 100毫秒应该足够确保 put() 被调用

        // 验证线程仍然在阻塞（意味着队列已满，put() 正在等待）
        assertTrue(putThread.isAlive());

        // 现在移除一个元素，让 put() 不再阻塞
        store.take();

        // 等待 put() 完成
        putThread.join(1000);  // 最多等待1秒
        assertFalse(putThread.isAlive());  // 线程应该已经结束
    }

}
