package skynet.boot.common.pool;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

public class ObjectPoolTest {

    private ObjectPool2<Worker> objectPool;

    @BeforeEach
    public void setUp() throws Exception {
        // 初始化对象池，大小为 5
        objectPool = new ObjectPool2<>(5, Worker::new);
    }

    @AfterEach
    public void tearDown() throws Exception {
        // 关闭对象池
        objectPool.close();
    }

    /**
     * 测试对象池初始化时的大小
     */
    @Test
    public void testPoolInitialization() {
        // 测试对象池初始化时的大小
        assertEquals(5, objectPool.getFreeSize());
        assertEquals(0, objectPool.getUsedSize());
    }

    /**
     * 测试借用对象
     *
     * @throws Exception
     */
    @Test
    public void testBorrowObject() throws Exception {
        // 借用一个对象
        Worker worker = objectPool.borrowObject();

        // 验证池中可用对象数减少
        assertNotNull(worker);
        assertEquals(4, objectPool.getFreeSize());
        assertEquals(1, objectPool.getUsedSize());
    }

    /**
     * 测试归还对象
     *
     * @throws Exception
     */
    @Test
    public void testReturnObject() throws Exception {
        // 借用并归还一个对象
        Worker worker = objectPool.borrowObject();
        assertNotNull(worker);
        objectPool.returnObject(worker);

        // 验证池中对象数恢复
        assertEquals(5, objectPool.getFreeSize());
        assertEquals(0, objectPool.getUsedSize());
    }

    /**
     * 测试清空池子并关闭
     *
     * @throws Exception
     */
    @Test
    public void testClearAndClose() throws Exception {
        // 借用一个对象
        Worker worker = objectPool.borrowObject();
        assertNotNull(worker);

        // 调用 close 清空池
        objectPool.close();

        // 验证池子是否清空
        assertEquals(0, objectPool.getFreeSize());
        assertEquals(0, objectPool.getUsedSize());
        assertTrue(objectPool.getObjList().isEmpty());
    }

    /**
     * 测试借用多个对象并归还
     *
     * @throws Exception
     */
    @Test
    public void testMultipleBorrowAndReturn() throws Exception {
        // 借用多个对象
        Worker worker1 = objectPool.borrowObject();
        Worker worker2 = objectPool.borrowObject();

        // 验证可用数量和已用数量
        assertEquals(3, objectPool.getFreeSize());
        assertEquals(2, objectPool.getUsedSize());

        // 归还对象
        objectPool.returnObject(worker1);
        objectPool.returnObject(worker2);

        // 验证归还后可用数量恢复
        assertEquals(5, objectPool.getFreeSize());
        assertEquals(0, objectPool.getUsedSize());
    }

    /**
     * 测试对象池的线程安全性
     *
     * @throws InterruptedException
     */
    @Test
    public void testThreadSafety() throws InterruptedException {
        AtomicInteger successCount = new AtomicInteger(0);

        Runnable task = () -> {
            for (int i = 0; i < 10; i++) {
                Worker worker = objectPool.borrowObject();
                if (worker != null) {
                    successCount.incrementAndGet();
                    objectPool.returnObject(worker);
                }
            }
        };

        // 多线程测试对象池
        Thread thread1 = new Thread(task);
        Thread thread2 = new Thread(task);
        thread1.start();
        thread2.start();
        thread1.join();
        thread2.join();

        // 验证借用和归还操作是否线程安全
        assertEquals(20, successCount.get());
        assertEquals(5, objectPool.getFreeSize());
    }

    // Worker 类的简单实现
    static class Worker implements AutoCloseable {
        private final int id;

        Worker(int id) {
            this.id = id;
        }

        @Override
        public void close() throws Exception {
            // 模拟关闭资源
        }
    }
}
