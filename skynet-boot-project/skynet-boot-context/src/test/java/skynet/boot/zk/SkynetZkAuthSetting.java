//package skynet.zk;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.util.Assert;
//import org.springframework.util.StringUtils;
//import skynet.cloud.zookeeper.SkynetZkProperties;
//
//import java.io.File;
//import java.io.FileWriter;
//import java.io.IOException;
//
//
/// **
// * zk  SASL 登录验证 工具类
// */
//@Slf4j
//public class SkynetZkAuthSetting implements AutoCloseable {
//
//    /**
//     * java security login file path
//     */
//    public static final String JAVA_SECURITY_logIN_CONF = "java.security.auth.login.config";
//
//    private final SkynetZkProperties skynetZkProperties;
//
//    private File jaasFile;
//
//    public SkynetZkAuthSetting(SkynetZkProperties skynetZkProperties) {
//        Assert.notNull(skynetZkProperties, "The zookeeper sasl username is blank.");
//        this.skynetZkProperties = skynetZkProperties;
//    }
//
//    /**
//     * 设置jaas.conf文件
//     *
//     * @throws IOException
//     */
//    public void setJaasFile() throws IOException {
//
//        if (StringUtils.hasText(skynetZkProperties.getUsername()) && StringUtils.hasText(skynetZkProperties.getPassword())) {
//            if (jaasFile == null || !jaasFile.exists()) {
//                jaasFile = File.createTempFile("skynet.zk.jaas", ".cfg");
//                jaasFile.deleteOnExit();
//                log.info("jaasFile={}", jaasFile);
//
//                try (FileWriter writer = new FileWriter(jaasFile)) {
//                    writer.write(String.format("Client {" + System.lineSeparator() +
//                            "    org.apache.zookeeper.server.auth.DigestLoginModule required" + System.lineSeparator() +
//                            "    username=\"%s\"" + System.lineSeparator() +
//                            "    password=\"%s\";\n" + System.lineSeparator() +
//                            "};", skynetZkProperties.getUsername().trim(), skynetZkProperties.getPassword().trim()));
//                    writer.flush();
//                } catch (IOException e) {
//                    throw new IOException("Failed to create jaas.cfg File");
//                }
//                System.setProperty(JAVA_SECURITY_logIN_CONF, jaasFile.toString());
//                log.info("jaas.conf文件路径= {}", jaasFile);
//            }
//        }
//    }
//
//    public String getJaasFile() {
//        return jaasFile != null ? jaasFile.toString() : "";
//    }
//
//    /**
//     * -Djava.security.auth.login.config= skynet.zk.jaas.conf
//     *
//     * @return
//     */
//    public String getVmOptions() {
//        return jaasFile != null ? String.format("-D%s=%s", JAVA_SECURITY_logIN_CONF, jaasFile) : "";
//    }
//
//    @Override
//    public void close() throws Exception {
//        if (jaasFile != null && jaasFile.exists()) {
//            jaasFile.delete();
//        }
//    }
//}
