package skynet.boot.zk;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.recipes.cache.ChildData;
import org.junit.Test;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.boot.zookeeper.ZkConfigService;
import skynet.boot.zookeeper.impl.ZkConfigServiceImpl;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/26 20:30
 */
@Slf4j
public class ZkConfigServiceTest {

    static private ZkConfigService zkConfigService;
    //    static String user = "skynet";
//    static String pwd = "skynet2230";
    static String user = "";
    static String pwd = "";
    static String connectString = "localhost:2181";
    static int sessionTimeout = 20000;
    static int connectionTimeout = 20000;

    public static void main(String[] args) throws Exception {
        SkynetZkProperties skynetZkProperties = new SkynetZkProperties();
        zkConfigService = new ZkConfigServiceImpl(skynetZkProperties);
        zkConfigService.putNode("/zk/node1", "1111");
        zkConfigService.putNode("/zk/node1", "222");

        //删除 + 添加 + 查询(查询子节点) + 新增临时节点 + 新增自增key临时节点
//        TestDemo();
//        //导出
//        TestExportData();
//        //观察者 当前节点 /zk/node2
//        //TestWatchData();
//        //观察者 当前节点下所有子节点 /zk
        for (int i = 1; i <= 800; i++) {
            TestWatchChildrenObserver();
        }

        Thread.sleep(Long.MAX_VALUE);

    }

    /**
     * 删除 + 添加 + 查询(查询子节点) + 临时节点 + 临时节点自增key
     *
     * @throws Exception
     */
    @Test
    public static void TestDemo() throws Exception {

        String path = "/zk";
        zkConfigService.deleteNode(path);

        boolean exist = zkConfigService.exists(path);
        System.out.println(exist);

        path = "/zk/node1";
        String data = "node1_data";
        zkConfigService.putNode(path, data);
        log.debug("{}={}", path, zkConfigService.getData(path));

        exist = zkConfigService.exists(path);
        System.out.println(exist);

        path = "/zk/node2";
        data = "node2_data";
        zkConfigService.putNode(path, data);

        List<String> childrenList = zkConfigService.getChildren("/zk");
        System.out.println("childrenList.size=" + childrenList.size());

        Map<String, String> childrenMap = zkConfigService.getChildrenWithData("/zk");
        System.out.println("childrenMap.size=" + childrenMap.size());
        childrenMap.forEach((k, v) -> System.out.println(k + "=" + v));

        //zkConfigService.deleteNode("/zk");

        //临时节点
        for (int index = 0; index < 10; index++) {
            path = "/zk/ephemeral";
            String p = zkConfigService.putEphemeralNode(path + "/" + index, data + "_" + index);
            log.debug("path={}", p);
        }
        path = "/zk/sequence";
        for (int index = 0; index < 10; index++) {
            String p = zkConfigService.putSequenceNode(path, "prefix", data + "_" + index);
            log.debug("path={}", p);
        }
        Map<String, String> childrenSequenceMap = zkConfigService.getChildrenWithData("/zk/sequence");
        childrenSequenceMap.forEach((k, v) -> System.out.println(k + "=" + v));

        log.info("end.");

    }

    /**
     * 观察者 当前节点
     *
     * @throws Exception
     */
    @Test
    public static void TestWatchData() throws Exception {
        zkConfigService.watchData("/zk/node2", (o, arg) -> {
            ChildData data = (ChildData) arg;
            System.out.println("收到通知:" + data.getPath() + "--数据：" + new String(data.getData(), StandardCharsets.UTF_8).trim());

        });
    }

    /**
     * 观察者 当前节点下所有子节点
     *
     * @throws Exception
     */
    public static void TestWatchChildrenObserver() throws Exception {
        zkConfigService.watchChildrenObserver("/zk", (o, arg) -> System.out.println("收到通知--数据：" + arg));
    }

    /**
     * 导出
     *
     * @throws Exception
     */
    public static void TestExportData() throws Exception {
        Map<String, String> exportMap = zkConfigService.exportData("/zk");
        System.out.println("导出：" + JSON.toJSONString(exportMap));
    }


}
