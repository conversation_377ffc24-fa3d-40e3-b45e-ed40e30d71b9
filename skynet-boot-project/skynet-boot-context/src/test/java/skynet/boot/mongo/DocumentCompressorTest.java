package skynet.boot.mongo;

import org.bson.types.Binary;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class DocumentCompressorTest {
    private DocumentCompressor compressor;
    private DocumentCompressor smallThresholdCompressor;

    @BeforeEach
    void setUp() {
        compressor = new DocumentCompressor(5); // 使用较小的阈值便于测试
        smallThresholdCompressor = new DocumentCompressor(50); // 小于最小阈值的压缩器
    }

    @Test
    void testCompressWithNullDocument() {
        assertNull(compressor.compress(null));
    }

    @Test
    void testCompressWithEmptyDocument() {
        Map<String, Object> emptyDoc = new HashMap<>();
        Map<String, Object> result = compressor.compress(emptyDoc);
        assertEquals(emptyDoc, result);
    }

    @Test
    void testCompressWithSmallThreshold() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("text", "test");
        Map<String, Object> result = smallThresholdCompressor.compress(doc);
        assertEquals(doc, result);
    }

    @Test
    void testCompressString() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("shortText", "test");
        doc.put("longText", "this is a very long text that should be compressed");

        Map<String, Object> compressed = compressor.compress(doc);

        assertEquals("test", compressed.get("shortText"));
        assertInstanceOf(byte[].class, compressed.get("longText_str_gzip"));
    }

    @Test
    void testCompressFloatArray() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("shortArray", new float[]{1.1f, 1.2f});
        doc.put("longArray", new float[]{1.1f, 1.2f, 1.3f, 1.4f, 1.5f, 1.6f});

        Map<String, Object> compressed = compressor.compress(doc);

        assertArrayEquals(new float[]{1.1f, 1.2f}, (float[]) compressed.get("shortArray"), 0.001f);
        assertInstanceOf(byte[].class, compressed.get("longArray_fa_gzip"));
    }

    @Test
    void testCompressIntArray() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("shortArray", new int[]{1, 2});
        doc.put("longArray", new int[]{1, 2, 3, 4, 5, 6});

        Map<String, Object> compressed = compressor.compress(doc);

        assertArrayEquals(new int[]{1, 2}, (int[]) compressed.get("shortArray"));
        assertInstanceOf(byte[].class, compressed.get("longArray_ia_gzip"));
    }

    @Test
    void testCompressLongArray() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("shortArray", new long[]{1L, 2L});
        doc.put("longArray", new long[]{1L, 2L, 3L, 4L, 5L, 6L});

        Map<String, Object> compressed = compressor.compress(doc);

        assertArrayEquals(new long[]{1L, 2L}, (long[]) compressed.get("shortArray"));
        assertTrue(compressed.get("longArray_la_gzip") instanceof byte[]);
    }

    @Test
    void testCompressFloatList() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("shortList", Arrays.asList(1.1f, 1.2f));
        doc.put("longList", Arrays.asList(1.1f, 1.2f, 1.3f, 1.4f, 1.5f, 1.6f));

        Map<String, Object> compressed = compressor.compress(doc);

        assertEquals(Arrays.asList(1.1f, 1.2f), compressed.get("shortList"));
        assertInstanceOf(byte[].class, compressed.get("longList_fa_gzip"));
    }

    @Test
    void testCompressIntegerList() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("shortList", Arrays.asList(1, 2));
        doc.put("longList", Arrays.asList(1, 2, 3, 4, 5, 6));

        Map<String, Object> compressed = compressor.compress(doc);

        assertEquals(Arrays.asList(1, 2), compressed.get("shortList"));
        assertInstanceOf(byte[].class, compressed.get("longList_ia_gzip"));
    }

    @Test
    void testCompressLongList() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("shortList", Arrays.asList(1L, 2L));
        doc.put("longList", Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L));

        Map<String, Object> compressed = compressor.compress(doc);

        assertEquals(Arrays.asList(1L, 2L), compressed.get("shortList"));
        assertInstanceOf(byte[].class, compressed.get("longList_la_gzip"));
    }

    @Test
    void testCompressNestedDocument() {
        Map<String, Object> nestedDoc = new HashMap<>();
        nestedDoc.put("longText", "this is a very long text that should be compressed");

        Map<String, Object> doc = new HashMap<>();
        doc.put("nested", nestedDoc);

        Map<String, Object> compressed = compressor.compress(doc);
        Map<String, Object> compressedNested = (Map<String, Object>) compressed.get("nested");

        assertInstanceOf(byte[].class, compressedNested.get("longText_str_gzip"));
    }

    @Test
    void testCompressNestedList() {
        Map<String, Object> nestedDoc = new HashMap<>();
        nestedDoc.put("longText", "this is a very long text that should be compressed");

        Map<String, Object> doc = new HashMap<>();
        doc.put("nestedList", Arrays.asList(nestedDoc));

        Map<String, Object> compressed = compressor.compress(doc);
        List<Object> compressedList = (List<Object>) compressed.get("nestedList");
        Map<String, Object> compressedNested = (Map<String, Object>) compressedList.get(0);

        assertInstanceOf(byte[].class, compressedNested.get("longText_str_gzip"));
    }

    @Test
    void testDecompressWithNullDocument() {
        assertNull(compressor.decompress(null));
    }

    @Test
    void testDecompressWithEmptyDocument() {
        Map<String, Object> emptyDoc = new HashMap<>();
        Map<String, Object> result = compressor.decompress(emptyDoc);
        assertEquals(emptyDoc, result);
    }

    @Test
    void testFullCompressionDecompression() {
        String longText = "this is a very long text that should be compressed".repeat(20);
        // 准备测试数据
        Map<String, Object> original = new HashMap<>();
        original.put("shortText", "test");
        original.put("longText", longText);
        original.put("shortFloatArray", new float[]{1.1f, 1.2f});
        original.put("longFloatArray", new float[]{1.1f, 1.2f, 1.3f, 1.4f, 1.5f, 1.6f});
        original.put("shortIntArray", new int[]{1, 2});
        original.put("longIntArray", new int[]{1, 2, 3, 4, 5, 6});
        original.put("shortLongArray", new long[]{1L, 2L});
        original.put("longLongArray", new long[]{1L, 2L, 3L, 4L, 5L, 6L});
        original.put("shortFloatList", Arrays.asList(1.1f, 1.2f));
        original.put("longFloatList", Arrays.asList(1.1f, 1.2f, 1.3f, 1.4f, 1.5f, 1.6f));
        original.put("shortIntList", Arrays.asList(1, 2));
        original.put("longIntList", Arrays.asList(1, 2, 3, 4, 5, 6));
        original.put("shortLongList", Arrays.asList(1L, 2L));
        original.put("longLongList", Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L));


        Binary binary = new Binary(longText.getBytes(StandardCharsets.UTF_8));
        original.put("binary", binary);

        // 压缩
        Map<String, Object> compressed = DocumentCompressor.getDefault().compress(original);

        // 压缩
        Map<String, Object> compressedx = DocumentCompressor.getDefault().compress(compressed);
        System.out.println(compressedx);


        // 解压
        Map<String, Object> decompressed = DocumentCompressor.getDefault().decompress(compressed);

        // 验证结果
        assertEquals("test", decompressed.get("shortText"));
        assertEquals("this is a very long text that should be compressed".repeat(20), decompressed.get("longText"));
        assertArrayEquals(new float[]{1.1f, 1.2f}, (float[]) decompressed.get("shortFloatArray"), 0.001f);
        assertArrayEquals(new float[]{1.1f, 1.2f, 1.3f, 1.4f, 1.5f, 1.6f}, (float[]) decompressed.get("longFloatArray"), 0.001f);
        assertArrayEquals(new int[]{1, 2}, (int[]) decompressed.get("shortIntArray"));
        assertArrayEquals(new int[]{1, 2, 3, 4, 5, 6}, (int[]) decompressed.get("longIntArray"));
        assertArrayEquals(new long[]{1L, 2L}, (long[]) decompressed.get("shortLongArray"));
        assertArrayEquals(new long[]{1L, 2L, 3L, 4L, 5L, 6L}, (long[]) decompressed.get("longLongArray"));

        // 使用自定义比较方法处理基本类型和包装类型的比较
        assertNumberSequenceEquals(Arrays.asList(1.1f, 1.2f), decompressed.get("shortFloatList"), 0.001);
        assertNumberSequenceEquals(Arrays.asList(1.1f, 1.2f, 1.3f, 1.4f, 1.5f, 1.6f), decompressed.get("longFloatList"), 0.001);
        assertNumberSequenceEquals(Arrays.asList(1, 2), decompressed.get("shortIntList"));
        assertNumberSequenceEquals(Arrays.asList(1, 2, 3, 4, 5, 6), decompressed.get("longIntList"));
        assertNumberSequenceEquals(Arrays.asList(1L, 2L), decompressed.get("shortLongList"));
        assertNumberSequenceEquals(Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L), decompressed.get("longLongList"));
    }

    /**
     * 比较数值序列是否相等，支持List和数组类型，忽略基本类型和包装类型的区别
     */
    private void assertNumberSequenceEquals(List<?> expected, Object actual) {
        assertNumberSequenceEquals(expected, actual, 0.0001);
    }

    /**
     * 比较数值序列是否相等，支持List和数组类型，支持浮点数误差
     */
    private void assertNumberSequenceEquals(List<?> expected, Object actual, double delta) {
        if (actual == null) {
            assertNull(expected);
            return;
        }

        int expectedSize = expected.size();
        int actualSize;

        if (actual instanceof List<?>) {
            List<?> actualList = (List<?>) actual;
            actualSize = actualList.size();
            assertEquals(expectedSize, actualSize, "Sequences have different sizes");

            for (int i = 0; i < expectedSize; i++) {
                Object expectedValue = expected.get(i);
                Object actualValue = actualList.get(i);
                assertNumberEquals(expectedValue, actualValue, delta, i);
            }
        } else if (actual instanceof float[]) {
            float[] actualArray = (float[]) actual;
            actualSize = actualArray.length;
            assertEquals(expectedSize, actualSize, "Sequences have different sizes");

            for (int i = 0; i < expectedSize; i++) {
                Object expectedValue = expected.get(i);
                float actualValue = actualArray[i];
                assertNumberEquals(expectedValue, actualValue, delta, i);
            }
        } else if (actual instanceof int[]) {
            int[] actualArray = (int[]) actual;
            actualSize = actualArray.length;
            assertEquals(expectedSize, actualSize, "Sequences have different sizes");

            for (int i = 0; i < expectedSize; i++) {
                Object expectedValue = expected.get(i);
                int actualValue = actualArray[i];
                assertNumberEquals(expectedValue, actualValue, delta, i);
            }
        } else if (actual instanceof long[]) {
            long[] actualArray = (long[]) actual;
            actualSize = actualArray.length;
            assertEquals(expectedSize, actualSize, "Sequences have different sizes");

            for (int i = 0; i < expectedSize; i++) {
                Object expectedValue = expected.get(i);
                long actualValue = actualArray[i];
                assertNumberEquals(expectedValue, actualValue, delta, i);
            }
        } else {
            fail("Unsupported actual value type: " + actual.getClass());
        }
    }

    /**
     * 比较两个数值是否相等，支持不同数值类型间的比较
     */
    private void assertNumberEquals(Object expected, Object actual, double delta, int index) {
        if (!(expected instanceof Number)) {
            assertEquals(expected, actual, "Non-number elements at index " + index + " are not equal");
            return;
        }

        double expectedValue = ((Number) expected).doubleValue();
        double actualValue;

        if (actual instanceof Number) {
            actualValue = ((Number) actual).doubleValue();
        } else {
            fail("Element at index " + index + " is not a number: " + actual);
            return;
        }

        assertEquals(expectedValue, actualValue, delta,
                "Numbers at index " + index + " are not equal within delta " + delta);
    }

    @Test
    void testDecompressWithBinaryType() {
        // 准备测试数据
        Map<String, Object> doc = new HashMap<>();
        doc.put("longText", "this is a very long text that should be compressed");

        // 压缩
        Map<String, Object> compressed = compressor.compress(doc);

        // 将 byte[] 转换为 Binary 类型
        byte[] compressedData = (byte[]) compressed.get("longText_str_gzip");
        Map<String, Object> binaryDoc = new HashMap<>();
        binaryDoc.put("longText_str_gzip", new Binary(compressedData));

        // 解压
        Map<String, Object> decompressed = compressor.decompress(binaryDoc);

        // 验证结果
        assertEquals("this is a very long text that should be compressed", decompressed.get("longText"));
    }

    @Test
    void testCompressWithMixedContent() {
        Map<String, Object> doc = new HashMap<>();
        doc.put("null", null);
        doc.put("boolean", true);
        doc.put("int", 42);
        doc.put("long", 42L);
        doc.put("float", 42.0f);
        doc.put("double", 42.0d);

        Map<String, Object> compressed = compressor.compress(doc);

        assertNull(compressed.get("null"));
        assertEquals(true, compressed.get("boolean"));
        assertEquals(42, compressed.get("int"));
        assertEquals(42L, compressed.get("long"));
        assertEquals(42.0f, compressed.get("float"));
        assertEquals(42.0d, compressed.get("double"));
    }
}