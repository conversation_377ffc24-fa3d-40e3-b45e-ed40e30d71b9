package skynet.boot.metrics;

import skynet.boot.common.concurrent.ParallelService;
import skynet.boot.metrics.domain.MetricsLabel;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("deprecation")
public class SkynetLabelMetricsTest {

    public static void main(String[] args) throws Exception {
        testThreadGaugeCounter();
        // testThread();
        // testFun();
    }

    public static void testThreadGaugeCounter() throws Exception {
        SkynetMetricsService skynetMetricsService = new SkynetMetricsService(null, null);

        int size = 1000;
        List<Integer> taskList = new ArrayList<>(size);
        String metricName = "metricName";
        skynetMetricsService.counterIncrement(metricName);

        long start = System.currentTimeMillis();

        for (int i = 0; i < size; i++) {
            skynetMetricsService.counter(metricName, 1);
            taskList.add(i);
        }
        System.err.println(System.currentTimeMillis() - start);

        ParallelService<Integer, Integer> parallelService = new ParallelService<>(10);

        start = System.currentTimeMillis();
        parallelService.submit(taskList, input -> {
            skynetMetricsService.counter(metricName, (input % 2 == 0) ? 1 : -1);
            return 1;
        });

        // 一直等待结束
        parallelService.getResult();
        System.err.println(System.currentTimeMillis() - start);
        parallelService.close();

    }

    public static void testThread() throws Exception {
        SkynetMetricsService skynetMetricsService = new SkynetMetricsService(null, null);

        int size = 1000;
        List<Integer> taskList = new ArrayList<>(size);
        String metricName = "metricName";
        skynetMetricsService.counterIncrement(metricName);

        long start = System.currentTimeMillis();

        for (int i = 0; i < size; i++) {
            skynetMetricsService.counterIncrement(metricName);
            taskList.add(i);
        }
        System.err.println(System.currentTimeMillis() - start);

        ParallelService<Integer, Integer> parallelService = new ParallelService<>(10);

        start = System.currentTimeMillis();
        parallelService.submit(taskList, input -> {
            if (input % 2 == 0)
                skynetMetricsService.counterIncrement(metricName);
            else
                skynetMetricsService.counterDecrement(metricName);

            return 1;
        });

        // 一直等待结束
        parallelService.getResult();
        System.err.println(System.currentTimeMillis() - start);
        parallelService.close();

    }

    public static void testFun() {
        SkynetMetricsService skynetLabelMetricsService = new SkynetMetricsService(null, null);

        String key = "lid-lan";
        skynetLabelMetricsService.counter(key, 2);
        skynetLabelMetricsService.counter(key, 2);

        skynetLabelMetricsService.counter(key, 2, new MetricsLabel("lan", "engl"));
        skynetLabelMetricsService.counter(key, 2, new MetricsLabel("lan", "chin"));
        skynetLabelMetricsService.counter(key, 2, new MetricsLabel("lan", "chin"));

        skynetLabelMetricsService.gauge(key, 2);
        skynetLabelMetricsService.gauge(key, 5);

        skynetLabelMetricsService.gauge(key, 2, new MetricsLabel("lan", "engl"));
        skynetLabelMetricsService.gauge(key, 2, new MetricsLabel("lan", "chin"));
        skynetLabelMetricsService.gauge(key, 2, new MetricsLabel("lan", "chin"));


        key = "vspp-lan";
        skynetLabelMetricsService.counter(key, 2);
        skynetLabelMetricsService.counter(key, 2);

        skynetLabelMetricsService.counter(key, 2, new MetricsLabel("lan", "engl"));
        skynetLabelMetricsService.counter(key, 2, new MetricsLabel("lan", "chin"));
        skynetLabelMetricsService.counter(key, 2, new MetricsLabel("lan", "chin"));

        skynetLabelMetricsService.gauge(key, 2);
        skynetLabelMetricsService.gauge(key, 5);

        skynetLabelMetricsService.gauge(key, 2, new MetricsLabel("lan", "engl"));
        skynetLabelMetricsService.gauge(key, 2, new MetricsLabel("lan", "chin"));
        skynetLabelMetricsService.gauge(key, 2, new MetricsLabel("lan", "chin"));


    }

}
