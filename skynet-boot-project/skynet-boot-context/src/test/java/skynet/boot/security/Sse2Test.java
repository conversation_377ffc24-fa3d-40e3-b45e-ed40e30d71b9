package skynet.boot.security;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.BufferedSource;
import org.springframework.util.StringUtils;
import skynet.boot.security.client.SignAuthOkHttpClient;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Sse2Test {
    public static void main(String[] args) throws Exception {

        SignAuthOkHttpClient authHttpClient = new SignAuthOkHttpClient(10000);

        String data = "{\n" +
                "    \"model\": \"general\",\n" +
                "    \"messages\": [\n" +
                "        {\n" +
                "            \"role\": \"user\",\n" +
                "            \"content\": \"写作文 《春天》 500字\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"stream\": true\n" +
                "}";

        // Regular expression to match JSON text
        String regex = "data: (\\{.*\\})";

        // Compile the pattern
        Pattern pattern = Pattern.compile(regex);

        String url = "https://spark-api-open.xf-yun.com/v1/chat/completions";
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer 14b5d6c53dd0807717f020a6:Mjk0MTkgyZjMwZmQ0MDVhNWFk");

        CountDownLatch latch = new CountDownLatch(1);
        authHttpClient.post(url, header, data, new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                // Read the SSE data stream
                try (ResponseBody responseBody = response.body()) {
                    if (responseBody != null) {
                        BufferedSource source = responseBody.source();
                        while (!source.exhausted()) {
                            String line = source.readUtf8Line();
                            if (StringUtils.hasText(line)) {
                                // Match the pattern
                                Matcher matcher = pattern.matcher(line);

                                if (matcher.find()) {
                                    String jsonText = matcher.group(1);
                                    JSONObject jsonObject = JSON.parseObject(jsonText);
                                    System.out.println(jsonObject);
                                    System.out.println();
                                } else {
                                    System.out.println("No JSON found.");
                                }
                            }
                        }
                    }
                }
                latch.countDown();
            }
        });

        latch.await();
        System.err.println("end");
    }


}
