package skynet.boot.security;

import org.springframework.http.HttpMethod;
import skynet.boot.security.auth.AuthUtils;

public class AuthTest {


//httpMethod=GET
//requestUrl=http://172.31.201.14:8585/skynet-demo/api/gateway/test1
//apiKey=skynet-demo
//apiSecret=********************************
//apiKeyKey=null

    public static void main(String[] args) {


        HttpMethod httpMethod = HttpMethod.GET;
        String requestUrl = "http://172.31.201.14:8585/skynet-demo/api/gateway/test1";
        String apiKey = "skynet-demo";
        String apiSecret = "********************************";
        String apiKeyKey = null;

        String url = AuthUtils.assembleRequestUrl(httpMethod, requestUrl, apiKey, apiSecret, apiKeyKey);
        System.out.println(url);
    }
}
