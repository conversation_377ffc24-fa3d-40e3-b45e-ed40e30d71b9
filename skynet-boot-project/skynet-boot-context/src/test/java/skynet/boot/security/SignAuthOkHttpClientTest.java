package skynet.boot.security;

import skynet.boot.security.client.SignAuthOkHttpClient;

public class SignAuthOkHttpClientTest {

    public static void main(String[] args) throws Exception {
        long timeout = 5000;
        String apiKey = "feign";
        String apiSecret = "S1K2Y3N4E5T0F8B76E7181A12BF1FNET";
        String url = "http://172.31.164.48:9081/skynet/security/token";
        SignAuthOkHttpClient authHttpClient = new SignAuthOkHttpClient(apiKey, apiSecret, timeout);
        String result = authHttpClient.execute("GET", url, null, null);
        System.out.println(result);
        authHttpClient.close();

    }
}


