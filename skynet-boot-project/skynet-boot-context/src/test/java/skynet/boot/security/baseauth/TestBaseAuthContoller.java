package skynet.boot.security.baseauth;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestBaseAuthContoller {

    /**
     * 测试验证 需BaseAuth认证页面
     *
     * @return
     */
    @PreAuthorize("hasRole('user')")
    @GetMapping("/userHello")
    public ResponseEntity<String> getHelloworld() {
        return ResponseEntity.ok("userHello");

    }
}
