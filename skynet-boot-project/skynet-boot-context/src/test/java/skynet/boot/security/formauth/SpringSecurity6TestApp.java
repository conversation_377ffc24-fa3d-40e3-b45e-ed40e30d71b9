package skynet.boot.security.formauth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;

/**
 * 测试spring-security6 form认证
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, MongoAutoConfiguration.class})
public class SpringSecurity6TestApp {
    public static void main(String[] args) {
        SpringApplication.run(SpringSecurity6TestApp.class, args);
    }
}
