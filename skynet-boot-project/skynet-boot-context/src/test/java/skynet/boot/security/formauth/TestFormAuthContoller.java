package skynet.boot.security.formauth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class TestFormAuthContoller {

    /**
     * 登录 form接口
     *
     * @param username
     * @param pwd
     * @return
     */
    @RequestMapping("/toLogin")
    public String toLogin(@RequestParam String username,
                          @RequestParam String pwd) {
        log.info("前台接收到的用户名为：==========" + username);
        log.info("前台接收到的密码为：==========" + pwd);
//        String login = userService.login(username, pwd);
        return "index";
    }

    //跳转登陆页面
    @RequestMapping("/toLoginPage")
    public String toLogin() {
        return "views/login";
    }

    /**
     * 模拟首页index
     *
     * @return
     */
    @GetMapping("/index")
    public String toIndex() {
        return "views/index";
    }
}
