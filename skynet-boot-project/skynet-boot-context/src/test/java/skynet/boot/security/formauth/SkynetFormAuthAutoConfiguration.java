package skynet.boot.security.formauth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationEventPublisher;
import org.springframework.security.authentication.DefaultAuthenticationEventPublisher;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;


@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableWebSecurity
public class SkynetFormAuthAutoConfiguration {

    /**
     * 配置 HTTP Security demo
     *
     * @param http
     * @return
     * @throws Exception
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeHttpRequests((authorize) -> authorize
//                        .requestMatchers("/","/index").permitAll()
                                .requestMatchers("/toLogin", "/toLoginPage").permitAll()
                                .anyRequest().authenticated()
                )
                // 配置登录页面 和 登录成功后页面
                .formLogin(form -> form.loginPage("/toLoginPage").permitAll()
                        .loginProcessingUrl("/toLogin").defaultSuccessUrl("/index")
                        .passwordParameter("pwd")
                        .usernameParameter("username")
                )

                // 退出时，让session失效
                .logout(logout -> logout.invalidateHttpSession(true));


        // csrf配置
        http.csrf(c -> c.disable());
//        http.cors((c-> c.disable()));
//        http.headers(h->h.frameOptions(f-> f.disable()));

        // 构建过滤链并返回
        return http.build();
    }


    /**
     * 认证凭证 demo
     *
     * @return
     */
    @Bean
    InMemoryUserDetailsManager userDetailsService() {
        UserDetails user = User.withDefaultPasswordEncoder()
                .username("user")
                .password("pass")
                .roles("user")
                .build();

        return new InMemoryUserDetailsManager(user);
    }

    /**
     * 发布身份验证事件
     *
     * @param delegate
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(AuthenticationEventPublisher.class)
    DefaultAuthenticationEventPublisher defaultAuthenticationEventPublisher(ApplicationEventPublisher delegate) {
        return new DefaultAuthenticationEventPublisher(delegate);
    }

}