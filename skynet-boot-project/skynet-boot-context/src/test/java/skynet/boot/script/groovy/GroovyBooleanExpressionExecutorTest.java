package skynet.boot.script.groovy;

import com.alibaba.fastjson2.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

public class GroovyBooleanExpressionExecutorTest {

    /**
     * 测试基本语法
     */
    @Test
    void test_Eval() throws Exception {
        try (GroovyBooleanExpressionExecutor executor = new GroovyBooleanExpressionExecutor()) {
            Assert.assertTrue(executor.eval("1+1 == 2"));
            Assert.assertFalse(executor.eval("1+1 == 3"));
            Assert.assertTrue(executor.eval("'hello' + ' ' + 'world' == 'hello world'"));
            Assert.assertTrue(executor.eval("100 > 99"));
        }
    }

    /**
     * 测试上下文语法
     */
    @Test
    void test_Eval_With_Context() throws Exception {
        JSONObject context = new JSONObject()
                .fluentPut("userId", 100)
                .fluentPut("userName", "zhangsan")
                .fluentPut("request", new JSONObject()
                        .fluentPut("a", 1)
                        .fluentPut("b", 2));
        try (GroovyBooleanExpressionExecutor executor = new GroovyBooleanExpressionExecutor()) {
            Assertions.assertTrue(executor.eval("$.userId == 100", context));
            Assert.assertFalse(executor.eval("$.userId == '100'", context)); // 由于 Groovy 是强类型，所以不相等
            Assert.assertTrue(executor.eval("$.request.a + $.request.b == 3", context));
            Assert.assertTrue(executor.eval("$.userId == 100 && $.userName == 'zhangsan'", context));
        }
    }

    /**
     * 测试数组语法
     */
    @Test
    void test_Eval_With_Array() throws Exception {
        List<JSONObject> array = Arrays.asList(
                new JSONObject().fluentPut("name", "zhangsan").fluentPut("score", 90),
                new JSONObject().fluentPut("name", "lisi").fluentPut("score", 100)
        );
        try (GroovyBooleanExpressionExecutor executor = new GroovyBooleanExpressionExecutor()) {
            Assert.assertTrue(executor.eval("$[0].score > 80 && $[1].score > 80", array));
        }
    }
}
