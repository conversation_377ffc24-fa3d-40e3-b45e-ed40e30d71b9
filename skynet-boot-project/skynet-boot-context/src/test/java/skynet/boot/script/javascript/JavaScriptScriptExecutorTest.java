package skynet.boot.script.javascript;

import com.alibaba.fastjson2.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class JavaScriptScriptExecutorTest {

    @Test
    void test_Eval() throws Exception {
        try (JavaScriptScriptExecutor executor = new JavaScriptScriptExecutor()) {
            Assert.assertEquals("hello", executor.eval("function demo() { return 'hello' }", "demo"));
        }
    }

    @Test
    void test_Eval_With_Context() throws Exception {
        JSONObject context = new JSONObject()
                .fluentPut("userId", 100)
                .fluentPut("userName", "zhangsan")
                .fluentPut("request", new JSONObject()
                        .fluentPut("a", 1)
                        .fluentPut("b", 2));
        try (JavaScriptScriptExecutor executor = new JavaScriptScriptExecutor()) {
            Assert.assertEquals(3.0, executor.eval("function sum(context) { return context.request.a + context.request.b }", "sum", context));
        }
    }
}
