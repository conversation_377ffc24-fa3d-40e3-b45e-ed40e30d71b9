package skynet.boot.script;

import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;

import java.nio.file.Files;
import java.nio.file.Paths;

class CodeExecutorBuilderTest {

    @Test
    void build() throws Exception {

        CodeExecutorBuilder codeExecutorBuilder = new CodeExecutorBuilder("groovy");
        CodeExecutor codeExecutor = codeExecutorBuilder.build();

//        String script = "def execute(jsonObject) {\n" +
//                "    println(jsonObject)\n" +
//                "}";

//        String script = new String(Files.readAllBytes(Paths.get("src/test/resources/xx.groovy")));
//        String jsonContent = new String(Files.readAllBytes(Paths.get("src/test/resources/in.json")));
        String script = new String(Files.readAllBytes(Paths.get("src/test/resources/incode.groovy")));
        String jsonContent = new String(Files.readAllBytes(Paths.get("src/test/resources/indata.json")));
        JSONObject jsonObject = JSONObject.parseObject(jsonContent);
        Object obj = codeExecutor.evalScript(script, "execute", jsonObject);

        System.out.println(obj);

//        codeExecutor.evalScript(script, "{method}", "{args}");
        codeExecutor.close();
    }
}