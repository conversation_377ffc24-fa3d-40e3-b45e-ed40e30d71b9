package skynet.boot.script.javascript;

import com.alibaba.fastjson2.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class JavaScriptExpressionExecutorTest {

    @Test
    void test_Eval() throws Exception {
        try (JavaScriptExpressionExecutor executor = new JavaScriptExpressionExecutor()) {
            Assert.assertEquals(2, executor.eval("1+1"));
            Assert.assertEquals("hello world", executor.eval("'hello' + ' ' + 'world'"));
            Assert.assertEquals(true, executor.eval("100 > 99"));
        }
    }

    @Test
    void test_Eval_With_Context() throws Exception {
        JSONObject context = new JSONObject()
                .fluentPut("userId", 100)
                .fluentPut("userName", "zhangsan")
                .fluentPut("request", new JSONObject()
                        .fluentPut("a", 1)
                        .fluentPut("b", 2));
        try (JavaScriptExpressionExecutor executor = new JavaScriptExpressionExecutor()) {
            Assert.assertEquals(100, executor.eval("$.userId", context));
            Assert.assertEquals(3.0, executor.eval("$.request.a + $.request.b", context)); // JavaScript 返回的是浮点数
            Assert.assertEquals(true, executor.eval("$.userId == 100 && $.userName == 'zhangsan'", context));
        }
    }
}
