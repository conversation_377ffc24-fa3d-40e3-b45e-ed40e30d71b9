package skynet.boot.script.groovy;

import com.alibaba.fastjson2.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class GroovyExpressionExecutorTest {

    /**
     * 基于 Groovy 实现的表达式执行器，执行结果为 Object 类型
     */
    @Test
    void test_Eval() throws Exception {
        try (GroovyExpressionExecutor executor = new GroovyExpressionExecutor()) {
            Assert.assertEquals(2, executor.eval("1+1"));
            Assert.assertEquals("hello world", executor.eval("'hello' + ' ' + 'world'"));
            Assert.assertEquals(true, executor.eval("100 > 99"));
        }
    }

    /**
     * 基于 Groovy 实现的表达式执行器，执行结果为 Object 类型
     */
    @Test
    void test_Eval_With_Context() throws Exception {
        JSONObject context = new JSONObject()
                .fluentPut("userId", 100)
                .fluentPut("userName", "zhangsan")
                .fluentPut("request", new JSONObject()
                        .fluentPut("a", 1)
                        .fluentPut("b", 2));
        try (GroovyExpressionExecutor executor = new GroovyExpressionExecutor()) {
            Assert.assertEquals(100, executor.eval("$.userId", context));
            Assert.assertEquals(3, executor.eval("$.request.a + $.request.b", context));
            Assert.assertEquals(true, executor.eval("$.userId == 100 && $.userName == 'zhangsan'", context));
        }
    }
}
