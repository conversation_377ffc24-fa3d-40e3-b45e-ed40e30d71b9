package skynet.boot.script.javascript;

import com.alibaba.fastjson2.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

public class JavaScriptBooleanExpressionExecutorTest {

    @Test
    void testEval() throws Exception {
        try (JavaScriptBooleanExpressionExecutor executor = new JavaScriptBooleanExpressionExecutor()) {
            Assert.assertTrue(executor.eval("1+1==2"));
            Assert.assertFalse(executor.eval("1+1==3"));
            Assert.assertTrue(executor.eval("'hello' + ' ' + 'world' == 'hello world'"));
            Assert.assertTrue(executor.eval("100 > 99"));
        }
    }

    @Test
    void testEval2() throws Exception {
        JSONObject context = new JSONObject()
                .fluentPut("userId", 100)
                .fluentPut("userName", "zhangsan")
                .fluentPut("request", new JSONObject()
                        .fluentPut("a", 1)
                        .fluentPut("b", 2));
        try (JavaScriptBooleanExpressionExecutor executor = new JavaScriptBooleanExpressionExecutor()) {
            Assert.assertTrue(executor.eval("$.userId == 100", context));
            Assert.assertTrue(executor.eval("$.userId == '100'", context));
            Assert.assertTrue(executor.eval("$.request.a + $.request.b == 3", context));
            Assert.assertTrue(executor.eval("$.userId == 100 && $.userName == 'zhangsan'", context));
        }
    }

    @Test
    void test_Eval_With_Array() throws Exception {
        List<JSONObject> array = Arrays.asList(
                new JSONObject().fluentPut("name", "zhangsan").fluentPut("score", 90),
                new JSONObject().fluentPut("name", "lisi").fluentPut("score", 100)
        );
        try (JavaScriptBooleanExpressionExecutor executor = new JavaScriptBooleanExpressionExecutor()) {
            Assert.assertTrue(executor.eval("$[0].score > 80 && $[1].score > 80", array));
        }
    }
}
