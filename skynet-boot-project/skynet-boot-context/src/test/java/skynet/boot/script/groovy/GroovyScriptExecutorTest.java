package skynet.boot.script.groovy;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class GroovyScriptExecutorTest {

    /**
     * 测试执行脚本(无参数)
     */
    @Test
    void test_Eval() throws Exception {
        try (GroovyScriptExecutor executor = new GroovyScriptExecutor()) {
            Assert.assertEquals("hello", executor.eval("def demo() { return 'hello' }", "demo"));
        }
    }

    /**
     * 测试执行脚本(有参数)
     */
    @Test
    void test_Eval_With_Context() throws Exception {
        JSONObject context = new JSONObject()
                .fluentPut("userId", 100)
                .fluentPut("userName", "zhangsan")
                .fluentPut("request", new JSONObject()
                        .fluentPut("a", 1)
                        .fluentPut("b", 2));
        try (GroovyScriptExecutor executor = new GroovyScriptExecutor()) {
            Assert.assertEquals(3, executor.eval("def sum(context) { return context.request.a + context.request.b }", "sum", context));

            Object result = executor.eval("def sum(context) { return ['x': context.request.a, 'y': context.request.b ] }", "sum", context);
            System.out.println(JSON.toJSONString(result));

            String s = "${userName}-${userId}";
            s = s.replaceAll("\\$\\{", "\\$\\{context\\.");
            result = executor.eval("def build(context) { return \"" + s + "\" }", "build", context);
            System.out.println(result);
        }
    }
}
