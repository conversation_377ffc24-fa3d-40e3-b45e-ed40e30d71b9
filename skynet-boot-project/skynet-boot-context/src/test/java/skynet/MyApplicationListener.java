package skynet;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

public class MyApplicationListener implements ApplicationListener<ContextRefreshedEvent> {

    public MyApplicationListener() {
        System.err.println("init MyApplicationListener");

    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {

        String logginFile = event.getApplicationContext().getEnvironment().getProperty("log_FILE");
        System.err.println(logginFile);
    }

}
