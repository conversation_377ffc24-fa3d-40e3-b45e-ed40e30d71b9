# deploy in local server or config server
# time zone
spring.jackson.time-zone=GMT+8
# This Application infomations
info.name=@project.artifactId@
info.description=@project-description@
info.version=@project.version@
info.buildtime=@build-timestamp@
###################################################################
#server info
#server.port=${random.int[6000,7999]}
#server.session.timeout=300000
server.tomcat.uri-encoding=UTF-8
#<<<<<<<<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>#
spring.aop.auto=true
spring.aop.proxy-target-class=false
management.health.defaults.enabled=false
#skynet base setting
#skynet.actionName=ant-demo-v10@ant
skynet.actionId=ant-demo-v10@ant
skynet.actionTitle=[\u57FA\u7840\u670D\u52A1]\u5192\u70DF\u9A8C\u8BC1\u670D\u52A1
skynet.actionDesc=[\u57FA\u7840\u670D\u52A1]\u5192\u70DF\u9A8C\u8BC1\u670D\u52A1
skynet.home=/iflytek/server/skynet
skynet.ipAddress=127.0.0.1
spring.autoconfigure.exclude=
#
#server.grpc.netty.enable-native-transport = true
#server.grpc.port=${server.port}
#server.forward-headers-strategy=native
#skynet.zookeeper
#skynet.zookeeper.enabled=true
#skynet.zookeeper.cluster_name=skynet
#skynet.zookeeper.server_list=**************:2181
#skynet.zookeeper.session_timeout=20000
#skynet.zookeeper.connection_timeout=30000
#skynet.zookeeper.onlinePath=/skynet/cluster/online/action
#eureka servers setting
#eureka.client.serviceUrl.defaultZone=http://**************:7070/eureka/
#skynet logging setting
logging.file.path=../log
#logging.file: ${skynet.actionName}.log
logging.config=classpath:logback-spring.xml
#logger
logging.level.root=INFO
logging.level.com.iflytek=INFO
logging.level.org.springframework=ERROR
logging.level.skynet.boot.common.FileZipUtil=INFO
logging.level.skynet.boot=INFO
logging.level.skynet.boot.agent.ScheduledService=INFO
logging.level.skynet.boot.agent.UpdateService=INFO
logging.level.skynet.boot.agent=INFO
logging.level.skynet.boot.core.config=ERROR
logging.level.skynet.boot.core.xray=ERROR
logging.level.skynet.boot.logging.Log2KafkaProducer=ERROR
logging.level.skynet.boot.logging=ERROR
logging.level.skynet.boot.service=INFO
logging.level.skynet.boot.xmanager=INFO
skynet.api.jsondoc.enabled=false
springfox.documentation.enabled=false
spring.security.user.name=




 

