<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="shortcut icon" href="fav.ico"/>
    <link href="login.css" rel="stylesheet"/>
    <script src="js/vue.js"></script>
    <script src="js/jsencrypt.js"></script>
    <title th:text="${title}"></title>
    <!--    <base th:href="${base}"/>-->
</head>
<body>
<div class="m-common-login" id="login-box">
    <div class="header-index">
        <div class="header-inner">
            <div class="logo-box"></div>
        </div>
    </div>
    <div class="login-bg">
        <div class="login-content">
            <div class="login-content-detail">
                <div class="login-title"><span>账号密码登录</span><span class="login-en">USER LOGIN</span></div>
                <div class="login-main">
                    <div class="input-p user">
                        <input type="text" placeholder="USER" autocomplete="off" v-model="userName"/>
                    </div>
                    <div class="input-p password">
                        <input type="password" placeholder="PASS" autocomplete="off" v-model="password"
                               @keyup.stop.enter="login"/>
                    </div>
                    <div class="check-ope" :class="{ active: remend }" @click="_remend()">Save Username</div>
                </div>
                <div class="login-button" @click="login"></div>
                <div class="error-clazz" v-show="errorMsg">{{errorMsg}}</div>
            </div>
        </div>
    </div>
</div>
<script>
    new Vue({
      el: '#login-box',
      data: function () {
        return {
          inputDesc: '',
          showInput: false,
          remend: false,
          userName: '',
          password: '',
          userInfo: {},
          pbKey: '',
          errorMsg: ''
        }
      },
      watch: {
        userName () {
          this.errorMsg = ''
        },
        password () {
          this.errorMsg = ''
        }
      },
      methods: {
        async  postData(url = '', data = {}) {
          // Default options are marked with *
          const response = await fetch(url, {
            method: 'POST', // *GET, POST, PUT, DELETE, etc.
            mode: 'cors', // no-cors, *cors, same-origin
            cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
            credentials: 'same-origin', // include, *same-origin, omit
            headers: {
              'Content-Type': 'application/json'
              // 'Content-Type': 'application/x-www-form-urlencoded',
            },
            redirect: 'follow', // manual, *follow, error
            referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
            body: JSON.stringify(data) // body data type must match "Content-Type" header
          });
          return response.json(); // parses JSON response into native JavaScript objects
        },
        encryptText(text) {
          const encrypt = new JSEncrypt();
          encrypt.setPublicKey(this.pbKey);
          return encrypt.encrypt(text);
        },
        _remend () {
          this.remend = !this.remend
        },
        login () {
          if (this.userName === '' || this.password === '') {
            this.errorMsg = 'Username Or Password cannot be empty'
            return false
          }
          this.remend ? localStorage.setItem('username', this.userName) : localStorage.removeItem('username')
          this.postData('./security/login', {
            username: this.userName,
            password: this.encryptText(this.password)
          })
          .then(data => {
            if (data.code) {
              this.errorMsg = data.message
            } else {
              const _search =  window.location.search
              let _url = '/'
              if (_search) {
                _url = _search.replace('?redirect=', '') || '/'
              }
              window.location.href = _url
            }
          });

        }
      },
      computed: {
      },
      created () {
        if (localStorage.getItem('username')) {
          this.userName = localStorage.getItem('username')
          this.remend = true
        }
        const _this = this
        fetch('./security/publicKey')
        .then(function(response) {
          return response.text();
        })
        .then(function(myJson) {
          _this.pbKey = myJson
        });
      },
      mounted () {
      }
    })
</script>
</body>
</html>
