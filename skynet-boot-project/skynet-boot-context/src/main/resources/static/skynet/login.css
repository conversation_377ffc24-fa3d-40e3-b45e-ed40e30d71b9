/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 */
html, body, div, span, applet, object, iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
input {
  margin: 0; padding: 0;border: 0 ;font-size: 100% ;font-weight: normal; vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block
}

body {
  line-height: 1
}

blockquote,
q {
  quotes: none
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: none
}

:focus {
  outline: 0
}


/* custom */

body {
  -webkit-text-size-adjust: none ; 
  webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

/* login css 样式 */

.m-common-login .header-index {
  width: 100%;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: no-repeat;
  position: absolute;
  z-index: 100
}

.m-common-login .header-inner{
  position: relative;
  margin: auto;
  height: 50px;
  width: 1200px;
}

.m-common-login .logo-box {
  background: url('./img/logo.png') no-repeat left center;
  height: 100%;
  min-width: 158px;
}

.m-common-login .login-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url('./img/login-bg.png') no-repeat center;
  background-size: cover;
  min-height: 620px;
}

.m-common-login .login-content {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 714px;
  height: 448px;
  margin: -230px 0 0 -358px;
  background: url('./img/login-content.png') no-repeat center;
}

.m-common-login .login-content-detail {
  margin-left: 370px;
  margin-top: 41px;
  padding: 60px 30px;
}

.m-common-login .login-title {
  display: flex;
  font-size: 22px;
  color: #2072f5;
  align-items: center;
  height: 24px;
  line-height: 24px;
}

.m-common-login .login-en {
  margin-left: 12px;
  font-size: 20px;
  border-top: 1px solid #d6d6d6;
  border-bottom: 1px solid #d6d6d6;
  color: #d6d6d6;
}

.m-common-login .login-main {
  margin-top: 34px;
  font-size: 14px;
}

.m-common-login  .input-p{
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  height: 38px;
  width: 282px;
  line-height: 36px;
  padding: 0 15px 0 48px;
  outline: none;
  border-radius: 4px 0 0 4px;
}
.m-common-login input {
  width: 100%; 
  height: 34px; 
  outline: none;
}
.m-common-login .user {
  background: url('./img/login-user.png') no-repeat 14px center;
}
.m-common-login .password {
  margin-top: 20px; 
  background: url('./img/login-pw.png') no-repeat 14px center;
}
.m-common-login .check-ope {
  cursor: pointer;
  margin-top: 15px;
  color: #666666;
  padding-left: 26px;
  height: 18px;
  line-height: 18px;
  background: url('./img/check-off.png') no-repeat 2px center;
  transition: all .3s ease;
}

.m-common-login .check-ope.active {
  background: url('./img/check-on.png') no-repeat 2px center;
}

.m-common-login  .login-button {
  margin-top: 22px;
  height: 44px;
  background: url('./img/login-button.png') no-repeat 1px center;
  cursor: pointer;
  width: 282px;
}
.m-common-login .error-clazz {
  background: url('./img/error_msg.png') no-repeat 2px center;
  padding-left: 28px;
  margin-top: 12px ;
  position: absolute;
  color: red
}