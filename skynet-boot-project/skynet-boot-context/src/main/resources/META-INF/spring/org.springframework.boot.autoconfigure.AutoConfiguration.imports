# Enable Auto Configuration
skynet.boot.info.SkynetInfoContributor
skynet.boot.config.SkynetAutoConfiguration
skynet.boot.config.SkynetSpringfoxAutoConfiguration
skynet.boot.web.config.SkynetWebAutoConfiguration
skynet.boot.logging.config.SkynetLoggingAutoConfiguration
skynet.boot.exception.config.SkynetExceptionAutoConfiguration
skynet.boot.exception.handler.SkynetExceptionHandlerAutoConfiguration
skynet.boot.security.config.SkynetAuthClientAutoConfiguration
skynet.boot.security.config.SkynetSecurityPropertiesAutoConfiguration
skynet.boot.security.config.SkynetSecurityAutoConfiguration
skynet.boot.security.config.SkynetSignAuthAutoConfiguration
skynet.boot.security.config.SkynetFormAuthAutoConfiguration
skynet.boot.security.config.SkynetBaseAuthAutoConfiguration
skynet.boot.security.config.SkynetWebFluxSecurityConfiguration
skynet.boot.zookeeper.SkynetZkRewriteAutoConfiguration
skynet.boot.zookeeper.SkynetZkAutoConfiguration
skynet.boot.zookeeper.ZookeeperDiscoveryMetadataRegisterAutoConfiguration
skynet.boot.zookeeper.ZookeeperDiscoveryUnRegisterEndpointAutoConfiguration
skynet.boot.tlb.config.TlbAutoConfiguration
org.springframework.cloud.tlb.discovery.TlbDiscoveryAutoConfiguration
org.springframework.cloud.tlb.extension.config.TlbExtensionAutoConfiguration
org.springframework.cloud.tlb.extension.config.TlbFeignAutoConfiguration
org.springframework.cloud.tlb.extension.config.TlbRestTemplateAutoConfiguration
org.springframework.cloud.tlb.extension.config.TlbWebMvcAutoConfiguration
org.springframework.cloud.tlb.serviceregistry.TlbDiscoveryMetadataRegisterAutoConfiguration
org.springframework.cloud.tlb.serviceregistry.TlbServiceRegistryAutoConfiguration
org.springframework.cloud.tlb.discovery.reactive.TlbReactiveDiscoveryClientAutoConfiguration
skynet.boot.discovery.SkynetDiscoveryAutoConfiguration
skynet.boot.metrics.SkynetMetricAutoConfiguration
skynet.boot.mongo.SkynetMongoAutoConfiguration