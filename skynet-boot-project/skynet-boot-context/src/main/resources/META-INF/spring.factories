# ----------------------------------------------------------
org.springframework.context.ApplicationListener=\
  skynet.boot.config.SkynetApplicationEventListener
# ----------------------------------------------------------
# Application ContextInitializer
org.springframework.context.ApplicationContextInitializer=\
    skynet.boot.config.SkynetAppContextInitializer,\
    skynet.boot.logging.config.SkynetLoggingApplicationContextInitializer,\
    skynet.boot.security.config.ExcludeSecurityApplicationContextInitializer
# ----------------------------------------------------------
# Application Listeners
#org.springframework.context.ApplicationListener=\
#  skynet.cloud.metrics.SkynetPoolMetrics
# ----------------------------------------------------------
#BootstrapConfiguration
#org.springframework.cloud.bootstrap.BootstrapConfiguration=skynet.boot.core.config.RemotePropertySourceLocator
#SpringApplicationRunListener
#org.springframework.boot.SpringApplicationRunListener=skynet.boot.MySpringApplicationRunListener
#Application Listeners
#org.springframework.context.ApplicationListener=skynet.boot.MyApplicationListener