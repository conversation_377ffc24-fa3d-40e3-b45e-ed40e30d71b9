###################################################################
spring.data.web.pageable.one-indexed-parameters=true
###################################################################
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
###################################################################
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}]){cyan}%clr(:){faint} %m%n
logging.pattern.file=%d{MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- }[%t] %-40.40logger{39}[%3line{3}]: %m%n
###################################################################
server.tomcat.threads.max=256
server.tomcat.threads.min-spare=16
server.tomcat.accept-count=256
###################################################################
spring.datasource.hikari.maximum-pool-size=20
###################################################################
feign.httpclient.max-connections=128
feign.httpclient.max-connections-per-route=32
###################################################################
logging.level.org.springframework.cloud.tlb=INFO
#\u5FFD\u7565\u6307\u5B9A\u6B63\u5219\u5339\u914D\u7684\u7F51\u5361\u7684\u914D\u7F6E\uFF0C\u6211\u8FD9\u91CC\u914D\u7F6E\u4E86VM\u865A\u62DF\u673A\u548CDocker\u7684
#spring.cloud.inetutils.ignoredInterfaces=['VMware.*','Hyper-V.*']
#\u6307\u5B9A\u9ED8\u8BA4IP\uFF0C\u53EF\u4EE5\u4F7FIP\u6BB5
#spring.cloud.inetutils.preferredNetworks=['192.168']
#spring.cloud.inetutils.use-only-site-local-interfaces=true