# test in local server or config server
#server info
#server.port=${random.int[6000,7999]}
server.tomcat.uri-encoding=UTF-8
#skynet common setting
springfox.documentation.enabled=true
#skynet action setting
skynet.action-point=ant-demo-v10@ant
#skynet.zookeeper
#skynet.zookeeper.enabled=true
#skynet.zookeeper.cluster_name=skynet
#skynet.zookeeper.server_list=**************:2181
#skynet.zookeeper.session_timeout=20000
#skynet.zookeeper.connection_timeout=30000
#skynet.zookeeper.onlinePath=/skynet/cluster/online/action
#skynet logging setting
logging.file.path=../log
logging.file.name=${skynet.actionName}.log
logging.config=classpath:logback-spring.xml
#logger
logging.level.root=INFO
logging.level.com.iflytek=INFO
logging.level.org.springframework=ERROR
logging.level.skynet.boot.common.FileZipUtil=INFO
logging.level.skynet.boot.agent.ScheduledService=INFO
logging.level.skynet.boot.agent.UpdateService=INFO
logging.level.skynet.boot.agent=INFO
logging.level.skynet.boot.core.config=ERROR
logging.level.skynet.boot.core.xray=ERROR
logging.level.skynet.boot.logging.Log2KafkaProducer=ERROR
logging.level.skynet.boot.logging=ERROR
logging.level.skynet.boot.service=INFO
logging.level.skynet.boot.xmanager=INFO
logging.level.skynet.boot=INFO


 

