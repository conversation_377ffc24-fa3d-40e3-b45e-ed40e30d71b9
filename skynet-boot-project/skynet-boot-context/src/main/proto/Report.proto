syntax = "proto3";
package Report;
option java_package = "skynet.boot.tlb.core";

message ReportResponse{
    int32 ErrorCode=1;//上报接口调用结果
    string ErrorInfo=2;//错误描述信息
}

//上报给LB的服务信息
message ServiceInfo{
    // ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
    // ServiceName——服务完整名称，如iat_hf
    // ServiceIP——服务IP，如**************
    // ServicePort——服务端口号
    // MaxLic——最大授权
    // UsedLic——当前使用授权
    // CPU——CPU使用率百分比，50表示50%
    // Memory——内存使用率百分比，60表示60%
    // bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
    // bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
    // bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
    // resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
    // MetaData——扩展数据
    map<string,string> Info=1;
}
//服务向LB上报
service Report {
    //向LB上报服务器信息
    rpc ReportServiceInfo(stream ServiceInfo) returns (ReportResponse){}
}