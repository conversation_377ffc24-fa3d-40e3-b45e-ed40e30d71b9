// file content from https://git.xfyun.cn/TURING/LB/src/master/GetServer/GetServer.proto

// protobuf3

syntax = "proto3";

package GetServer;
option java_package = "skynet.boot.tlb.core";

message ServiceName{
  string ServiceName = 1;
}

//获取服务器信息时的参数
message ServerParam {
  string ServerName = 1;//要获取的服务名称
  int32 Number = 2;//要获取的服务地址数量
  string UID = 3;//UID用于个性化
  string Reserved = 4;//扩展字段
}

message ServerParamEx{
  string ServerName = 1;//要获取的服务名称
  int32 Number = 2;//要获取的服务地址数量
  //Map中基础信息如下：
  //"bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
  //"reqType"，请求类型
  //      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
  //      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
  //      空或"common"时，代表普通获取最佳服务请求。
  //"resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
  //"tag"  A&&B   A||B   !(A&&B)
  map<string, string> ServiceInfo = 3;
}

//单个服务的IP端口信息
message ServerAddress{
  string ServerIP = 1;
  int32 ServerPort = 2;
}

//多个服务的IP端口信息
message ServerAddresses{
  int32 ErrorCode = 1;//GetBestServer接口调用结果
  string ErrorInfo = 2;//错误信息
  repeated ServerAddress AddressList = 3;//获取到的服务器信息列表
}

//GetServiceInfo时获取到的服务信息列表
message ServiceInfoList{
  int32 ErrorCode = 1;//GetServiceInfo接口调用结果
  string ErrorInfo = 2;//调用错误信息

  //JSON格式信息示例
  //{
  //    "asr_hf": [
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":5090,
  //            "max_lic":200,
  //            "used_lic":55,
  //            "cpu":51.5,
  //            "mem":22.2
  //        },
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":5090,
  //            "max_lic":150,
  //            "used_lic":55,
  //            "cpu":51.5,
  //            "mem":33.3
  //        }
  //    ],
  //
  //    "tts_xtts": [
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":10006,
  //            "max_lic":200,
  //            "used_lic":55,
  //            "cpu":99.9,
  //            "mem":66.6
  //        },
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":10006,
  //            "max_lic":200,
  //            "used_lic":55,
  //            "cpu":51.5,
  //            "mem":22.2
  //        }
  //    ]
  //}
  string ServiceInfo = 3;//JSON格式的服务信息列表
}

message LBVersion{
  string Version = 1;
}

message EmptyMsg{}

//面向业务层的接口
service LoadBalance {
  //获取最佳服务地址
  rpc GetBestServer(ServerParam) returns (ServerAddresses){}

  //获取最佳服务实例信息（包含meta，tag）
  rpc GetBestServerInstances(ServerParamEx) returns (ServiceInfoList){}

  //获取最佳服务地址(ServerParamEx中最后一个字段为Map)
  rpc GetBestServerEx(ServerParamEx) returns (ServerAddresses){}

  //获取服务信息
  rpc GetServiceInfo(ServiceName) returns (ServiceInfoList) {}

  //获取LB版本(用于检测与LB之前的连接)
  rpc GetVersion(EmptyMsg) returns(LBVersion){}
}