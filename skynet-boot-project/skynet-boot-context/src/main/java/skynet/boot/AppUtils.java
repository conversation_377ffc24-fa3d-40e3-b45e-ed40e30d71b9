package skynet.boot;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

/**
 * Utility class for bootstrapping Skynet Spring Boot applications
 * 
 * This class provides a static method {@link #run} for launching Spring Boot
 * applications
 * with Skynet-specific configurations and initializations.
 *
 * Features:
 * 1. Configures JSON serialization options
 * 2. Sets default timezone to Asia/Shanghai
 * 3. Bootstraps Spring Boot application and retrieves application context
 * 4. Records and logs application startup metrics
 * 5. Registers application shutdown hooks
 *
 * Example usage:
 * 
 * <pre>
 * public class MyApplication {
 *     public static void main(String[] args) {
 *         AppUtils.run(MyApplication.class, args);
 *     }
 * }
 * </pre>
 *
 * Important notes:
 * 1. Ensure the primary source class is properly configured as a Spring Boot
 * application
 * 2. Verify that AppContext bean is defined in the application context
 * 3. Configure logging framework (logback/log4j) appropriately
 * 4. Set appropriate log levels for production environments
 *
 * <AUTHOR> [Oct 19, 2017 2:14:00 PM]
 * @see SpringApplication
 * @see ConfigurableApplicationContext
 * @see Environment
 * @see StopWatch
 * @see AppContext
 */
@Slf4j
public final class AppUtils {

    /**
     * Default timezone for the application
     */
    private static final String DEFAULT_TIMEZONE = "Asia/Shanghai";

    /**
     * Application startup banner template
     */
    private static final String STARTUP_BANNER = """

            -----------------------------------------------------------------------------
            \t\
            Application:\t%s is running! [%s] [cost = %5.3f seconds]
            \t\
            Local: \t\t%s://%s:%s%s
            \t\
            External: \t%s
            \t\
            Profile(s): \t%s
            -----------------------------------------------------------------------------""";

    /**
     * Private constructor to prevent instantiation
     */
    private AppUtils() {
        throw new UnsupportedOperationException("Utility class should not be instantiated");
    }

    /**
     * Launches a Spring Boot application with Skynet-specific configurations
     *
     * @param primarySource The main application class (must not be null)
     * @param args          Command line arguments
     * @throws IllegalArgumentException if primarySource is null
     */
    public static void run(Class<?> primarySource, String... args) {
        Assert.notNull(primarySource, "Primary source must not be null");

        try {
            configureJsonSerializer();
            ConfigurableApplicationContext context = bootstrapApplication(primarySource, args);
            Environment env = context.getEnvironment();
            AppContext appContext = context.getBean(AppContext.class);

            // Log application startup information
            logApplicationStartup(appContext, env, args);

            // Register shutdown hook
            registerShutdownHook(appContext);
        } catch (Throwable e) {
            log.error("Application startup failed: {}", e.getMessage(), e);
            System.exit(-1);
        }
    }

    /**
     * Configures JSON serialization settings
     */
    private static void configureJsonSerializer() {
        log.info("Configuring JSON serialization settings");

        // Use enum names instead of ordinal values
        JSON.config(JSONWriter.Feature.WriteEnumsUsingName, true);

        // Disable circular reference detection for better performance
        JSON.config(JSONWriter.Feature.ReferenceDetection, false);

        // Enable large object handling
        JSON.config(JSONWriter.Feature.LargeObject, true);
    }

    /**
     * Bootstraps the Spring Boot application
     *
     * @param primarySource The main application class
     * @param args          Command line arguments
     * @return The configured application context
     */
    private static ConfigurableApplicationContext bootstrapApplication(Class<?> primarySource, String... args) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // Set default timezone
        TimeZone.setDefault(TimeZone.getTimeZone(DEFAULT_TIMEZONE));

        // Create and run Spring application
        SpringApplication app = new SpringApplication(primarySource);
        ConfigurableApplicationContext context = app.run(args);

        // Record startup time
        stopWatch.stop();
        AppContext appContext = context.getBean(AppContext.class);
        appContext.setStartCost(stopWatch.getTotalTimeMillis());

        return context;
    }

    /**
     * Logs application startup information
     *
     * @param appContext The application context
     * @param env        The Spring environment
     * @param args       Command line arguments
     */
    private static void logApplicationStartup(AppContext appContext, Environment env, String... args) {
        String springApplicationName = env.getProperty("spring.application.name", appContext.getNodeName());
        log.debug("AppContext = {}", appContext);
        log.info("Starting {} with arguments: [{}]", appContext.getNodeName(), String.join(",", args));

        String protocol = appContext.isSsl() ? "https" : "http";
        String startupMessage = String.format(STARTUP_BANNER,
                springApplicationName,
                appContext.getNodeName(),
                appContext.getStartCost() / 1000.0,
                protocol,
                appContext.getHostName(),
                appContext.getPort(),
                appContext.getContextPath(),
                appContext.getUri(),
                String.join(",", env.getActiveProfiles()));

        if (log.isInfoEnabled()) {
            log.info(startupMessage);
        } else {
            System.out.println(startupMessage);
        }
    }

    /**
     * Registers a shutdown hook for graceful application termination
     *
     * @param appContext The application context
     */
    private static void registerShutdownHook(AppContext appContext) {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Application {} is shutting down...", appContext.getNodeName());
            log.info("Application {} has been stopped", appContext.getNodeName());
        }));
    }
}