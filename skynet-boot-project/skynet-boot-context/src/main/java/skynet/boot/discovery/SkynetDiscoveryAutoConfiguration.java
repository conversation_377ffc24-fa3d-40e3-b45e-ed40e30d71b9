package skynet.boot.discovery;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetDiscoveryClient;
import skynet.boot.discovery.support.SkynetLoadBalancerImpl;
import skynet.boot.discovery.support.SkynetServiceChooser;
import skynet.boot.discovery.support.SkynetServiceChooser4Lb;
import skynet.boot.discovery.support.SkynetServiceChooser4Tlb;
import skynet.boot.tlb.TlbClientSelector;
import skynet.boot.tlb.config.TlbAutoConfiguration;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/1 08:28
 */
@Slf4j
@ConditionalOnClass({DiscoveryClient.class})
@ConditionalOnBean(annotation = EnableSkynetDiscoveryClient.class, value = {DiscoveryClient.class})
@AutoConfigureAfter({TlbAutoConfiguration.class})
@Configuration(proxyBeanMethods = false)
public class SkynetDiscoveryAutoConfiguration {
    @Bean
    public SkynetDiscoveryClient skynetDiscoveryClient(DiscoveryClient discoveryClient) {
        return new SkynetDiscoveryClient(discoveryClient);
    }


    @Bean
    public SkynetServiceChooser skynetServiceChooser4lb(DiscoveryClient discoveryClient) {
        return new SkynetServiceChooser4Lb(discoveryClient);
    }

    @Bean
    @ConditionalOnBean({TlbSelectHeaderProperties.class, TlbClientSelector.class})
    public SkynetServiceChooser4Tlb skynetServiceChooser4tlb(TlbSelectHeaderProperties tlbSelectHeaderProperties,
                                                             TlbClientSelector tlbClientSelector,
                                                             List<TlbDiscoveryContextConfigurer> configurerList) {
        return new SkynetServiceChooser4Tlb(tlbSelectHeaderProperties, tlbClientSelector, configurerList);
    }

    @Bean
    @ConditionalOnMissingBean(SkynetLoadBalancer.class)
    public SkynetLoadBalancer skynetLoadBalancer(List<SkynetServiceChooser> loadBalancerList) {
        return new SkynetLoadBalancerImpl(loadBalancerList);
    }
}
