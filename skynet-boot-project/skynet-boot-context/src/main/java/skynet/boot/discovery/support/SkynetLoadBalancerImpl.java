package skynet.boot.discovery.support;

import com.alibaba.fastjson2.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriBuilder;
import org.springframework.web.util.UriComponentsBuilder;
import skynet.boot.discovery.SkynetLoadBalancer;
import skynet.boot.discovery.exception.NotFoundServiceException;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;


/***
 * SkynetLoadBalancer
 *
 * 支持 lb，tlb，多URL
 *
 * <AUTHOR>
 */
@Slf4j
public class SkynetLoadBalancerImpl implements SkynetLoadBalancer {
    // 服务选择器 map, key: 服务发现的 scheme，如 lb； tlb等
    private final Map<String, SkynetServiceChooser> serviceChooserMap;
    // 不支持解析的 uri 缓存
    private final Cache<String, Integer> notSupportUriCache;

    /**
     * 构造函数，初始化服务选择器 map 和缓存
     *
     * @param loadBalancerList 服务选择器列表
     */
    public SkynetLoadBalancerImpl(List<SkynetServiceChooser> loadBalancerList) {
        log.debug("loadBalancerList.size={}", loadBalancerList.size());
        this.serviceChooserMap = new LinkedHashMap<>(loadBalancerList.size());
        for (SkynetServiceChooser skynetServiceChooser : loadBalancerList) {
            if (StringUtils.hasText(skynetServiceChooser.getScheme())) {
                // 将服务选择器放入 map 中
                if (this.serviceChooserMap.containsKey(skynetServiceChooser.getScheme())) {
                    throw new RuntimeException(
                            String.format("The service discovery for Scheme %s has been registered.[%s]",
                                    skynetServiceChooser.getScheme(), skynetServiceChooser.getClass()));
                }
                this.serviceChooserMap.put(skynetServiceChooser.getScheme(), skynetServiceChooser);
                log.debug("Scheme={},ServiceChooser={}", skynetServiceChooser.getScheme(), skynetServiceChooser);
            } else {
                throw new RuntimeException(String.format("the %s Scheme is empty.", skynetServiceChooser.getClass()));
            }
        }
        log.info("Support Scheme={} service discovery client", JSON.toJSONString(serviceChooserMap.keySet()));


        // 初始化缓存
        this.notSupportUriCache = CacheBuilder.newBuilder()
                .maximumSize(128)
                .expireAfterWrite(Duration.ofMinutes(5))
                .removalListener(notification -> log.debug("notSupportUriCache remove key={}; cause={}", notification.getKey(), notification.getCause()))
                .concurrencyLevel(1)
                .initialCapacity(1)
                .recordStats()
                .build();
    }


    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     * 此方法解释了为什么需要根据URI获取实际的服务地址，通过服务发现机制，将虚拟的服务地址
     * 替换为实际在线的服务节点地址和端口。
     *
     * @param uri 传入的服务URI，可以是HTTP、WebSocket或gRPC协议的地址。
     *            示例包括：
     *            - http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *            - ws://tlb:ocr-service/api/v3/talk
     * @return 返回替换后的URI，例如：
     * - http://127.0.0.1:9098/api/v3/func
     * - ws://{ip}:{port}/api/v3/talk
     */
    @Override
    public Optional<URI> choose(String uri) {
        // 调用重载的choose方法，传入uri和一个空的映射，这里暗示了实际的服务选择逻辑可能依赖于额外的参数或配置。
        return this.choose(uri, Collections.emptyMap());
    }


    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     *
     * @param uri     http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *                ws://tlb:ocr-service/api/v3/talk
     *                grpc://lb:ocr-service/api/v3/talk
     * @param context 服务发现上下文，如，存放LB tag 筛选表达式（KEY:TLB_SERVICE_TAG_SELECTOR）或个性化业务BizId（KEY:TLB_SERVICE_BIZ_ID）
     * @return 替换后的URI eg: http://127.0.0.1:9098/api/v3/func
     * *            ws://{ip}:{port}/api/v3/talk
     * *            grpc://{ip}:{port}/api/v3/talk
     */
    @Override
    public Optional<URI> choose(String uri, Map<String, String> context) {
        Assert.hasText(uri, "uri is blank.");
        log.debug("uri={};context={}", uri, context);

        //根据 传入目标服务的 URI，替换成实际的服务URL地址。得到 ws://************:8080/sample/info
        try {
            // 将 uri 以分号或逗号为分隔符拆分成多个 uri，然后选择其中一个
            List<String> uriList = Arrays.stream(uri.replace(";", ",").split(",")).filter(StringUtils::hasText).collect(Collectors.toList());
            // 随机选择一个 uri
            int index = (uriList.size() > 1) ? new Random().nextInt(uriList.size()) : 0;
            URI url = chooseOne(uriList.get(index), context);
            log.debug("real url={}", url);
            return Optional.of(url);
        } catch (URISyntaxException e) {
            log.error("choose url error.", e);
        }
        log.debug("url is empty");
        return Optional.empty();
    }

    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     *
     * @param uri http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *            ws://tlb:ocr-service/api/v3/talk
     * @return 实际 {ip}:{port} eg: 127.0.0.1:9098
     */
    @Override
    public Optional<String> chooseEndpoint(String uri) {
        return this.chooseEndpoint(uri, Collections.emptyMap());
    }

    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     *
     * @param uri     http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *                ws://tlb:ocr-service/api/v3/talk
     * @param context 服务发现上下文，如，存放LB tag 筛选表达式（KEY:TLB_SERVICE_TAG_SELECTOR）或个性化业务BizId（KEY:TLB_SERVICE_BIZ_ID）
     * @return 实际 {ip}:{port} eg: 127.0.0.1:9098
     */
    @Override
    public Optional<String> chooseEndpoint(String uri, Map<String, String> context) {
        Optional<URI> result = this.choose(uri, context);
        return result.map(value -> String.format("%s:%s", value.getHost(), value.getPort()));
    }


    /**
     * 选择一个服务实例的 uri
     *
     * @param uri     原始的 uri
     * @param context 上下文信息
     * @return 服务实例的 uri
     * @throws URISyntaxException URI 解析异常
     */
    private URI chooseOne(String uri, Map<String, String> context) throws URISyntaxException {
        // 解析 uri
        URI uriObj = new URI(uri);
        context = Optional.ofNullable(context).orElse(Collections.emptyMap());

        // 如果该 uri 在缓存中已经被标记为不支持解析，直接返回原始的 uri
        if (notSupportUriCache.getIfPresent(uri) != null) {
            log.debug("the {} does not require parsing and conversion.", uri);
            return uriObj;
        }

        try {
            // 解析 uri 中的 host，得到服务实例的 scheme 和 id
            URI discoveryHost = new URI(uriObj.getAuthority());
            String discoveryServiceScheme = discoveryHost.getScheme();
            if (!StringUtils.hasText(discoveryServiceScheme)) {
                throw new URISyntaxException(uriObj.getAuthority(), String.format("The %s discoveryHost=%s format invalid", uriObj.getAuthority(), discoveryHost));
            }
            String discoveryServiceId = discoveryHost.getSchemeSpecificPart();
            if (serviceChooserMap.containsKey(discoveryServiceScheme)) {
                //根据 服务发现URL的 scheme 获取 对应的 scheme 的 服务发现选择器，
                // 再 传入服务标识 ServiceId，和 服务协议的scheme，获取服务实例
                ServiceInstance serviceInstance = serviceChooserMap.get(discoveryServiceScheme).choose(discoveryServiceId, context);
                log.debug("serviceInstance={}", serviceInstance);
                if (serviceInstance == null) {
                    //输出详细的上下文：uri, context
                    String msg = String.format("[uri=%s][context=%s][%s]", uri, JSON.toJSONString(context), JSON.toJSONString(serviceChooserMap.get(discoveryServiceScheme).getStatus()));
                    throw new NotFoundServiceException(discoveryServiceId, msg);
                }
                //根据服务协议从 ServiceInstance 中获取需要的端口, 如果没有不存在直接取 实例的port（一般是http 协议）
                int port = serviceInstance.getPort();
                if (serviceInstance.getMetadata() != null) {
                    port = Integer.parseInt(serviceInstance.getMetadata().getOrDefault(String.format("%s.port", uriObj.getScheme()), String.valueOf(port)));
                }
                UriBuilder builder = UriComponentsBuilder.fromUri(uriObj).host(serviceInstance.getHost()).port(port);
                return builder.build();
            } else {
                log.warn("Not supported [{}] type for service discovery.[{}]", discoveryServiceScheme, uri);
                this.notSupportUriCache.put(uri, 1);
                return uriObj;
//                throw new RuntimeException(String.format("Not supported [%s] type for service discovery.", discoveryServiceScheme));
            }
        } catch (URISyntaxException e) {
            log.warn("the {} does not require parsing and conversion.", uriObj.getAuthority());
            this.notSupportUriCache.put(uri, 1);
            //直接使用原始的 uri;
            return uriObj;
        }
    }

//    public static void main(String[] args) throws URISyntaxException {
//        String url = "ws://swsj.talker.v2:9979/turing/v3/gpt";
//
//        DefaultLoadBalancer defaultLoadBalancer = new DefaultLoadBalancer(new ArrayList<>());
//        Object obj = defaultLoadBalancer.choose(url, new HashMap<>());
//        System.out.println(obj);
//
//        url = "http://127.0.0.1:9098/api/v3/func";
//        defaultLoadBalancer = new DefaultLoadBalancer(new ArrayList<>());
//        obj = defaultLoadBalancer.choose(url, new HashMap<>());
//        System.out.println(obj);
//    }
}
