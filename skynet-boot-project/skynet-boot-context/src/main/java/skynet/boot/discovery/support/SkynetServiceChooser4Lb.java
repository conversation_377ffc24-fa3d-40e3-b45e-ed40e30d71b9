package skynet.boot.discovery.support;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * spring-cloud-discovery-client 服务发现实现
 *
 * <AUTHOR>
 */
@Slf4j
public class SkynetServiceChooser4Lb implements SkynetServiceChooser {

    private final DiscoveryClient discoveryClient;

    public SkynetServiceChooser4Lb(DiscoveryClient discoveryClient) {
        this.discoveryClient = discoveryClient;
    }

    @Override
    public String getScheme() {
        return "lb";
    }

    @Override
    public ServiceInstance choose(String serviceId, Map<String, String> context) {

        Assert.hasText(serviceId, "serviceId is empty.");
        Assert.notNull(context, "context is null.");

        //TODO： biz ,tag
        log.debug("Fetch service [serviceId={}] ...", serviceId);
        List<org.springframework.cloud.client.ServiceInstance> objList = discoveryClient.getInstances(serviceId);
        log.debug("Fetch service [serviceId={}; service.size={}] ...", serviceId, objList.size());

        if (objList.isEmpty()) {
            return null;
        }
        //随机选择一个
        int index = (objList.size() > 1) ? new Random().nextInt(objList.size()) : 0;

        return objList.get(index);
    }
}
