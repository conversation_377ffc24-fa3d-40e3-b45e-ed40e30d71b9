package skynet.boot.discovery.support;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.tlb.discovery.TlbServiceInstance;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import skynet.boot.discovery.TlbDiscoveryContext;
import skynet.boot.discovery.TlbDiscoveryContextConfigurer;
import skynet.boot.tlb.TlbClientSelector;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;
import skynet.boot.tlb.domain.LbFeature;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图聆LB 的服务发现选择器
 *
 * <AUTHOR>
 */
@Slf4j
public class SkynetServiceChooser4Tlb implements SkynetServiceChooser {

    private static final Map<String, String> EMPTY_MAP = new HashMap<>(0); // 定义常量，避免每次创建空Map
    private static final int DEFAULT_TOP_N = 1; // 默认返回1个实例
    private static final int MAX_TOP_N = 256; // 最大支持返回256个实例

    private final TlbClientSelector tlbClientSelector;
    private final TlbSelectHeaderProperties tlbSelectHeaderProperties;
    private final List<TlbDiscoveryContextConfigurer> configurerList;

    public SkynetServiceChooser4Tlb(TlbSelectHeaderProperties tlbSelectHeaderProperties, TlbClientSelector tlbClientSelector, List<TlbDiscoveryContextConfigurer> configurerList) {
        this.tlbSelectHeaderProperties = tlbSelectHeaderProperties;
        this.tlbClientSelector = tlbClientSelector;
        this.configurerList = configurerList.stream()
                .sorted(Comparator.comparing(TlbDiscoveryContextConfigurer::getOrder))
                .collect(Collectors.toList());
    }

    @Override
    public String getScheme() {
        return "tlb";
    }

    public ServiceInstance choose(String serviceId) {
        return this.choose(serviceId, EMPTY_MAP);
    }

    @Override
    public ServiceInstance choose(String serviceId, Map<String, String> context) {
        Assert.hasText(serviceId, "serviceId is empty.");
        Assert.notNull(context, "context is null.");

        TlbDiscoveryContext ctx = createTlbDiscoveryContext(DEFAULT_TOP_N, "");

        // 使用传入的参数覆盖上下文中的信息
        String tagSelector = context.getOrDefault(tlbSelectHeaderProperties.getTagSelectorKey(), "");
        String bizId = context.getOrDefault(tlbSelectHeaderProperties.getBizIdKey(), "");
        if (StringUtils.hasText(tagSelector)) {
            ctx.setTagSelector(tagSelector);
        }
        if (StringUtils.hasText(bizId)) {
            ctx.setBizId(bizId);
        }

        List<ServiceInstance> resultList = getInstances(serviceId, ctx);
        return resultList.size() == 1 ? resultList.get(0) : null;
    }

    public List<ServiceInstance> getInstances(String serviceId, int topN, String defaultTagSelector) {
        TlbDiscoveryContext ctx = createTlbDiscoveryContext(topN, defaultTagSelector);
        return getInstances(serviceId, ctx);
    }

    private List<ServiceInstance> getInstances(String serviceId, TlbDiscoveryContext ctx) {
        LbFeature[] features = ctx.isDisabledSelectServerDefault()
                ? new LbFeature[]{LbFeature.DISABLED_SELECT_SERVER_DEFAULT}
                : new LbFeature[0];

        List<skynet.boot.tlb.client.data.TlbServiceInstance> tlbServiceInstances = tlbClientSelector.getBestServerInstances(
                serviceId, ctx.getTagSelector(), ctx.getBizId(), ctx.getTopN(), features
        );

        if (log.isDebugEnabled()) {
            log.debug("ServiceId={} Instances={}", serviceId, JSON.toJSONString(tlbServiceInstances));
        }

        if (tlbServiceInstances.isEmpty()) {
            log.warn("Not found service [serviceId={}, tagSelector={}, bizId={}]", serviceId, ctx.getTagSelector(), ctx.getBizId());
        }

        return tlbServiceInstances.stream().map(TlbServiceInstance::new).collect(Collectors.toList());
    }

    public Map<String, List<skynet.boot.tlb.client.data.TlbServiceInstance>> getInstancesMap(String defaultTagSelector) {
        TlbDiscoveryContext ctx = createTlbDiscoveryContext(MAX_TOP_N, defaultTagSelector);
        Map<String, List<skynet.boot.tlb.client.data.TlbServiceInstance>> serviceInstanceMap = tlbClientSelector.getServiceInstanceMap();

        if (StringUtils.hasText(ctx.getTagSelector())) {
            for (Map.Entry<String, List<skynet.boot.tlb.client.data.TlbServiceInstance>> entry : serviceInstanceMap.entrySet()) {
                entry.getValue().removeIf(instance -> !instance.getTagList().contains(ctx.getTagSelector()));
            }

            serviceInstanceMap = serviceInstanceMap.entrySet().stream()
                    .filter(entry -> !entry.getValue().isEmpty())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        log.debug("Filtered serviceInstanceMap size = {}", serviceInstanceMap.size());
        return serviceInstanceMap;
    }

    @Override
    public Object getStatus() {
        return tlbClientSelector.getLbStatus();
    }

    @NotNull
    private TlbDiscoveryContext createTlbDiscoveryContext(int topN, String defaultTagSelector) {
        Assert.isTrue(topN > 0, "The topN must be greater than 0.");

        TlbDiscoveryContext ctx = new TlbDiscoveryContext(topN);
        ctx.setTagSelector(defaultTagSelector);

        // 通过外界干预配置
        log.debug("Before applying configurers: {}", ctx);
        configurerList.forEach(configurer -> configurer.apply(ctx));
        log.debug("After applying configurers: {}", ctx);

        return ctx;
    }
}