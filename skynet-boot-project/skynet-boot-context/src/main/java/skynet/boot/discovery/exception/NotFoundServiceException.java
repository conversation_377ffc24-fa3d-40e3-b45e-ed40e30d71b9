package skynet.boot.discovery.exception;

import lombok.Getter;
import skynet.boot.exception.SkynetException;

/**
 * <AUTHOR>
 */
@Getter
public class NotFoundServiceException extends SkynetException {

    public String serviceId;

    public NotFoundServiceException(String serviceId, String msg) {
        super(-1, String.format("The serviceId=%s not found.(%s)", serviceId, msg));
        this.serviceId = serviceId;
    }
}
