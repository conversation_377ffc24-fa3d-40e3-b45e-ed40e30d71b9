package skynet.boot.discovery;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.util.Assert;
import skynet.boot.SkynetConsts;
import skynet.boot.common.OsUtil;

import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/30 19:53
 */
@Slf4j
public class SkynetDiscoveryClient {

    private final DiscoveryClient discoveryClient;

    public SkynetDiscoveryClient(DiscoveryClient discoveryClient) {
        this.discoveryClient = discoveryClient;
    }

    /**
     * 通过 服务名称 随机获取一个服务实例
     *
     * @param serviceId
     * @return
     */
    public ServiceInstance getInstance(String serviceId) {
        return getInstance(serviceId, false);
    }

    /**
     * 通过服务信息的 Meta 中的 key=value 过滤获取服务实例列表。
     *
     * @param serviceId 服务名称
     * @param mKey      元数据 key
     * @param mValue    元数据 value
     * @return 实例列表
     */
    public List<ServiceInstance> getInstances(String serviceId, String mKey, String mValue) {
        Assert.hasText(serviceId, "The serviceId is blank.");
        Assert.hasText(mKey, "The mKey is blank.");
        Assert.hasText(mValue, "The mValue is blank.");

        log.debug("Fetch Instance serviceId={} by metadata {}={}", serviceId, mKey, mValue);

        List<ServiceInstance> allInstances = discoveryClient.getInstances(serviceId);
        if (log.isTraceEnabled()) {
            log.trace("Fetch Instance serviceId={} ServiceInstanceList={}", serviceId, JSON.toJSONString(allInstances));
        }

        List<ServiceInstance> filteredInstances = allInstances.stream()
                .filter(instance -> mValue.equalsIgnoreCase(instance.getMetadata().getOrDefault(mKey, "")))
                .collect(Collectors.toList());

        log.debug("Fetched instance list: serviceId={}, totalCount={}, filteredCount={}", serviceId, allInstances.size(), filteredInstances.size());
        return filteredInstances;
    }

    /**
     * 获取指定服务ID的服务实例
     *
     * @param serviceId  服务ID
     * @param firstLocal 是否优先使用本地服务实例
     * @return 服务实例
     */
    public ServiceInstance getInstance(String serviceId, boolean firstLocal) {
        Assert.hasText(serviceId, "The serviceId is blank.");
        log.debug("Fetch Instance serviceId={}  firstLocal={}", serviceId, firstLocal);

        List<ServiceInstance> instances = discoveryClient.getInstances(serviceId);
        if (firstLocal) {
            instances = prioritizeLocalInstances(instances);
        }
        ServiceInstance serviceInstance = getRandomInstance(instances);

        log.debug("ServiceInstance={}", serviceInstance);
        return serviceInstance;
    }

    /**
     * 将本地服务实例排在列表前面
     *
     * @param instances 服务实例列表
     * @return 排序后的服务实例列表
     */
    private List<ServiceInstance> prioritizeLocalInstances(List<ServiceInstance> instances) {
        List<ServiceInstance> localInstances = instances.stream().filter(i -> OsUtil.isLocalIP(i.getHost())).collect(Collectors.toList());
        if (!localInstances.isEmpty()) {
            instances = localInstances;
        }
        return instances;
    }

    /**
     * 从服务实例列表中随机选择一个服务实例
     *
     * @param instances 服务实例列表
     * @return 随机选择的服务实例，如果实例列表为空则返回null
     */
    private ServiceInstance getRandomInstance(List<ServiceInstance> instances) {
        if (instances.isEmpty()) {
            return null;
        }
        int index = instances.size() > 1 ? new Random().nextInt(instances.size()) : 0;
        return instances.get(index);
    }


    public List<ServiceInstance> getInstances(String serviceId) {
        Assert.hasText(serviceId, "The serviceId is blank.");
        return discoveryClient.getInstances(serviceId);
    }

    /**
     * 通过 服务坐标(app@plugin) 随机获取一个服务实例
     *
     * @param actionPoint skynet中的服务坐标 格式如：app@plugin
     * @return
     */
    public ServiceInstance getInstanceByActionPoint(String actionPoint) {
        return getInstanceByActionPoint(actionPoint, false);
    }

    /**
     * 通过 服务坐标 获取随机获取一个服务实例
     *
     * @param actionPoint skynet中的服务坐标 格式如：app@plugin
     * @param firstLocal  是否本机优先
     * @return
     */
    public ServiceInstance getInstanceByActionPoint(String actionPoint, boolean firstLocal) {
        List<ServiceInstance> allNodeList = getInstancesByActionPoint(actionPoint);
        if (firstLocal && allNodeList.size() > 1) {
            // 优先本地IP服务
            List<ServiceInstance> localList = allNodeList.stream().filter(x -> OsUtil.isLocalIP(x.getHost())).collect(Collectors.toList());
            log.debug("The actionPoint={} all instance size={}, local instance size= {}", actionPoint, allNodeList.size(), localList.size());

            if (!localList.isEmpty()) {
                allNodeList = localList;
            }
        }
        ServiceInstance serviceInstance = (allNodeList.isEmpty()) ? null :
                ((allNodeList.size() == 1) ? allNodeList.get(0) : (allNodeList.get(new Random().nextInt(allNodeList.size()))));

        log.debug("ServiceInstance={}", serviceInstance);
        return serviceInstance;
    }

    /**
     * 通过 服务坐标 获取所有 在线服务实例列表
     *
     * @param actionPoint skynet中的服务坐标 格式如：app@plugin
     * @return
     */
    public List<ServiceInstance> getInstancesByActionPoint(String actionPoint) {
        Assert.hasText(actionPoint, "The actionPoint is blank.");
        String[] items = actionPoint.trim().split("@");
        Assert.isTrue(items.length == 2, "The ActionPoint format is invalid. actionPoint=" + actionPoint);
        List<ServiceInstance> instances = getInstances(items[0].trim(), SkynetConsts.DISCOVER_METADATA_SKYNET_PLUGIN_CODE_KEY, items[1].trim());
        log.debug("The actionPoint={},instances size= {}", actionPoint, instances.size());
        return instances;
    }

    public DiscoveryClient getClient() {
        return this.discoveryClient;
    }
}
