package skynet.boot.discovery.support;

import org.springframework.cloud.client.ServiceInstance;

import java.util.Map;

/**
 * 服务发现 选择器
 *
 * <AUTHOR>
 */
public interface SkynetServiceChooser {

    String getScheme();

    /**
     * 选择服务实例
     *
     * @param serviceId 服务Id
     * @param context   服务发现上下文，如，存放TLB tag 筛选表达式（KEY:TLB_SERVICE_TAG_SELECTOR）或个性化业务BizId（KEY:TLB_SERVICE_BIZ_ID）
     * @return 服务实例信息
     */
    ServiceInstance choose(String serviceId, Map<String, String> context);


    /**
     * 获取 服务发现选择器的 状态，目的 在异常情况下，排查问题
     *
     * @return 选择器状态
     */
    default Object getStatus() {
        return null;
    }
}
