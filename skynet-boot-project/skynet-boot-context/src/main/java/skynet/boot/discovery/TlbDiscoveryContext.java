package skynet.boot.discovery;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;


/**
 * TLB服务发现 上下文
 */
@Getter
@Setter
@Accessors(chain = true)
public class TlbDiscoveryContext extends Jsonable {
    /**
     * 根据服务标签，选择服务，
     * <p>
     * tag 表达式  如： A&&B&&C   A||B||C  !(A&&B&&C)
     */
    private String tagSelector;
    /**
     * 根据BizId 获取服务
     */
    private String bizId;
    /**
     * 获取topN个服务
     */
    private int topN = 1;

    /**
     * 通过BizId获取服务时，是否禁用兜底，默认是false。（默认是兜底的）
     */
    private boolean disabledSelectServerDefault = false;


    public TlbDiscoveryContext(int topN) {
        this.topN = topN;
    }

    public TlbDiscoveryContext(String tagSelector, String bizId, int topN) {
        this.tagSelector = tagSelector;
        this.bizId = bizId;
        this.topN = topN;
    }
}