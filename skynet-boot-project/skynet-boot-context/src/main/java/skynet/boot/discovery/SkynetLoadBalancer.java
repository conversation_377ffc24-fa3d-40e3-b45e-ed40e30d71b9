package skynet.boot.discovery;

import java.net.URI;
import java.util.Map;
import java.util.Optional;

/**
 * Skynet LoadBalancer
 * 根据URL 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
 * 支持协议： lb，tlb，多ip
 * <p>
 * 示例：
 * <p>
 * 输入：
 * - http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
 * - ws://tlb:ocr-service/api/v3/talk
 * 返回：
 * - http://127.0.0.1:9098/api/v3/func
 * - ws://{ip}:{port}/api/v3/talk
 *
 * <AUTHOR>
 */
public interface SkynetLoadBalancer {

    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     * 此方法解释了为什么需要根据URI获取实际的服务地址，通过服务发现机制，将虚拟的服务地址
     * 替换为实际在线的服务节点地址和端口。
     *
     * @param uri 传入的服务URI，可以是HTTP、WebSocket或gRPC协议的地址。
     *            示例包括：
     *            - http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *            - ws://tlb:ocr-service/api/v3/talk
     * @return 返回替换后的URI，例如：
     * - http://127.0.0.1:9098/api/v3/func
     * - ws://{ip}:{port}/api/v3/talk
     */
    Optional<URI> choose(String uri);

    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     *
     * @param uri     http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *                ws://tlb:ocr-service/api/v3/talk
     *                grpc://lb:ocr-service/api/v3/talk
     * @param context 服务发现上下文，如，存放LB tag 筛选表达式（KEY:TLB_SERVICE_TAG_SELECTOR）或个性化业务BizId（KEY:TLB_SERVICE_BIZ_ID）
     * @return 替换后的URI eg: http://127.0.0.1:9098/api/v3/func
     * *            ws://{ip}:{port}/api/v3/talk
     * *            grpc://{ip}:{port}/api/v3/talk
     */
    Optional<URI> choose(String uri, Map<String, String> context);

    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     *
     * @param uri http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *            ws://tlb:ocr-service/api/v3/talk
     * @return 实际 {ip}:{port} eg: 127.0.0.1:9098
     */
    Optional<String> chooseEndpoint(String uri);

    /**
     * 根据URI 获取 实际的目标服务地址（通过服务发现用在线的IP:PORT替换）
     *
     * @param uri     http://127.0.0.1:9098/api/v3/func,http://127.0.0.1:9098/api/v3/func
     *                ws://tlb:ocr-service/api/v3/talk
     * @param context 服务发现上下文，如，存放LB tag 筛选表达式（KEY:TLB_SERVICE_TAG_SELECTOR）或个性化业务BizId（KEY:TLB_SERVICE_BIZ_ID）
     * @return 实际 {ip}:{port} eg: 127.0.0.1:9098
     */
    Optional<String> chooseEndpoint(String uri, Map<String, String> context);
}
