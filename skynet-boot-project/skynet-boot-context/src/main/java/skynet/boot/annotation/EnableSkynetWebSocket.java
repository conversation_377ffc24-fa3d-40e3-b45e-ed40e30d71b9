package skynet.boot.annotation;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import skynet.boot.websocket.WebSocketConfig;

import java.lang.annotation.*;

/**
 * * 启用 Skynet WebSocket 配置
 *
 *
 * <pre>
 *
 *     需要添加 POM依赖：
 *
 *     &lt;dependency&gt;
 *           &lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;
 *           &lt;artifactId&gt;spring-boot-starter-websocket&lt;/artifactId&gt;
 *      &lt;/dependency&gt;
 *
 * </pre>
 *
 * <AUTHOR> [2019年6月15日 上午8:10:49]
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Inherited
@EnableWebSocket
@Import(WebSocketConfig.class)
@ComponentScan(basePackages = {"skynet.boot.websocket"})
public @interface EnableSkynetWebSocket {

}
