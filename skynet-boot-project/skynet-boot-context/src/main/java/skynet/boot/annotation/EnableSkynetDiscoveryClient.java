package skynet.boot.annotation;

import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

import java.lang.annotation.*;

/**
 * 启用 图聆SkynetDiscovery 客户端 功能
 * <p>
 * 主要是 对 DiscoveryClient 进行了一个二次封装
 * <p>
 * 主要特性：可以通过skynet服务托管的 服务坐标[eg:action@plugin]获取 服务实例, 或本机优先等
 *
 * </pre>
 *
 * <AUTHOR> [2018年9月27日 上午9:17:37]
 */
@EnableDiscoveryClient
@EnableSkynetTlbClient
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
public @interface EnableSkynetDiscoveryClient {
}
