package skynet.boot.annotation;

import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import skynet.boot.mysql.MySqlConfiguration;

import java.lang.annotation.*;

/**
 * 启用 MySQL 功能
 *
 * <AUTHOR>
 */
@EnableSkynetException
@EnableSkynetWebConfig
@EnableJpaAuditing
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Inherited
@Import({MySqlConfiguration.class})
public @interface EnableSkynetMySql {
}
