package skynet.boot.annotation;

import org.springframework.context.annotation.Import;
import skynet.boot.config.JacksonConfiguration;
import skynet.boot.config.SkynetWebConfig;

import java.lang.annotation.*;

/**
 * * 启用 Skynet Web相关的配置，包括：
 * <p>
 * cors协议支持
 * 跨域过滤器
 *
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2019年6月15日 上午8:10:49]
 */
@EnableSkynetWebGzipDecompression
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Inherited
@Import({SkynetWebConfig.class, JacksonConfiguration.class})
public @interface EnableSkynetWebConfig {

}
