package skynet.boot.annotation;

import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

import java.lang.annotation.*;

/**
 * 启用 TlbDiscoveryClient  [spring.cloud.tlb.discovery.enabled]   功能
 *
 * </pre>
 *
 * <AUTHOR> [2018年9月27日 上午9:17:37]
 */
@EnableDiscoveryClient
@EnableSkynetTlbClient
@EnableSkynetTlbExtension
@EnableSkynetDiscoveryClient
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
public @interface EnableSkynetTlbDiscoveryClient {
}
