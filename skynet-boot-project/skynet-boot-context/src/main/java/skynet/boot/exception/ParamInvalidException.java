package skynet.boot.exception;

/**
 * 参数异常
 *
 * <AUTHOR>
 */
public class ParamInvalidException extends SkynetException {

    private static final long serialVersionUID = -930765468111L;
    private final String paramName;

    public ParamInvalidException(String paramName) {
        super(SkynetErrorCode.PARAM_INVALID);
        this.paramName = paramName;
    }

    public ParamInvalidException(String paramName, String message) {
        super(SkynetErrorCode.PARAM_INVALID.getCode(), String.format("%s:%s", SkynetErrorCode.PARAM_INVALID.getMessage(), message));
        this.paramName = paramName;
    }

    @Override
    public String getMessage() {
        return String.format("%s[%s]", super.getMessage(), paramName);
    }
}
