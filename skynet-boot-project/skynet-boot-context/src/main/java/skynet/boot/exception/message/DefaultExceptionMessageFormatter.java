package skynet.boot.exception.message;

import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.exception.SkynetException;

/**
 * 默认异常转换器
 *
 * <AUTHOR>
 */
public class DefaultExceptionMessageFormatter implements ExceptionMessageFormatter {

    /**
     * 转换通用异常
     */
    @Override
    public ErrorMessage format(Throwable e, int code) {
        if (e instanceof MethodArgumentNotValidException) {
            return new SkynetErrorMessage(getCurrentTraceId(), SkynetErrorCode.PARAM_INVALID.getCode(), buildMethodArgumentNotValidExceptionMessage((MethodArgumentNotValidException) e));
        } else {
            return new SkynetErrorMessage(getCurrentTraceId(), code, e.getMessage());
        }
    }

    private String buildMethodArgumentNotValidExceptionMessage(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder sb = new StringBuilder("Parameter Validation Failed：");
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            sb.append("【")
                    .append(fieldError.getField())
                    .append("】")
                    .append(fieldError.getDefaultMessage())
                    .append("；");
        }
        return sb.toString();
    }

    /**
     * 转换 Skynet 异常
     */
    @Override
    public ErrorMessage format(SkynetException e) {
        return new SkynetErrorMessage(getCurrentTraceId(), e.getCode(), e.getMessage());
    }
}
