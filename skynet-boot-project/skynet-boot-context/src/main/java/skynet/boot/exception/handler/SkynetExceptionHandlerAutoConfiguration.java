package skynet.boot.exception.handler;

import feign.FeignException;
import feign.RetryableException;
import jakarta.validation.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.Ordered;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import skynet.boot.annotation.EnableSkynetException;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.exception.config.SkynetExceptionAutoConfiguration;
import skynet.boot.exception.message.DefaultExceptionMessageFormatter;
import skynet.boot.exception.message.ExceptionMessageFormatter;

/**
 * 异常配置类，当项目中存在自定义的 RestControllerAdvice 时，使用自定义的；
 * 否则使用默认的 GlobalExceptionAdvice 处理类。
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetException.class)
@AutoConfigureBefore(SkynetExceptionAutoConfiguration.class)
@Import(SkynetExceptionHandler4ServletException.class)
public class SkynetExceptionHandlerAutoConfiguration implements Ordered {

    @Bean
    @ConditionalOnMissingBean(ExceptionMessageFormatter.class)
    public ExceptionMessageFormatter defaultExceptionMessageFormatter() {
        log.info("Build DefaultExceptionMessageFormatter.");
        return new DefaultExceptionMessageFormatter();
    }

    @Bean
    public SkynetExceptionHandler<IllegalArgumentException> illegalArgumentExceptionExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () ->
                new ExceptionDescriptor.ExceptionInfo(SkynetErrorCode.PARAM_INVALID)) {
        };
    }

    @ConditionalOnClass({NoResourceFoundException.class})
    static class NoResourceFoundExceptionHandlerConfiguration {
        @Bean
        public SkynetExceptionHandler<NoResourceFoundException> noResourceFoundExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<>(formatter, () ->
                    new ExceptionDescriptor.ExceptionInfo(404, "NoResourceFoundException")) {
            };
        }
    }

    @ConditionalOnClass({MethodArgumentNotValidException.class})
    static
    class MethodArgumentNotValidExceptionHandlerConfiguration {
        @Bean
        public SkynetExceptionHandler<MethodArgumentNotValidException> methodArgumentNotValidExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<>(formatter, () ->
                    new ExceptionDescriptor.ExceptionInfo(-1010, "Parameter Validation Exception")) {
            };
        }
    }

    @ConditionalOnClass({ValidationException.class})
    static
    class ValidationExceptionHandlerConfiguration {

        @Bean
        public SkynetExceptionHandler<ValidationException> validationExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<ValidationException>(formatter, () ->
                    new ExceptionDescriptor.ExceptionInfo(-1020, "Parameter Exception")) {
            };
        }
    }

    @ConditionalOnClass({ClientAbortException.class})
    static
    class ClientAbortExceptionHandlerConfiguration {

        @Bean
        public SkynetExceptionHandler<ClientAbortException> clientAbortExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<ClientAbortException>(formatter, () ->
                    new ExceptionDescriptor.ExceptionInfo(-1030, "Client Request Interrupted Exception")) {
            };
        }
    }

    @ConditionalOnClass({RetryableException.class})
    static class RetryableExceptionHandlerConfiguration {
        @Bean
        public SkynetExceptionHandler<feign.RetryableException> retryableExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<feign.RetryableException>(formatter, () ->
                    new ExceptionDescriptor.ExceptionInfo(-1040, "Feign Request Interrupted Exception")) {
            };
        }
    }

    @ConditionalOnClass({FeignException.class})
    static class FeignExceptionHandlerConfiguration {
        @Bean
        public SkynetExceptionHandler<FeignException> feignExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<FeignException>(formatter, () ->
                    new ExceptionDescriptor.ExceptionInfo(-1041, "Feign Request Exception")) {
            };
        }
    }
    
    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE;
    }
}
