package skynet.boot.exception.handler;

import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import skynet.boot.annotation.EnableSkynetException;
import skynet.boot.exception.config.SkynetExceptionAutoConfiguration;
import skynet.boot.exception.message.ExceptionMessageFormatter;

/**
 * <AUTHOR>
 * @date 2022/6/27 20:19
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetException.class)
@AutoConfigureBefore(SkynetExceptionAutoConfiguration.class)
public class SkynetExceptionHandler4ServletException {


    @Bean
    public SkynetExceptionHandler<HttpRequestMethodNotSupportedException> httpRequestMethodNotSupportedExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.METHOD_NOT_ALLOWED)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<HttpMediaTypeNotSupportedException> httpMediaTypeNotSupportedExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.UNSUPPORTED_MEDIA_TYPE)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<HttpMediaTypeNotAcceptableException> httpMediaTypeNotAcceptableExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.NOT_ACCEPTABLE)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<MissingPathVariableException> missingPathVariableExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.INTERNAL_SERVER_ERROR)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<MissingServletRequestParameterException> missingServletRequestParameterExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.BAD_REQUEST)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<ServletRequestBindingException> servletRequestBindingExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.BAD_REQUEST)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<ConversionNotSupportedException> conversionNotSupportedExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.INTERNAL_SERVER_ERROR)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<TypeMismatchException> typeMismatchExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.BAD_REQUEST)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<HttpMessageNotReadableException> httpMessageNotReadableExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.BAD_REQUEST)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<HttpMessageNotWritableException> httpMessageNotWritableExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.INTERNAL_SERVER_ERROR)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<MethodArgumentNotValidException> methodArgumentNotValidExceptionHandler4ServletException(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.BAD_REQUEST)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<MissingServletRequestPartException> missingServletRequestPartExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.BAD_REQUEST)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<BindException> bindExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.BAD_REQUEST)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<NoHandlerFoundException> noHandlerFoundExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.NOT_FOUND)) {
        };
    }

    @Bean
    public SkynetExceptionHandler<AsyncRequestTimeoutException> asyncRequestTimeoutExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.SERVICE_UNAVAILABLE)) {
        };
    }
}
