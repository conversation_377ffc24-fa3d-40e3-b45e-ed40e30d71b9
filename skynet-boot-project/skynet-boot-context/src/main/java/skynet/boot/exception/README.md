## skynet-framework统一异常处理机制

### 1. 快速开始

maven parent引入 skynet-boot-starter-parent

```xml
<parent>
    <groupId>com.iflytek.skynet</groupId>
    <artifactId>skynet-boot-starter-parent</artifactId>
    <version>4.0.14</version>
    <relativePath/>
</parent>
```

并在项目启动类上添加 `@EnableSkynetException` 注解，即可开启skynet-framework统一异常处理机制。

```java
@RestController
@EnableSkynetException
@SpringBootApplication
public class Application {

    public static void main(String[] args) {
        AppUtils.run(Application.class, args);
    }
    
    @GetMapping("/usual")
    public Object usual() throws Exception {
        throw new UsualException();
    }

    @GetMapping("/extend")
    public Object extend() throws Exception {
        throw new ExtendException();
    }
}
```

*注意：请不要自定义 `@RestControllerAdvice`，否则将使用您自定义的异常处理，而使得 skynet-framework统一异常处理机制失效。*

假如有一个自定义的异常 UsualException, 建议继承 `SkynetException`

```java
public class UsualException extends SkynetException {
    public UsualException() {
        super(3045, "this is a usual exception");
    }
}
```

我们请求 `/usual `时，controller抛出了UsualException()，skynet-framework统一异常处理机制即会捕获这个异常并返回response。

```json
{
  "header": {
    "traceId": null,
    "code": 3045,
    "message": "this is a usual exception"
  }
}
```

### 2. 自定义异常处理器

默认情况下，异常的错误码返回都是-1，如何自定义异常的错误码呢？可以通过自定义一个异常处理器来解决。

以`ExtendException`为例

```java
public class ExtendException extends Exception {
    public ExtendException() {
        super("this is a extend exception");
    }
}
```

我们为其定义一个异常处理器，ExtendExceptionHandler。注意**异常处理器需是抽象类SkynetExceptionHandler的子类**。并通过构造方法为父类
`SkynetExceptionHandler<E>`传入一个`ExtendExceptionDescriptor`对象。

```java
@Component
public class ExtendExceptionHandler extends SkynetExceptionHandler<ExtendException> {

    public ExtendExceptionHandler(ExceptionMessageFormatter formatter) {
        super(formatter, new ExtendExceptionDescriptor());
    }

    /**
     * 自定义定义异常码、异常名称。 异常名称一般在error日志中打印，不在返回信息中体现
     */
    static class ExtendExceptionDescriptor implements ExceptionDescriptor {
        @Override
        public ExceptionInfo getInfo() {
            return new ExceptionInfo(-888, "扩展的异常");
        }
    }
}
```

然后请求 `/extend `时，controller抛出了ExtendException()，返回的错误信息如下，返回的错误码即为

```json
{
  "header": {
    "traceId": null,
    "code": -888,
    "message": "this is a extend exception"
  }
}
```

日志中异常信息。

```text
2022-07-04 11:41:30.542 ERROR 21196 --- [nio-8080-exec-1] s.b.e.handler.SkynetExceptionHandler     : 扩展使用的异常: code=-888; url=/extend; this is a extend exception
```

### 3. 自定义异常返回格式

也可以通过自定义 `ExceptionMessageFormatter` 实现类，来实现自定义格式的返回 response。需要注意的是，自定义的
`ExceptionMessageFormatter` 是对整个应用生效的，所有的异常信息都会以自定义的格式返回。

仍以ExtendException为例，*

*

自定义一个消息格式化器TimestampExceptionMessageFormatter，实现ExceptionMessageFormatter接口；并定义一个消息对象TimestampErrorMessage，实现ErrorMessage接口
**。

先看下 `ExceptionMessageFormatter` 接口：

```java
/**
 * 异常信息转换 接口规范
 */
public interface ExceptionMessageFormatter {
    String CURRENT_TRACE_ID_KEY = "SKYNET_CURRENT_TRACE_ID";
    /** 转换通用异常 */
    ErrorMessage format(Throwable e, int code);

    /** 转换 SkynetException 异常 */
    ErrorMessage format(SkynetException e);

    /** 缺省 获取当前 TraceId  */
    default String getCurrentTraceId() {
        return MDC.get(CURRENT_TRACE_ID_KEY);
    }
}
```

自定义异常消息格式化器 `TimestampExceptionMessageFormatter`

```java
@Component
public class TimestampExceptionMessageFormatter implements ExceptionMessageFormatter {
    @Override
    public ErrorMessage format(Throwable e, int code) {
        // 返回自定义的消息对象
        return new TimestampErrorMessage(getCurrentTraceId(), code, e.getMessage());
    }

    @Override
    public ErrorMessage format(SkynetException e) {
        return format(e, e.getCode());
    }
}
```

```java
public class TimestampErrorMessage implements ErrorMessage {
    private final String traceId;
    private final Integer code;
    private final String message;
    private final long timestamp;// 在返回消息中增加时间戳字段

    public TimestampErrorMessage(String traceId, Integer code, String message) {
        this.traceId = traceId;
        this.code = code;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }
    @Override
    public String getTraceId() {
        return traceId;
    }
    @Override
    public int getCode() {
        return code;
    }
    @Override
    public String getMessage() {
        return message;
    }
    public long getTimestamp() {
        return timestamp;
    }
}
```

此时请求 `/extend `时，controller抛出了ExtendException()，返回的错误信息如下

```json
{
  "traceId": null,
  "code": -888,
  "message": "this is a extend exception",
  "timestamp": 1656905418321
}
```

### 4.目前已经内置的 异常处理器：

- MethodArgumentNotValidException
- ValidationException
- ClientAbortException
- feign.RetryableException
- SkynetException
- Exception

```java

@Bean
public SkynetExceptionHandler<MethodArgumentNotValidException> MethodArgumentNotValidExceptionHandler(ExceptionMessageFormatter formatter) {
    return new SkynetExceptionHandler<MethodArgumentNotValidException>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-1010, "参数校验异常")) {
    };
}

@Bean
public SkynetExceptionHandler<ValidationException> ValidationExceptionHandler(ExceptionMessageFormatter formatter) {
    return new SkynetExceptionHandler<ValidationException>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-1020, "参数异常")) {
    };
}

@Bean
public SkynetExceptionHandler<ClientAbortException> ClientAbortExceptionHandler(ExceptionMessageFormatter formatter) {
    return new SkynetExceptionHandler<ClientAbortException>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-1030, "客户端请求中断异常")) {
    };
}

public SkynetExceptionHandler<feign.RetryableException> RetryableExceptionHandler(ExceptionMessageFormatter formatter) {
    return new SkynetExceptionHandler<feign.RetryableException>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-1040, "Feign请求中断异常")) {
    };
}
```

### 5.异常处理器查看：

可以通过Springboot actuator 的 `skynet-exception-handler` 端点查看。
访问地址：[http://{ip:port}/actuator/skynet-exception-handler]
> 目标服务需要引入Actuator

处理器示例：

```json
{
    "skynet.sample.exception.extend.ExtendException": {
        "code": -888,
        "name": "扩展使用的异常"
    },
    "org.apache.catalina.connector.ClientAbortException": {
        "code": -1030,
        "name": "客户端请求中断异常"
    },
    "org.springframework.web.bind.MethodArgumentNotValidException": {
        "code": -1010,
        "name": "参数校验异常"
    },
    "skynet.boot.exception.SkynetException": {
        "code": -1,
        "name": "系统异常"
    },
    "java.lang.Exception": {
        "code": -1,
        "name": "系统异常"
    }
}
```

### 6.异常指标：

发生异常时，skynet异常处理模块也增加了 `skynet.metric.exception` prometheus 指标,
tag只包含了 异常编码：code；

Promethus 指标示例：

```ini
# HELP skynet_metric_exception_total  
# TYPE skynet_metric_exception_total counter
skynet_metric_exception_total{code="-1",} 10.0
skynet_metric_exception_total{code="-888",} 12.0
```

### 7.异常处理高级定制

如果发现 skynet全局异常处理不满足项目的需求，可以自定义覆盖。

#### 自定义MessageFormatter

```java
// 建议继承 DefaultExceptionMessageFormatter
@Component
public class SkyboxExceptionMessageFormatter extends DefaultExceptionMessageFormatter {

    @Override
    public ErrorMessage format(Throwable e, int code) {
        return new JSONResultErrorMessage(code, e.getMessage());
    }

    static class JSONResultErrorMessage implements ErrorMessage {
        private String msg;
        private int code;

        public JSONResultErrorMessage(int code, String msg) {
            this.msg = msg;
            this.code = code;
        }

        @Override
        public String getTraceId() {
            return null;
        }

        @Override
        public int getCode() {
            return code;
        }

        @Override
        public String getMessage() {
            return getMsg();
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public void setCode(int code) {
            this.code = code;
        }
    }
}
```

#### 自定义Advice

```java
/** 统一异常处理 <AUTHOR> @date 2022/7/4 1:40 PM  **/
@Slf4j
@RestControllerAdvice
public class SkyboxExceptionAdvice extends SkynetExceptionAdvice {
    public SkyboxExceptionAdvice(SkynetExceptionHandlerManager exceptionHandlerManager) {
        super(exceptionHandlerManager);
    }
    /** * 系统异常 返回 采用 500错误码  */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrorMessage handlerException(Exception e) throws IOException {
        e.printStackTrace();
        if(log.isDebugEnabled()) {
            log.debug("skybox system exception error msg {}", e.getMessage());
        }
        return super.handlerException(e);
    }

    /**  系统未登陆 返回 采用 401 错误码 */
    @ExceptionHandler(SkyboxExceptionUnauthorized.class)
    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    public ErrorMessage handlerException(SkyboxExceptionUnauthorized e) throws IOException {
        e.printStackTrace();
        if(log.isDebugEnabled()) {
            log.debug("skybox unauthorized exception error msg {}", e.getMessage());
        }
        return super.handlerException(e);
    }
}
```

#### AutoConfiguration系统配置：

```java
@EnableSkynetException
@Configuration(proxyBeanMethods = false)
// 注意：After(SkynetExceptionAutoConfiguration.class
@AutoConfigureAfter(SkynetExceptionAutoConfiguration.class)
public class BoxStoreAutoConfiguration {

    @Bean
    @ConfigurationProperties("skybox")
    public BoxStoreProperties boxProperties() {
        return new BoxStoreProperties();
    }

    @Bean
    public SkynetExceptionHandler<HttpRequestMethodNotSupportedException> skynetExceptionHandler(ExceptionMessageFormatter formatter) {
        return new SkynetExceptionHandler<HttpRequestMethodNotSupportedException>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(HttpStatus.NOT_FOUND.value(), SKyboxExceptionEnum.NOT_FOUND.getMsg())) {
        };
    }

    //覆盖 skynet-boot的 全局 ExceptionAdvice
    @Bean
    public SkyboxExceptionAdvice skyboxExceptionHandler(SkynetExceptionHandlerManager handlerManager) {
        return new SkyboxExceptionAdvice(handlerManager);
    }
}

```
