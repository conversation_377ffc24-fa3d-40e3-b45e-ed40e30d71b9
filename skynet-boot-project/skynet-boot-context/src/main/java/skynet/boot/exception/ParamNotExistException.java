package skynet.boot.exception;

/**
 * 参数不存在异常
 *
 * <AUTHOR>
 */
public class ParamNotExistException extends SkynetException {

    private static final long serialVersionUID = -930765411185L;
    private final String paramName;

    public ParamNotExistException(String paramName) {
        super(SkynetErrorCode.PARAM_NOT_EXIST);
        this.paramName = paramName;
    }

    @Override
    public String getMessage() {
        return String.format("%s[%s]", SkynetErrorCode.PARAM_NOT_EXIST.getMessage(), paramName);
    }
}
