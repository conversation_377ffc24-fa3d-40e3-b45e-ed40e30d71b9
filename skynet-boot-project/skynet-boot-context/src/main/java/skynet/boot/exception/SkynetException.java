package skynet.boot.exception;

/**
 * SkynetException 异常
 *
 * <AUTHOR>
 */
public class SkynetException extends RuntimeException {

    private static final long serialVersionUID = -9304876546811185L;
    private final int code;

    public SkynetException(SkynetErrorCode errorCode) {
        this(errorCode.getCode(), errorCode.getMessage());
    }

    public SkynetException(int code, String message, Object... args) {
        this(code, String.format(message, args));
    }

    public SkynetException(int code, String message) {
        super(message);
        this.code = code;
    }

    public SkynetException(int code, String message, Throwable e) {
        super(message, e);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
