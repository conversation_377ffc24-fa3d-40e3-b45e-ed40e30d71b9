package skynet.boot.exception.message;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;

/**
 * 默认异常错误消息体
 *
 * <AUTHOR>
 */
@Getter
public class SkynetErrorMessage implements ErrorMessage {


    private final JSONObject header;

    public SkynetErrorMessage(String traceId, int code, String message) {
        this.header = new JSONObject()
                .fluentPut("traceId", traceId)
                .fluentPut("code", code)
                .fluentPut("message", message);
    }

    public SkynetErrorMessage(String traceId, int code, String message, String path) {
        this.header = new JSONObject()
                .fluentPut("traceId", traceId)
                .fluentPut("code", code)
                .fluentPut("message", message)
                .fluentPut("path", path);
    }

    /**
     * 返回 TraceId
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Override
    public String getTraceId() {
        return header.getString("traceId");
    }

    /**
     * 返回错误码
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Override
    public int getCode() {
        return header.getIntValue("code");
    }

    /**
     * 返回错误消息
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Override
    public String getMessage() {
        return header.getString("message");
    }


    /**
     * 返回错误消息
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Override
    public String getPath() {
        return header.getString("path");
    }
}
