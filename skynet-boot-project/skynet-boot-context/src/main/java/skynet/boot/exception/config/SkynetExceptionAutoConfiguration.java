package skynet.boot.exception.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import skynet.boot.annotation.EnableSkynetException;
import skynet.boot.annotation.EnableSkynetMetrics;
import skynet.boot.exception.handler.*;
import skynet.boot.exception.message.ExceptionMessageFormatter;

import java.util.List;

/**
 * 异常配置类，当项目中存在自定义的 RestControllerAdvice 时，使用自定义的；
 * 否则使用默认的 GlobalExceptionAdvice 处理类。
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableSkynetMetrics
@ConditionalOnBean(annotation = EnableSkynetException.class)
@ConditionalOnProperty(value = "skynet.exception.enabled", matchIfMissing = true)
public class SkynetExceptionAutoConfiguration implements Ordered {
    @Bean
    @ConfigurationProperties(prefix = "skynet.exception")
    public SkynetExceptionProperties skynetExceptionProperties() {
        return new SkynetExceptionProperties();
    }

    @Bean
    public SkynetExceptionHandlerManager skynetExceptionHandlerManager(ExceptionMessageFormatter formatter,
                                                                       List<SkynetExceptionHandler<?>> handlerList) {
        log.info("Build SkynetExceptionHandlerManager.");
        return new SkynetExceptionHandlerManager(formatter, handlerList) {
        };
    }

    @Bean
    @ConditionalOnMissingBean(value = SkynetExceptionAdvice.class)
    public SkynetExceptionAdvice skynetExceptionAdvice(SkynetExceptionHandlerManager exceptionHandlerManager) {
        log.info("Build SkynetExceptionAdvice.");
        return new SkynetExceptionAdvice(exceptionHandlerManager) {
        };
    }

    @Bean
    @ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "servlet", matchIfMissing = true)
    public ExceptionHandlerFilter exceptionHandlerFilter(SkynetExceptionHandlerManager exceptionHandlerManager) {
        return new ExceptionHandlerFilter(exceptionHandlerManager);
    }

    @ConditionalOnClass({Endpoint.class})
    static class SkynetExceptionHandlerEndpointConfiguration {
        @Bean
        public SkynetExceptionHandlerEndpoint skynetExceptionHandlerEndpoint(SkynetExceptionHandlerManager manager) {
            return new SkynetExceptionHandlerEndpoint(manager);
        }
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE;
    }
}
