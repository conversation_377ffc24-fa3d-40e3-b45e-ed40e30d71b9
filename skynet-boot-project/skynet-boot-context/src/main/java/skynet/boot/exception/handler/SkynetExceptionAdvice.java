package skynet.boot.exception.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import skynet.boot.exception.message.ErrorMessage;

import java.io.IOException;

/**
 * 全局异常处理，当出现异常时，构造 ApiResponse 返回格式。
 * 注意，为了让这个异常处理类可以被覆盖，需要定义成 abstract class，
 * 并在 ExceptionConfig 中定义 @Bean 时创建实例。
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public abstract class SkynetExceptionAdvice implements Ordered {

    private final SkynetExceptionHandlerManager exceptionHandlerManager;

    public SkynetExceptionAdvice(SkynetExceptionHandlerManager exceptionHandlerManager) {
        this.exceptionHandlerManager = exceptionHandlerManager;
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public ErrorMessage handlerException(Exception e) throws IOException {
        return exceptionHandlerManager.format(e);
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
