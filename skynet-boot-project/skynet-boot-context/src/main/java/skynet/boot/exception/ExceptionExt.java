package skynet.boot.exception;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 异常工具类
 *
 * <AUTHOR>
 * @date 2022/8/9 13:50
 */
public class ExceptionExt {
    /**
     * 获取异常堆栈的Message
     *
     * @param exp
     * @return
     */
    public static List<String> getMergedMessages(Throwable exp) {
        List<String> msgList = new ArrayList<>(1);
        if (exp != null) {
            fillMessage(msgList, exp);
        }
        return msgList;
    }

    /**
     * 合并所有的异常信息
     *
     * @param exp
     * @return
     */
    public static String mergedMessage(Throwable exp) {
        List<String> msgList = getMergedMessages(exp);
        return StringUtils.join(msgList, ";");
    }

    private static void fillMessage(List<String> msgList, Throwable exp) {
        if (exp == null) {
            return;
        }
        msgList.add(exp.getMessage());
        if (exp.getCause() == null) {
        } else {
            fillMessage(msgList, exp.getCause());
        }
    }
}
