package skynet.boot.exception;

/**
 * 重名异常
 *
 * <AUTHOR>
 */
public class DuplicateNameException extends SkynetException {

    private static final long serialVersionUID = -930765468113676L;
    private final String paramName;

    public DuplicateNameException(String paramName) {
        super(SkynetErrorCode.ENTITY_EXIST.getCode(), String.format(SkynetErrorCode.ENTITY_EXIST.getMessage(), paramName));
        this.paramName = paramName;
    }

    @Override
    public String getMessage() {
        return String.format(SkynetErrorCode.ENTITY_EXIST.getMessage(), paramName);
    }
}
