package skynet.boot.exception;

/**
 * Skynet 错误码
 */
public enum SkynetErrorCode {

    SUCCESS(0, "SUCCESS"),
    ERROR(-1, "Unknown Error"),
    TIME_OUT(-2, "Time Out"),

    /* Parameter */
    PARAM_ERROR(-100, "Parameter error, please check the configuration parameters【%s】"),
    PARAM_NOT_EXIST(-101, "Param Not Exist"),
    PARAM_INVALID(-102, "Param Invalid"),

    /* Entity */
    ENTITY_NOT_FOUND(-201, "Data Not Found"),
    ENTITY_EXIST(-202, "Entity Exist【%s】");

    private final int code;
    private final String message;

    SkynetErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
