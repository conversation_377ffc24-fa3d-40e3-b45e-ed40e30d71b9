package skynet.boot.exception.handler;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;
import skynet.boot.exception.SkynetErrorCode;

/**
 * 异常描述
 *
 * <AUTHOR>
 * @date 2022/6/27 22:01
 */
public interface ExceptionDescriptor {

    ExceptionInfo getInfo();


    @Getter
    @Setter
    class ExceptionInfo {

        /**
         * 异常码
         */
        private int code = -1;

        /**
         * 异常名称，如：数据库异常
         */
        private String name = "Unknown Exception";

        public ExceptionInfo() {

        }

        public ExceptionInfo(SkynetErrorCode skynetErrorCode) {
            this(skynetErrorCode.getCode(), skynetErrorCode.getMessage());
        }

        public ExceptionInfo(HttpStatus status) {
            this(status.value(), status.getReasonPhrase());
        }

        public ExceptionInfo(int code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
