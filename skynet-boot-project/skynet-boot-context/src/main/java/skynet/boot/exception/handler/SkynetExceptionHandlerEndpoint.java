package skynet.boot.exception.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Lazy;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Skynet 全局异常处理 Endpoint
 *
 * <AUTHOR> [2018年8月22日 下午2:57:14]
 */
@Slf4j
@Lazy
@Endpoint(id = "skynet-exception-handler")
public class SkynetExceptionHandlerEndpoint {

    private final SkynetExceptionHandlerManager skynetExceptionHandlerManager;

    public SkynetExceptionHandlerEndpoint(SkynetExceptionHandlerManager skynetExceptionHandlerManager) {
        this.skynetExceptionHandlerManager = skynetExceptionHandlerManager;
    }

    @ReadOperation
    public Object invoke() {
        log.debug("do get ServiceStatus...");
        Map<Class<?>, SkynetExceptionHandler<?>> handlerMap = skynetExceptionHandlerManager.getHandlerMap();
        Map<String, Object> map = new LinkedHashMap<>(handlerMap.size());
        handlerMap.forEach((k, v) -> map.put(k.getName(), v.getInfo()));
        return map;
    }
}