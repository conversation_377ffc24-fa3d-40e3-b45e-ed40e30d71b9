package skynet.boot.exception.handler;

import jakarta.servlet.FilterChain;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * RestControllerAdvice 注解只能处理 Controller 层的异常，无法处理 WebFilter 的异常，
 * 所以这里注册一个通用的 WebFilter 拦截所有请求，并将异常信息处理成统一格式。
 * <p>
 * 注意：Servlet Filter 规范并没有定义 order 属性，不能保证这个异常处理器被第一个处理。
 * 但是 filterName 是有序的，所以这个异常处理器名称定义成两个下划线开头，如果有其他 Filter
 * 的名称也是两个或三个下划线开头，可能会拦截不到。
 *
 * <AUTHOR>
 */
@Slf4j
@WebFilter(filterName = "__ExceptionHandlerFilter", urlPatterns = "/**/api/**")
public class ExceptionHandlerFilter extends OncePerRequestFilter {

    private final SkynetExceptionHandlerManager exceptionHandlerManager;

    public ExceptionHandlerFilter(SkynetExceptionHandlerManager exceptionHandlerManager) {
        this.exceptionHandlerManager = exceptionHandlerManager;
    }


    @Override
    public void doFilterInternal(@NotNull HttpServletRequest request,
                                 @NotNull HttpServletResponse response,
                                 @NotNull FilterChain filterChain) throws IOException {
        try {
            response.setCharacterEncoding("UTF-8");
            filterChain.doFilter(request, response);
        } catch (Throwable e) {
            exceptionHandlerManager.format(e, true);
        }
    }
}
