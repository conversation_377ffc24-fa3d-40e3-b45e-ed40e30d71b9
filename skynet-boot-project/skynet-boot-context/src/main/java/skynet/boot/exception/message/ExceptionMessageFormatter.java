package skynet.boot.exception.message;

import org.slf4j.MDC;
import skynet.boot.SkynetConsts;
import skynet.boot.exception.SkynetException;

/**
 * 异常信息转换 接口规范
 *
 * <AUTHOR>
 */
public interface ExceptionMessageFormatter {

    /**
     * 转换通用异常
     */
    ErrorMessage format(Throwable e, int code);

    /**
     * 转换 SkynetException 异常
     */
    ErrorMessage format(SkynetException e);

    /**
     * 获取当前 TraceId
     *
     * @return
     */
    default String getCurrentTraceId() {
        return MDC.get(SkynetConsts.MDC_CURRENT_TRACE_ID_KEY);
    }
}
