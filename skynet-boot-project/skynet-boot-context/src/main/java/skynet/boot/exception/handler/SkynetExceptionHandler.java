package skynet.boot.exception.handler;

import lombok.extern.slf4j.Slf4j;
import skynet.boot.exception.message.ErrorMessage;
import skynet.boot.exception.message.ExceptionMessageFormatter;

/**
 * <AUTHOR>
 * @date 2022/6/27 20:19
 */
@Slf4j
public abstract class SkynetExceptionHandler<E extends Throwable> {

    protected final ExceptionMessageFormatter formatter;
    private final ExceptionDescriptor exceptionDescriptor;

    protected SkynetExceptionHandler(ExceptionMessageFormatter formatter) {
        this.formatter = formatter;
        this.exceptionDescriptor = () -> new ExceptionDescriptor.ExceptionInfo(-1, "Skynet System Exception");
    }

    public SkynetExceptionHandler(ExceptionMessageFormatter formatter, ExceptionDescriptor exceptionDescriptor) {
        this.formatter = formatter;
        this.exceptionDescriptor = exceptionDescriptor;
    }

    public ErrorMessage apply(String url, E e) {
        ExceptionDescriptor.ExceptionInfo info = getInfo();
        showLog(url, info, e);
        return formatter.format(e, info.getCode());
    }

    protected void showLog(String url, ExceptionDescriptor.ExceptionInfo info, E e) {
        log.error("{}: code={}; url={}; {}", info.getName(), info.getCode(), url, e.getMessage());
    }

    public ExceptionDescriptor.ExceptionInfo getInfo() {
        return exceptionDescriptor.getInfo();
    }
}
