package skynet.boot.exception.message;

/**
 * 异常信息
 *
 * <AUTHOR>
 */
public interface ErrorMessage {

    /**
     * 获取TraceId
     *
     * @return
     */
    String getTraceId();

    /**
     * 返回错误码
     *
     * @return
     */
    int getCode();

    /**
     * 返回错误信息
     *
     * @return
     */
    String getMessage();

    /**
     * 路由信息
     *
     * @return
     */
    String getPath();
}
