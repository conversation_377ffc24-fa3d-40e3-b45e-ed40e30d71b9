package skynet.boot.exception;

/**
 * 超时异常
 *
 * <AUTHOR>
 */
public class TimeoutException extends SkynetException {

    private static final long serialVersionUID = -93076546811185L;
    private final long timeout;

    public TimeoutException(long timeout) {
        super(SkynetErrorCode.TIME_OUT);
        this.timeout = timeout;
    }

    public TimeoutException(long timeout, String message) {
        super(SkynetErrorCode.TIME_OUT.getCode(), String.format("%s:%s", SkynetErrorCode.TIME_OUT.getMessage(), message));
        this.timeout = timeout;
    }

    @Override
    public String getMessage() {
        return String.format("[timeout=%ss]%s", timeout, super.getMessage());
    }
}
