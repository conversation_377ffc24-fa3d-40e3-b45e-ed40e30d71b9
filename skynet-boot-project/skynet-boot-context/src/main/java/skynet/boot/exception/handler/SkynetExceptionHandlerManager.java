package skynet.boot.exception.handler;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ResolvableType;
import org.springframework.http.HttpStatus;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import skynet.boot.exception.SkynetException;
import skynet.boot.exception.message.ErrorMessage;
import skynet.boot.exception.message.ExceptionMessageFormatter;
import skynet.boot.metrics.SkynetMetricsService;
import skynet.boot.metrics.domain.MetricsLabel;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 异常处理器管理
 *
 * <AUTHOR>
 * @date 2022/6/28 11:36
 */
@Slf4j
public class SkynetExceptionHandlerManager {

    private final static String EXCEPTION_METRIC_NAME = "skynet.metric.exception";
    private final Map<Class<?>, SkynetExceptionHandler<?>> handlerMap;

    @Autowired(required = false)
    private SkynetMetricsService skynetMetricsService;

    public SkynetExceptionHandlerManager(ExceptionMessageFormatter formatter, List<SkynetExceptionHandler<?>> handlerList) {
        this.handlerMap = new LinkedHashMap<>(handlerList.size());

        for (SkynetExceptionHandler<?> handler : handlerList) {
            Object targetObject = AopProxyUtils.getSingletonTarget(handler);
            if (targetObject == null) {
                targetObject = handler;
            }
            ResolvableType t = ResolvableType.forInstance(targetObject);
            if (t.getSuperType().getGenerics().length == 1) {
                handlerMap.put(t.getSuperType().getGenerics()[0].resolve(), handler);
            } else {
                log.warn("The handler [{}] implementation does not conform to the specification", handler);
            }
        }
        SkynetExceptionHandler<?> skynetExceptionHandler = handlerMap.remove(SkynetException.class);
        if (skynetExceptionHandler == null) {
            skynetExceptionHandler = new SkynetExceptionHandler<SkynetException>(formatter) {
                @Override
                public ErrorMessage apply(String url, SkynetException e) {
                    ExceptionDescriptor.ExceptionInfo info = new ExceptionDescriptor.ExceptionInfo(e.getCode(), "Business Exception");
                    showLog(url, info, e);
                    return formatter.format(e, info.getCode());
                }
            };
        }
        handlerMap.put(SkynetException.class, skynetExceptionHandler);

        SkynetExceptionHandler<?> defaultHandler = handlerMap.remove(Exception.class);
        if (defaultHandler == null) {
            defaultHandler = new SkynetExceptionHandler<Exception>(formatter) {
                @Override
                protected void showLog(String url, ExceptionDescriptor.ExceptionInfo info, Exception e) {
                    log.error(String.format("%s: url=%s; %s", info.getName(), url, e.getMessage()), e);
                }
            };
        }
        handlerMap.put(Exception.class, defaultHandler);
    }

    public ErrorMessage format(Throwable e) throws IOException {
        return format(e, false);
    }

    public ErrorMessage format(Throwable e, boolean isFilterBefore) throws IOException {
        log.debug("format exception isFilterBefore={}, e={}", isFilterBefore, e.getMessage());
        ServletRequestAttributes servlet = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        SkynetExceptionHandler handler = handlerMap.get(Exception.class);
        //先按照 异常类型相同，再按照异常是否继承关系
        if (handlerMap.containsKey(e.getClass())) {
            handler = handlerMap.get(e.getClass());
        } else {
            for (Map.Entry<Class<?>, SkynetExceptionHandler<?>> item : handlerMap.entrySet()) {
                if (item.getKey().isAssignableFrom(e.getClass())) {
                    handler = item.getValue();
                    break;
                }
            }
        }
        assert servlet != null;
        ErrorMessage errorMessage = handler.apply(servlet.getRequest().getRequestURI(), e);
        if (skynetMetricsService != null) {
            skynetMetricsService.counterIncrement(EXCEPTION_METRIC_NAME, new MetricsLabel("code", String.valueOf(errorMessage.getCode())));
        }
        if (isFilterBefore) {
            assert servlet.getResponse() != null;
            servlet.getResponse().setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            servlet.getResponse().getWriter().write(JSON.toJSONString(errorMessage));
        }
        return errorMessage;
    }

    Map<Class<?>, SkynetExceptionHandler<?>> getHandlerMap() {
        return handlerMap;
    }
}