package skynet.boot.exception;

/**
 * 数据实体不存在异常
 *
 * <AUTHOR>
 */
public class EntityNotFoundException extends SkynetException {

    private static final long serialVersionUID = -93076546878477L;

    public EntityNotFoundException() {
        super(SkynetErrorCode.ENTITY_NOT_FOUND.getCode(), SkynetErrorCode.ENTITY_NOT_FOUND.getMessage());
    }

    @Override
    public String getMessage() {
        return SkynetErrorCode.ENTITY_NOT_FOUND.getMessage();
    }
}
