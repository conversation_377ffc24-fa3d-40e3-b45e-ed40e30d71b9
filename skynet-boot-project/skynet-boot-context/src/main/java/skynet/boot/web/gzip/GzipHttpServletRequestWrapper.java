package skynet.boot.web.gzip;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.zip.GZIPInputStream;

/**
 * HttpServletRequest wrapper for handling Gzip-encoded request bodies.
 * This class decompresses the Gzip input stream and provides it as a ServletInputStream.
 *
 * <AUTHOR> 2025年04月15日15:20:25
 * @since 4.3.3
 */
@Slf4j
public class GzipHttpServletRequestWrapper extends HttpServletRequestWrapper {
    private final ServletInputStream gzipInputStream;

    /**
     * Constructs a new GzipHttpServletRequestWrapper.
     *
     * @param request The original HttpServletRequest.
     * @throws IOException If an I/O error occurs while creating the GzipInputStream.
     */
    public GzipHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);

        log.debug("GzipHttpServletRequestWrapper");
        // Get the original input stream and wrap it in a GzipInputStream
        InputStream originalInputStream = request.getInputStream();
        GZIPInputStream gis = new GZIPInputStream(originalInputStream);

        // Create a ServletInputStream that reads from the GzipInputStream
        gzipInputStream = new ServletInputStream() {
            @Override
            public int read() throws IOException {
                return gis.read();
            }

            @Override
            public boolean isFinished() {
                try {
                    return gis.available() == 0;
                } catch (IOException e) {
                    return true;
                }
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener listener) {
                // No-op: ReadListener is not supported
            }
        };
    }

    /**
     * Returns the decompressed ServletInputStream.
     *
     * @return The ServletInputStream for the decompressed request body.
     */
    @Override
    public ServletInputStream getInputStream() {
        return gzipInputStream;
    }
}
