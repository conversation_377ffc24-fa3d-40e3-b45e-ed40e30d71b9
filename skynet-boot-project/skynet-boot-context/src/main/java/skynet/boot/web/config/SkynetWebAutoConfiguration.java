package skynet.boot.web.config;

import jakarta.servlet.DispatcherType;
import jakarta.servlet.Filter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import skynet.boot.annotation.EnableSkynetWebGzipDecompression;
import skynet.boot.web.gzip.GzipDecompressingFilter;

/**
 * Auto-configuration class for Skynet Web module.
 * This class configures beans and properties related to web functionalities,
 * such as Gzip decompression filter.
 *
 * <AUTHOR> 2025年04月15日15:20:25
 * @since 4.3.3
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
public class SkynetWebAutoConfiguration {

    /**
     * Creates and configures the SkynetWebProperties bean.
     *
     * @return SkynetWebProperties instance with configuration properties.
     */
    @Bean
    @ConfigurationProperties(prefix = "skynet.web")
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public SkynetWebProperties skynetWebProperties() {
        return new SkynetWebProperties();
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(Filter.class)
    public static class FilterBeanAutoConfiguration {

        /**
         * Registers the GzipDecompressingFilter if the corresponding property is enabled
         * and the EnableSkynetWebGzipDecompression annotation is present.
         *
         * @param skynetWebProperties Configuration properties for Skynet Web.
         * @return FilterRegistrationBean for GzipDecompressingFilter.
         */
        @Bean
        @ConditionalOnProperty(value = "skynet.web.gzip-decompression.enabled", matchIfMissing = true)
        @ConditionalOnBean(annotation = EnableSkynetWebGzipDecompression.class)
        public FilterRegistrationBean<GzipDecompressingFilter> skynetWebGzipFilter(SkynetWebProperties skynetWebProperties) {
            log.info("SkynetWebAutoConfiguration skynetWebGzipFilter.SkynetWebProperties={}", skynetWebProperties);
            FilterRegistrationBean<GzipDecompressingFilter> registrationBean = new FilterRegistrationBean<>();
            registrationBean.setFilter(new GzipDecompressingFilter());
            registrationBean.setDispatcherTypes(DispatcherType.REQUEST);

            // Add URL patterns for the filter from configuration properties
            skynetWebProperties.getGzipDecompression().getUrlPatterns().forEach(registrationBean::addUrlPatterns);

            // Set the order of the filter
            registrationBean.setOrder(skynetWebProperties.getGzipDecompression().getOrder());
            return registrationBean;
        }
    }
}
