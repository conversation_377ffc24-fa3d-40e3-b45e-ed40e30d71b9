package skynet.boot.web.gzip;


import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * Servlet filter for decompressing Gzip-encoded HTTP requests.
 * This filter wraps the request in a GzipHttpServletRequestWrapper if the
 * "Content-Encoding" header is set to "gzip".
 *
 * <AUTHOR> 2025年04月15日15:20:25
 * @since 4.3.3
 */
@Slf4j
public class GzipDecompressingFilter implements Filter {

    /**
     * Filters incoming requests and wraps them in a GzipHttpServletRequestWrapper
     * if they are Gzip-encoded.
     *
     * @param request  The incoming ServletRequest.
     * @param response The outgoing ServletResponse.
     * @param chain    The filter chain to pass the request and response to.
     * @throws IOException      If an I/O error occurs.
     * @throws ServletException If a servlet error occurs.
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        log.debug("GzipDecompressingFilter doFilter request.");

        // Check if the request is Gzip-encoded
        if (request instanceof HttpServletRequest &&
                "gzip".equalsIgnoreCase(((HttpServletRequest) request).getHeader("Content-Encoding"))) {
            log.debug("GzipDecompressingFilter doFilter request is gzip-encoded");

            // Wrap the request in a GzipHttpServletRequestWrapper
            GzipHttpServletRequestWrapper wrapper = new GzipHttpServletRequestWrapper((HttpServletRequest) request);
            chain.doFilter(wrapper, response);
        } else {
            // Pass the request and response as-is
            chain.doFilter(request, response);
        }
    }
}
