package skynet.boot.web.config;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.List;

/**
 * Configuration properties for Skynet Web module.
 * This class holds properties related to Gzip decompression and other web settings.
 *
 * <AUTHOR> 2025年04月15日15:20:25
 * @since 4.3.3
 */
@Getter
@Setter
public class SkynetWebProperties extends Jsonable {

    // Gzip decompression-related properties
    private GzipDecompression gzipDecompression = new GzipDecompression();

    /**
     * Nested class for Gzip decompression configuration.
     */
    @Getter
    @Setter
    public static class GzipDecompression extends Jsonable {
        // Whether Gzip decompression is enabled
        private boolean enabled = true;

        // URL patterns to which the Gzip decompression filter applies
        private List<String> urlPatterns = List.of("/*");

        // Order of the Gzip decompression filter
        private int order = -1;
    }
}
