package skynet.boot.web;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;

/**
 * Utility class for handling HTTP servlet operations, specifically for writing
 * files to HTTP responses.
 * This class provides methods for implementing file download functionality in
 * web applications.
 *
 * Key features:
 * - Handles file downloads with proper content type and disposition headers
 * - Supports UTF-8 encoding for file names
 * - Implements buffered streaming for efficient file transfer
 * - Compatible with various browsers through proper header encoding
 *
 * Usage example:
 * 
 * <pre>{@code
 * HttpServletRequest request = ...;
 * HttpServletResponse response = ...;
 * File file = new File("/path/to/file");
 *
 * HttpServletUtils httpServletUtils = new HttpServletUtils(request, response);
 * try {
 *     httpServletUtils.copyFileToResponse(file);
 * } catch (IOException e) {
 *     // Handle exception
 * }
 * }</pre>
 *
 * Important notes:
 * 1. Ensure the file exists and is readable before calling copyFileToResponse
 * 2. The method sets appropriate content type and disposition headers for file
 * download
 * 3. File names are UTF-8 encoded for cross-browser compatibility
 * 4. All streams are properly closed to prevent resource leaks
 *
 * Implementation example in a servlet or controller:
 * 
 * <pre>{@code
 * @GetMapping("/download")
 * public void downloadFile(HttpServletRequest request, HttpServletResponse response) {
 *     File file = new File("/path/to/file");
 *     HttpServletUtils httpServletUtils = new HttpServletUtils(request, response);
 *     httpServletUtils.copyFileToResponse(file);
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @see jakarta.servlet.http.HttpServletRequest
 * @see jakarta.servlet.http.HttpServletResponse
 * @see java.io.File
 * @see java.io.IOException
 */
public class HttpServletUtils {

    /**
     * Default buffer size for file transfer (4KB)
     */
    private static final int DEFAULT_BUFFER_SIZE = 4096;

    /**
     * Default character encoding
     */
    private static final String DEFAULT_ENCODING = "UTF-8";

    /**
     * The HTTP servlet request
     */
    private final HttpServletRequest request;

    /**
     * The HTTP servlet response
     */
    private final HttpServletResponse response;

    /**
     * Constructs a new HttpServletUtils instance
     *
     * @param request  The HTTP servlet request
     * @param response The HTTP servlet response
     */
    public HttpServletUtils(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
    }

    /**
     * Writes a file to the HTTP response for download
     * 
     * This method:
     * 1. Sets the appropriate content type based on the file
     * 2. Configures response headers for file download
     * 3. Streams the file content to the response
     *
     * @param file The file to be written to the response
     * @throws IOException              if an I/O error occurs during file transfer
     * @throws IllegalArgumentException if the file is null or does not exist
     */
    public void copyFileToResponse(File file) throws IOException {
        // Validate input
        if (file == null || !file.exists()) {
            throw new IllegalArgumentException("File must not be null and must exist");
        }

        // Set content type and encoding
        String contentType = request.getServletContext().getMimeType(file.getName());
        response.setContentType(contentType != null ? contentType : "application/octet-stream");
        response.setCharacterEncoding(DEFAULT_ENCODING);

        // Configure response headers for file download
        setFileDownloadHeaders(file);

        // Stream file content to response
        transferFileContent(file);
    }

    /**
     * Sets the Content-Disposition header for file download
     * Handles UTF-8 encoding of the filename for cross-browser compatibility
     *
     * @param file The file being downloaded
     * @throws UnsupportedEncodingException if UTF-8 encoding is not supported
     */
    private void setFileDownloadHeaders(File file) throws UnsupportedEncodingException {
        String encodedFileName = URLEncoder.encode(file.getName(), DEFAULT_ENCODING);
        String contentDisposition = String.format(
                "attachment; filename=%s; filename*=utf-8''%s",
                encodedFileName,
                encodedFileName);
        response.setHeader("Content-Disposition", contentDisposition);
    }

    /**
     * Transfers the file content to the response output stream
     * Uses buffered streams for efficient transfer
     *
     * @param file The file to transfer
     * @throws IOException if an I/O error occurs during transfer
     */
    private void transferFileContent(File file) throws IOException {
        try (OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = Files.newInputStream(file.toPath());
                BufferedInputStream bufferedInput = new BufferedInputStream(inputStream)) {

            byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
            int bytesRead;
            while ((bytesRead = bufferedInput.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
    }
}
