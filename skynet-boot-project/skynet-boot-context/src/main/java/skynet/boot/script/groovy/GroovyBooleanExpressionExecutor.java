package skynet.boot.script.groovy;

import skynet.boot.script.BooleanExpressionExecutor;

/**
 * 基于 Groovy 实现的布尔表达式执行器，执行结果为布尔类型
 *
 * <AUTHOR>
 */
public class GroovyBooleanExpressionExecutor implements BooleanExpressionExecutor {

    private final GroovyExpressionExecutor groovyExpressionExecutor;

    public GroovyBooleanExpressionExecutor() {
        this.groovyExpressionExecutor = new GroovyExpressionExecutor();
    }

    public GroovyBooleanExpressionExecutor(GroovyExpressionExecutor groovyExpressionExecutor) {
        this.groovyExpressionExecutor = groovyExpressionExecutor;
    }

    @Override
    public boolean eval(String expression) throws Exception {
        String result = String.valueOf(groovyExpressionExecutor.eval(expression));
        return Boolean.parseBoolean(result);
    }

    @Override
    public boolean eval(String expression, Object context) throws Exception {
        String result = String.valueOf(groovyExpressionExecutor.eval(expression, context));
        return Boolean.parseBoolean(result);
    }

    @Override
    public void close() throws Exception {
        groovyExpressionExecutor.close();
    }
}
