package skynet.boot.script.javascript;

import org.apache.commons.lang3.StringUtils;
import skynet.boot.script.ScriptExecutor;

import javax.script.*;


/**
 * 基于 JavaScript 实现的脚本执行器，执行结果为 Object 类型
 *
 * <AUTHOR>
 */
public class JavaScriptScriptExecutor implements ScriptExecutor {

    @Override
    public Object eval(String script, String method) throws Exception {
        return this.eval(script, method, new Object[0]);
    }

    @Override
    public Object eval(String script, String method, Object... args) throws Exception {
        ScriptEngine scriptEngine = new ScriptEngineManager().getEngineByName("graal.js");
        CompiledScript compiledScript = ((Compilable) scriptEngine).compile(script);
        Bindings bindings = scriptEngine.getBindings(ScriptContext.ENGINE_SCOPE);
        if (StringUtils.isBlank(method) && args != null && args.length > 0 && args[0] != null) {
            bindings.put("$", args[0]);
        }
        Object evalResult = compiledScript.eval(bindings);
        if (StringUtils.isBlank(method)) {
            return evalResult;
        }
        Invocable invocable = (Invocable) compiledScript.getEngine();
        return invocable.invokeFunction(method, args);
    }

    @Override
    public void close() throws Exception {

    }
}
