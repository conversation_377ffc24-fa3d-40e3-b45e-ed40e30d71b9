package skynet.boot.script;

import skynet.boot.exception.SkynetException;
import skynet.boot.script.groovy.GroovyBooleanExpressionExecutor;
import skynet.boot.script.groovy.GroovyExpressionExecutor;
import skynet.boot.script.groovy.GroovyScriptExecutor;
import skynet.boot.script.javascript.JavaScriptBooleanExpressionExecutor;
import skynet.boot.script.javascript.JavaScriptExpressionExecutor;
import skynet.boot.script.javascript.JavaScriptScriptExecutor;

/**
 * 构建脚本执行器，支持 groovy 和 javascript 两种语言
 *
 * <AUTHOR>
 */
public class CodeExecutorBuilder {

    private ScriptExecutor scriptExecutor = null;
    private ExpressionExecutor expressionExecutor = null;
    private BooleanExpressionExecutor booleanExpressionExecutor = null;

    public CodeExecutorBuilder(String lang) {
        if ("groovy".equals(lang)) {
            this.scriptExecutor = new GroovyScriptExecutor();
            this.expressionExecutor = new GroovyExpressionExecutor((GroovyScriptExecutor) scriptExecutor);
            this.booleanExpressionExecutor = new GroovyBooleanExpressionExecutor((GroovyExpressionExecutor) expressionExecutor);
        } else if ("javascript".equals(lang)) {
            this.scriptExecutor = new JavaScriptScriptExecutor();
            this.expressionExecutor = new JavaScriptExpressionExecutor((JavaScriptScriptExecutor) scriptExecutor);
            this.booleanExpressionExecutor = new JavaScriptBooleanExpressionExecutor((JavaScriptExpressionExecutor) expressionExecutor);
        } else {
            throw new SkynetException(-1, "Unknown code language");
        }
    }

    public CodeExecutor build() {
        return new CodeExecutor() {

            @Override
            public Object evalScript(String script, String method) throws Exception {
                return scriptExecutor.eval(script, method);
            }

            @Override
            public Object evalScript(String script, String method, Object... args) throws Exception {
                return scriptExecutor.eval(script, method, args);
            }

            @Override
            public Object evalExpression(String expression) throws Exception {
                return expressionExecutor.eval(expression);
            }

            @Override
            public Object evalExpression(String expression, Object context) throws Exception {
                return expressionExecutor.eval(expression, context);
            }

            @Override
            public boolean evalBooleanExpression(String expression) throws Exception {
                return booleanExpressionExecutor.eval(expression);
            }

            @Override
            public boolean evalBooleanExpression(String expression, Object context) throws Exception {
                return booleanExpressionExecutor.eval(expression, context);
            }

            @Override
            public void close() throws Exception {
                scriptExecutor.close();
                expressionExecutor.close();
                booleanExpressionExecutor.close();
            }

        };
    }
}
