package skynet.boot.script.javascript;

import lombok.extern.slf4j.Slf4j;
import skynet.boot.script.BooleanExpressionExecutor;

/**
 * 基于 JavaScript 实现的布尔表达式执行器，执行结果为布尔类型
 */
@Slf4j
public class JavaScriptBooleanExpressionExecutor implements BooleanExpressionExecutor {

    private final JavaScriptExpressionExecutor javaScriptExpressionExecutor;

    public JavaScriptBooleanExpressionExecutor() {
        this.javaScriptExpressionExecutor = new JavaScriptExpressionExecutor();
    }

    public JavaScriptBooleanExpressionExecutor(JavaScriptExpressionExecutor javaScriptExpressionExecutor) {
        this.javaScriptExpressionExecutor = javaScriptExpressionExecutor;
    }

    @Override
    public boolean eval(String expression) throws Exception {
        String result = String.valueOf(javaScriptExpressionExecutor.eval(expression));
        return Boolean.parseBoolean(result);
    }

    @Override
    public boolean eval(String expression, Object context) throws Exception {
        String result = String.valueOf(javaScriptExpressionExecutor.eval(expression, context));
        log.debug("context={}; expression={}; eval result={}", context, expression, result);
        return Boolean.parseBoolean(result);
    }

    @Override
    public void close() throws Exception {
        javaScriptExpressionExecutor.close();
    }
}
