package skynet.boot.script;

/**
 * 执行代码
 *
 * <AUTHOR>
 */
public interface CodeExecutor extends AutoCloseable {

    /**
     * 给定一个脚本和要执行的方法，给出执行结果。
     */
    Object evalScript(String script, String method) throws Exception;

    /**
     * 给定一个脚本和要执行的方法，传入参数得到执行结果。
     */
    Object evalScript(String script, String method, Object... args) throws Exception;

    /**
     * 给定一个表达式，给出执行结果。
     */
    Object evalExpression(String expression) throws Exception;

    /**
     * 给定一个表达式和上下文参数，给出执行结果。
     * 注意上下文参数在表达式中可以使用 $ 引用。
     */
    Object evalExpression(String expression, Object context) throws Exception;

    /**
     * 给定一个表达式，给出执行结果。
     */
    boolean evalBooleanExpression(String expression) throws Exception;

    /**
     * 给定一个表达式和上下文参数，给出执行结果。
     * 注意上下文参数在表达式中可以使用 $ 引用。
     */
    boolean evalBooleanExpression(String expression, Object context) throws Exception;
}
