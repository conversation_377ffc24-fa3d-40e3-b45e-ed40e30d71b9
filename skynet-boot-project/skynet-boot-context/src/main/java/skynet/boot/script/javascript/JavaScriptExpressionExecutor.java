package skynet.boot.script.javascript;

import skynet.boot.script.ExpressionExecutor;

/**
 * 基于 JavaScript 实现的表达式执行器，执行结果为 Object 类型
 */
public class JavaScriptExpressionExecutor implements ExpressionExecutor {

    private final JavaScriptScriptExecutor javaScriptScriptExecutor;

    public JavaScriptExpressionExecutor() {
        this.javaScriptScriptExecutor = new JavaScriptScriptExecutor();
    }

    public JavaScriptExpressionExecutor(JavaScriptScriptExecutor javaScriptScriptExecutor) {
        this.javaScriptScriptExecutor = javaScriptScriptExecutor;
    }

    @Override
    public Object eval(String expression) throws Exception {
        return javaScriptScriptExecutor.eval(expression, null);
    }

    @Override
    public Object eval(String expression, Object context) throws Exception {
        return javaScriptScriptExecutor.eval(expression, null, context);
    }

    @Override
    public void close() throws Exception {

    }
}
