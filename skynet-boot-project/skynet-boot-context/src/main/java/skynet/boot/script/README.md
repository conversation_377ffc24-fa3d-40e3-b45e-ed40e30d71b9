# 脚本执行

Skynet 封装了一些常见脚本的执行器，用于方便地执行脚本代码，如 JavaScript 和 Groovy 等。

## JavaScript

使用 JDK 自带的 `jakarta.script.ScriptEngine` 执行 JavaScript 脚本，提供了如下两个执行器：

* `JavaScriptExpressionExecutor` - 基于 JavaScript 实现的表达式执行器，执行结果为 Object 类型
* `JavaScriptBooleanExpressionExecutor` - 基于 JavaScript 实现的布尔表达式执行器，执行结果为布尔类型

使用方法如下：

```java
try (JavaScriptExpressionExecutor executor = new JavaScriptExpressionExecutor();) {

    // 不带上下文
    System.out.println(executor.eval("1+1"));

    // 带上下文
    JSONObject context = new JSONObject()
        .fluentPut("a", 1)
        .fluentPut("b", 2);
    System.out.println(executor.eval("$.a + $.b", context));
}
```

可以在执行表达式脚本的时候带上一个上下文参数，在表达式中通过 `$` 符号引用该参数。

## Groovy

使用 `org.apache.groovy` 提供的 `groovy-all` 工具库执行 Groovy 脚本，提供了如下两个执行器：

* `GroovyExpressionExecutor` - 基于 Groovy 实现的表达式执行器，执行结果为 Object 类型
* `GroovyBooleanExpressionExecutor` - 基于 Groovy 实现的布尔表达式执行器，执行结果为布尔类型

使用时需要在项目中添加 `groovy-all` 依赖：

```xml
<dependency>
    <groupId>org.apache.groovy</groupId>
    <artifactId>groovy-all</artifactId>
    <version>3.0.11</version>
    <type>pom</type>
</dependency>
```

使用方法和 JavaScript 一致：

```java
try (GroovyExpressionExecutor executor = new GroovyExpressionExecutor();) {

    // 不带上下文
    System.out.println(executor.eval("1+1"));

    // 带上下文
    JSONObject context = new JSONObject()
        .fluentPut("a", 1)
        .fluentPut("b", 2);
    System.out.println(executor.eval("$.a + $.b", context));
}
```

可以在执行表达式脚本的时候带上一个上下文参数，在表达式中通过 `$` 符号引用该参数。

## Spring 配置

脚本执行器内部在执行脚本时，会预先对代码进行编译，并使用 Guava 对编译结果进行缓存，为了有效利用缓存，提高执行效率，建议将执行器实例化为一个单例的
Spring Bean 来使用：

```java
@Bean
public ExpressionExecutor javaScriptExpressionExecutor() {
    return new JavaScriptExpressionExecutor()
}

@Bean
public BooleanExpressionExecutor javaScriptBooleanExpressionExecutor() {
    return new JavaScriptBooleanExpressionExecutor();
}
```

## 最佳实践

//CodeExecutor 内部已经做了缓存处理

```java
    CodeExecutorBuilder codeExecutorBuilder = new CodeExecutorBuilder("groovy");
    CodeExecutor codeExecutor = codeExecutorBuilder.build();
    codeExecutor.evalScript("{script}", "{method}", "{args}");
    codeExecutor.close();
```