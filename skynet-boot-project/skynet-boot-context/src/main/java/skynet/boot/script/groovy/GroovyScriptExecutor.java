package skynet.boot.script.groovy;

import com.alibaba.fastjson2.JSONFactory;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalListener;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyCodeSource;
import groovy.lang.GroovyObject;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.groovy.runtime.EncodingGroovyMethods;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.StringUtils;
import skynet.boot.script.ScriptExecutor;

import java.time.Duration;
import java.util.concurrent.Executors;

/**
 * 基于 Groovy 实现的脚本执行器，执行结果为 Object 类型
 *
 * <AUTHOR>
 */
@Slf4j
public class GroovyScriptExecutor implements ScriptExecutor {

    private final ListeningExecutorService executor = MoreExecutors.listeningDecorator(Executors.newSingleThreadExecutor());
    private final LoadingCache<String, GroovyObject> groovyObjectCache;

    private final GroovyClassLoader groovyClassLoader = new GroovyClassLoader();

    //默认5年刷新一次，也就是不刷新
    public final static Duration DEFAULT_REFRESH_TIMEOUT = Duration.ofDays(365 * 5);
    public final static int DEFAULT_CACHE_SIZE = 128 * 10000;

    public final static String DEFAULT_REFRESH_TIMEOUT_KEY = "skynet.boot.script.groovy.cache.size";
    public final static String DEFAULT_CACHE_SIZE_KEY = "skynet.boot.script.groovy.cache.timeout";

    public GroovyScriptExecutor() {
        //默认128W
        String size = System.getProperty(DEFAULT_REFRESH_TIMEOUT_KEY);
        int cacheSize = DEFAULT_CACHE_SIZE;
        if (StringUtils.hasText(size)) {
            try {
                cacheSize = Integer.parseInt(size);
            } catch (Exception e) {
                log.error("Parse {}={} error.", DEFAULT_REFRESH_TIMEOUT_KEY, size);
            }
        }

        Duration duration = DEFAULT_REFRESH_TIMEOUT;
        //eg: skynet.boot.script.groovy.cache.timeout=3h
        String timeout = System.getProperty(DEFAULT_CACHE_SIZE_KEY);
        if (StringUtils.hasText(timeout)) {
            try {
                duration = Duration.parse("PT" + timeout);
            } catch (Exception e) {
                log.error("Parse {}={} error.", DEFAULT_CACHE_SIZE_KEY, timeout);
            }
        }
        log.debug("cacheSize:{}; duration={}", cacheSize, duration);

        this.groovyObjectCache = CacheBuilder.newBuilder()
                .maximumSize(cacheSize)
                .concurrencyLevel(5)
                .recordStats()
                .refreshAfterWrite(duration)
                .removalListener((RemovalListener<String, GroovyObject>) notification -> {
                    log.debug("removed notification: {}", notification.getKey());
                    assert notification.getValue() != null;
                    JSONFactory.getDefaultObjectWriterProvider().cleanup(notification.getValue().getMetaClass().getClass().getClassLoader());
                    JSONFactory.getDefaultObjectReaderProvider().cleanup(notification.getValue().getMetaClass().getClass().getClassLoader());
                })
                .build(new CacheLoader<>() {

                    @NotNull
                    @Override
                    public GroovyObject load(@NotNull String key) throws Exception {
                        return convertScriptToGroovyObject(key);
                    }

                    @NotNull
                    @Override
                    public ListenableFuture<GroovyObject> reload(@NotNull String key, @NotNull GroovyObject oldValue) {
                        log.debug("reload key={}", key);
                        return executor.submit(() -> convertScriptToGroovyObject(key));
                    }
                });
    }

    /**
     * 给定一个脚本和要执行的方法，给出执行结果。
     */
    @Override
    public Object eval(String script, String method) throws Exception {
        log.debug("eval method={}", method);
        GroovyObject instance = groovyObjectCache.get(script);
        return instance.invokeMethod(method, null);
    }

    /**
     * 给定一个脚本和要执行的方法，传入参数得到执行结果。
     */
    @Override
    public Object eval(String script, String method, Object... args) throws Exception {
        GroovyObject instance = groovyObjectCache.get(script);
        return instance.invokeMethod(method, args);
    }

    @Override
    public void close() throws Exception {
        groovyClassLoader.close();
    }

    /**
     * 将脚本转换为 Groovy 对象，Groovy 脚本中一般包含一个或多个方法：
     * <p>
     * def sayHello(name) {
     * return 'Hello, ' + name
     * }
     */
    private GroovyObject convertScriptToGroovyObject(String script) throws Exception {
        String fileName = "Script_" + EncodingGroovyMethods.md5(script) + ".groovy";

        // 直接创建 GroovyCodeSource，无需 AccessController
        GroovyCodeSource gcs = new GroovyCodeSource(script, fileName, "/groovy/script");
        gcs.setCachable(true);

        Class<?> clazz = groovyClassLoader.parseClass(gcs);
        return (GroovyObject) clazz.newInstance();
    }
}
