package skynet.boot.script.groovy;

import skynet.boot.script.ExpressionExecutor;

/**
 * 基于 Groovy 实现的表达式执行器，执行结果为 Object 类型
 *
 * <AUTHOR>
 */
public class GroovyExpressionExecutor implements ExpressionExecutor {

    private final GroovyScriptExecutor groovyScriptExecutor;

    public GroovyExpressionExecutor() {
        this.groovyScriptExecutor = new GroovyScriptExecutor();
    }

    public GroovyExpressionExecutor(GroovyScriptExecutor groovyScriptExecutor) {
        this.groovyScriptExecutor = groovyScriptExecutor;
    }

    @Override
    public Object eval(String expression) throws Exception {
        String script = convertExpressionToScript(expression);
        return groovyScriptExecutor.eval(script, "eval");
    }

    @Override
    public Object eval(String expression, Object context) throws Exception {
        String script = convertExpressionToScript(expression);
        return groovyScriptExecutor.eval(script, "eval", context);
    }

    @Override
    public void close() throws Exception {
        groovyScriptExecutor.close();
    }

    /**
     * 将表达式转换为 Groovy 方法，表达式格式形如：
     * <p>
     * $.schoolId == '2300000001000000028'
     * <p>
     * 其中，$ 表示上下文参数。不过 Groovy 对象必须定义成一个方法才能调用，需要
     * 将表达式转换为方法形式：
     * <p>
     * def match(def _) {
     * return _.schoolId == '2300000001000000028'
     * }
     */
    private String convertExpressionToScript(String expression) {
        return String.format("def eval(def _) { return %s }",
                expression.replaceAll("\\$", "_"));
    }
}
