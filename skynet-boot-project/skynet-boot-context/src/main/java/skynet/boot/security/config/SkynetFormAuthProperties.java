package skynet.boot.security.config;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.util.StringUtils;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/5/28 20:34
 */
@Getter
@Setter
@Accessors(chain = true)
public class SkynetFormAuthProperties extends Jsonable implements SkynetAuthProperties {

    @Getter(AccessLevel.NONE)
    private final ServerProperties serverProperties;

    public static final String DEFAULT_LOGIN_PAGE = "/skynet/login";

    public SkynetFormAuthProperties(ServerProperties serverProperties) {
        this.serverProperties = serverProperties;
        this.pathPatterns.add("/**");
    }

    /**
     * 是否开启FormAuth验证
     */
    private boolean enabled = false;


    private List<String> ignorePatterns = new ArrayList<>();
    /**
     * 需要鉴权的 ant Pattern,默认所有的 /**
     */
    private List<String> pathPatterns = new ArrayList<>();

    /**
     * 密码重复验证错误，锁定周期
     */
    private int failLockDurationSecond = 3 * 60;
    /**
     * 密码验证错误 重试次数，默认5次
     */
    private int failTriesTimes = 5;

    /**
     * 代理contextPath，如nginx代理前缀
     */
    private String proxyContextPath = "";

    /**
     * 如果没有配置，才采用系统默认的 /skynet/login
     */
    private String loginPath = DEFAULT_LOGIN_PAGE;
    private String loginTitle = "Skynet Login Page";

    /**
     * 用户
     */
    private User user = new User();

    /**
     * @return
     */
    public String getFullLoginPage() {
        String url = StringUtils.hasText(loginPath) ? loginPath : DEFAULT_LOGIN_PAGE;
        //如果非 HTTP开头的，将拼接 proxyPath，contextPath
        return url.toUpperCase().startsWith("HTTP") ? url :
                String.format("%s%s%s", StringUtils.hasText(proxyContextPath) ? proxyContextPath.trim() : "",
                        StringUtils.hasText(serverProperties.getServlet().getContextPath()) ? serverProperties.getServlet().getContextPath().trim() : "", url);
    }

    /**
     * Jwt 属性配置
     */
    private String jwtCookiePath = "/";
    private String jwtCookieDomain;

    /**
     * 超时时间秒 默认是10分钟
     */
    private int jwtExpiresSecond = 10 * 60;

    /**
     * 默认： "SKYNET_JWT_TOKEN:" + UUID.randomUUID();
     */
    private String jwtTokenSessionKey = "SKYNET_JWT_TOKEN:" + UUID.randomUUID();


    //skynet.security.form-auth.default-login-enabled
    private boolean defaultLoginEnabled = true;


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class User extends Jsonable {

        /**
         * Default user name.
         */
        private String name = "user";

        /**
         * Password for the default user name.
         */
        private String password = "";
    }


    //    /**
//     * 加密器配置
//     */
//    private Pbkdf pbkdf = new Pbkdf();
//    @Getter
// @Setter
//    public static class Pbkdf {
//        private String secret = "skynet";
//        private int saltLength = 16;
//        private int iterations = 185000;
//        private int hashWidth = 256;
//    }
}
