## 安全组件（skynet-boot-starter-security）

主要功能AUTH组件：

- skynet.security.sign-auth API接口签名鉴权
- skynet.security.ip-auth API接口IP校验
- skynet.security.base-auth BaseAuth鉴权
- skynet.security.form-auth Form表单验证
- 鉴权服务访问客户端工具
- 鉴权异常处理
- FAQ

### 快速开始

#### POM依赖

```xml
<dependency>
  <groupId>com.iflytek.skynet</groupId>
  <artifactId>skynet-boot-starter-security</artifactId>
</dependency>
```

#### 代码集成

在SpringBoot的启动main入口类上添加 `@EnableSkynetSecurity` 注解，同时配合对应功能属性配置就可使用。

```java
// SpringBoot 启动入口
import org.springframework.boot.autoconfigure.SpringBootApplication;
import skynet.boot.AppUtils;
import skynet.boot.annotation.EnableSkynetSecurity;

@EnableSkynetSecurity
@SpringBootApplication
public class MyBootApp {
    public static void main(String[] args) {
        AppUtils.run(MyBootApp.class, args);
    }
}
```

#### 属性配置

在服务端属性配置中增加如下配置：

```properties
#--------------------------------------------------------------#
# 默认为 false（启用skynet.security 功能 base-auth|form-auth），如果为false时，启用 spring.security 原生功能。
skynet.security.enabled=true
#--------------------------------------------------------------#
skynet.security.sign-auth.enabled=true
skynet.security.sign-auth.path-patterns=/skynet/test/api/**
skynet.security.sign-auth.ignore-patterns=/skynet/test/none/**
# 认证账号 key：appId, value:secret，开放App和加密秘钥
skynet.security.sign-auth.app[sampleApp]=8h65ca0f8b76e7181a12bf1fa6f485fd
# 时钟偏移，单位秒,（缺省：300秒，5分钟）
skynet.security.sign-auth.offset-second=300
#--------------------------------------------------------------#
skynet.security.ip-auth.enabled=false
skynet.security.ip-auth.white-ip-list=**********
#skynet.security.ip-auth.black-ip-list=127.0.0.1,**********
#--------------------------------------------------------------#
skynet.security.base-auth.enabled=true
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=admin
skynet.security.base-auth.actuator-security-enabled=true
skynet.security.base-auth.path-patterns=/skynet/test/base/**
skynet.security.base-auth.ignore-patterns=/actuator/beans
skynet.security.base-auth.actuator-ignore-endpoints=
#--------------------------------------------------------------#
skynet.security.form-auth.enabled=true
skynet.security.form-auth.user.name=admin
skynet.security.form-auth.user.password=admin
skynet.security.form-auth.ignore-patterns=/actuator/mappings,/login,/hello
skynet.security.form-auth.path-patterns=/**
#skynet.security.rsa-private-file=classpath:key/rsa_2048_priv.pem
#skynet.security.rsa-public-file=classpath:key/rsa_2048_pub.pem
#--------------------------------------------------------------#
```

### 1. skynet.security.sign-auth API接口签名鉴权

#### 1.1 功能描述：

> 此组件提供对restful api 以及 websocket api 接口进行签名校验权限功能。
> 开启签名鉴权模式，配置应用appId，应用密钥appSecret后，
> 当客户端请求服务时，必须携带此应用的密钥信息，才能完成接口访问，否则返回401错误码，拒绝访问接口。
> 默认情况下，该模式是关闭状态。根据业务场景需要，可开启。

签名算法：
>
用户向服务器申请生成一个凭证，凭证是key/secret的密钥对。客户端对Method、Accept、Date以及其他Header字段和Url进行规定方式拼接后，通过哈希算法（如HMAC-SHA256）和用户的secret对请求进行签名。最后将key、使用的算法、参与签名的头部字段以及计算后的签名放入头部字段"
Authorization"中

请求头示例:

```
Content-Type:application/json
Accept:application/json,version=1.0

Date : Tue, 26 Jun 2018 12:27:03 UTC
Host:"your host"
Digest：SHA-256=xxxx
Authorization: hmac api_key="your_key", algorithm="hmac-sha256", headers="host date request-line", signature="base64_digest"
```

签名参数 描述
Date 请求日期，utc 时区。格式如 ： Tue, 26 Jun 2018 12:27:03 UTC
Host 请求主机，计算签名时需要该header
Digest body 的摘要，用sha256 计算,计算方法为 Digest="SHA-256="+base64(sha256(body))
Authorization 鉴权参数，具体构建防范如下

> 此算法来源与 讯飞云API，与其保持一致，详细见：[讯飞接口](https://aidocs.xfyun.cn/docs/public/http.html)

#### 1.2 属性配置

在服务端属性配置中增加如下配置：

```properties
#--------------------------------------------------------------#
# 开启Skynet安全功能，默认是关闭的
skynet.security.enabled=true
# 开启签名鉴权
skynet.security.sign-auth.enabled=true
# 鉴权拦截请求路径模板, 多个条件使用英文逗号隔开,默认值是 所有路径
skynet.security.sign-auth.path-patterns=/**
# 忽略鉴权的api路径模板，一般是拦截请求路径的子集，优先级比 pathPatterns 高，缺省：/info,/health,/skynet/health,/actuator/health
skynet.security.sign-auth.ignore-patterns=/info,/health,/skynet/health,/actuator/health
# 认证账号 key：appId, value:secret，开放App和加密秘钥
skynet.security.sign-auth.app[sampleApp]=8h65ca0f8b76e7181a12bf1fa6f485fd
# 时钟偏移，单位秒,（缺省：300秒，5分钟），
skynet.security.sign-auth.offset-second=300
#--------------------------------------------------------------#
```

备注说明：
> `skynet.security.sign-auth.offset-second=0` 时，不做时间偏移校验。
>

#### 1.3 客户端集成

见 #鉴权服务访问客户端工具 节

---

### 2. skynet.security.ip-auth API接口IP校验

#### 2.1 功能描述

> 组件提供对 `restful api` 以及 `websocket api` 接口对请求来源IP进行校验， 验证条件包括IP地址的黑白名单。
> 如果同时开启sign-auth模式，IP黑白名单模式将优先进行校验。

#### 2.2 属性配置

在服务端属性配置中增加如下配置：

```properties
#--------------------------------------------------------------#
# 是否开启黑白名单，默认为false
skynet.security.ip-auth.enabled=true
# 鉴权拦截请求路径模板, 多个条件使用英文逗号隔开,默认值是 所有路径
skynet.security.ip-auth.path-patterns=/**
# 忽略鉴权的api路径模板，一般是拦截请求路径的子集，优先级比 pathPatterns 高，缺省：/info,/health,/skynet/health,/actuator/health
skynet.security.ip-auth.ignore-patterns=/info,/health,/skynet/health,/actuator/health
# 配置了白名单，客户端ip在白名单内， 则允许访问，否则拒绝。多个ip请用英文逗号分隔
skynet.security.ip-auth.white-ip-list=**********
# 配置了黑名单，客户端ip在黑名单内， 则拒绝，否则允许访问。如果配置了白名单，则黑名单配置无效。多个ip请用英文逗号分隔
#skynet.security.ip-auth.black-ip-list=127.0.0.1,**********
#--------------------------------------------------------------#
```

---

### 3. skynet.security.base-auth BaseAuth鉴权

#### 3.1 功能描述

> 对当前服务指定的URL 进行BaseAuth拦截鉴权，弹出BaseAuth登录窗口输入密码进行验证。
> 特性：
> - 验证失败N次后，自动锁定M分钟。（次数和锁定周期可配）
> - 客户端采用 jwt token

#### 3.2 属性配置

```properties
#--------------------------------------------------------------#
# 是否启用 base-auth 默认为false
skynet.security.base-auth.enabled=true
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=admin
# 鉴权拦截路径，默认为空，多个用逗号分隔
skynet.security.base-auth.path-patterns=
# 忽略拦截路径，默认为空，一般是 拦截路径的子集，多个用逗号分隔
skynet.security.base-auth.ignore-patterns=
# 是否开通 Spring Actuator 安全验证，默认为true
skynet.security.base-auth.actuator-security-enabled=true
# 忽略的 Spring Actuator endpoint，默认为：health,refresh
skynet.security.base-auth.actuator-ignore-endpoints=health,refresh
# 密码重复验证错误，锁定周期，默认 3分钟
skynet.security.base-auth.fail-lock-duration-second=180
#  密码验证错误 重试次数，默认5次
skynet.security.base-auth.fail-tries-times=5
#--------------------------------------------------------------#
```

---

### 4. skynet.security.form-auth Form表单验证

#### 4.1 功能描述

> 对当前服务 指定Web表单页面 进行 `form`表单鉴权拦截，将直接调转到默认登录页面并进行验证（浏览器访问来源）。
> 特性：
> - 前端登录验证密码传输将通过RSA公钥自动加密。（公钥和私钥可配）
> - 验证失败N次后，自动锁定M分钟。（次数和锁定周期可配）
> - 客户端采用 `jwt token`
> - 非浏览器客户端访问，授权失败将返回401.

#### 4.2 属性配置

常用配置属性

```properties
#--------------------------------------------------------------#
# 是否启用 form-auth 默认是关闭的
skynet.security.form-auth.enabled=true
skynet.security.form-auth.user.name=admin
skynet.security.form-auth.user.password=admin
# 鉴权拦截路径，默认 /**
skynet.security.form-auth.path-patterns=/**
# 忽略拦截路径，默认为空，一般是 拦截路径的子集
skynet.security.form-auth.ignore-patterns=
# 超时时间秒 默认是10分钟
skynet.security.form-auth.jwt-expires-second=600
# 密码重复验证错误，锁定周期，默认 3分钟
skynet.security.form-auth.fail-lock-duration-second=180
#  密码验证错误 重试次数，默认5次
skynet.security.form-auth.fail-tries-times=5
#--------------------------------------------------------------#
```

> 备注说明：如果同时开启了  `sign-auth` 鉴权功能，`form-auth` 将自动添加到忽略路径中，不需要配置。

高级属性配置（需要定制扩展时使用）：

```properties
#--------------------------------------------------------------#
# 自定义 公钥私钥，详细，可见下面 《4.5 修改Form表单加密 默认公钥和私钥》
skynet.security.rsa-private-file=classpath:key/rsa_2048_priv.pem
skynet.security.rsa-public-file=classpath:key/rsa_2048_pub.pem

# 登录验证路径
skynet.security.form-auth.login-path=/skynet/login
# 登录form表单标题，默认：Skynet统一登录页面
skynet.security.form-auth.login-title=Skynet统一登录页面
# 代理contextPath，如nginx代理前缀
skynet.security.form-auth.proxy-context-path=
# cookie 域 路径
skynet.security.form-auth.jwt-cookie-domain=
# 默认 / 
skynet.security.form-auth.jwt-cookie-path=/
# "SKYNET_JWT_TOKEN:" + UUID.randomUUID();
skynet.security.form-auth.jwt-token-session-key=
#--------------------------------------------------------------#
```

> **说明: **
> 同时开启了 `base-auth` 和  `form-auth` 功能时，建议 Spring Actuator 除了 `health`,`refresh`,`promethus` 端点
> 也采用 `form-auth`验证。

**示例配置如下：**

```properties
#--------------------------------------------------------------#
skynet.security.base-auth.enabled=true
skynet.security.base-auth.path-patterns=/actuator/prometheus
skynet.security.base-auth.actuator-security-enabled=false
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=skynet2230
#---------------------------------------------------------------------
skynet.security.form-auth.enabled=true
# add demo home page
skynet.security.form-auth.path-patterns=/**
skynet.security.form-auth.ignore-patterns=/tuling/**,/turing/**
skynet.security.form-auth.user.name=${skynet.security.base-auth.user.name}
skynet.security.form-auth.user.password=${skynet.security.base-auth.user.password}
#--------------------------------------------------------------#
```

#### 4.4 form-auth 对外提供的Rest-API访问地址

- /skynet/security/login
- /skynet/security/logout

**login 接口：**
**Request**

```json
{
	"username":"skynet.security.form-auth.user.name 配置的用户名",
	"password":"一般是加密后的"
}
```

**Response**

```json
{
	"code":0,
	"message":"ok",
	"data":{
		"tokenHeader":"Bearer",
 "token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTY1NzI1ODQ0OCwiY3JlYXRlZCI6MTY1NzE3MjA0ODU0MiwiYXV0aG9yaXRpZXMiOltdfQ.YHlbdsZzWqCYuLdEl-heOVIar327a7AYZBnV8LNLTNe7mqmX9x5JFCqubcJxzy2I1aIJ0FA2-z5QYfaXB4dtMw"
	}
} 
```

---

### 5. 加密工具 SkynetEncryption

在使用的地方直接注解 `@Autowired` `SkynetEncryption skynetEncryption` 就可以直接使用
> 前提开启了 `@EnableSkynetSecurity` 同时 配置了 `skynet.security.enabled=true`

#### 5.1 SkynetEncryption 接口

```java
    /** 获取公钥Key */
    public String getPublicKey() 

    /** 获取私钥Key */
    public String getPrivateKey() 

    /** 加密 */
    public String encrypt(String text) throws Exception 

     /** 加密 */
    public byte[] encrypt(byte[] dataByte) throws Exception

    /** 解密 */
    public String decrypt(String base64) throws Exception 

    /** 解密 */
    public byte[] decrypt(byte[] dataByte) throws Exception

    /** 签名 */
    public String sign(String text) throws Exception 

    /** 验证 */
    public boolean verify(String text, String sign) throws Exception

    /** 签名 */
    public byte[] sign(byte[] data) throws Exception

    /** 验证 */
    public boolean verify(byte[] data, byte[] sign) throws Exception
```

#### 5.2 对外提供的Rest-API URL

- /skynet/security/publicKey
- /skynet/security/encrypt
- /skynet/security/decrypt

#### 5.3 修改加密 默认公钥和私钥

##### a.重新生成rsa公钥私钥：

在Linux下执行以下命令，

```shell
openssl genrsa -out rsa_2048_priv.pem 2048
openssl rsa -pubout -in rsa_2048_priv.pem -out rsa_2048_pub.pem
cat rsa_2048_priv.pem
cat rsa_2048_pub.pem
```

将以上2个文件 复制到自己项目的 resource 或其他目录

##### b.修改rsa文件配置：

```properties
# 将pem文件放到resource下（新建myapp-rsa目录），修复app配置
skynet.security.rsa-private-file=classpath:myapp-rsa/rsa_2048_priv.pem
skynet.security.rsa-public-file=classpath:myapp-rsa/rsa_2048_pub.pem
```

---

### 6. 鉴权服务访问客户端工具

skynet 目前已经内置，如下客户端访问工具类：

- SignAuthOkHttpClient
- BaseAuthRestTemplateBuilder
- SignAuthRestTemplateBuilder
- SignAuthWebSocketClient
- AuthRestTemplateBuilder 同时 `sign-auth` 或 `base-auth`鉴权，同时兼顾，**重点推荐使用**
- SignAuthFeignClientConfiguration feign请求拦截器 配置，**重点推荐使用**

所有 auth 客户端属性配置如下：

```properties
#--------------------------------------------------------------#
# sign-auth:  与服务端保持一致
skynet.security.auth-client.api-key=sampleApp
skynet.security.auth-client.api-secret=8h65ca0f8b76e7181a12bf1fa6f485fd
# base-auth 
skynet.security.auth-client.user=admin
skynet.security.auth-client.password=skynet2230
skynet.security.auth-client.timeout=5s
#--------------------------------------------------------------#
```

#### SignAuthOkHttpClient

由于依赖 okhttp 客户端 需要自行引入依赖

```xml
    <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
    </dependency>
```

Http请求方式一：签名数据存放在请求消息头中

```java
public class SampleClient {

    public static void main(String[] args) throws IOException {
        long timeout = 5000;
        String apiKey = "sampleApp";
        String apiSecret = "8h65ca0f8b76e7181a12bf1fa6f485fd";
        String url = "http://localhost:8080/app";
        SignAuthOkHttpClient authHttpClient = new SignAuthOkHttpClient(apiKey, apiSecret, timeout);
        String result = authHttpClient.execute("GET", url, null, null);
        System.out.println(result);
        authHttpClient.close();
    }
}
```

Http请求方式二：签名数据放在请求url参数中

```java
public class SampleClient {

    public static void main(String[] args) throws IOException {
        long timeout = 5000;
        String apiKey = "sampleApp";
        String apiSecret = "8h65ca0f8b76e7181a12bf1fa6f485fd";
        String url = "http://localhost:8080/app";
        String requestUrl = AuthUtils.assembleRequestUrl(url, apiKey, apiSecret);
        AuthHttpClient authHttpClient = new AuthHttpClient(timeout);
        String result = authHttpClient.execute("GET", requestUrl, null, null);
        System.out.println(result);
        authHttpClient.close();
    }
}
```

WebSocket请求方式：签名数据放在请求url参数中

```java
 String requestUrl = AuthUtils.assembleRequestUrl(wsUrl, apiKey, apiSecret);
```

#### SignAuthWebSocketClient

//TODO:

#### SignAuthRestTemplateBuilder

//TODO:

#### BaseAuthRestTemplateBuilder

//TODO:

#### AuthRestTemplateBuilder（推荐）

RestTemplate 构造器，内部通过传入的 sign-auth 或 base-auth 需要的 key、secret，或 username、password，自动添加相应的
拦截器，添加用户鉴权信息。

```java

@Configuration(proxyBeanMethods = false)
public class AuthClientConfig {

    @Value("${skynet.boot.auth.app.apiKey:skynet}")
    private String apiKey;

    @Value("${skynet.boot.auth.app.apiSecret:8H65CA0F8B76E7181A12BF1FA6F485FD}")
    private String apiSecret;

    @Value("${skynet.fetch.server.status.timeout.seconds:5}")
    private int timeout;

    private final SkynetBaseAuthProperties.User user;

    public AuthClientConfig(SkynetBaseAuthProperties securityProperties) {
        this.user = securityProperties.getUser();
    }

    //通过 AuthRestTemplateBuilder 构建 待鉴权的 RestTemplate
    @Bean
    public RestTemplate authRestTemplate(AuthRestTemplateBuilder authRestTemplateBuilder) {
        SkynetAuthClientProperties properties = new SkynetAuthClientProperties();
        //设置 sign-auth 和 base-auth 需要的 key、secret，username、password
        properties.setApiKey(getApiKey()).setApiSecret(getApiSecret()).setTimeout(getTimeout());
        properties.setUser(user.getName()).setPassword(user.getPassword());
        return authRestTemplateBuilder.build(properties);
    }
```

使用 `authRestTemplate` ，采用  `@Qualifier("authRestTemplate") RestTemplate authRestTemplate`

```java
@Slf4j
@RestController
@ExposeSwagger2
public class V3SysInfoController implements V3SysInfo {

    private final RestTemplate authRestTemplate;

    public V3SysInfoController (@Qualifier("authRestTemplate") RestTemplate authRestTemplate) {
         this.authRestTemplate = authRestTemplate;
    }
 
    @Override
    public List<ConnectionStat> getConnectionStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception {

        // ...
        String uri = String.format("http://%s:%d/skynet/agent/sysinfo/connection", antActionStatus.getIp(), antActionStatus.getPort());
        log.debug("Query target={} by uri={}", target, uri);
        return authRestTemplate.getForObject(uri, ConnectionStat.class);
    }
}

```

#### SignAuthFeignClientConfiguration

为了给feign定义中，自动添加 `sign-auth` 鉴权信息拦截器，skynet也定义了相关的 `ClientConfiguration`

备注：由于依赖 feign 客户端 需要自行引入依赖 （版本号skynet-boot-starter-parent已经统一定义了）

```xml
<dependency>
  <groupId>org.springframework.cloud</groupId>
  <artifactId>spring-cloud-starter-openfeign</artifactId>
</dependency>
<dependency>
  <groupId>io.github.openfeign</groupId>
  <artifactId>feign-httpclient</artifactId>
</dependency>
```

`FeignClient` 定义示例

重点 `configuration = SignAuthFeignClientConfiguration.class`

```java
/***
 *  用户登录管理
 * <AUTHOR>
 */
//注意：configuration = SignAuthFeignClientConfiguration.class
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = SignAuthFeignClientConfiguration.class, contextId = "AuthLogin")
@Tag(name = "v0.系统登录验证", description = "系统登录验证", hidden = true)
public interface AuthLogin {

    String PREFIX = "/skynet/auth";

    @PostMapping(value = PREFIX + "/login", produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "登录")
    @ApiParam(name = "loginInfo", required = true, value = "用户名、密码")
    SkynetApiResponse<AccessToken> login(@RequestBody LoginInfo loginInfo);

    @Operation(summary = "修改密码")
    @PostMapping(value = PREFIX + "/updatePwd", produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    SkynetApiResponse<Void> updatePwd(@RequestBody UpdatePasswordInfo info) throws Exception;
}
```

再看下 `SignAuthFeignClientConfiguration` 定义：

```java
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.util.Assert;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.config.SkynetAuthClientProperties;

/**
 * * feign请求拦截器 配置，
 * * 在拦截器中添加鉴权的签名信息
 *
 * <AUTHOR>
 */
@Slf4j
public class SignAuthFeignClientConfiguration {
    // 目标服务需要 定义 SkynetAuthClientProperties 属性，传入 `apiKey`和`apiSecret`
    @Bean
    public RequestInterceptor authSignFeignInterceptor(SkynetAuthClientProperties authProperties) {
        return new SignAuthFeignInterceptor(authProperties);
    }

    public static class SignAuthFeignInterceptor implements RequestInterceptor {
        private final SkynetAuthClientProperties authProperties;

        public SignAuthFeignInterceptor(SkynetAuthClientProperties authProperties) {
            this.authProperties = authProperties;
        }

        @Override
        public void apply(RequestTemplate requestTemplate) {
            log.debug("Apply SignAuthFeignInterceptor ...");
            Assert.hasText(authProperties.getApiKey(), "apiKey is blank.");
            Assert.hasText(authProperties.getApiSecret(), "apiSecret is blank.");


            log.debug("assembleAuthorizationHeaders header ...");
            Map<String, String> headers = AuthUtils.assembleAuthorizationHeaders(
                    HttpMethod.resolve(requestTemplate.method()),
                    requestTemplate.feignTarget().url() + requestTemplate.url(),
                    authProperties.getApiKey(), authProperties.getApiSecret(),
                    requestTemplate.bodyTemplate());

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                log.debug("{}={}", entry.getKey(), entry.getValue());
                requestTemplate.header(entry.getKey(), entry.getValue());
            }

            log.debug("Apply SignAuthFeignInterceptor OK.");
        }
    }
}

```

### 7.鉴权异常处理

#### 7.1 客户端鉴权异常处理

一般情况下，如果鉴权未通过会返回http状态码为401或403，客户端可以从响应消息中拿到状态码，并在消息头中拿到错误信息。示例代码如下

```java
if (response.code() != HTTP_OK) {
    if (response.code() == HTTP_FORBIDDEN || response.code() == HTTP_UNAUTHORIZED) {
        throw new  IOException(String.format("code:%s, message:%s", response.code(), response.header("errorMsg")));
    }
    throw new IOException(response.toString());
}
```

#### 7.2 异常鉴权请求监控

如果目标服务 引入了 Prometheus 对于鉴权不通过的异常请求，将自动生成如下指标，可以配合 Skynet监控组件进行预警。

访问http://ip:port/actuator/prometheus，当有鉴权异常请求时，会采集到异常指标，如:

```
TYPE skynet_security_auth-fail_count_total counter
skynet_security_auth-fail_count_total{ip="*************",type="sign",} 1.0
skynet_security_auth-fail_count_total{ip="*************",type="sign",} 2.0
skynet_security_auth-fail_count_total{ip="**********",type="sign",} 1.0
```

> 其中 `skynet_security_auth-fail_count_total` 为指标名，支持指标tag为ip和type两种。 type=sign|ip|form|base ip为异常请求的ip地址。

### 8. FAQ

#### 8.1 使用springboot-security 原生功能，如何设置？

已经引入了 `skynet-boot-starter-security`，想全局关闭鉴权功能，使用springboot-security
原生功能，只需添加 `skynet.security.enabled`=`false`即可，

```properties
skynet.security.enabled=false
```

因为当 skynet.security.enabled=true 时 skynet 会自动 添加 `spring.autoconfigure.exclude` 排除一下 `AutoConfiguration`

- `org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration`
- `org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration`

如果同时也不想使用 springboot-security 原生功能，

```properties
skynet.security.enabled=true
skynet.security.base-auth.enabled=false
skynet.security.form-auth.enabled=false
```

#### 8.2 如何实现UI免登陆

开启 api-auth 鉴权，让第三方 通够 auth-client 请求 `/skynet/security/token` 接口 获取 验证 `token`，
然后通过 页面URL后面增加 `skynet_token=xxxx`，就能免登录了。

关键配置如下：

```properties
#--------------------------------------------------------------#
skynet.security.sign-auth.enabled=true
skynet.security.sign-auth.path-patterns=/skynet/security/token
skynet.security.sign-auth.app[otherApp]=8h65ca0f8b76e7181a12bf1fa6f485fd
#--------------------------------------------------------------#
```

`skynet-boot-starter-security` 主要引入如下依赖，目标服务可以根据需要进行排除。

```xml
<dependency>
    <groupId>com.google.guava</groupId>
    <artifactId>guava</artifactId>
</dependency>
<!-- 与Base-Auth 和 Form-Auth有关 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
</dependency>
<!-- 与Form-Auth有关 -->
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-thymeleaf</artifactId>
</dependency>
<!-- 与authClient有关 -->
<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
</dependency>
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
    <optional>true</optional>
</dependency>
<dependency>
    <groupId>io.github.openfeign</groupId>
    <artifactId>feign-httpclient</artifactId>
    <optional>true</optional>
</dependency>
<dependency>
    <groupId>com.squareup.okhttp3</groupId>
    <artifactId>okhttp</artifactId>
    <optional>true</optional>
</dependency>
```




