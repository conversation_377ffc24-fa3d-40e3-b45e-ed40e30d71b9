package skynet.boot.security.auth.filter;


import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.util.StopWatch;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.auth.exception.SkynetAuthException;
import skynet.boot.security.config.SkynetSignAuthProperties;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * skynet.security.api-auth 校验过滤器
 *
 * <AUTHOR> by jianwu6 on 2020/4/30 13:43
 */
@Slf4j
public class AuthFilterBase4Sign extends AuthFilterBase {

    private final SkynetSignAuthProperties authProperties;

    public AuthFilterBase4Sign(SkynetSignAuthProperties authProperties, SkynetSecurityMetrics skynetSecurityMetrics) {
        super(authProperties, skynetSecurityMetrics);
        this.authProperties = authProperties;
    }

    @Override
    void filter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("Signature Verification start ...");

        String ip = request.getRemoteAddr();
        try {
            AuthInfoFetcher.AuthInfo authInfo = AuthInfoFetcher.getAuth(request);

            //时钟校验
            if (authProperties.getOffsetSecond() > 0) {
                SimpleDateFormat format = new SimpleDateFormat(AuthUtils.DATE_FORMAT_PATTERN, Locale.US);
                format.setTimeZone(TimeZone.getTimeZone("GMT"));
                Date date;
                try {
                    date = format.parse(authInfo.getDate());
                } catch (ParseException e) {
                    log.error("Client Date RFC 1123:{}.", authInfo.getDate());
                    //throw new AuthException("时钟偏移校验失败,时间格式错误。如：[Mon, 10 Oct 2019 08:42:28 UTC]", HttpStatus.FORBIDDEN);
                    throw new SkynetAuthException("Clock offset check failed, time format error. eg:[Mon, 10 Oct 2019 08:42:28 UTC]", HttpStatus.FORBIDDEN);
                }

                LocalDateTime clientLocalDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                log.debug("Client Date LocalDateTime:{}.", clientLocalDateTime);

                long seconds = Duration.between(clientLocalDateTime, LocalDateTime.now()).getSeconds();
                if (seconds > authProperties.getOffsetSecond()) {
                    log.warn("ClockCheckFilter Verification Failure.[date offset {} > {} second].", seconds, authProperties.getOffsetSecond());
                    //时钟偏移校验失败
                    throw new SkynetAuthException("Clock offset check failure", HttpStatus.FORBIDDEN);
                }
            }

            //校验apiKey是否已配置
            if (!authProperties.getApp().containsKey(authInfo.getApiKey())) {
                log.warn("The server has not authorized the api_key: {}", authInfo.getApiKey());
                throw new SkynetAuthException(String.format("api_key[%s] is illegal.", authInfo.getApiKey()), HttpStatus.UNAUTHORIZED);
            }

            //比对签名
            String apiSecret = authProperties.getApp().get(authInfo.getApiKey());
            String serverSign = AuthUtils.getSignature(authInfo.getHost(), authInfo.getDate(), authInfo.getRequestLine(), apiSecret);

            String clientSign = authInfo.getSignature();
            log.debug("\nclient signature=\t{}\nserver signature=\t{}", clientSign, serverSign);

            if (clientSign.equals(serverSign)) {
                log.debug("Signature Verification OK.");
                filterChain.doFilter(servletRequest, servletResponse);
            } else {
                log.warn("IP={} Signature verification failed. clientSign={}, serverSign={}", authInfo.getHost(), clientSign, serverSign);
                throw new SkynetAuthException("Signature is not equal", HttpStatus.FORBIDDEN);
            }
        } catch (SkynetAuthException ex) {
            log.warn("Check auth error= {}.[RemoteIP={};RequestUri={}]", ex.getMessage(), ip, request.getRequestURI());
            skynetSecurityMetrics.authFail("sign", ip);
            response.setHeader(AuthUtils.SKYNET_AUTH_ERR_MESSAGE, ex.getMessage());
            response.sendError(ex.getCode(), ex.getMessage());
        } finally {
            log.debug("AuthFilterBase4Sign end.[cost:{}]", stopWatch);
        }
    }
}
