package skynet.boot.security.form.jwt;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import skynet.boot.security.config.SkynetFormAuthProperties;
import skynet.boot.security.form.controller.SkynetSecurityAuthController;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * Security相关操作
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtSecurityUtils {

    public static final String AUTHORIZATION_HEADER = "Authorization";
    public static final String BEARER_HEADER = "Bearer ";
    public static final String BASIC_HEADER = "basic ";
    private final static String ACCESS_TOKEN_KEY;

    public final static String SKYNET_TOKEN_KEY = "skynet.token.key";

    static {
        ACCESS_TOKEN_KEY = System.getProperty(SKYNET_TOKEN_KEY, "skynet_token_" + System.currentTimeMillis());
    }

//    /**
//     * 获取令牌进行认证
//     *
//     * @param request
//     */
//    public static void checkAuthentication(HttpServletRequest request) {
//        // 获取令牌并根据令牌获取登录认证信息
//        String token = getToken(request);
//        Authentication authentication = JwtTokenUtils.getAuthenticationFromToken(token);
//        // 设置登录认证信息到上下文
//        SecurityContextHolder.getContext().setAuthentication(authentication);
//    }

    /**
     * 获取当前用户名
     *
     * @return
     */
    public static String getUsername() {
        return getUsername(getAuthentication());
    }

    /**
     * 获取用户名
     *
     * @return
     */
    public static String getUsername(Authentication authentication) {
        String username = null;
        if (authentication != null) {
            Object principal = authentication.getPrincipal();
            if (principal != null) {
                username = (principal instanceof UserDetails) ? ((UserDetails) principal).getUsername() : principal.toString();
            }
        }
        return username;
    }

    /**
     * 获取当前登录信息
     *
     * @return
     */
    public static Authentication getAuthentication() {
        if (SecurityContextHolder.getContext() == null) {
            return null;
        }
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return
     */
    public static List<String> getTokens(HttpServletRequest request) {
        List<String> tokens = new ArrayList<>();

        //从 Header 中获取  skynet_token
        String token = request.getHeader(ACCESS_TOKEN_KEY);
        if (StringUtils.isNoneBlank(token)) {
            tokens.add(token);
        }

        //从  从URL中获取
        if (request.getParameterNames() != null) {
            Enumeration<?> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = (String) paramNames.nextElement();
                if (ACCESS_TOKEN_KEY.equalsIgnoreCase(paramName)) {
                    if (StringUtils.isNoneBlank(request.getParameter(paramName))) {
                        tokens.add(request.getParameter(paramName));
                        break;
                    }
                }
            }
        }

        //从Cookie中获取
        if (request.getCookies() != null) {
            for (Cookie cookie : request.getCookies()) {
                if (ACCESS_TOKEN_KEY.equals(cookie.getName())) {
                    if (StringUtils.isNoneBlank(cookie.getValue())) {
                        tokens.add(cookie.getValue());
                    }
                }
            }
        }
        //从 Header 中获取  Authorization
        token = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.isNoneBlank(token) && token.contains(BEARER_HEADER)) {
            tokens.add(token.trim().substring(BEARER_HEADER.length()).trim());
        }

        return tokens;
    }


    public static void setCookie(HttpServletResponse response, String token, SkynetFormAuthProperties formProperties) {
        Cookie cookie = new Cookie(JwtSecurityUtils.ACCESS_TOKEN_KEY, token);
        if (StringUtils.isNoneBlank(formProperties.getJwtCookiePath())) {
            cookie.setPath(formProperties.getJwtCookiePath());
        }
        if (StringUtils.isNoneBlank(formProperties.getJwtCookieDomain())) {
            cookie.setDomain(formProperties.getJwtCookieDomain());
        }
        response.addCookie(cookie);
    }

    /**
     * 系统登录认证
     *
     * @param userName
     * @param formProperties
     * @return
     */
    public static String createToken(UserDetailsService userDetailsService, String userName,
                                     SkynetFormAuthProperties formProperties) {
        UserDetails user = userDetailsService.loadUserByUsername(userName);
        Authentication authentication = new TestingAuthenticationToken(user, user.getAuthorities());

        // 生成令牌并返回给客户端
        return JwtTokenUtils.generateToken(authentication, formProperties.getJwtExpiresSecond());
    }

    public static String login(HttpServletResponse response, AuthenticationManager authenticationManager,
                               SkynetSecurityAuthController.SkynetLoginInfo skynetLoginInfo,
                               SkynetFormAuthProperties formProperties) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(skynetLoginInfo.getUsername(), skynetLoginInfo.getPassword(), new ArrayList<>()));
        if (authentication.isAuthenticated()) {
            String token = JwtTokenUtils.generateToken(authentication, formProperties.getJwtExpiresSecond());
            setCookie(response, token, formProperties);
            return token;
        }
        throw new BadCredentialsException("Login failed, incorrect username or password");
    }
}
