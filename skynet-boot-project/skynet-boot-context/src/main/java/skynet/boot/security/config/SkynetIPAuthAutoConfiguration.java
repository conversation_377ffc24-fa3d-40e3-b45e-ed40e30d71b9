package skynet.boot.security.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.security.auth.filter.AuthFilterBase4Ip;
import skynet.boot.security.condition.ConditionalOnIPAuth;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

/**
 * ip-auth 支持签名，以及ip黑白名单
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnIPAuth
@AutoConfigureAfter({SkynetSecurityAutoConfiguration.class})
@ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "servlet", matchIfMissing = true)
public class SkynetIPAuthAutoConfiguration {

    @Bean
    public FilterRegistrationBean<?> registrationSkynetAuthFilter4ip(SkynetIPAuthProperties skynetIPAuthProperties, SkynetSecurityMetrics skynetSecurityMetrics) {
        log.info("Init registrationSkynetAuthFilter4IP.");
        FilterRegistrationBean<?> filterRegistrationBean = new FilterRegistrationBean<>(new AuthFilterBase4Ip(skynetIPAuthProperties, skynetSecurityMetrics));
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setOrder(skynetIPAuthProperties.getFilterOrder() - 1);

        return filterRegistrationBean;
    }
}
