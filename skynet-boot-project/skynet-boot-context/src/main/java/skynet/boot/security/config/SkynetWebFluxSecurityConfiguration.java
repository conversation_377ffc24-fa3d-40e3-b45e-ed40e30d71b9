package skynet.boot.security.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableReactiveMethodSecurity;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.userdetails.MapReactiveUserDetailsService;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import reactor.core.publisher.Mono;
import skynet.boot.security.condition.ConditionalOnSecurity;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableWebFluxSecurity
@EnableReactiveMethodSecurity
@AutoConfigureAfter({SkynetSecurityPropertiesAutoConfiguration.class})
@ConditionalOnSecurity
@ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "reactive")
public class SkynetWebFluxSecurityConfiguration implements Customizer<ServerHttpSecurity.HttpBasicSpec> {

    @Bean
    public SecurityWebFilterChain configureSecurity(ServerHttpSecurity serverHttpSecurity, SkynetBaseAuthProperties skynetBaseAuthProperties) {
        // 检查配置，避免空指针异常
        List<String> ignorePaths = Optional.ofNullable(skynetBaseAuthProperties.getIgnorePatterns()).orElse(Collections.emptyList());
        List<String> authPaths = Optional.ofNullable(skynetBaseAuthProperties.getPathPatterns()).orElse(Collections.emptyList());

        log.info("Set httpBasic authPaths={}", authPaths);
        log.info("Set httpBasic ignorePaths={}", ignorePaths);

        // 禁用 CSRF
        serverHttpSecurity.csrf(ServerHttpSecurity.CsrfSpec::disable);

        serverHttpSecurity.authorizeExchange(authorize -> {
            if (!authPaths.isEmpty()) {
                // 需要鉴权的路径
                authorize.pathMatchers(authPaths.toArray(new String[0])).authenticated();
            }

            if (!ignorePaths.isEmpty()) {
                // 忽略的路径无需鉴权
                authorize.pathMatchers(ignorePaths.toArray(new String[0])).permitAll();
            } else {
                // 默认放行其他路径
                authorize.anyExchange().permitAll();
            }
        });
        // 配置 HTTP Basic 认证
        serverHttpSecurity.httpBasic(Customizer.withDefaults());
        return serverHttpSecurity.build();
    }


    @Override
    public void customize(ServerHttpSecurity.HttpBasicSpec httpBasicSpec) {
        httpBasicSpec.authenticationEntryPoint((exchange, ex) -> {
            log.error("Authentication exchange path {} error: ", exchange.getRequest().getPath());

            exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
            return Mono.fromRunnable(() -> {
                ServerHttpResponse response = exchange.getResponse();
                response.setStatusCode(HttpStatus.UNAUTHORIZED);
                response.getHeaders().set("WWW-Authenticate", "Basic realm=SkynetAuth");
            });
        });
    }

    @Bean
    public MapReactiveUserDetailsService userDetailsService(SkynetBaseAuthProperties skynetBaseAuthProperties) {
        PasswordEncoder encoder = PasswordEncoderFactories.createDelegatingPasswordEncoder();
        // 定义用户名和密码
        User.UserBuilder user1 = User.builder().passwordEncoder(encoder::encode);
        user1.username(skynetBaseAuthProperties.getUser().getName()).password(skynetBaseAuthProperties.getUser().getPassword()).roles("USER", "ADMIN");
        // 不可删除，认证默认密码账号
        User.UserBuilder user2 = User.builder().passwordEncoder(encoder::encode);
        user2.username("taguser").password("tagpassword").roles("USER", "ADMIN");
        return new MapReactiveUserDetailsService(user1.build(), user2.build());
    }
}
