package skynet.boot.security.controller;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Lazy;
import skynet.boot.security.condition.ConditionalOnSecurity;
import skynet.boot.security.config.SkynetAuthProperties;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * skynet-security-properties 相关属性 Endpoint
 *
 * <AUTHOR>
 */
@Slf4j
@Lazy
@ConditionalOnSecurity
@Endpoint(id = "skynet-security-properties")
public class SkynetSecurityEndpoint {

    private final List<SkynetAuthProperties> properties;


    public SkynetSecurityEndpoint(List<SkynetAuthProperties> properties) {
        this.properties = properties;
    }

    @ReadOperation
    public Object invoke() {
        log.debug("do get skynet-security-properties...");
        Map<String, Object> status = new LinkedHashMap<>();
        status.put("properties", JSON.toJSONString(properties));
        return status;
    }
}