package skynet.boot.security.form.data;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

/**
 * token 信息.
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccessToken extends Jsonable {
    /**
     * token.
     */
    private String token;

    /**
     * token类型, 默认basic.
     */
    private String tokenHeader = "Bearer";

    public AccessToken() {
    }

    public AccessToken(String token) {
        this.token = token;
    }
}

//
//    /**
//     * 到期时间， 时间戳，1970年毫秒数.
//     */
//    private long expires;
