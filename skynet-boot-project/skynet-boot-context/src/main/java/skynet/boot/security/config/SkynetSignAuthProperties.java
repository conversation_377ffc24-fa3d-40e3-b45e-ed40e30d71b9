package skynet.boot.security.config;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 *     skynet.security.api-auth 配置项
 * </pre>
 *
 * <AUTHOR> [ 2020/4/29 15:15]
 */
@Getter
@Setter
public class SkynetSignAuthProperties extends SkynetSecurityProperties {

    /**
     * 认证账号 key：appId, value:secret
     */
    @JSONField(ordinal = 100)
    private Map<String, String> app = new HashMap<>();

    /**
     * 时钟偏移，单位秒,（缺省：300秒，5分钟）
     */
    @JSONField(ordinal = 120)
    private long offsetSecond = 5 * 60;

    /**
     * 权限认证过滤器执行排序,default=0
     */
    @JSONField(ordinal = 140)
    private int filterOrder = 0;
}
