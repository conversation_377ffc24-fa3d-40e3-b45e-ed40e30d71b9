package skynet.boot.security.auth.filter;

import com.alibaba.fastjson2.JSON;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.auth.exception.SkynetAuthException;

import java.io.Serializable;
import java.util.Base64;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 *     AuthInfo 获取
 * </pre>
 *
 * <AUTHOR> [ 2020/4/29 20:51]
 */
@Slf4j
class AuthInfoFetcher {

    private static final String API_KEY = "api_key";
    private static final String ALGORITHM = "algorithm";
    private static final String HEADERS = "headers";
    private static final String SIGNATURE = "signature";

    /**
     * 获取认证信息
     *
     * @param request
     * @return
     */
    public static AuthInfo getAuth(HttpServletRequest request) throws SkynetAuthException {

        String requestIP = request.getRemoteAddr();

        //优先从请求URL上获取
        String strAuth = getQuery(request, AuthUtils.AUTH_KEY.toLowerCase());
        log.debug("{}={}", AuthUtils.AUTH_KEY, strAuth);

        if (StringUtils.hasText(strAuth)) {
            try {
                strAuth = new String(Base64.getDecoder().decode(strAuth));
            } catch (Throwable ex) {
                log.warn("[IP={}] Base64 decode error.[strAuth={}]", requestIP, strAuth);
                throw new SkynetAuthException("Decode Authentication Base64 error", HttpStatus.FORBIDDEN);
            }
        } else {
            strAuth = getHeader(request, AuthUtils.AUTH_KEY);
        }

        log.debug("[IP={}] The authorization string\t{}", requestIP, strAuth);

        if (!StringUtils.hasText(strAuth)) {
            log.warn("[IP={}]  Miss authorization[query:{}]", requestIP, request.getPathInfo());
            throw new SkynetAuthException("Miss authorization");
        }
        //忽略 hmac 标识
        strAuth = strAuth.replaceAll("hmac ", "").trim();
        strAuth = strAuth.replaceAll("\"", "");

        AuthInfo authInfo;
        try {
            authInfo = decoAuth(request, strAuth);
        } catch (SkynetAuthException e) {
            throw new SkynetAuthException(String.format("decode auth error(%s)", e.getMessage()), HttpStatus.FORBIDDEN);
        }
        if (authInfo == null) {
            log.warn("authorization is null");
            throw new SkynetAuthException("Authorization is null");
        }
        log.debug("[IP={}] Fetch auth=\t{}", requestIP, authInfo);
        log.debug("========================================================");

        return authInfo;
    }


    /**
     * 装配 AuthInfo
     * <p>
     * Host: serverIP:port
     * Date: Tue, 26 Jun 2018 12:27:03 UTC
     * Digest: SHA-256=zc5lbDoNIhuwTUEzk+bu5OqCdHNxQxi4uKM5ghI2IDQ=
     * Authorization: hmac api_key="your_key", algorithm="hmac-sha256", headers="host date request-line digest", signature="base64_digest"
     * <p>
     * <p>
     * Digest: SHA-256=base64(sha256(body))
     *
     * @param request
     * @param strAuth
     * @return
     * @throws SkynetAuthException
     */
    private static AuthInfo decoAuth(HttpServletRequest request, String strAuth) throws SkynetAuthException {

        if (log.isDebugEnabled()) {
            debugHeader(request);
        }

        Map<String, String> map = parseAuthStr(strAuth);
        AuthInfo authInfo = new AuthInfo();
        authInfo.setApiKey(map.get(API_KEY));
        authInfo.setAlgorithm(map.get(ALGORITHM));
        authInfo.setHeaders(map.get(HEADERS));
        authInfo.setSignature(map.get(SIGNATURE));

        String strHeader = authInfo.getHeaders();
        strHeader.replaceAll("\"", "");

        //host date request-line digest

        String[] headerArr = strHeader.split("\\s+");

        authInfo.setHost(getParam(request, headerArr[0]));
        authInfo.setDate(getParam(request, headerArr[1]));

        if (!StringUtils.hasText(authInfo.getHost()) || !StringUtils.hasText(authInfo.getDate())) {
            log.warn("host or date is not exist.");
            throw new SkynetAuthException("host or date is not exist.");
        }

        if (strHeader.contains("request-line")) {
            String strPath = request.getServletPath();
            log.debug("request-line  path:{}", strPath);
            log.debug("request-line  getMethod:{}", request.getMethod());
            String requestLine = AuthUtils.getRequestLine(request.getMethod(), strPath);
            log.debug("request-line:{}", requestLine);
            authInfo.setRequestLine(requestLine);
        }

        if (strHeader.toLowerCase().contains("digest")) {
            String digest = getParam(request, "digest");
            log.debug("digest: {}", digest);
            if (StringUtils.hasText(digest)) {
                throw new SkynetAuthException("miss digest");
            }
            digest = digest.replaceAll(AuthUtils.DIGEST_NAME + "=", "");
            authInfo.setDigest(digest);
        }

        log.trace("get auth info= {}", authInfo);
        return authInfo;
    }

    private static String getValue(String key) throws SkynetAuthException {
        int index = key.indexOf("=");
        log.debug("value {}", key);
        if (index < 0) {
            log.warn("auth info error. key= {}", key);
            throw new SkynetAuthException("auth info error");
        }
        return key.substring(index + 1);
    }


    private static String getParam(HttpServletRequest request, String key) {
        String paramStr = getHeader(request, key);
        if (!StringUtils.hasText(paramStr)) {
            paramStr = getQuery(request, key);
        }
        return paramStr;
    }


    private static Map<String, String> parseAuthStr(String strAuth) {
        String[] authArray = strAuth.split(",");
        if (authArray.length != 4) {
            log.warn("authorization format is illegal. [authorization={}]", strAuth);
            throw new SkynetAuthException("authorization format is illegal");
        }
        Map<String, String> map = new HashMap<>();
        for (String content : authArray) {

            int index = content.indexOf("=");
            log.debug("value {}", content);
            if (index < 0) {
                log.warn("auth info error. key={}", content);
                throw new SkynetAuthException("auth info error.");
            }
            String key = content.substring(0, index);
            String value = content.substring(index + 1);

            if (!StringUtils.hasText(key) || !StringUtils.hasText(value)) {
                log.warn("authorization format is illegal. [authorization={}]", strAuth);
                throw new SkynetAuthException("authorization format is illegal.");
            }
            map.put(key.trim(), value);
        }
        if (!map.containsKey(API_KEY) || !map.containsKey(ALGORITHM) || !map.containsKey(HEADERS) || !map.containsKey(SIGNATURE)) {
            log.warn("authorization format is illegal. [authorization={}]", strAuth);
            throw new SkynetAuthException("authorization format is illegal.");
        }
        return map;
    }

    private static String getHeader(HttpServletRequest request, String key) {
        String ret = null;
        if (request.getParameterNames() != null) {
            Enumeration<?> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = (String) headerNames.nextElement();
                if (key.equalsIgnoreCase(headerName)) {
                    ret = request.getHeader(headerName);
                    break;
                }
            }
        }
        log.trace("getHeader: key={}, value={}", key, ret);
        return ret;
    }

    private static String getQuery(HttpServletRequest request, String key) {
        String ret = null;
        if (request.getParameterNames() != null) {
            Enumeration<?> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = (String) paramNames.nextElement();
                if (key.equalsIgnoreCase(paramName)) {
                    ret = request.getParameter(paramName);
                    break;
                }
            }
        }
        log.trace("getQuery: key={}, value={}", key, ret);
        return ret;
    }

    private static void debugHeader(HttpServletRequest request) {
        log.debug("--header --------------------------------------------");
        log.debug("-- RequestURI={} ", request.getRequestURI());
        Enumeration<?> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = (String) headerNames.nextElement();
            log.debug("{}={}", headerName, request.getHeader(headerName));
        }
        log.debug("----------------------------------------------");
    }

    @Getter
    @Setter
    public static class AuthInfo implements Serializable {
        private String apiKey;
        private String algorithm;
        private String headers;
        private String signature;
        private String host;
        private String date;
        private String requestLine;
        private String digest;

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }
}
