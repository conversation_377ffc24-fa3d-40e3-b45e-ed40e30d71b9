package skynet.boot.security.form;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint;
import skynet.boot.common.domain.SkynetResponse;
import skynet.boot.security.config.SkynetFormAuthProperties;

import java.io.IOException;

/**
 * 自定义的BasicAuthenticationEntryPoint
 */
public class SkynetFormAuthenticationEntryPoint extends BasicAuthenticationEntryPoint {

    private final SkynetFormAuthProperties properties;

    public SkynetFormAuthenticationEntryPoint(SkynetFormAuthProperties properties) {
        this.properties = properties;
    }

    /**
     * 自定义请求失败后的响应信息
     *
     * @param request
     * @param response
     * @param authException
     * @throws IOException
     */
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        request.getSession().removeAttribute(properties.getJwtTokenSessionKey());
        response.setContentType("application/json; charset=utf-8");
        SkynetResponse<?> skynetResponse = new SkynetResponse<>(HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED.getReasonPhrase());
        response.getWriter().print(skynetResponse);
        response.getWriter().flush();
        response.getWriter().close();
    }


}
