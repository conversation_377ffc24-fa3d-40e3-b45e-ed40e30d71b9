package skynet.boot.security.form.controller;//package skynet.sample.security.server.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import skynet.boot.security.condition.ConditionalOnFormAuth;
import skynet.boot.security.condition.ConditionalOnFormLogin;
import skynet.boot.security.config.SkynetFormAuthProperties;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/23 19:36
 */
@Slf4j
@Controller
@ConditionalOnFormAuth
@ConditionalOnFormLogin
public class SkynetLoginController {

    private final ServerProperties serverProperties;
    private final SkynetFormAuthProperties skynetFormAuthProperties;

    public SkynetLoginController(ServerProperties serverProperties, SkynetFormAuthProperties skynetFormAuthProperties) {
        this.serverProperties = serverProperties;
        this.skynetFormAuthProperties = skynetFormAuthProperties;
    }


    @GetMapping(SkynetFormAuthProperties.DEFAULT_LOGIN_PAGE)
    public String login(Model model) {

        List<String> paths = Arrays.asList(skynetFormAuthProperties.getProxyContextPath(),
                serverProperties.getServlet().getContextPath());

        String base = paths.stream().filter(StringUtils::hasText).collect(Collectors.joining("/"));

        model.addAttribute("base", base);
        model.addAttribute("date", new Date());
        model.addAttribute("title", skynetFormAuthProperties.getLoginTitle());

        return "skynet/login";
    }
}