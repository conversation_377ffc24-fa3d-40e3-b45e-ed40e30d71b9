//package skynet.boot.security.baseauth;
//
//import com.google.common.cache.Cache;
//import com.google.common.cache.CacheBuilder;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import skynet.boot.security.metrics.SkynetSecurityMetrics;
//
//import java.util.concurrent.TimeUnit;
//
///**
// * 自定义的PasswordEncoder
// */
//@Slf4j
//public class BaseAuthPasswordEncoder implements PasswordEncoder {
//
//    /**
//     * 密码重复验证错误，锁定周期
//     */
//    private int failLockDurationSecond = 3 * 60;
//
//    /**
//     * 密码验证错误 重试次数，默认5次
//     */
//    private int failTriesTimes = 5;
//    private final Cache<String, Integer> authFailCache;
//    private final SkynetSecurityMetrics metrics;
//
//    public BaseAuthPasswordEncoder(SkynetSecurityMetrics metrics) {
//        this.metrics = metrics;
//        this.authFailCache = CacheBuilder.newBuilder().maximumSize(16).expireAfterAccess(
//                failLockDurationSecond, TimeUnit.SECONDS).concurrencyLevel(5).recordStats().build();
//    }
//
//    public BaseAuthPasswordEncoder(int failLockDurationSecond, int failTriesTimes, SkynetSecurityMetrics metrics) {
//        this.failLockDurationSecond = failLockDurationSecond;
//        this.failTriesTimes = failTriesTimes;
//        this.authFailCache = CacheBuilder.newBuilder().maximumSize(16).expireAfterAccess(
//                this.failLockDurationSecond, TimeUnit.SECONDS).concurrencyLevel(5).recordStats().build();
//        this.metrics = metrics;
//    }
//
//    @Override
//    public String encode(CharSequence rawPassword) {
//        return String.valueOf(rawPassword);
//    }
//
//    @Override
//    public boolean matches(CharSequence rawPassword, String encodedPassword) {
//        // Dubbo请求场景下 HttpServletRequest 为空
//        String ip = RequestContextHolder.getRequestAttributes() != null
//                ? ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getRemoteAddr()
//                : "127.0.0.1";
//
//        // 用户错误达到指定次数，拒绝验证
//        Integer times = authFailCache.getIfPresent(ip);
//        if (times != null && times >= this.failTriesTimes) {
//            log.warn("RemoteAddress={}, Verification failed {} times, will lock for {} seconds.", ip,
//                    this.failTriesTimes, this.failLockDurationSecond);
//            return false;
//        }
//
//        boolean ok = encodedPassword.equals(rawPassword.toString());
//        if (!ok) {
//            log.warn("The base auth password is wrong.[RemoteAddress={};times={}]", ip, times);
//            authFailCache.put(ip, times == null ? 1 : ++times);
//            metrics.authFail("base-auth", ip);
//        } else {
//            authFailCache.invalidate(ip);
//        }
//        return ok;
//    }
//
//}
