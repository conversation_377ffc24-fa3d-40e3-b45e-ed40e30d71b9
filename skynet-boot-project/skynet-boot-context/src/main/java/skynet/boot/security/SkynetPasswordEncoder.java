package skynet.boot.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

@Slf4j
public class SkynetPasswordEncoder implements PasswordEncoder {

    private final SkynetEncryption skynetEncryption;
    private final SkynetSecurityMetrics metrics;

    public SkynetPasswordEncoder(SkynetEncryption skynetEncryption, SkynetSecurityMetrics metrics) {
        this.skynetEncryption = skynetEncryption;
        this.metrics = metrics;
    }

    @Override
    public String encode(CharSequence rawPassword) {
        log.debug("encode rawPassword={};", rawPassword);
        try {
            return skynetEncryption.encrypt(String.valueOf(rawPassword));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean matches(CharSequence rawPassword, String encodePassword) {
        log.debug("matches rawPassword={}; encodePassword={}", rawPassword, encodePassword);
        if (StringUtils.hasText(rawPassword) && StringUtils.hasText(encodePassword)) {
            try {
                if (rawPassword.length() > 50) {
                    rawPassword = skynetEncryption.decrypt(rawPassword.toString());
                }
            } catch (Throwable e) {
                log.error("decrypt error:{}", e.getMessage());
                return false;
            }
            boolean ok = encodePassword.equals(rawPassword.toString());
            if (!ok) {
                String ip = RequestContextHolder.getRequestAttributes() != null
                        ? ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getRemoteAddr()
                        : "127.0.0.1";
                log.warn("The from auth password is wrong.");
                metrics.authFail("skynet-auth", ip);

            }
            return ok;
        }
        return false;
    }
}