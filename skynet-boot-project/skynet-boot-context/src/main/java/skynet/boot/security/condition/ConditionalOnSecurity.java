package skynet.boot.security.condition;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import skynet.boot.annotation.EnableSkynetSecurity;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Inherited
@ConditionalOnBean(annotation = EnableSkynetSecurity.class)
@ConditionalOnProperty(value = "skynet.security.enabled")
public @interface ConditionalOnSecurity {

}
