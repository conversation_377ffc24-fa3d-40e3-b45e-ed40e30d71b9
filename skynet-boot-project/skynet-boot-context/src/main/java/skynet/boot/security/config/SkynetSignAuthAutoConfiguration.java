package skynet.boot.security.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.security.auth.filter.AuthFilterBase4Sign;
import skynet.boot.security.condition.ConditionalOnSignAuth;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

/**
 * sign-auth 支持签名
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnSignAuth
@AutoConfigureAfter({SkynetSecurityAutoConfiguration.class})
@ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "servlet", matchIfMissing = true)
public class SkynetSignAuthAutoConfiguration {

    @Bean
    public FilterRegistrationBean<?> registrationSkynetAuthFilter4Sign(SkynetSignAuthProperties skynetSignAuthProperties,
                                                                       SkynetSecurityMetrics skynetSecurityMetrics) {
        log.info("Init registrationSkynetAuthFilter4Sign.");
        FilterRegistrationBean<?> filterRegistrationBean = new FilterRegistrationBean<>(new AuthFilterBase4Sign(skynetSignAuthProperties, skynetSecurityMetrics));
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setOrder(skynetSignAuthProperties.getFilterOrder());
        return filterRegistrationBean;
    }
}
