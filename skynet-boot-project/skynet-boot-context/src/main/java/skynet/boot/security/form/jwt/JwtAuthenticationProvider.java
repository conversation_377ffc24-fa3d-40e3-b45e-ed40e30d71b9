package skynet.boot.security.form.jwt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.userdetails.UserDetailsService;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.SkynetPasswordEncoder;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

/**
 * 身份验证提供者
 * <p>
 * 用户获取服务
 * 密码加密算法
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtAuthenticationProvider extends DaoAuthenticationProvider {

    public JwtAuthenticationProvider(UserDetailsService userDetailsService,
                                     SkynetEncryption skynetEncryption, SkynetSecurityMetrics metrics) {

        super.setUserDetailsService(userDetailsService);
        super.setPasswordEncoder(new SkynetPasswordEncoder(skynetEncryption, metrics));
    }
}