package skynet.boot.security.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.SkynetPasswordEncoder;
import skynet.boot.security.baseauth.BaseAuthAuthenticationProvider;
import skynet.boot.security.baseauth.BaseAuthUserDetailsService;
import skynet.boot.security.condition.ConditionalOnBaseAuth;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/28 22:22
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBaseAuth
@AutoConfigureAfter({SkynetSecurityAutoConfiguration.class})
@ConditionalOnClass(WebSecurityConfiguration.class)
@EnableWebSecurity
@ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "servlet", matchIfMissing = true)
public class SkynetBaseAuthAutoConfiguration {

    @Bean
    public SkynetHttpBasicConfigurerCustomizer skynetHttpBasicConfigurerCustomizer() {
        return new SkynetHttpBasicConfigurerCustomizer();
    }

    @Bean
    public SecurityFilterChain skynetBaseSecurityFilterChain(SkynetBaseAuthProperties properties, HttpSecurity http,
                                                             SkynetHttpBasicConfigurerCustomizer httpCustomizer,
                                                             SkynetEncryption skynetEncryption, SkynetSecurityMetrics metrics) throws Exception {

        // 创建并配置 BaseAuthAuthenticationProvider
        BaseAuthAuthenticationProvider authProvider = new BaseAuthAuthenticationProvider();
        authProvider.setPasswordEncoder(new SkynetPasswordEncoder(skynetEncryption, metrics));
        authProvider.setUserDetailsService(new BaseAuthUserDetailsService(properties));

        // 注册 AuthenticationProvider 到 HttpSecurity
        http.authenticationProvider(authProvider);

        //未开启  form-auth  将 swagger 设为 base-auth
        if (properties.isEnabled()) {
            log.info("pathPatterns addAll systemPaths");
            properties.getPathPatterns().addAll(SkynetSecurityProperties.SYSTEM_PATHS);
        }

        //待认证路径
        List<String> authPaths = properties.getPathPatterns();
        List<String> ignorePaths = properties.getIgnorePatterns();

        log.info("Set httpBasic authPaths={}", authPaths);
        log.info("Set httpBasic ignorePaths={}", ignorePaths);

        // csrf配置
        http.csrf(AbstractHttpConfigurer::disable);
        http.httpBasic(httpCustomizer);

        if (authPaths.isEmpty()) {
            http.authorizeHttpRequests(authorizeHttpRequests -> authorizeHttpRequests
                    .anyRequest().permitAll());
            return http.build();
        }

        if (ignorePaths.isEmpty()) {
            http.authorizeHttpRequests(authorizeHttpRequests -> authorizeHttpRequests
                    //需要鉴权的 Patterns
                    .requestMatchers(authPaths.toArray(new String[0])).authenticated()
                    .anyRequest().permitAll());
        } else {
            http.authorizeHttpRequests(authorizeHttpRequests -> authorizeHttpRequests
                    //忽略的 Patterns
                    .requestMatchers(ignorePaths.toArray(new String[0])).permitAll()
                    //需要鉴权的 Patterns
                    .requestMatchers(authPaths.toArray(new String[0])).authenticated()
                    .anyRequest().permitAll());
        }

        return http.build();
    }

}
