package skynet.boot.security.metrics;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

/**
 * 度量指标采集
 *
 * <AUTHOR> by jianwu6 on 2020/5/27 18:31
 */
@Slf4j
public class SkynetSecurityMetrics4Prometheus implements SkynetSecurityMetrics {


    private final MeterRegistry registry;

    public SkynetSecurityMetrics4Prometheus(MeterRegistry registry) {
        this.registry = registry;
    }

    /**
     * 统计客户端签名非法请求数量
     *
     * @param ip 客户端IP
     */
    @Override
    public void authFail(String type, String ip) {
        try {
            log.debug("authMetrics, type={}; ip={}", type, ip);
            Counter counter = this.registry.counter("skynet.security.auth-fail.count", "type", type, "ip", ip);
            counter.increment();
        } catch (Throwable e) {
            log.error("counterIncrement{} error={}", type, e.getMessage());
        }
    }
}
