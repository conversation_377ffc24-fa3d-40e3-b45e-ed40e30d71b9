package skynet.boot.security.baseauth;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.www.BasicAuthenticationEntryPoint;
import org.springframework.util.Assert;

import java.io.IOException;

/**
 * 自定义的BasicAuthenticationEntryPoint
 */
public class SkynetBasicAuthenticationEntryPoint extends BasicAuthenticationEntryPoint {
    private String realmName;

    @Override
    public void afterPropertiesSet() {
        this.realmName = "SkynetAuth";
        Assert.hasText(this.realmName, "realmName must be specified");
    }

    /**
     * 自定义请求失败后的响应信息
     *
     * @param request
     * @param response
     * @param authException
     * @throws IOException
     */
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        response.setHeader("WWW-Authenticate", "Basic realm=\"" + this.realmName + "\"");
        response.sendError(HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED.getReasonPhrase());

        // response.setCharacterEncoding("UTF-8");
        // response.setContentType("application/json; charset=utf-8");
        // response.addHeader("Access-Control-Allow-Origin", "*");
        // //接口返回json
        // PrintWriter pw = response.getWriter();
        // String responseText = "{\"code\":401,\"data\":\"Unauthorized\"}".toString();
        // pw.print(responseText);
        // //关闭输出流
        // pw.close();
    }


}
