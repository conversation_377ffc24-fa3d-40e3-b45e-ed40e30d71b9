package skynet.boot.security.config;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/28 20:34
 */
@Getter
@Setter
@Accessors(chain = true)
public class SkynetBaseAuthProperties extends Jsonable implements SkynetAuthProperties {

    public SkynetBaseAuthProperties() {
        this.actuatorIgnoreEndpoints.addAll(Arrays.asList("health", "refresh"));
    }

    /**
     * 是否开启BaseAuth验证
     */
    @JSONField(ordinal = 10)
    private boolean enabled = false;

    /**
     * 需要鉴权的 ant Pattern
     */
    @JSONField(ordinal = 20)
    private List<String> pathPatterns = new ArrayList<>(0);

    /**
     * 忽略的 Patterns ，一般是 pathPatterns 中的子集
     */
    @JSONField(ordinal = 30)
    private List<String> ignorePatterns = new ArrayList<>(0);

    /**
     * 开启Actuator 安全权限
     */
    @JSONField(ordinal = 40)
    private boolean actuatorSecurityEnabled = true;

    /**
     * 忽略的 actuator 端点
     */
    @JSONField(ordinal = 50)
    private List<String> actuatorIgnoreEndpoints = new ArrayList<>();

    /**
     * 密码重复验证错误，锁定周期
     */
    @JSONField(ordinal = 60)
    private int failLockDurationSecond = 3 * 60;

    /**
     * 密码验证错误 重试次数，默认5次
     */
    @JSONField(ordinal = 70)
    private int failTriesTimes = 5;

    /**
     * 用户
     */
    @JSONField(ordinal = 80)
    private User user = new User();

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class User extends Jsonable {

        /**
         * Default user name.
         */
        private String name = "user";

        /**
         * Password for the default user name.
         */
        private String password = "";
    }
}
