package skynet.boot.security.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.firewall.StrictHttpFirewall;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.condition.ConditionalOnFormAuth;
import skynet.boot.security.form.DefaultUserDetailsService;
import skynet.boot.security.form.SkynetFormAuthenticationEntryPoint;
import skynet.boot.security.form.jwt.JwtAuthenticationFilter;
import skynet.boot.security.form.jwt.JwtAuthenticationProvider;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * TODO: 表单认证
 * @date 2022/5/28 22:22
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnFormAuth
@ConditionalOnClass(WebSecurityConfiguration.class)
@EnableWebSecurity
@Import({
        skynet.boot.security.form.controller.SkynetLoginController.class,
        skynet.boot.security.form.controller.SkynetSecurityAuthController.class
})
@AutoConfigureAfter({SkynetSecurityAutoConfiguration.class})
@ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "servlet", matchIfMissing = true)
public class SkynetFormAuthAutoConfiguration {

    @Bean
    public StrictHttpFirewall strictHttpFirewall() {
        return new StrictHttpFirewall();
    }

    @Bean
    @ConditionalOnMissingBean(UserDetailsService.class)
    public UserDetailsService defaultUserDetailsService(SkynetFormAuthProperties skynetFormAuthProperties) {
        return new DefaultUserDetailsService(skynetFormAuthProperties.getUser());
    }

    //    @Bean
//    public JwtAuthenticationProvider jwtAuthenticationProvider(UserDetailsService userDetailsService,
//                                                               SkynetEncryption skynetEncryption, SkynetSecurityMetrics metrics) {
//        log.info("build JwtAuthenticationProvider.");
//        return new JwtAuthenticationProvider(userDetailsService, skynetEncryption, metrics);
//    }
//
//    @Bean
//    public AuthenticationManager authenticationManager(JwtAuthenticationProvider jwtAuthenticationProvider) {
//        return new ProviderManager(jwtAuthenticationProvider);
//    }

    @Bean
    public AuthenticationManager authenticationManager(UserDetailsService userDetailsService,
                                                       SkynetEncryption skynetEncryption, SkynetSecurityMetrics metrics) {
        return new ProviderManager(new JwtAuthenticationProvider(userDetailsService, skynetEncryption, metrics));
    }


    @Bean
    public SkynetFormLoginConfigurerCustomizer skynetFormLoginConfigurerCustomizer() {
        return new SkynetFormLoginConfigurerCustomizer();
    }

    /**
     * form认证配置
     *
     * @param properties
     * @param http
     * @return SecurityFilterChain
     * @throws Exception
     */
    @Bean
    @Order(1)
    public SecurityFilterChain securityFilterChain(SkynetFormAuthProperties properties,
                                                   SkynetSignAuthProperties signAuthProperties,
                                                   HttpSecurity http,
                                                   AuthenticationManager authenticationManager) throws Exception {


        //未开启  form-auth  将 swagger 设为 base-auth
        if (properties.isEnabled()) {
            //优先采用 form 表单验证
            properties.getPathPatterns().addAll(SkynetSecurityProperties.SYSTEM_PATHS);
        }

        //Form-Auth 忽略 sign-auth 授权的 path，不用拦截
        if (signAuthProperties.isEnabled()) {
            properties.getIgnorePatterns().addAll(signAuthProperties.getPathPatterns());
        }

        List<String> authPaths = new ArrayList<>(properties.getPathPatterns());
        log.info("Set httpForm authPaths={}", authPaths);

        // csrf配置
        http.csrf(AbstractHttpConfigurer::disable);
        //禁用 X-Frame-Options 响应头
//        http.headers(HeadersConfigurer)
        //Session状态
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        //异常处理
        http.exceptionHandling((exceptions) -> exceptions
                .authenticationEntryPoint(new SkynetFormAuthenticationEntryPoint(properties)));
        http.formLogin(Customizer.withDefaults());
        if (!authPaths.isEmpty()) {
            http.authorizeHttpRequests(authorizeHttpRequests -> authorizeHttpRequests
                    //需要鉴权的 Patterns
                    .requestMatchers(authPaths.toArray(new String[0])).authenticated()
                    .anyRequest().permitAll());
        } else {
            http.authorizeHttpRequests(authorize -> authorize
                    .anyRequest().permitAll());
        }
        http.addFilterBefore(new JwtAuthenticationFilter(authenticationManager, properties), UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    /**
     * 配置WebSecurity
     *
     * @param properties
     * @return
     */
    @Bean
    public WebSecurityCustomizer webSecurityCustomizer(SkynetFormAuthProperties properties) {
        List<String> ignorePatterns = getIgnorePatterns(properties);
        return web -> web.ignoring().requestMatchers(ignorePatterns.toArray(new String[ignorePatterns.size()]));
    }

    /**
     * 忽略的 Patterns 处理
     *
     * @param properties
     * @return
     */
    private List<String> getIgnorePatterns(SkynetFormAuthProperties properties) {
        List<String> ignorePatterns = properties.getIgnorePatterns();

        //排除登录页面
        ignorePatterns.add(properties.getLoginPath());
        //默认登录页面也添加
        ignorePatterns.add(String.format("%s", SkynetFormAuthProperties.DEFAULT_LOGIN_PAGE));
        // spring boot 3 开始不支持 `/**/**.js` 这种格式的 pattern，`**` 否则会报
        // No more pattern data allowed after {*...} or ** pattern element 错误
        ignorePatterns.addAll(Arrays.asList("/skynet/js/**", "/skynet/login.css", "/skynet/img/**", "/skynet/fav.ico"
                , "/skynet/security/publicKey"
                , "/skynet/security/login"
                , "/skynet/security/logout"));

        return ignorePatterns;
    }
}