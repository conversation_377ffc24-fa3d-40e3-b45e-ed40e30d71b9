package skynet.boot.security.form.data;

/**
 * <AUTHOR>
 * @date 2022/6/18 20:19
 */

public enum AuthResult {
    /**
     * 成功
     */
    SUCCESS("SUCCESS", 1),
    /**
     * 无权限
     */
    NOAUTH("Unauthorized", 401),

    /**
     * 认证过期
     */
    AUTH_TIMEOUT("Auth Timeout", 3),

    /**
     * token 头不正确
     */
    INVALID_HEADER("The token header is incorrect", 4),
    /**
     * 校验失败
     */
    CHECK_ERROR("Verification Failed", 5);

    private final String message;
    private final int code;

    AuthResult(String message, int code) {
        this.message = message;
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public int getCode() {
        return code;
    }
}
