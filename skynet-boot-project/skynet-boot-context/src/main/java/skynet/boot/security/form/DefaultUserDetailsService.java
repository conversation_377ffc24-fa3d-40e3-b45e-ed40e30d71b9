package skynet.boot.security.form;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.util.StringUtils;
import skynet.boot.security.config.SkynetFormAuthProperties;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/18 20:13
 */
public class DefaultUserDetailsService implements UserDetailsService {

    private final SkynetFormAuthProperties.User user;

    public DefaultUserDetailsService(SkynetFormAuthProperties.User user) {
        this.user = user;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        if (StringUtils.hasText(username) && username.equalsIgnoreCase(user.getName())) {
            Set<String> permissions = Collections.emptySet();
            List<GrantedAuthority> grantedAuthorities = permissions.stream().map(GrantedAuthorityImpl::new).collect(Collectors.toList());
            return new org.springframework.security.core.userdetails.User(username, user.getPassword(), grantedAuthorities);
        }
        throw new UsernameNotFoundException("The user does not exist[" + username + "]");
    }
}
