package skynet.boot.security.form.controller;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.common.domain.SkynetResponse;
import skynet.boot.security.condition.ConditionalOnFormAuth;
import skynet.boot.security.condition.ConditionalOnFormLogin;
import skynet.boot.security.config.SkynetFormAuthProperties;
import skynet.boot.security.form.data.AccessToken;
import skynet.boot.security.form.jwt.JwtSecurityUtils;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 用户登录管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@EnableSkynetSwagger2
@ConditionalOnFormAuth
@ConditionalOnFormLogin
@RequestMapping(value = "/skynet/security", produces = {MediaType.APPLICATION_JSON_VALUE})
public class SkynetSecurityAuthController {

    private final SkynetFormAuthProperties authProperties;
    private final AuthenticationManager authenticationManager;
    private final UserDetailsService userDetailsService;
    private final Cache<String, Integer> authFailCache;

    public SkynetSecurityAuthController(AuthenticationManager authenticationManager,
                                        SkynetFormAuthProperties skynetFormAuthProperties,
                                        UserDetailsService userDetailsService) throws Exception {

        this.authProperties = skynetFormAuthProperties;
        this.authenticationManager = authenticationManager;
        this.userDetailsService = userDetailsService;
        this.authFailCache = CacheBuilder.newBuilder().maximumSize(16).expireAfterAccess(
                authProperties.getFailLockDurationSecond(), TimeUnit.SECONDS).concurrencyLevel(5).recordStats().build();
    }

    @PostMapping("/login")
    public SkynetResponse<AccessToken> login(@RequestBody SkynetLoginInfo skynetLoginInfo,
                                             HttpServletRequest request, HttpServletResponse response) {
        //用户错误达到指定次数，拒绝验证
        String key = request.getRemoteAddr();
        Integer times = authFailCache.getIfPresent(key);

        try {
            if (times != null && times >= authProperties.getFailTriesTimes()) {
                throw new LockedException(String.format("Verification failed %s times, will lock for %s seconds", authProperties.getFailTriesTimes(), authProperties.getFailLockDurationSecond()));
            }

            String token = JwtSecurityUtils.login(response, this.authenticationManager, skynetLoginInfo, authProperties);
            if (StringUtils.hasText(token)) {
                authFailCache.invalidate(key);
            }
            AccessToken accessToken = new AccessToken(token);
            log.debug("token= {}", accessToken);
            return SkynetResponse.success(accessToken);
        } catch (LockedException e) {
            return SkynetResponse.fail(HttpStatus.UNAUTHORIZED.value(), e.getMessage());
        } catch (BadCredentialsException e) {
            //用户错误达到指定次数，拒绝验证
            log.debug("auth-err, key={};times={}", key, times);
            authFailCache.put(key, times == null ? 1 : ++times);
            return SkynetResponse.fail(HttpStatus.UNAUTHORIZED.value(), "Login failed, incorrect username or password");
        }
    }

    /**************************************************************************************************/
    @GetMapping("/logout")
    public SkynetResponse<String> logout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.debug("Logout..");
        JwtSecurityUtils.setCookie(response, null, authProperties);
        HttpSession session = request.getSession();
        if (null != session) {
            session.removeAttribute(authProperties.getJwtTokenSessionKey());
            session.invalidate();
        }
        String accept = request.getHeader("accept");
        if (org.springframework.util.StringUtils.hasText(accept) && accept.toLowerCase().contains("text/html")) {
            //浏览器过来的请求
            String location = authProperties.getFullLoginPage();
            response.sendRedirect(location);
            log.debug("SendRedirect={}", location);
        }
        return SkynetResponse.success("logout ok.");
    }


    /**
     * 获取 缺省用户 token ，用于免登陆
     * <p>
     * 此接口 需要利用 skynet-boot-auth 鉴权访问
     *
     * @return
     */
    @GetMapping("/token")
    public SkynetResponse<AccessToken> token() {
        String token = JwtSecurityUtils.createToken(userDetailsService, "admin", authProperties);
        AccessToken accessToken = new AccessToken(token);
        log.debug("token= {}", accessToken);
        return SkynetResponse.success(accessToken);
    }

    /**
     * 检查登录
     *
     * @return
     */
    @GetMapping("/check")
    public SkynetResponse<String> check() {
        log.debug("check..");
        return SkynetResponse.success("check ok.");
    }

    @GetMapping("/refresh")
    public SkynetResponse<String> refresh(HttpServletRequest request) {
        log.debug("refresh token..");
        String token = (String) request.getSession().getAttribute(authProperties.getJwtTokenSessionKey());
        return SkynetResponse.success(token);
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SkynetLoginInfo extends Jsonable {
        private String username;
        private String password;

        @Override
        public String toString() {
            return super.toString();
        }
    }
}
