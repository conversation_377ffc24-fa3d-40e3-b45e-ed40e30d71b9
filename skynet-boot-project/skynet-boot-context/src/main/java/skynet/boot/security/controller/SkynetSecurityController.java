package skynet.boot.security.controller;

import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.condition.ConditionalOnSecurity;

/**
 * skynet 安全 加解密
 *
 * <AUTHOR>
 * @date 2022/5/28 20:28
 */
@RestController
@RequestMapping("/skynet/security")
@ExposeSwagger2
@ConditionalOnSecurity
public class SkynetSecurityController {
    private final SkynetEncryption skynetEncryption;

    public SkynetSecurityController(SkynetEncryption skynetEncryption) {
        this.skynetEncryption = skynetEncryption;
    }

    /**
     * 获取公钥
     *
     * @return
     */
    @GetMapping(value = "/publicKey")
    public String fetchPublicKey() {
        return skynetEncryption.getPublicKey();
    }


    /**
     * 解密
     *
     * @param base64Data
     * @return
     * @throws Exception
     */
    @PostMapping({"decrypt"})
    public String decrypt(@RequestBody String base64Data) throws Exception {
        return skynetEncryption.decrypt(base64Data.trim());
    }

    /**
     * 加密
     *
     * @param data
     * @return
     * @throws Exception
     */
    @PostMapping({"encrypt"})
    public String encrypt(@RequestBody String data) throws Exception {
        return skynetEncryption.encrypt(data.trim());
    }
}
