package skynet.boot.security.config;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.time.Duration;

/**
 * <pre>
 *     skynet.security.auth-client 配置项
 * </pre>
 *
 * <AUTHOR> 2022年05月20日22:58:04
 */
@Getter
@Setter
@Accessors(chain = true)
public class SkynetAuthClientProperties extends Jsonable {

    /**
     * signAuth apiKey;
     */
    private String apiKey;
    /**
     * signAuth apiSecret
     */
    private String apiSecret;

    /**
     * 请求超时时间
     */
    private Duration timeout = Duration.ofSeconds(5);

    /**
     * BaseAuth user
     */
    private String user;
    /**
     * BaseAuth password
     */
    private String password;
}
