package skynet.boot.security.client;

import lombok.Getter;
import lombok.Setter;
import okhttp3.Interceptor;
import skynet.boot.common.domain.Jsonable;

import java.util.List;

@Getter
@Setter
public class SignAuthOkHttpClientProperties extends Jsonable {

    /**
     * 接口认证
     */
    private String apiKey;
    private String apiSecret;

    /**
     * 超时时间
     */
    private long timeout = 5 * 1000;

    /**
     * 连接超时时间
     */
    private long connectTimeout = 5 * 1000;

    /**
     * 自定义接口拦截器
     */
    private List<Interceptor> interceptors;

    /**
     * 线程池最大空闲连接
     */
    private int maxIdleConnections = 256;

    /**
     * 连接池中连接的最大保留时间
     */
    private int keepAliveDurationMills = 5 * 60 * 1000;

    /**
     * 最大并发请求数
     */
    private int maxRequests = 1024;

    /**
     * 单个主机最大请求并发数
     */
    private int maxRequestPerHost = 256;
}
