package skynet.boot.security.client;

import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.util.TimeValue;
import org.apache.hc.core5.util.Timeout;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.MediaType;
import org.springframework.http.client.*;
import org.springframework.http.client.support.HttpRequestWrapper;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.config.SkynetAuthClientProperties;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * SignAuth & baseAuth RestTemplate Builder
 *
 * <AUTHOR>
 * @date 2022年06月24日23:25:06
 */
@Slf4j
public class AuthRestTemplateBuilder {

    private final RestTemplateBuilder builder;

    public AuthRestTemplateBuilder(RestTemplateBuilder builder) {
        this.builder = builder;
    }

    public RestTemplate build(SkynetAuthClientProperties properties) {
        log.debug("skynetAuthClientProperties={}", properties);
        Assert.notNull(properties, "The skynetAuthClientProperties is empty.");
//                Assert.hasText(properties.getApiKey(), "The skynetAuthClientProperties.getApiKey() is empty.");
//        Assert.hasText(properties.getApiSecret(), "The skynetAuthClientProperties.getApiSecret() is empty.");
//        Assert.hasText(properties.getUser(), "The skynetAuthClientProperties.getUser() is empty.");
//        Assert.hasText(properties.getPassword(), "The skynetAuthClientProperties.getPassword() is empty.");

        RestTemplate restTemplate = builder.build();
        ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient(properties.getTimeout()));
        restTemplate.setRequestFactory(requestFactory);
        restTemplate.getInterceptors().add(new SignAuthRestTemplateInterceptor(properties));
        return restTemplate;
    }

    /**
     * Apache HttpClient
     *
     * @return
     */
    public static HttpClient httpClient(Duration timeout) {
        // 支持HTTP、HTTPS

        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", SSLConnectionSocketFactory.getSocketFactory())
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(100);
        connectionManager.setValidateAfterInactivity(TimeValue.of(2000, TimeUnit.MILLISECONDS));


        RequestConfig requestConfig = RequestConfig.custom()
                // 服务器返回数据(response)的时间，超时抛出read timeout
                .setResponseTimeout(Timeout.of(timeout))
                // 连接上服务器(握手成功)的时间，超时抛出connect timeout
                .setConnectTimeout(Timeout.of(timeout))
                // 从连接池中获取连接的超时时间，超时抛出ConnectionPoolTimeoutException
                .setConnectionRequestTimeout(Timeout.ofSeconds(1))
                .build();
        return HttpClientBuilder.create().setDefaultRequestConfig(requestConfig).setConnectionManager(connectionManager).build();
    }

    static class SignAuthRestTemplateInterceptor implements ClientHttpRequestInterceptor {

        private final SkynetAuthClientProperties properties;

        public SignAuthRestTemplateInterceptor(SkynetAuthClientProperties properties) {
            this.properties = properties;
        }

        @NotNull
        @Override
        public ClientHttpResponse intercept(HttpRequest httpRequest, @NotNull byte[] bytes,
                                            @NotNull ClientHttpRequestExecution clientHttpRequestExecution) throws IOException {
            log.debug("Add base header for url={} ..", httpRequest.getURI());
            if (StringUtils.hasText(properties.getUser()) && StringUtils.hasText(properties.getPassword())) {
                String auth = genAuth(properties.getUser(), properties.getPassword());
                httpRequest.getHeaders().add(HttpHeaders.AUTHORIZATION, auth);
                if (httpRequest.getHeaders().getContentType() == null) {
                    httpRequest.getHeaders().setContentType(MediaType.APPLICATION_JSON);
                }
            }
            return clientHttpRequestExecution.execute(new MyHttpRequestWrapper(httpRequest), bytes);
        }

        /**
         * 生成Basic认证方式的HTTP HEADER
         */
        private static String genAuth(String user, String pwd) {
            return "Basic " + Base64.getEncoder().encodeToString(String.format("%s:%s", user, pwd).getBytes());
        }

        private class MyHttpRequestWrapper extends HttpRequestWrapper {
            public MyHttpRequestWrapper(HttpRequest request) {
                super(request);
            }

            @NotNull
            @Override
            public URI getURI() {
                if (StringUtils.hasText(properties.getApiKey()) && StringUtils.hasText(properties.getApiSecret())) {
                    String uri = AuthUtils.assembleRequestUrl(super.getMethod(), super.getURI().toString(), properties.getApiKey(), properties.getApiSecret());
                    log.debug("uri with auth = {}", uri);
                    try {
                        return new URI(uri);
                    } catch (URISyntaxException e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    return super.getURI();
                }
            }
        }
    }
}
