package skynet.boot.security.form.jwt;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import skynet.boot.security.form.GrantedAuthorityImpl;

import javax.crypto.SecretKey;
import java.util.*;

/**
 * JWT工具类 *
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtTokenUtils {

    /**
     * 用户名称
     */
    private static final String USERNAME = Claims.SUBJECT;
    /**
     * 创建时间
     */
    private static final String CREATED = "created";
    /**
     * 权限列表
     */
    private static final String AUTHORITIES = "authorities";

    // 密钥生成（基于安全随机数）
    private static final SecretKey key = Jwts.SIG.HS256.key().build();


    /**
     * 生成令牌
     *
     * @param authentication 用户
     * @return 令牌
     */
    public static String generateToken(Authentication authentication, int expiresSecond) {
        Map<String, Object> claims = new HashMap<>(3);
        claims.put(USERNAME, JwtSecurityUtils.getUsername(authentication));
        claims.put(CREATED, new Date());
        claims.put(AUTHORITIES, authentication.getAuthorities());
        return generateToken(claims, expiresSecond);
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private static String generateToken(Map<String, Object> claims, int expiresSecond) {
        Date expirationDate = new Date(System.currentTimeMillis() + expiresSecond * 1000L);
        log.debug("GenerateToken expiresSecond={}", expiresSecond);
        return Jwts.builder()
                .claims(claims) // 设置自定义声明
                .expiration(expirationDate) // 设置过期时间
                .signWith(key) // 使用 HMAC-SHA256 签名
                .compact(); // 生成最终 Token
    }


    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public static String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }

    /**
     * 根据请求令牌获取登录认证信息
     *
     * @param token 令牌
     * @return 用户名
     */
    public static Authentication getAuthenticationFromToken(String token) {
        Authentication authentication = null;
        // 获取请求携带的令牌
        if (token != null) {
            // 请求令牌不能为空
            if (JwtSecurityUtils.getAuthentication() == null) {
                // 上下文中Authentication为空
                Claims claims = getClaimsFromToken(token);
                if (claims == null) {
                    return null;
                }
                String username = claims.getSubject();
                if (username == null) {
                    return null;
                }
                if (isTokenExpired(token)) {
                    return null;
                }
                Object authors = claims.get(AUTHORITIES);
                List<GrantedAuthority> authorities = new ArrayList<>(0);
                if (authors instanceof List) {
                    for (Object object : (List) authors) {
                        authorities.add(new GrantedAuthorityImpl((String) ((Map<?, ?>) object).get("authority")));
                    }
                }
                authentication = new UsernamePasswordAuthenticationToken(username, null, authorities);
            } else {
                if (validateToken(token, JwtSecurityUtils.getUsername())) {
                    // 如果上下文中Authentication非空，且请求令牌合法，直接返回当前登录认证信息
                    authentication = JwtSecurityUtils.getAuthentication();
                }
            }
        }
        return authentication;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private static Claims getClaimsFromToken(String token) {

        try {
            return Jwts.parser()
                    .verifyWith(key) // 验证签名密钥
                    .build()
                    .parseSignedClaims(token)
                    .getPayload(); // 获取声明内容
        } catch (ExpiredJwtException e) {
            log.debug("Token 已过期");
            throw e;
        } catch (JwtException e) {
            log.debug("Token 无效或签名错误");
            throw e;
        }
    }


    /**
     * 验证令牌
     *
     * @param token
     * @param username
     * @return
     */
    public static Boolean validateToken(String token, String username) {
        String userName = getUsernameFromToken(token);
        return (userName.equals(username) && !isTokenExpired(token));
    }

    /**
     * 刷新令牌
     *
     * @param token
     * @return
     */
    public static String refreshToken(String token, int expiresSecond) {
        String refreshedToken;
        try {
            Claims claims = getClaimsFromToken(token);
            // 在最新版本中 claims 是只读的，需要另创建一个 map 才能修改
            Map<String, Object> claimsMap = new HashMap<>(claims);
            claimsMap.put(CREATED, new Date());
            refreshedToken = generateToken(claimsMap, expiresSecond);
        } catch (Throwable e) {
            refreshedToken = null;
        }
        return refreshedToken;
    }

    /**
     * 判断令牌是否过期
     *
     * @param token 令牌
     * @return 是否过期
     */
    public static Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Throwable e) {
            return false;
        }
    }
}