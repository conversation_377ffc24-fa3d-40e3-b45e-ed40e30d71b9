package skynet.boot.security.config;

import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.FormLoginConfigurer;

public class SkynetFormLoginConfigurerCustomizer implements Customizer<FormLoginConfigurer<HttpSecurity>> {

    @Override
    public void customize(FormLoginConfigurer<HttpSecurity> httpSecurityFormLoginConfigurer) {
        httpSecurityFormLoginConfigurer
                //默认为username password,可以在这里进行修改
                .usernameParameter("username")
                .passwordParameter("password")
        ;
    }
}
