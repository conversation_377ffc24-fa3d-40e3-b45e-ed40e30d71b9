package skynet.boot.security.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import skynet.boot.security.SkynetEncryption;
import skynet.boot.security.condition.ConditionalOnSecurity;
import skynet.boot.security.controller.SkynetSecurityController;
import skynet.boot.security.controller.SkynetSecurityEndpoint;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/28 22:22
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@Import({
        SkynetSecurityController.class,
})
@ConditionalOnSecurity
public class SkynetSecurityPropertiesAutoConfiguration {

    @Bean
    @ConfigurationProperties("skynet.security")
    public SkynetSecurityProperties skynetSecurityProperties() {
        return new SkynetSecurityProperties();
    }

    @Bean
    @ConfigurationProperties("skynet.security.sign-auth")
    public SkynetSignAuthProperties skynetSignAuthProperties() {
        return new SkynetSignAuthProperties();
    }

    @Bean
    @ConfigurationProperties("skynet.security.ip-auth")
    public SkynetIPAuthProperties skynetIpAuthProperties() {
        return new SkynetIPAuthProperties();
    }

    @Bean
    @ConfigurationProperties("skynet.security.base-auth")
    public SkynetBaseAuthProperties skynetBaseAuthProperties() {
        return new SkynetBaseAuthProperties();
    }

    @Bean
    @ConfigurationProperties("skynet.security.form-auth")
    public SkynetFormAuthProperties skynetFormAuthProperties(ServerProperties serverProperties) {
        return new SkynetFormAuthProperties(serverProperties);
    }

    @Bean
    public SkynetSecurityEndpoint skynetSecurityEndpoint(List<SkynetAuthProperties> properties) {
        return new SkynetSecurityEndpoint(properties);
    }

    @Bean
    public SkynetEncryption skynetRsaKeyManager(ApplicationContext context,
                                                @Qualifier("skynetSecurityProperties") SkynetSecurityProperties properties) throws Exception {
        return new SkynetEncryption(context, properties);
    }
}