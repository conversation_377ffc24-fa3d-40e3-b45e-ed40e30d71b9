package skynet.boot.security.config;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.MapPropertySource;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 * 没有使用skynet.security form-auth 和 base-auth，就自动排除 SecurityAutoConfiguration
 *
 * </pre>
 *
 * <AUTHOR>
 * @date 2022年06月19日11:34:47
 */
@Slf4j
public class ExcludeSecurityApplicationContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    private static volatile boolean isFirst = true;

    @Override
    public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {

        if (isFirst) {
            isFirst = false;
            return;
        }

        String exclude = applicationContext.getEnvironment().getProperty("skynet.security.enabled", "true");
        if ("true".equalsIgnoreCase(exclude)) {
            exclude = applicationContext.getEnvironment().getProperty("spring.autoconfigure.exclude", "");
            exclude = exclude + (StringUtils.hasText(exclude) ? "," : "")
                    + "org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration"
                    + ",org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration";

            Map<String, Object> map = new HashMap<>();
            map.put("spring.autoconfigure.exclude", exclude);
            map.put("auto.config.form.skynet.class", this.getClass().toString());
            applicationContext.getEnvironment().getPropertySources().addFirst(new MapPropertySource("SkynetExcludeSecurityPropertySource", map));
            log.info("Config skynetExcludeSecurityPropertySource spring.autoconfigure.exclude={}", exclude);
        }
    }
}