package skynet.boot.security.baseauth;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.util.StringUtils;
import skynet.boot.security.config.SkynetBaseAuthProperties;
import skynet.boot.security.form.GrantedAuthorityImpl;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Skynet BaseAuth 自定义认证逻辑
 */
public class BaseAuthUserDetailsService implements UserDetailsService {

    private SkynetBaseAuthProperties.User user;

    public BaseAuthUserDetailsService(SkynetBaseAuthProperties baseProperties) {
        this.user = baseProperties.getUser();
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        if (StringUtils.hasText(username) && username.equalsIgnoreCase(user.getName())) {
            Set<String> permissions = Collections.emptySet();
            List<GrantedAuthority> grantedAuthorities = permissions.stream().map(GrantedAuthorityImpl::new)
                    .collect(Collectors.toList());
            return new org.springframework.security.core.userdetails.User(username, user.getPassword(),
                    grantedAuthorities);
        }
        throw new UsernameNotFoundException("The user does not exist[" + username + "]");
    }

}
