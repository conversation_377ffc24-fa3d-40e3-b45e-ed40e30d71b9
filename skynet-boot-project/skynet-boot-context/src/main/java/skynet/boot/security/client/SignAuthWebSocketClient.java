package skynet.boot.security.client;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.Assert;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.WebSocketConnectionManager;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import skynet.boot.security.auth.AuthUtils;

import java.io.IOException;
import java.net.URISyntaxException;

/**
 * SignAuthWebSocketClient
 *
 * <AUTHOR>
 */
@Slf4j
public class SignAuthWebSocketClient implements AutoCloseable {

    private final String apiKey;
    private final String apiSecret;
    private WebSocketConnectionManager webSocketConnectionManager;
    private MyHandler myHandler;

    public SignAuthWebSocketClient(String apiKey, String apiSecret) {
        Assert.hasText(apiKey, "The apiKey is empty.");
        Assert.hasText(apiSecret, "The apiSecret is empty.");
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
    }

    public void connect(String uri, SignAuthWebSocketMessageHandler signAuthWebSocketMessageHandler) throws URISyntaxException {
        log.debug("uri={}", uri);

        uri = AuthUtils.assembleRequestUrl(uri, apiKey, apiSecret);
        log.debug("auth url={}", uri);
        StandardWebSocketClient client = new StandardWebSocketClient();
        this.myHandler = new MyHandler(signAuthWebSocketMessageHandler);
        webSocketConnectionManager = new WebSocketConnectionManager(client, myHandler, uri);
        webSocketConnectionManager.start();
        log.debug("webSocketClient.connect()");
    }

    public void send(String message) throws IOException {
        log.debug("send message={}", message);
        myHandler.send(message);
    }

    @Override
    public void close() throws Exception {
        if (this.webSocketConnectionManager != null) {
            this.webSocketConnectionManager.stop();
            this.webSocketConnectionManager = null;
        }
    }

    static class MyHandler extends TextWebSocketHandler {

        private final SignAuthWebSocketMessageHandler SignAuthWebSocketMessageHandler;
        WebSocketSession webSocketSession;

        MyHandler(SignAuthWebSocketMessageHandler signAuthWebSocketMessageHandler) {
            this.SignAuthWebSocketMessageHandler = signAuthWebSocketMessageHandler;
        }

        @Override
        public void afterConnectionEstablished(WebSocketSession session) throws Exception {
            this.SignAuthWebSocketMessageHandler.onOpen();
            this.webSocketSession = session;
            super.afterConnectionEstablished(session);
        }

        @Override
        protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
            log.debug("receive: {}", message.getPayload());
            this.SignAuthWebSocketMessageHandler.onMessage(message.getPayload());
            //  super.handleTextMessage(session, message);
        }

        public void send(String message) throws IOException {
            webSocketSession.sendMessage(new TextMessage(message));
        }

        @Override
        public void handleTransportError(@NotNull WebSocketSession session, Throwable exception) throws Exception {
            this.SignAuthWebSocketMessageHandler.onError(exception);
        }

        @Override
        public void afterConnectionClosed(@NotNull WebSocketSession session, CloseStatus status) throws Exception {
            this.SignAuthWebSocketMessageHandler.onClose(status.getCode(), status.getReason(), true);
        }
    }
}