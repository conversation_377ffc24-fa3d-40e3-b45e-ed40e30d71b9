package skynet.boot.security.config;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

/**
 * <pre>
 *     skynet.security.ip-auth 配置项
 * </pre>
 *
 * <AUTHOR> [ 2020/4/29 15:15]
 */
@Getter
@Setter
public class SkynetIPAuthProperties extends SkynetSecurityProperties {

    /**
     * 权限认证过滤器执行排序,default=0
     */
    @JSONField(ordinal = 100)
    private int filterOrder = 0;


    /**
     * 白名单ip列表 优先与 黑名单
     */
    @JSONField(ordinal = 110)
    private String[] whiteIpList = new String[0];

    /**
     * 黑名单ip列表
     */
    @JSONField(ordinal = 120)
    private String[] blackIpList = new String[0];
}
