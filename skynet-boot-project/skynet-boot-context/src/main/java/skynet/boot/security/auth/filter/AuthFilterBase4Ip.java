package skynet.boot.security.auth.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.util.StopWatch;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.auth.exception.SkynetAuthException;
import skynet.boot.security.config.SkynetIPAuthProperties;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

import java.io.IOException;


/**
 * IP黑白名单过滤
 *
 * <AUTHOR> by jianwu6 on 2020/5/26 17:09
 */
@Slf4j
public class AuthFilterBase4Ip extends AuthFilterBase {

    private final SkynetIPAuthProperties authProperties;

    public AuthFilterBase4Ip(SkynetIPAuthProperties authProperties, SkynetSecurityMetrics skynetSecurityMetrics) {
        super(authProperties, skynetSecurityMetrics);
        this.authProperties = authProperties;
    }

    @Override
    void filter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        String ip = request.getRemoteAddr();
        log.debug("BlackWhite Filter start [current request IP={}]...", ip);
        try {
            //白名单 优先于 黑名单
            if (authProperties.getWhiteIpList() != null && authProperties.getWhiteIpList().length > 0) {
                if (isContain(authProperties.getWhiteIpList(), ip)) {
                    log.debug("white ip,[IP={}]", ip);
                    filterChain.doFilter(servletRequest, servletResponse);
                } else {
                    throw new SkynetAuthException(String.format("It is not white ip![IP=%s]", ip), HttpStatus.UNAUTHORIZED);
                }
            }
            if (authProperties.getBlackIpList() != null && authProperties.getBlackIpList().length > 0) {
                if (isContain(authProperties.getBlackIpList(), ip)) {
                    throw new SkynetAuthException(String.format("It is black ip![IP=%s]", ip), HttpStatus.UNAUTHORIZED);
                } else {
                    log.debug("not black ip.[IP={}]", ip);
                    filterChain.doFilter(servletRequest, servletResponse);
                }
            }
            filterChain.doFilter(servletRequest, servletResponse);

        } catch (SkynetAuthException ex) {
            log.warn("BlackWhiteFilter error: {}.[RemoteIP={};RequestUri={}]", ex.getMessage(), ip, request.getRequestURI());
            skynetSecurityMetrics.authFail("ip", ip);
            response.setHeader(AuthUtils.SKYNET_AUTH_ERR_MESSAGE, ex.getMessage());
            response.sendError(ex.getCode(), ex.getMessage());
        } finally {
            log.debug("BlackWhiteFilter end.[cost:{}]", stopWatch);
        }
    }

    private static boolean isContain(String[] ipList, String requestIp) {
        for (String ip : ipList) {
            if (ip.equals(requestIp)) {
                return true;
            }
        }
        return false;
    }

}
