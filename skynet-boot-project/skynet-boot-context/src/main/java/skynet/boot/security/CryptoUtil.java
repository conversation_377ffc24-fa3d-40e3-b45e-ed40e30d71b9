package skynet.boot.security;


import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 加密工具类
 * <p>
 * 提供了基于AES算法的字符串加密和解密功能。使用ECB模式和PKCS5Padding填充方式，
 * 并采用Base64编码存储加密后的数据。该工具类主要用于敏感数据的加密保护。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/25 20:32
 */
@Slf4j
public class CryptoUtil {

    /**
     * 使用AES算法加密文本
     * <p>
     * 该方法使用AES/ECB/PKCS5Padding算法对文本进行加密，并将结果转换为Base64编码的字符串。
     * 加密过程使用UTF-8字符集处理输入和输出。
     * </p>
     *
     * @param plaintext 需要加密的原始文本
     * @param key       加密密钥，应为有效的AES密钥
     * @return 加密后的Base64编码字符串
     * @throws RuntimeException 如果加密过程中发生错误
     */
    public static String encrypt(String plaintext, String key) {
        // 将密钥转换为 SecretKeySpec 对象
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
        // 创建 Cipher 对象
        Cipher cipher = null;
        try {
            cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            // 加密 plaintext
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            // 将加密后的字节数组转换为 Base64 编码的字符串
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解密已加密的文本
     * <p>
     * 该方法使用AES/ECB/PKCS5Padding算法对Base64编码的加密文本进行解密。
     * 解密过程使用UTF-8字符集处理输入和输出。
     * </p>
     *
     * @param ciphertext 需要解密的Base64编码字符串
     * @param key        解密密钥，应与加密时使用的密钥相同
     * @return 解密后的原始文本
     * @throws RuntimeException 如果解密过程中发生错误，如密钥不正确或数据格式不正确
     */
    public static String decrypt(String ciphertext, String key) {
        // 将密钥转换为 SecretKeySpec 对象
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");

        // 创建 Cipher 对象
        Cipher cipher = null;
        try {
            cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            // 将 Base64 编码的字符串转换为字节数组
            byte[] encryptedBytes = Base64.getDecoder().decode(ciphertext);
            // 解密
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            // 将解密后的字节数组转换为字符串
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试加密和解密功能的示例方法
     * <p>
     * 该方法演示了如何使用加密和解密方法进行数据保护。
     * 首先加密一个测试字符串，然后解密并验证结果。
     * </p>
     *
     * @param args 命令行参数（未使用）
     * @throws Exception 如果加密或解密过程中发生错误
     */
    public static void main(String[] args) throws Exception {
        String key = "VISpQS+qpIaNtUc7oC8XFN6g59F6RI**";
        String plaintext = "MifeNx0Sr+3rnkj58AbV";
        String secret = encrypt(plaintext, key);
        System.out.println(plaintext);
        System.out.println(secret);
        System.out.println(decrypt(secret, key));
    }
}