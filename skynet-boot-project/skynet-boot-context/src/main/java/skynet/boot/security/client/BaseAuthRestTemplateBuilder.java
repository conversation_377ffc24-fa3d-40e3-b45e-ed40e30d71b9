package skynet.boot.security.client;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.MediaType;
import org.springframework.http.client.*;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import skynet.boot.security.config.SkynetAuthClientProperties;

import java.io.IOException;
import java.util.Base64;

/**
 * BaseAuth RestTemplate Builder
 *
 * <AUTHOR>
 * @date 2022年05月19日22:29:09
 */
@Slf4j
public class BaseAuthRestTemplateBuilder {

    private final RestTemplateBuilder builder;

    public BaseAuthRestTemplateBuilder(RestTemplateBuilder builder) {
        this.builder = builder;
    }

    public RestTemplate build(SkynetAuthClientProperties properties) {
        log.debug("skynetAuthClientProperties={}", properties);
        Assert.notNull(properties, "The skynetAuthClientProperties is empty.");
        Assert.hasText(properties.getUser(), "The skynetAuthClientProperties.getUser() is empty.");
        Assert.hasText(properties.getPassword(), "The skynetAuthClientProperties.getPassword() is empty.");

        RestTemplate restTemplate = builder.build();
        ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(AuthRestTemplateBuilder.httpClient(properties.getTimeout()));
        restTemplate.setRequestFactory(requestFactory);
        restTemplate.getInterceptors().add(new BaseAuthRestTemplateInterceptor(properties));
        return restTemplate;
    }

    static class BaseAuthRestTemplateInterceptor implements ClientHttpRequestInterceptor {

        private final SkynetAuthClientProperties properties;

        public BaseAuthRestTemplateInterceptor(SkynetAuthClientProperties properties) {
            this.properties = properties;
        }

        @NotNull
        @Override
        public ClientHttpResponse intercept(HttpRequest httpRequest, @NotNull byte[] bytes,
                                            @NotNull ClientHttpRequestExecution clientHttpRequestExecution) throws IOException {
            log.debug("Add base header for url={} ..", httpRequest.getURI());

            String auth = genAuth(properties.getUser(), properties.getPassword());
            httpRequest.getHeaders().add(HttpHeaders.AUTHORIZATION, auth);
            if (httpRequest.getHeaders().getContentType() == null) {
                httpRequest.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            }
            return clientHttpRequestExecution.execute(httpRequest, bytes);
        }

        /**
         * 生成Basic认证方式的HTTP HEADER
         *
         * @param user
         * @param pwd
         * @return
         */
        private String genAuth(String user, String pwd) {
            return "Basic " + Base64.getEncoder().encodeToString(String.format("%s:%s", user, pwd).getBytes());
        }
    }
}
