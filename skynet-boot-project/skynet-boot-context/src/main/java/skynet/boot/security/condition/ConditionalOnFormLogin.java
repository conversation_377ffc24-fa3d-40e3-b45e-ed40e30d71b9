package skynet.boot.security.condition;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.lang.annotation.*;

/**
 * 是否禁用了 default-login，
 * <p>
 * 应用场景： 使用自定义登录页面 和API时，可以将默认的进行关闭
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Inherited
@ConditionalOnSecurity
@ConditionalOnProperty(value = "skynet.security.form-auth.default-login-enabled", matchIfMissing = true)
public @interface ConditionalOnFormLogin {

}
