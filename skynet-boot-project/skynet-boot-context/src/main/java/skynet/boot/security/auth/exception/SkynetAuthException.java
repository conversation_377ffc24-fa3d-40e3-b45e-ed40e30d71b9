package skynet.boot.security.auth.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import skynet.boot.exception.SkynetException;

/**
 * <pre>
 *     认证异常类
 * </pre>
 *
 * <AUTHOR> [ 2020/4/29 20:48]
 */
@Getter
public class SkynetAuthException extends SkynetException {

    private final HttpStatus httpStatus;

    public SkynetAuthException() {
        this("Unauthorized");
    }

    public SkynetAuthException(String message) {
        this(message, HttpStatus.UNAUTHORIZED);
    }

    public SkynetAuthException(String message, HttpStatus httpStatus) {
        super(httpStatus.value(), message);
        this.httpStatus = httpStatus;
    }
}
