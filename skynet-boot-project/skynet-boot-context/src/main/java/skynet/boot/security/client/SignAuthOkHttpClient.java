package skynet.boot.security.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import okio.BufferedSink;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpMethod;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import skynet.boot.security.auth.AuthUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 *  HttpClientBase
 * </pre>
 *
 * <AUTHOR>
 * @date 2019-10-16 13:58
 */
@Slf4j
public class SignAuthOkHttpClient implements AutoCloseable {

    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    private final Map<Boolean, OkHttpClient> okHttpClientMap = new ConcurrentHashMap<>(2);
    private final static int HTTP_OK = 200;
    private final static int HTTP_UNAUTHORIZED = 401;
    private final static int HTTP_FORBIDDEN = 403;
    private final SignAuthOkHttpClientProperties properties;

    public SignAuthOkHttpClient(long timeout) {
        this(null, null, timeout);
    }

    /**
     * @param apiKey
     * @param apiSecret
     * @param timeout   单位： 毫秒
     */
    public SignAuthOkHttpClient(String apiKey, String apiSecret, long timeout) {
        this(apiKey, apiSecret, timeout, Collections.emptyList());
    }

    /**
     * @param apiKey
     * @param apiSecret
     * @param timeout      单位： 毫秒
     * @param interceptors 拦截器
     */
    public SignAuthOkHttpClient(String apiKey, String apiSecret, long timeout, List<Interceptor> interceptors) {
        this.properties = new SignAuthOkHttpClientProperties();
        this.properties.setApiKey(apiKey);
        this.properties.setApiSecret(apiSecret);
        this.properties.setTimeout(timeout);
        this.properties.setInterceptors(interceptors);
    }

    /**
     * @param properties
     */
    public SignAuthOkHttpClient(SignAuthOkHttpClientProperties properties) {
        this.properties = properties;
    }

    public <T> T get(String url, Map<String, String> header, TypeReference<T> type) throws Exception {
        return this.execute(HttpMethod.GET, url, header, null, type);
    }

    public <T> T get(String url, Map<String, String> header, Class<T> clazz) throws Exception {
        return this.execute(HttpMethod.GET, url, header, null, clazz);
    }

    public <T> T post(String url, Map<String, String> header, Object bodyData, TypeReference<T> type) throws Exception {
        return this.execute(HttpMethod.POST, url, header, bodyData, type);
    }

    public <T> T post(String url, Map<String, String> header, Object bodyData, Class<T> clazz) throws Exception {
        return this.execute(HttpMethod.POST, url, header, bodyData, clazz);
    }

    public <T> T delete(String url, Map<String, String> header, Object bodyData, Class<T> clazz) throws Exception {
        return this.execute(HttpMethod.DELETE, url, header, bodyData, clazz);
    }

    public <T> T delete(String url, Map<String, String> header, Object bodyData, TypeReference<T> type) throws Exception {
        return this.execute(HttpMethod.DELETE, url, header, bodyData, type);
    }

    public <T> T put(String url, Map<String, String> header, Object bodyData, Class<T> clazz) throws Exception {
        return this.execute(HttpMethod.PUT, url, header, bodyData, clazz);
    }

    public <T> T put(String url, Map<String, String> header, Object bodyData, TypeReference<T> type) throws Exception {
        return this.execute(HttpMethod.PUT, url, header, bodyData, type);
    }

    public <T> T head(String url, Map<String, String> header, Class<T> clazz) throws Exception {
        return this.execute(HttpMethod.HEAD, url, header, null, clazz);
    }

    public <T> T head(String url, Map<String, String> header, TypeReference<T> type) throws Exception {
        return this.execute(HttpMethod.HEAD, url, header, null, type);
    }


    public <T> T execute(HttpMethod httpMethod, String url, Map<String, String> header, Object bodyData, TypeReference<T> type) throws Exception {
        String body = this.execute(httpMethod.toString(), url, header, bodyData);
        return StringUtils.hasText(body) ? JSON.parseObject(body, type) : null;
    }

    public <T> T execute(HttpMethod httpMethod, String url, Map<String, String> header, Object bodyData, Class<T> clazz) throws Exception {
        String body = this.execute(httpMethod.toString(), url, header, bodyData);
        return StringUtils.hasText(body) ? JSON.parseObject(body, clazz) : null;
    }

    public String execute(HttpMethod httpMethod, String url, Map<String, String> header, Object bodyData) throws Exception {
        return this.execute(httpMethod.toString(), url, header, bodyData);
    }

    public String execute(String httpMethod, String url, Map<String, String> header, Object bodyData) throws Exception {
        try (Response response = executeRequest(httpMethod, url, header, bodyData)) {
            if (response.code() != HTTP_OK) {
                if (response.code() == HTTP_FORBIDDEN || response.code() == HTTP_UNAUTHORIZED) {
                    throw new IOException(String.format("code=%s, message=%s", response.code(), response.header(AuthUtils.SKYNET_AUTH_ERR_MESSAGE)));
                }
                throw new IOException(response.toString());
            }
            assert response.body() != null;
            String body = response.body().string();
            log.debug("Response body:{}", body);
            return body;
        } catch (Throwable e) {
            log.error("Http [{}]{}[timeout={}ms]\terror.Message={}", httpMethod, url, this.properties.getTimeout(), (e instanceof IOException) ? null : e.getMessage());
            throw e;
        }
    }

    /**
     * sse 场景，可以通过回调返回请求
     *
     * @param url
     * @param header
     * @param bodyData
     * @param callback
     * @throws Exception
     * @since 4.0.16
     */
    public void post(String url, Map<String, String> header, Object bodyData, @NotNull Callback callback) throws Exception {
        this.execute(HttpMethod.POST.toString(), url, header, bodyData, callback);
    }

    /**
     * sse 场景，可以通过回调返回请求
     *
     * @param httpMethod
     * @param url
     * @param header
     * @param bodyData
     * @param callback
     * @throws Exception
     * @since 4.0.16
     */
    public void execute(String httpMethod, String url, Map<String, String> header, Object bodyData, @NotNull Callback callback) throws Exception {
        Assert.notNull(callback, "callback is null.");
        Request request = buildRequest(httpMethod, url, header, bodyData);
        getOkHttpClient(url).newCall(request).enqueue(callback);
    }


    public Response executeRequest(String httpMethod, String url, Map<String, String> header, Object bodyData) throws Exception {
        Request request = buildRequest(httpMethod, url, header, bodyData);
        return executeRequest(url, request);
    }

    private static @NotNull Request buildRequest(String httpMethod, String url, Map<String, String> header, Object bodyData) {
        Assert.hasText(httpMethod, "httpMethod is blank.");
        Assert.hasText(url, "url is blank.");
        log.debug("request url= {}", url);
        log.debug("request param= {}", bodyData);

        RequestBody requestBody = bodyData != null ? RequestBody.create(bodyData instanceof String ? ((String) bodyData) : JSON.toJSONString(bodyData), JSON_MEDIA_TYPE) : null;
        Headers headers = Headers.of(header == null ? Collections.EMPTY_MAP : header);
        return new Request.Builder().method(httpMethod, requestBody).headers(headers).url(url).build();
    }


    public Response executeRequest(String url, Request request) throws Exception {
        Response response = getOkHttpClient(url).newCall(request).execute();
        if (log.isDebugEnabled()) {
            log.debug("response= {}", JSON.toJSONString(response));
        }
        return response;
    }


    @Override
    public void close() throws Exception {
        log.debug("Close OkHttpClient");
        try {
            for (OkHttpClient okHttpClient : okHttpClientMap.values()) {
                okHttpClient.dispatcher().executorService().shutdown();
                okHttpClient.connectionPool().evictAll();
            }
            okHttpClientMap.clear();
        } catch (Throwable e) {
            log.error("Close okHttpClient error= {}", e.getMessage());
        }
    }

    public synchronized OkHttpClient getOkHttpClient(String url) {
        boolean isSsl = url.toLowerCase().startsWith("https://");
        return okHttpClientMap.computeIfAbsent(isSsl, this::buildHttpClient);
    }

    private OkHttpClient buildHttpClient(boolean ssl) {

        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(this.properties.getMaxRequests()); // 设置最大并发请求数
        dispatcher.setMaxRequestsPerHost(this.properties.getMaxRequestPerHost()); // 设置每个主机的最大并发请求数
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .retryOnConnectionFailure(true)
                .connectionPool(new ConnectionPool(this.properties.getMaxIdleConnections(), this.properties.getKeepAliveDurationMills(), TimeUnit.MILLISECONDS))
                .readTimeout(this.properties.getTimeout(), TimeUnit.MILLISECONDS)
                .connectTimeout(this.properties.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(this.properties.getTimeout(), TimeUnit.MILLISECONDS)
                .dispatcher(dispatcher);

        if (this.properties.getInterceptors() != null) {
            log.debug("Add interceptors, count: {}", this.properties.getInterceptors().size());
            for (Interceptor interceptor : this.properties.getInterceptors()) {
                builder.addInterceptor(interceptor);
            }
        }
        builder.addInterceptor(new AuthOkHttpInterceptor(this.properties.getApiKey(), this.properties.getApiSecret()));
        if (ssl) {
            builder.sslSocketFactory(SslHelper.createSSLSocketFactory(), new SslHelper.TrustAllManager())
                    .hostnameVerifier(new SslHelper.trustAllHostnameVerifier());
        }
        return builder.build();
    }


    /**
     * <pre>
     *      网关鉴权信息拦截器
     * </pre>
     *
     * <AUTHOR> [ 2020/4/29 14:22]
     */
    @Slf4j
    static class AuthOkHttpInterceptor implements Interceptor {
        private final String apiKey;
        private final String apiSecret;

        public AuthOkHttpInterceptor(String apiKey, String apiSecret) {
            this.apiKey = apiKey;
            this.apiSecret = apiSecret;
        }

        @NotNull
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            if (StringUtils.hasText(apiKey) && StringUtils.hasText(apiSecret)) {
                log.debug("Adding auth headers for app: {}", apiKey);
                String body = null;
                if (request.body() != null && request.body().contentLength() > 0) {
                    BufferedSink sink = new Buffer();
                    request.body().writeTo(sink);
                    body = sink.getBuffer().readUtf8();
                }
                String srcUrl = request.url().toString();
                Map<String, String> headers = AuthUtils.assembleAuthorizationHeaders(
                        HttpMethod.valueOf(request.method()), srcUrl, apiKey, apiSecret, body);

                Request.Builder builder = request.newBuilder();
                headers.forEach(builder::addHeader);
                return chain.proceed(builder.build());
            }
            return chain.proceed(request);
        }
    }
}
