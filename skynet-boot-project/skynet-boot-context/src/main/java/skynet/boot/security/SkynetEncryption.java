package skynet.boot.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.util.Assert;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;
import skynet.boot.common.Files;
import skynet.boot.security.config.SkynetSecurityProperties;

import javax.crypto.Cipher;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.Signature;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.stream.Collectors;

/**
 * SkynetEncryption 类提供了基于 RSA 算法的加密、解密、签名和验证功能。
 * 该类通过读取配置文件中的公钥和私钥来初始化，并提供相应的方法进行数据的加密、解密、签名和验证。
 * <p>
 * 功能描述：
 * 1. 加密：使用公钥对数据进行加密。
 * 2. 解密：使用私钥对加密数据进行解密。
 * 3. 签名：使用私钥对数据进行签名。
 * 4. 验证：使用公钥对签名进行验证。
 * <p>
 * 使用注意事项：
 * 1. 确保配置文件中正确配置了 RSA 公钥和私钥文件路径。
 * 2. 加密和解密的数据长度受限于 RSA 密钥长度，通常不超过 117 字节。
 * 3. 签名和验证操作需要确保数据的一致性，否则验证会失败。
 * <p>
 * 调用示例代码：
 * <pre>
 * {@code
 * ApplicationContext context = ...; // 获取 Spring 应用上下文
 * SkynetSecurityProperties properties = ...; // 获取配置属性
 *
 * // 初始化 SkynetEncryption，开启了 skynet安全，直接@Autowired 即可使用
 *
 * SkynetEncryption encryption = new SkynetEncryption(context, properties);
 *
 * // 加密
 * String encryptedText = encryption.encrypt("Hello, World!");
 *
 * // 解密
 * String decryptedText = encryption.decrypt(encryptedText);
 *
 * // 签名
 * String signature = encryption.sign("Hello, World!");
 *
 * // 验证签名
 * boolean isVerified = encryption.verify("Hello, World!", signature);
 * }
 * </pre>
 *
 * <AUTHOR>
 * @date 2022/6/24 11:02
 */

@Slf4j
public class SkynetEncryption {
    private static final String RSA_ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "MD5withRSA";
    private final String publicKey;
    private final String privateKey;
    private final X509EncodedKeySpec publicKeySpec;
    private final PKCS8EncodedKeySpec privateKeySpec;

    public SkynetEncryption(ApplicationContext context, SkynetSecurityProperties properties) throws IOException {
        Assert.hasText(properties.getRsaPublicFile(), "RsaPublicFile Is Blank.");
        Assert.hasText(properties.getRsaPrivateFile(), "RsaPrivateFile Is Blank.");

        this.publicKey = read(context, properties.getRsaPublicFile());
        log.debug("public-key={}", publicKey);
        byte[] publicKeyByte = Base64.getDecoder().decode(publicKey);
        this.publicKeySpec = new X509EncodedKeySpec(publicKeyByte);

        this.privateKey = read(context, properties.getRsaPrivateFile());
        log.debug("private-key={}", privateKey);
        byte[] privateKeyByte = Base64.getDecoder().decode(privateKey);
        this.privateKeySpec = new PKCS8EncodedKeySpec(privateKeyByte);
    }

    private static String read(ApplicationContext context, String fileName) throws IOException {
        String content = "";
        if (fileName.startsWith("classpath:")) {
            Resource resource = context.getResource(fileName);
            content = new String(FileCopyUtils.copyToByteArray(resource.getInputStream()), StandardCharsets.UTF_8);
        } else {
            content = Files.readFileStringContent(ResourceUtils.getFile(fileName));
        }
        // 苹果电脑也会出现 "\r\n" 换行 偶现，调整通过字符包含换行符区分
        String splitPattern = content.contains("\r\n") ? "\r\n" : "\\n";
        return Arrays.stream(content.split(splitPattern)).filter(line -> StringUtils.hasText(line) && !line.startsWith("--")).collect(Collectors.joining());
    }

    /**
     * 获取公钥Key
     *
     * @return
     */
    public String getPublicKey() {
        return publicKey;
    }

    /**
     * 获取私钥Key
     *
     * @return
     */
    public String getPrivateKey() {
        return privateKey;
    }

    /**
     * 加密
     *
     * @param text
     * @return
     * @throws Exception
     */
    public String encrypt(String text) throws Exception {
        Assert.hasText(text, "The text is blank.");
        text = text.trim();
        log.debug("encrypt text. len={}", text.length());
        byte[] result = encrypt(text.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.getEncoder().encode(result), StandardCharsets.UTF_8);
    }

    /**
     * 加密
     *
     * @param dataByte
     * @return
     * @throws Exception
     */
    public byte[] encrypt(byte[] dataByte) throws Exception {
        log.debug("dataByte text. len={}", dataByte.length);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(publicKeySpec);
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(dataByte);
    }

    /**
     * 解密加密后的密码串，安全考虑，前端传递的密码使用了 RSA 加密，后端在校验密码前需要先解密
     */
    public String decrypt(String base64) throws Exception {
        Assert.hasText(base64, "The base64 is blank.");
        base64 = base64.trim();
        log.debug("decrypt base64. len={}", base64.length());
        byte[] result = this.decrypt(Base64.getDecoder().decode(base64));
        return new String(result, StandardCharsets.UTF_8);
    }

    /**
     * 解密加密后的密码串，安全考虑，前端传递的密码使用了 RSA 加密，后端在校验密码前需要先解密
     */
    public byte[] decrypt(byte[] dataByte) throws Exception {
        log.debug("decrypt dataByte. len={}", dataByte.length);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        RSAPrivateKey privateKey = (RSAPrivateKey) keyFactory.generatePrivate(privateKeySpec);
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return cipher.doFinal(dataByte);
    }

    /**
     * 签名
     *
     * @param text
     * @return 签名字符串(base64格式)
     * @throws Exception
     */
    public String sign(String text) throws Exception {
        byte[] singBytes = sign(text.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.getEncoder().encode(singBytes), StandardCharsets.UTF_8);
    }

    /**
     * 验证
     *
     * @param text 验证数据
     * @param sign 签名
     * @return
     * @throws Exception
     */
    public boolean verify(String text, String sign) throws Exception {
        byte[] textBytes = text.getBytes(StandardCharsets.UTF_8);
        byte[] signBytes = Base64.getDecoder().decode(sign);
        return verify(textBytes, signBytes);
    }

    /**
     * 签名
     *
     * @param data
     * @return
     * @throws Exception
     */
    public byte[] sign(byte[] data) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        RSAPrivateKey privateKey = (RSAPrivateKey) keyFactory.generatePrivate(privateKeySpec);
        Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
        sig.initSign(privateKey);
        sig.update(data);
        return sig.sign();
    }

    /**
     * 验证
     *
     * @param data 源数据
     * @param sign 校验数据
     * @return
     * @throws Exception
     */
    public boolean verify(byte[] data, byte[] sign) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(publicKeySpec);
        Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
        sig.initVerify(publicKey);
        sig.update(data);
        return sig.verify(sign);
    }
}



