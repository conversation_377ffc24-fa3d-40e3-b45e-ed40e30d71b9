package skynet.boot.security.auth.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import skynet.boot.security.config.SkynetSecurityProperties;
import skynet.boot.security.metrics.SkynetSecurityMetrics;

import java.io.IOException;


/**
 * 过滤器基类，地址放行
 *
 * <AUTHOR> by jianwu<PERSON> on 2020/5/28 10:45
 */
@Slf4j
public abstract class AuthFilterBase implements Filter {

    protected final SkynetSecurityProperties skynetSecurityProperties;
    protected final SkynetSecurityMetrics skynetSecurityMetrics;

    public AuthFilterBase(SkynetSecurityProperties skynetSecurityProperties, SkynetSecurityMetrics skynetSecurityMetrics) {
        this.skynetSecurityProperties = skynetSecurityProperties;
        this.skynetSecurityMetrics = skynetSecurityMetrics;
        log.info("auth-pathPattern = {}", skynetSecurityProperties.getPathPatterns());
        log.info("auth-ignorePattern = {};", skynetSecurityProperties.getIgnorePatterns());
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String requestPath = request.getServletPath();
        log.debug("RequestPath = {};", requestPath);

        AntPathMatcher antPathMatcher = new AntPathMatcher();
        //白名单路径，不做权限校验
        if (skynetSecurityProperties.getIgnorePatterns() != null) {
            for (String ignorePattern : skynetSecurityProperties.getIgnorePatterns()) {
                if (StringUtils.hasText(ignorePattern) && antPathMatcher.match(ignorePattern, requestPath)) {
                    log.debug("white path={};ignorePattern={}", requestPath, ignorePattern);
                    filterChain.doFilter(servletRequest, servletResponse);
                    return;
                }
            }
        }
        //符合 需要过滤的路径 要拦截
        if (skynetSecurityProperties.getPathPatterns() != null) {
            for (String pattern : skynetSecurityProperties.getPathPatterns()) {
                if (StringUtils.hasText(pattern) && antPathMatcher.match(pattern, requestPath)) {
                    log.debug("filter path = {}; pattern = {}", requestPath, pattern);
                    filter(servletRequest, servletResponse, filterChain);
                    return;
                }
            }
        }
        //未命中的路径的，全部忽略 过滤
        filterChain.doFilter(servletRequest, servletResponse);
    }

    abstract void filter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException;
}
