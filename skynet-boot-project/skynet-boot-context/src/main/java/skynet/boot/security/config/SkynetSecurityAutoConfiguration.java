package skynet.boot.security.config;

import io.micrometer.prometheusmetrics.PrometheusMeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.web.WebSecurityConfigurer;
import org.springframework.security.web.firewall.RequestRejectedException;
import skynet.boot.annotation.EnableSkynetException;
import skynet.boot.exception.config.SkynetExceptionAutoConfiguration;
import skynet.boot.exception.handler.ExceptionDescriptor;
import skynet.boot.exception.handler.SkynetExceptionHandler;
import skynet.boot.exception.message.ExceptionMessageFormatter;
import skynet.boot.security.condition.ConditionalOnSecurity;
import skynet.boot.security.metrics.SkynetSecurityMetrics;
import skynet.boot.security.metrics.SkynetSecurityMetrics4Prometheus;

import java.security.GeneralSecurityException;

/**
 * <AUTHOR>
 * @date 2022/5/28 22:22
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnSecurity
@AutoConfigureAfter(SkynetSecurityPropertiesAutoConfiguration.class)
@AutoConfigureBefore(SkynetExceptionAutoConfiguration.class)
@ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "servlet", matchIfMissing = true)
public class SkynetSecurityAutoConfiguration {

    @ConditionalOnClass(PrometheusMeterRegistry.class)
    @ConditionalOnBean(PrometheusMeterRegistry.class)
    static class MeterRegistryOnClassConfig {
        @Bean
        @Primary
        public SkynetSecurityMetrics skynetSecurityMetrics4Prometheus(PrometheusMeterRegistry registry) {
            log.info("SkynetSecurityMetrics4Prometheus init.");
            return new SkynetSecurityMetrics4Prometheus(registry);
        }
    }

    @ConditionalOnMissingClass("io.micrometer.prometheus.PrometheusMeterRegistry")
    static class MeterRegistryMissingClassConfig {
        @Bean
        public SkynetSecurityMetrics defaultSkynetSecurityMetric() {
            log.info("defaultSkynetSecurityMetric init.");
            return new SkynetSecurityMetrics() {
            };
        }
    }


    @ConditionalOnClass({WebEndpointProperties.class, WebSecurityConfigurer.class})
    static class ActuatorBaseAuthBeanPostProcessorConfiguration {

        @Bean
        public BeanPostProcessor actuatorBaseAuthBeanPostProcessor(WebEndpointProperties webEndpointProperties,
                                                                   SkynetBaseAuthProperties baseAuthProperties) {
            return new BeanPostProcessor() {
                @Override
                public Object postProcessAfterInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {

                    if (bean instanceof SkynetHttpBasicConfigurerCustomizer) {
                        String actuatorBasePath = webEndpointProperties.getBasePath();
                        if (baseAuthProperties.isActuatorSecurityEnabled()) {
                            baseAuthProperties.getPathPatterns().add(actuatorBasePath + "/**");
                            baseAuthProperties.getActuatorIgnoreEndpoints().forEach(x -> baseAuthProperties.getIgnorePatterns().add(actuatorBasePath + "/" + x));
                        }
                    }
                    return bean;
                }
            };
        }
    }

    @ConditionalOnClass({RequestRejectedException.class})
    @ConditionalOnBean(annotation = EnableSkynetException.class)
    static class RequestRejectedExceptionHandlerConfiguration {
        @Bean
        public SkynetExceptionHandler<RequestRejectedException> requestRejectedExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-2010, "Access Denied")) {
                @Override
                protected void showLog(String url, ExceptionDescriptor.ExceptionInfo info, RequestRejectedException e) {
                    log.warn("{}: url={}; {}", info.getName(), url, e.getMessage());
                }
            };
        }
    }

    @ConditionalOnBean(annotation = EnableSkynetException.class)
    static class GeneralSecurityExceptionExceptionHandlerConfiguration {
        @Bean
        public SkynetExceptionHandler<GeneralSecurityException> generalSecurityExceptionExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<>(formatter, () -> new ExceptionDescriptor.ExceptionInfo(-2020, "Encryption Or Decryption Exception")) {
                @Override
                protected void showLog(String url, ExceptionDescriptor.ExceptionInfo info, GeneralSecurityException e) {
                    log.warn("{}: url={}; {}", info.getName(), url, e.getMessage());
                }
            };
        }
    }
}