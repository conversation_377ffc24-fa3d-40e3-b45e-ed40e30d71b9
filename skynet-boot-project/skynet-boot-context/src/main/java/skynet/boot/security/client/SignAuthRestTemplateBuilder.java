package skynet.boot.security.client;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.MediaType;
import org.springframework.http.client.*;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.config.SkynetAuthClientProperties;

import java.io.IOException;
import java.util.Map;

/**
 * SignAuth RestTemplate Builder
 *
 * <AUTHOR>
 * @date 2022年05月19日22:29:35
 */
@Slf4j
public class SignAuthRestTemplateBuilder {

    private final RestTemplateBuilder builder;

    public SignAuthRestTemplateBuilder(RestTemplateBuilder builder) {
        this.builder = builder;
    }

    public RestTemplate build(SkynetAuthClientProperties properties) {
        log.debug("skynetAuthClientProperties={}", properties);
        Assert.notNull(properties, "The skynetAuthClientProperties is empty.");
        Assert.hasText(properties.getApiKey(), "The skynetAuthClientProperties.getApiKey() is empty.");
        Assert.hasText(properties.getApiSecret(), "The skynetAuthClientProperties.getApiSecret() is empty.");

        RestTemplate restTemplate = builder.build();
        ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(
                AuthRestTemplateBuilder.httpClient(properties.getTimeout()));
        restTemplate.setRequestFactory(requestFactory);
        restTemplate.getInterceptors().add(new SignAuthRestTemplateInterceptor(properties));
        return restTemplate;
    }

    static class SignAuthRestTemplateInterceptor implements ClientHttpRequestInterceptor {
        private final SkynetAuthClientProperties properties;

        public SignAuthRestTemplateInterceptor(SkynetAuthClientProperties properties) {
            this.properties = properties;
        }

        @NotNull
        @Override
        public ClientHttpResponse intercept(HttpRequest httpRequest, @NotNull byte[] bytes, @NotNull ClientHttpRequestExecution clientHttpRequestExecution) throws IOException {
            log.debug("Add skynet-auth-sign header for url={} ..", httpRequest.getURI());
            Map<String, String> headers = AuthUtils.assembleAuthorizationHeaders(HttpMethod.valueOf(httpRequest.getMethod().toString()),
                    httpRequest.getURI().toString(), properties.getApiKey(), properties.getApiSecret(), null);
            headers.forEach((k, v) -> {
                log.debug("{}={}", k, v);
                httpRequest.getHeaders().add(k, v);
            });
            if (httpRequest.getHeaders().getContentType() == null) {
                httpRequest.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            }
            return clientHttpRequestExecution.execute(httpRequest, bytes);
        }
    }
}
