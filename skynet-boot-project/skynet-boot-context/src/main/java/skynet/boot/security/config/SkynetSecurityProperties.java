package skynet.boot.security.config;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * base-auth|form-auth  功能开启属性配置
 * <p>
 * 增加此属性的主要目的是 为了 属性配置的智能感知提示
 *
 * <AUTHOR>
 * @date 2022/5/28 20:34
 */
@Getter
@Setter
@Accessors(chain = true)
public class SkynetSecurityProperties extends Jsonable implements SkynetAuthProperties {

    public static final List<String> SYSTEM_PATHS = Arrays.asList("/swagger**/**", "/v3/api-docs**/**", "/webjars**/**", "/skynet/security/*crypt");


    public static final String DEFAULT_PUBLIC_KEY_FILENAME = "classpath:skynet-k/r-s-a-2048-pub.p-em";
    public static final String DEFAULT_PRIVATE_KEY_FILENAME = "classpath:skynet-k/r-s-a-2048-pri.p-em";

    public SkynetSecurityProperties() {
        this.pathPatterns.add("/**");
        this.ignorePatterns.addAll(Arrays.asList("/info", "/health", "/skynet/health", "/actuator/health"));
    }

    /**
     * 默认为 false 将启用 spring.security 功能( 项目依赖了 spring-boot-starter-security)。
     * 为 true 时，（启用skynet.security 功能 base-auth|form-auth），
     */
    @JSONField(ordinal = 10)
    private boolean enabled = false;

    /**
     * 需要鉴权的api路径模板, 缺省所有的都拦截
     */
    @JSONField(ordinal = 20)
    private List<String> pathPatterns = new ArrayList<>();

    /**
     * 忽略鉴权的api路径模板，优先级比 pathPatterns 高
     *
     * <pre>
     *     缺省：/info,/health,/skynet/health,/actuator/health
     * </pre>
     */
    @JSONField(ordinal = 30)
    private List<String> ignorePatterns = new ArrayList<>();

    /**
     * 公钥文件名（PEM格式）
     * <p>
     * openssl genrsa -out rsa_2048_priv.pem 2048
     * openssl rsa -pubout -in rsa_2048_priv.pem -out rsa_2048_pub.pem
     * cat rsa_2048_priv.pem
     * cat rsa_2048_pub.pem
     */
    @JSONField(ordinal = 300)
    private String rsaPublicFile = DEFAULT_PUBLIC_KEY_FILENAME;

    /**
     * 私钥文件名（PEM格式）
     * <p>
     * openssl genrsa -out rsa_2048_priv.pem 2048
     * openssl rsa -pubout -in rsa_2048_priv.pem -out rsa_2048_pub.pem
     * cat rsa_2048_priv.pem
     * cat rsa_2048_pub.pem
     */
    @JSONField(ordinal = 400)
    private String rsaPrivateFile = DEFAULT_PRIVATE_KEY_FILENAME;

}
