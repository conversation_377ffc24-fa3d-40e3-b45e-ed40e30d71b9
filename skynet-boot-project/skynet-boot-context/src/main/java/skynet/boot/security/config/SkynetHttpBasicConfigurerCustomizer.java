package skynet.boot.security.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.HttpBasicConfigurer;
import skynet.boot.security.baseauth.SkynetBasicAuthenticationEntryPoint;

/**
 * 自定义HttpBasicConfigurer
 */
@Slf4j
@Order(1)
public class SkynetHttpBasicConfigurerCustomizer implements Customizer<HttpBasicConfigurer<HttpSecurity>> {

    @Override
    public void customize(HttpBasicConfigurer<HttpSecurity> httpSecurityHttpBasicConfigurer) {
        httpSecurityHttpBasicConfigurer
                .authenticationEntryPoint(new SkynetBasicAuthenticationEntryPoint());
    }

}
