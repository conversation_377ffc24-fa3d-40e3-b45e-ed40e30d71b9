package skynet.boot.security.client;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.util.Assert;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.config.SkynetAuthClientProperties;

import java.util.Map;

/**
 * * feign请求拦截器 配置，
 * * 在拦截器中添加鉴权的签名信息
 *
 * <AUTHOR>
 */
@Slf4j
public class SignAuthFeignClientConfiguration {

    @Bean
    public RequestInterceptor authSignFeignInterceptor(SkynetAuthClientProperties authProperties) {
        return new SignAuthFeignInterceptor(authProperties);
    }

    public static class SignAuthFeignInterceptor implements RequestInterceptor {
        private final SkynetAuthClientProperties authProperties;


        public SignAuthFeignInterceptor(SkynetAuthClientProperties authProperties) {
            this.authProperties = authProperties;
        }

        @Override
        public void apply(RequestTemplate requestTemplate) {
            log.debug("Apply SignAuthFeignInterceptor ...");
            Assert.hasText(authProperties.getApiKey(), "apiKey is blank.");
            Assert.hasText(authProperties.getApiSecret(), "apiSecret is blank.");


            log.debug("assembleAuthorizationHeaders header ...");
            Map<String, String> headers = AuthUtils.assembleAuthorizationHeaders(
                    HttpMethod.valueOf(requestTemplate.method()),
                    requestTemplate.feignTarget().url() + requestTemplate.url(),
                    authProperties.getApiKey(), authProperties.getApiSecret(),
                    requestTemplate.bodyTemplate());

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                log.debug("{}={}", entry.getKey(), entry.getValue());
                requestTemplate.header(entry.getKey(), entry.getValue());
            }

            log.debug("Apply SignAuthFeignInterceptor OK.");
        }
    }
}
