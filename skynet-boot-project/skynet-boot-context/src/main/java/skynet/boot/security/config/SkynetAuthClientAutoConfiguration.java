package skynet.boot.security.config;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.tlb.extension.config.TlbRestTemplateAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetAuthClient;
import skynet.boot.security.client.AuthRestTemplateBuilder;
import skynet.boot.security.client.BaseAuthRestTemplateBuilder;
import skynet.boot.security.client.SignAuthRestTemplateBuilder;

/**
 * api-auth 客户端
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(RestTemplateAutoConfiguration.class)
@AutoConfigureBefore(TlbRestTemplateAutoConfiguration.class)
@ConditionalOnBean(annotation = EnableSkynetAuthClient.class, value = {RestTemplateBuilder.class})
@ConditionalOnProperty(name = "spring.main.web-application-type", havingValue = "servlet", matchIfMissing = true)
public class SkynetAuthClientAutoConfiguration {

    @Bean
    public AuthRestTemplateBuilder authRestTemplateBuilder(RestTemplateBuilder builder) {
        return new AuthRestTemplateBuilder(builder);
    }

    @Bean
    public SignAuthRestTemplateBuilder signAuthRestTemplateBuilder(RestTemplateBuilder builder) {
        return new SignAuthRestTemplateBuilder(builder);
    }

    @Bean
    public BaseAuthRestTemplateBuilder baseAuthRestTemplateBuilder(RestTemplateBuilder builder) {
        return new BaseAuthRestTemplateBuilder(builder);
    }
}
