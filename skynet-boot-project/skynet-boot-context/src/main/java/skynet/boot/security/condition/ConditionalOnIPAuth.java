package skynet.boot.security.condition;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Inherited
@ConditionalOnSecurity
@ConditionalOnProperty(value = "skynet.security.ip-auth.enabled")
public @interface ConditionalOnIPAuth {

}
