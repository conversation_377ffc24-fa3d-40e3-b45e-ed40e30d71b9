package skynet.boot.security.form.jwt;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import skynet.boot.common.domain.SkynetResponse;
import skynet.boot.security.config.SkynetFormAuthProperties;
import skynet.boot.security.form.data.AuthResult;

import java.io.IOException;
import java.util.List;

/**
 * 登录认证检查过滤器
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtAuthenticationFilter extends BasicAuthenticationFilter {

    private final SkynetFormAuthProperties authProperties;

    public JwtAuthenticationFilter(AuthenticationManager authenticationManager, SkynetFormAuthProperties authProperties) {
        super(authenticationManager);
        this.authProperties = authProperties;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {

        // 获取令牌并根据令牌获取登录认证信息
        List<String> tokens = JwtSecurityUtils.getTokens(request);
        AuthResult authResult = AuthResult.NOAUTH;
        String tokenOK = "";
        for (String token : tokens) {
            try {
                Authentication authentication = JwtTokenUtils.getAuthenticationFromToken(token);
                // 设置登录认证信息到上下文
                SecurityContextHolder.getContext().setAuthentication(authentication);
                authResult = AuthResult.SUCCESS;
                tokenOK = token;
                break;
            } catch (io.jsonwebtoken.ExpiredJwtException e) {
                log.warn("Check token error:{}", e.getMessage());
                authResult = AuthResult.AUTH_TIMEOUT;
            } catch (io.jsonwebtoken.JwtException e) {
                log.warn("Check token error:{}", e.getMessage());
                authResult = AuthResult.INVALID_HEADER;
            } catch (Throwable e) {
                log.error("Check token error:", e);
                authResult = AuthResult.CHECK_ERROR;
            }
        }

        if (authResult == AuthResult.SUCCESS) {
            //刷新Cookie中的 tokenOK
            tokenOK = JwtTokenUtils.refreshToken(tokenOK, authProperties.getJwtExpiresSecond());
            JwtSecurityUtils.setCookie(response, tokenOK, authProperties);
            request.getSession().setAttribute(authProperties.getJwtTokenSessionKey(), tokenOK);
            log.debug("check token ok.");
            chain.doFilter(request, response);
        } else {
            String accept = request.getHeader("accept");
            if (org.springframework.util.StringUtils.hasText(accept) && accept.toLowerCase().contains("text/html")) {
                //浏览器过来的请求
                String location = String.format("%s?redirect=%s", authProperties.getFullLoginPage(), request.getRequestURI());
                log.debug("SendRedirect={}", location);
                response.sendRedirect(location);
            } else {
                //check error
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                request.getSession().removeAttribute(authProperties.getJwtTokenSessionKey());
                response.setContentType("application/json; charset=utf-8");
                SkynetResponse<?> skynetResponse = new SkynetResponse<>(authResult.getCode(), authResult.getMessage());
                response.getWriter().print(skynetResponse);
                response.getWriter().flush();
                response.getWriter().close();
                log.debug("check token error={}", authResult);
            }
        }
    }
}