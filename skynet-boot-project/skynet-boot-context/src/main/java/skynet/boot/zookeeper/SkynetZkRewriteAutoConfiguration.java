package skynet.boot.zookeeper;

import lombok.extern.slf4j.Slf4j;
import org.apache.curator.RetryPolicy;
import org.apache.curator.drivers.TracerDriver;
import org.apache.curator.ensemble.EnsembleProvider;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.zookeeper.ConditionalOnZookeeperEnabled;
import org.springframework.cloud.zookeeper.CuratorFrameworkCustomizer;
import org.springframework.cloud.zookeeper.ZookeeperAutoConfiguration;
import org.springframework.cloud.zookeeper.ZookeeperProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.ConditionalOnPropertyNotEmpty;
import skynet.boot.annotation.EnableSkynetZookeeper;

/**
 * 暂时不适用 ACL 鉴权了 by lyhu 2022年04月26日10:50:49
 * <p>
 * 代码有参考意义
 *
 * <AUTHOR>
 * @date 2021/2/5
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetZookeeper.class)
@ConditionalOnClass({ConditionalOnZookeeperEnabled.class, ZookeeperAutoConfiguration.class})
@ConditionalOnZookeeperEnabled
@ConditionalOnProperty(value = SkynetZkProperties.SKYNET_ZOOKEEPER_ENABLED)
@AutoConfigureBefore(ZookeeperAutoConfiguration.class)
public class SkynetZkRewriteAutoConfiguration {

    private final SkynetZkProperties skynetZkProperties;

    public SkynetZkRewriteAutoConfiguration(SkynetZkProperties skynetZkProperties) {
        this.skynetZkProperties = skynetZkProperties;
    }

    @Bean(name = "curatorFramework")
    @ConditionalOnPropertyNotEmpty(SkynetZkProperties.SKYNET_ZOOKEEPER_ACL)
    public CuratorFramework curatorFramework(RetryPolicy retryPolicy, ZookeeperProperties properties, ObjectProvider<CuratorFrameworkCustomizer> optionalCuratorFrameworkCustomizerProvider, ObjectProvider<EnsembleProvider> optionalEnsembleProvider, ObjectProvider<TracerDriver> optionalTracerDriverProvider) throws Exception {
        CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder();
        EnsembleProvider ensembleProvider = optionalEnsembleProvider.getIfAvailable();
        //添加授权
        addAuthInfo(builder);

        if (ensembleProvider != null) {
            builder.ensembleProvider(ensembleProvider);
        } else {
            builder.connectString(properties.getConnectString());
        }

        builder.sessionTimeoutMs((int) properties.getSessionTimeout().toMillis()).connectionTimeoutMs((int) properties.getConnectionTimeout().toMillis()).retryPolicy(retryPolicy);
        optionalCuratorFrameworkCustomizerProvider.orderedStream().forEach((curatorFrameworkCustomizer) -> curatorFrameworkCustomizer.customize(builder));
        CuratorFramework curator = builder.build();
        optionalTracerDriverProvider.ifAvailable((tracerDriver) -> {
            if (curator.getZookeeperClient() != null) {
                curator.getZookeeperClient().setTracerDriver(tracerDriver);
            }
        });
        curator.start();
        log.trace("blocking until connected to zookeeper for {} {}", properties.getBlockUntilConnectedWait(), properties.getBlockUntilConnectedUnit());
        curator.blockUntilConnected(properties.getBlockUntilConnectedWait(), properties.getBlockUntilConnectedUnit());
        log.trace("Connected to zookeeper");

        return curator;
    }

    /**
     * 添加授权
     *
     * @param builder
     * @return
     */
    private CuratorFrameworkFactory.Builder addAuthInfo(CuratorFrameworkFactory.Builder builder) {
        //解析ACL并将用户名密码赋值
        if (skynetZkProperties.getAclUser() != null) {
            SkynetZkProperties.ZkUser zkUser = skynetZkProperties.getAclUser();
            if (org.springframework.util.StringUtils.hasText(zkUser.getUsername())
                    && org.springframework.util.StringUtils.hasText(zkUser.getPassword())) {
                String userId = String.format("%s:%s", zkUser.getUsername(), zkUser.getPassword());
                builder = builder.authorization("digest", userId.getBytes());
                log.info("Set zookeeper authorization with acl digest.");
            }
        } else {
            log.info("Cannot resolve zookeeper username or password.");
        }
        return builder;
    }
}
