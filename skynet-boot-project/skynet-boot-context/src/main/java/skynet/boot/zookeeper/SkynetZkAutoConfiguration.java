package skynet.boot.zookeeper;

import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import skynet.boot.annotation.EnableSkynetZookeeper;
import skynet.boot.zookeeper.impl.ZkConfigServiceImpl;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetZookeeper.class)
@ConditionalOnClass(CuratorFramework.class)
@ConditionalOnProperty(value = SkynetZkProperties.SKYNET_ZOOKEEPER_ENABLED)
@AutoConfigureBefore(SkynetZkRewriteAutoConfiguration.class)
public class SkynetZkAutoConfiguration {

    @Bean
    @ConfigurationProperties(prefix = SkynetZkProperties.SKYNET_ZOOKEEPER)
    public SkynetZkProperties skynetZkProperties(Environment environment) {
        return new SkynetZkProperties(environment);
    }

    @Bean
    public ZkConfigService zkConfigService(SkynetZkProperties skynetZkProperties) throws Exception {
        return new ZkConfigServiceImpl(skynetZkProperties);
    }
}
