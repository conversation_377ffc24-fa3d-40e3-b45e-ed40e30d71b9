package skynet.boot.zookeeper;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import skynet.boot.common.domain.Jsonable;

import java.util.Base64;

/**
 * <pre>
 * skynet.zookeeper.enabled=true
 * skynet.zookeeper.cluster_name=skynet
 * skynet.zookeeper.server_list=127.0.0.1:2181
 * skynet.zookeeper.session_timeout=20000
 * skynet.zookeeper.connection_timeout=10000
 * #zookeeper  A-C——L related configuration, user:Base64 pa_ss_w_ord (plaintext ac_count p_d:skynet:skynet_2230)
 * skynet.zookeeper.acl=skynet:c2t5bmV0MjIzMA==
 * </pre>
 *
 * <AUTHOR> [Oct 11, 2017 11:40:06 AM]
 */
@Slf4j
@Getter
@Setter
public class SkynetZkProperties extends Jsonable {

    public static final String SKYNET_ZOOKEEPER = "skynet.zookeeper";
    public static final String SKYNET_ZOOKEEPER_ENABLED = SKYNET_ZOOKEEPER + ".enabled";
    public static final String SKYNET_ZOOKEEPER_CLUSTER_NAME = SKYNET_ZOOKEEPER + ".cluster_name";
    public static final String SKYNET_ZOOKEEPER_SERVER_LIST = SKYNET_ZOOKEEPER + ".server_list";
    public static final String SKYNET_ZOOKEEPER_SESSION_TIMEOUT = SKYNET_ZOOKEEPER + ".session_timeout";
    public static final String SKYNET_ZOOKEEPER_CONNECTION_TIMEOUT = SKYNET_ZOOKEEPER + ".connection_timeout";
    public static final String SKYNET_ZOOKEEPER_ACL = SKYNET_ZOOKEEPER + ".acl";

    public static final String SKYNET_ZOOKEEPER_SASL_USERNAME = SKYNET_ZOOKEEPER + ".username";
    public static final String SKYNET_ZOOKEEPER_SASL_PASSWORD = SKYNET_ZOOKEEPER + ".password";

    public static final String SKYNET_DEFAULT_CLUSTER_NAME = "skynet";
    public static final String SKYNET_DEFAULT_SERVER_LIST = "127.0.0.1:2181";


    /**
     * 是否开启，默认：true
     */
    @JSONField(ordinal = 5)
    @JsonProperty(index = 5)
    private boolean enabled = true;

    /***
     * 集群名称，缺省：skynet
     */
    @JSONField(ordinal = 10)
    @JsonProperty(index = 10)
    private String clusterName;

    /**
     * zk server 地址，多个用 127.0.0.1:2181
     */
    @JSONField(ordinal = 20)
    @JsonProperty(index = 20)
    private String serverList = SKYNET_DEFAULT_SERVER_LIST;

    /**
     * 客户端与ZooKeeper通信的心跳时间间隔，单位：毫秒，默认20000
     */
    @JSONField(ordinal = 30)
    @JsonProperty(index = 30)
    private int sessionTimeout = 20000;

    /**
     * 客户端与ZooKeeper通信的最大连接时间，单位：毫秒，默认10000
     */
    @JSONField(ordinal = 40)
    @JsonProperty(index = 40)
    private int connectionTimeout = 10000;

    /**
     * 缓存TTL 时间  单位：毫秒，默认 1200
     */
    @JSONField(ordinal = 40)
    @JsonProperty(index = 40)
    private int cacheTtl = 1200;


    /**
     * #zookeeper Authorization ACL related configuration, user:Base64 password
     * <p>
     * skynet.zookeeper.acl=skynet:c2t5bmV0MjIzMA==
     */
    @JSONField(ordinal = 50)
    @JsonProperty(index = 50)
    private String acl;


    public SkynetZkProperties() {
    }

    public SkynetZkProperties(Environment environment) {
        this.clusterName = environment.getProperty(SKYNET_ZOOKEEPER_CLUSTER_NAME, SKYNET_DEFAULT_CLUSTER_NAME);
        this.serverList = environment.getProperty(SKYNET_ZOOKEEPER_SERVER_LIST, SKYNET_DEFAULT_SERVER_LIST);
        this.sessionTimeout = Integer.parseInt(environment.getProperty(SKYNET_ZOOKEEPER_SESSION_TIMEOUT, "20000").trim());
        this.connectionTimeout = Integer.parseInt(environment.getProperty(SKYNET_ZOOKEEPER_CONNECTION_TIMEOUT, "10000").trim());
        this.acl = environment.getProperty(SKYNET_ZOOKEEPER_ACL, "");
    }

    public String getClusterName() {
        return StringUtils.hasText(clusterName) ? clusterName.trim() : SKYNET_DEFAULT_CLUSTER_NAME;
    }

    public ZkUser getAclUser() {
        //解析ACL并将用户名密码赋值
        if (StringUtils.hasText(this.acl)) {
            log.debug("Zookeeper acl={}", acl);
            synchronized (this) {
                String[] aclArrays = acl.split(":");
                if (aclArrays.length > 1) {
                    return new ZkUser().setUsername(aclArrays[0]).setPassword(new String(Base64.getDecoder().decode(aclArrays[1])));
                }
            }
        }
        return null;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class ZkUser {
        private String username;
        private String password;
    }
}
