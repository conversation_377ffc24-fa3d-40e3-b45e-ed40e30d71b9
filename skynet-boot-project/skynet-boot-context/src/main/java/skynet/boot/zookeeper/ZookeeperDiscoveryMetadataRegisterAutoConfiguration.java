package skynet.boot.zookeeper;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.zookeeper.discovery.ZookeeperDiscoveryProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.SkynetProperties;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({ZookeeperDiscoveryProperties.class})
@ConditionalOnBean(value = {ZookeeperDiscoveryProperties.class})
public class ZookeeperDiscoveryMetadataRegisterAutoConfiguration {

    @Bean
    public ZookeeperDiscoveryMetadataRegister zookeeperDiscoveryMetadataRegister(SkynetProperties skynetProperties,
                                                                                 ZookeeperDiscoveryProperties zookeeperDiscoveryProperties) {
        return new ZookeeperDiscoveryMetadataRegister(skynetProperties, zookeeperDiscoveryProperties);
    }
}
