package skynet.boot.zookeeper;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.DeleteOperation;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.cloud.zookeeper.serviceregistry.ServiceInstanceRegistration;
import org.springframework.cloud.zookeeper.serviceregistry.ZookeeperServiceRegistry;
import org.springframework.context.annotation.Lazy;

/**
 * zk 服务发现 反注册端点
 *
 * <AUTHOR> 2022年12月13日11:43:35
 */
@Slf4j
@Lazy
@Endpoint(id = "zookeeper-discovery-service-unregister")
public class ZookeeperDiscoveryUnRegisterEndpoint {

    private final ZookeeperServiceRegistry zookeeperServiceRegistry;
    private final ServiceInstanceRegistration registration;

    public ZookeeperDiscoveryUnRegisterEndpoint(ZookeeperServiceRegistry zookeeperServiceRegistry, ServiceInstanceRegistration registration) {
        this.zookeeperServiceRegistry = zookeeperServiceRegistry;
        this.registration = registration;
    }

    @DeleteOperation
    public Object invoke() {
        log.info("do zookeeper-discovery-unregister [ServiceId={}]...", registration.getServiceId());
        zookeeperServiceRegistry.deregister(registration);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("current-service-instance", registration.getServiceInstance());
        jsonObject.put("unregister", "ok");
        return jsonObject;
    }
}