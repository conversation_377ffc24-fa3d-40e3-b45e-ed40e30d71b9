package skynet.boot.zookeeper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.zookeeper.discovery.ZookeeperDiscoveryProperties;
import org.springframework.util.StringUtils;
import skynet.boot.SkynetConsts;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;

/**
 * 注册 PluginCode 到 spring.cloud.zookeeper.discovery.metadata["SKYNET_PLUGIN_CODE"]=skynet-stream
 * <p>
 * <p>
 * 注册时机：ZookeeperDiscoveryProperties 初始化后，
 * <p>
 * zk 汇报前（ZookeeperAutoServiceRegistrationAutoConfiguration）
 *
 * <AUTHOR>
 * @date 2022/5/1 08:28
 */
@Slf4j
public class ZookeeperDiscoveryMetadataRegister {


    public ZookeeperDiscoveryMetadataRegister(SkynetProperties skynetProperties,
                                              ZookeeperDiscoveryProperties zookeeperDiscoveryProperties) {
        if (StringUtils.hasText(skynetProperties.getPlugin())) {
            log.info("Register metadata of zookeeperDiscoveryProperties: {} = {}",
                    SkynetConsts.DISCOVER_METADATA_SKYNET_PLUGIN_CODE_KEY, skynetProperties.getPlugin());
            zookeeperDiscoveryProperties.getMetadata().put(SkynetConsts.DISCOVER_METADATA_SKYNET_PLUGIN_CODE_KEY,
                    skynetProperties.getPlugin());

            log.info("Register metadata of zookeeperDiscoveryProperties: {} = {}",
                    SkynetConsts.DISCOVER_METADATA_SKYNET_ACTION_PID, OsUtil.getCurrentPid());
            zookeeperDiscoveryProperties.getMetadata().put(SkynetConsts.DISCOVER_METADATA_SKYNET_ACTION_PID,
                    String.valueOf(OsUtil.getCurrentPid()));

        } else {
            log.warn("Current action plugin is blank.[actionPoint = {}]", skynetProperties.getActionPoint());
        }
    }
}
