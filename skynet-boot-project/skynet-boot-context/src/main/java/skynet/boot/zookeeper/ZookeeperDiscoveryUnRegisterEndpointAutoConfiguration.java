package skynet.boot.zookeeper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.zookeeper.discovery.ConditionalOnZookeeperDiscoveryEnabled;
import org.springframework.cloud.zookeeper.serviceregistry.ServiceInstanceRegistration;
import org.springframework.cloud.zookeeper.serviceregistry.ZookeeperServiceRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/5/1 08:28
 */
@Slf4j
@EnableDiscoveryClient
@Configuration(proxyBeanMethods = false)
@ConditionalOnZookeeperDiscoveryEnabled
@ConditionalOnClass({ZookeeperServiceRegistry.class})
@ConditionalOnBean({ZookeeperServiceRegistry.class})
public class ZookeeperDiscoveryUnRegisterEndpointAutoConfiguration {

    /**
     * zk 服务发现 反注册端点 实例
     *
     * @param zookeeperServiceRegistry
     * @param registration
     * @return
     */
    @Bean
    @ConditionalOnProperty(value = {"spring.cloud.service-registry.enabled"}, matchIfMissing = true)
    public ZookeeperDiscoveryUnRegisterEndpoint skynetDiscoveryUnRegisterEndpoint(ZookeeperServiceRegistry zookeeperServiceRegistry, ServiceInstanceRegistration registration) {
        return new ZookeeperDiscoveryUnRegisterEndpoint(zookeeperServiceRegistry, registration);
    }
}
