/**
 *
 */
package skynet.boot.zookeeper;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Observer;

/**
 * 配置服务，与ZK服务器通信，
 *
 * <pre>
 * 主要功能：ZK节点的基本操作，如 创建、获取、删除，监听等。
 * </pre>
 *
 * <AUTHOR>
 */
public interface ZkConfigService extends AutoCloseable {

    /**
     * 删除一个节点及其所有子节点
     *
     * @param path 节点路径，如：/redis/server1
     */
    void deleteNode(String path);

    /**
     * 判断是否存在一个path路径的节点
     *
     * @param path 节点路径，如：/webapp
     * @return 存在为true，不存在返回false
     */
    boolean exists(String path);

    /**
     * 获取孩子节点
     *
     * @param path 路径，如：/webapp
     * @return 孩子节点名称列表
     */
    List<String> getChildren(String path);

    /**
     * 得到子节点的数据
     *
     * @param path 节点路径，如：/webapp
     * @return 子节点名称和数据
     */
    Map<String, String> getChildrenWithData(String path);

    /**
     * 得到指定子节点的数据
     *
     * @param path            节点路径，如：/webapp
     * @param currentChildren 指定的子节点
     * @return 子节点名称和数据
     */
    Map<String, String> getChildrenWithData(String path, List<String> currentChildren);

    /**
     * 得到子节点的数据
     *
     * @param path             节点路径，如：/webapp
     * @param childrenObserver 子节点个数或每个子节点数据观察器，响应节点的变化
     * @return 子节点名称和数据
     */
    Map<String, String> getChildrenWithData(String path, final Observer childrenObserver);

    /**
     * 获取节点数据
     *
     * @param path 节点路径，如：/webapp
     * @return 节点的数据
     */
    String getData(String path);

    /**
     * 获取节点数据
     *
     * @param path         节点路径，如：/webapp
     * @param dataObserver 数据变化观察者
     * @return 节点的数据
     */
    String getData(String path, final Observer dataObserver);

    /**
     * 添加一个节点，如果父节点不存在则自动创建父节点
     *
     * @param path 节点路径，如：/redis
     * @param data 属性
     */
    void putNode(String path, String data);

    /**
     * 添加一个节点，如果父节点不存在则自动创建父节点
     *
     * @param path 节点路径，如：/redis
     * @param name 名称，如：server1
     * @param data 属性
     */
    void putNode(String path, String name, String data);

    void putNode(String path, byte[] data);

    void putNode(String path, String name, byte[] data);

    /**
     * 添加一个顺序节点
     *
     * @param path   节点路径，如：/webapp
     * @param prefix 名称前缀，如：server
     * @param data   属性
     * @return 完整的节点名称，如：server1
     */
    String putSequenceNode(String path, String prefix, String data);

    /**
     * 添加一个临时节点
     *
     * <pre>
     * </pre>
     *
     * @param path
     * @param data
     * @return
     */
    String putEphemeralNode(String path, String data);

    /**
     * 观察子节点的变化和子节点数据的变化
     *
     * @param parentPath       父节点路径，如：/webapp
     * @param childrenObserver 观察者
     */
    void watchChildrenObserver(String parentPath, final Observer childrenObserver);

    /**
     * 观察节点的数据变化，包括节点被删除意味了数据清空
     *
     * @param path         节点路径，如：/webapp
     * @param dataObserver 观察者
     */
    void watchData(String path, final Observer dataObserver);

    /**
     * 导入 数据
     *
     * @param importFile
     * @param overwrite
     * @throws IOException
     * @throws InterruptedException
     */
    void importData(List<String> importFile, Boolean overwrite) throws IOException, InterruptedException;

    /**
     * 导出 数据
     *
     * @param path
     * @return
     */
    Map<String, String> exportData(String path);


    /**
     * zk链接属性
     *
     * @return
     */
    SkynetZkProperties getZkProperties();

}