/*
 * Copyright © 2020-present zmzhou-star. All Rights Reserved.
 */

package skynet.boot.config;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.availability.AvailabilityChangeEvent;
import org.springframework.boot.context.event.*;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.ContextStoppedEvent;

/**
 * springboot生命周期
 * 为了方便 调试，如，设置断点，判断程序执行时机
 * by lyhu
 * 2022年08月25日12:09:59
 *
 * <AUTHOR>
 */
@Slf4j
public class SkynetApplicationEventListener implements ApplicationListener<ApplicationEvent> {

    @Override
    public void onApplicationEvent(@NotNull ApplicationEvent event) {
        try {
            // 在这里可以监听到Spring Boot的生命周期
            if (event instanceof ApplicationStartingEvent) {
                log.debug("ApplicationStartingEvent");
            } else if (event instanceof ApplicationEnvironmentPreparedEvent) {
                log.debug("ApplicationEnvironmentPreparedEvent");
            } else if (event instanceof ApplicationContextInitializedEvent) {
                log.debug("ApplicationContextInitializedEvent");
                ((ApplicationContextInitializedEvent) event).getApplicationContext().getEnvironment().getProperty("spring.autoconfigure.exclude");
            } else if (event instanceof ApplicationPreparedEvent) {
                log.debug("Spring container triggers before executing refresh");
            } else if (event instanceof ServletWebServerInitializedEvent) {
                log.debug("Servlet Web WebServerInitialized");
            } else if (event instanceof ContextRefreshedEvent) {
                log.debug("ContextRefreshedEvent");
            } else if (event instanceof ApplicationStartedEvent) {
                log.debug("ApplicationStartedEvent");
            } else if (event instanceof ApplicationReadyEvent) {
                log.debug("ApplicationReadyEvent");
            } else if (event instanceof AvailabilityChangeEvent) {
                log.debug("AvailabilityChangeEvent");
            } else if (event instanceof ApplicationFailedEvent) {
                log.debug("ApplicationFailedEvent");
            } else if (event instanceof ContextStoppedEvent) {
                log.debug("ContextStoppedEvent");
            } else if (event instanceof ContextClosedEvent) {
                log.debug("ContextClosedEvent");
            } else {
                log.debug("other event:{}", event);
            }
        } catch (Throwable e) {
            log.warn("onApplicationEvent error {}", e.getMessage());
        }
    }
}
