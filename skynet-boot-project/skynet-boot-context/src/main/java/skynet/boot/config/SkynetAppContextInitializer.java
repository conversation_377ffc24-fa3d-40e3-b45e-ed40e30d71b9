package skynet.boot.config;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.util.StringUtils;

/**
 * <pre>
 * 做一些 全局属性配置
 * </pre>
 *
 * <AUTHOR>
 * @date 2022年02月27日21:34:04
 */
public class SkynetAppContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {

        //保持以前的 skynet.api.swagger2.enabled 兼容,如果有配置，就同步修改 springfox.documentation.enabled 属性
        String oldEnabled = applicationContext.getEnvironment().getProperty("skynet.api.swagger2.enabled");
        if (StringUtils.hasText(oldEnabled)) {
            System.setProperty("springfox.documentation.enabled", String.valueOf("true".equalsIgnoreCase(oldEnabled)).toLowerCase());
        }

        //默认 禁用 swagger，必须需要手动开启
        //springboot 2.6.4  spring.mvc.pathmatch.matching-strategy 默认是PATH_PATTERN_PARSER, 由于 swagger-ui 需要 ANT_PATH_MATCHER
        if ("true".equalsIgnoreCase(applicationContext.getEnvironment().getProperty("springfox.documentation.enabled", "false"))) {
            System.setProperty("spring.mvc.pathmatch.matching-strategy", "ANT_PATH_MATCHER");
        }
    }
}