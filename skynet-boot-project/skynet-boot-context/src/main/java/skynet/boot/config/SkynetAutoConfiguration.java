package skynet.boot.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import skynet.boot.AppContext;
import skynet.boot.SkynetProperties;
import skynet.boot.common.JsonUtils;
import skynet.boot.common.SpringUtils;
import skynet.boot.common.domain.Jsonable;

/**
 * <AUTHOR>
 * @date 2022/5/30 13:29
 */
@Configuration(proxyBeanMethods = false)
@PropertySource("classpath:/skynet-boot/skynet-application.properties")
@Import(SpringUtils.class)
public class SkynetAutoConfiguration {

    @Bean
    public JsonUtils jsonUtils() {
        return new JsonUtils();
    }

    @Bean(SkynetProperties.BEAN_NAME)
    @ConfigurationProperties(prefix = skynet.boot.SkynetProperties.PROP_PREFIX)
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public SkynetProperties skynetProperties(Environment environment) {
        return new SkynetProperties(environment);
    }

    @Bean(AppContext.BEAN_NAME)
    public AppContext appContext(ApplicationContext applicationContext, SkynetProperties skynetProperties, Environment environment) throws Exception {
        return new AppContext(applicationContext, skynetProperties, environment);
    }


    /**
     * 是否开启 swagger2 api 文档
     *
     * @return
     */
    @Bean
    @ConfigurationProperties(prefix = "skynet.api.swagger2")
    public SkynetApiSwagger2Properties skynetApiSwagger2Properties() {
        return new SkynetApiSwagger2Properties();
    }


    /**
     * 是否开启 swagger2 api 文档
     * <p>
     * 请使用 skynet.api.swagger2.enabled 代替
     *
     * @return
     */
    @Bean
    @Deprecated
    @ConfigurationProperties(prefix = "springfox.documentation")
    public SpringfoxDocumentationProperties springfoxDocumentationProperties() {
        return new SpringfoxDocumentationProperties();
    }

    @Getter
    @Setter
    public static class SkynetApiSwagger2Properties extends Jsonable {
        /**
         * 是否开启 swagger2 api 文档
         */
        private boolean enabled;
    }

    /**
     * 请使用 skynet.api.swagger2.enabled 代替
     */
    @Deprecated
    @Getter
    @Setter
    public static class SpringfoxDocumentationProperties extends Jsonable {
        /**
         * 是否开启 swagger2 api 文档
         */
        private boolean enabled;
    }
}
