package skynet.boot.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import skynet.boot.web.HttpServletUtils;

/**
 * Web 相关配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
public class SkynetWebConfig {

    /**
     * cors协议支持
     *
     * @return cors协议支持对象
     */
    @Bean
    @ConditionalOnProperty(value = "skynet.web.cors.config.enabled", matchIfMissing = true)
    public WebMvcConfigurer skynetCoresConfigure() {
        return new WebMvcConfigurer() {

            @Override
            public void addCorsMappings(@NotNull CorsRegistry registry) {
                registry.addMapping("/**").allowedOriginPatterns("*")
                        .allowedMethods("HEAD", "GET", "PUT", "POST", "PATCH", "DELETE")
                        .allowedHeaders("*").allowCredentials(true);
            }
        };
    }

    /**
     * 跨域过滤器
     *
     * @return
     */
    @Bean
    @ConditionalOnProperty(value = "skynet.web.cors.config.enabled", matchIfMissing = true)
    public CorsFilter skynetCorsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedOriginPattern("*");
        corsConfiguration.addAllowedMethod("*");
        source.registerCorsConfiguration("/**", corsConfiguration);
        return new CorsFilter(source);
    }

    @Bean
    public HttpServletUtils httpServletUtils(HttpServletRequest request, HttpServletResponse response) {
        return new HttpServletUtils(request, response);
    }
}

