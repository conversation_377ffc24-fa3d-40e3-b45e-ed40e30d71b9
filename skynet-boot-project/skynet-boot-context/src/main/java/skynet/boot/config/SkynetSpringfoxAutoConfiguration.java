package skynet.boot.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.SkynetProperties;
import skynet.boot.annotation.EnableSkynetSwagger2;


/**
 * Swagger 配置
 * <p>
 * 集成服务，可以 重新定义 ApiInfo
 * <p>
 * <p>
 * spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetSwagger2.class)
@ConditionalOnProperty(value = {"springfox.documentation.enabled"}, havingValue = "true", matchIfMissing = true)
public class SkynetSpringfoxAutoConfiguration {

    @Bean
    public OpenAPI customOpenAPI(SkynetProperties skynetProperties) {
        return new OpenAPI()
                .info(new Info()
                        .title(String.format("[%s]Web API Service Interface Documentation", skynetProperties.getActionPoint()))
                        .version("3.0")
                        .description(String.format("[%s] WebAPIService Interface Documentation", skynetProperties.getActionDesc())));
    }

}


