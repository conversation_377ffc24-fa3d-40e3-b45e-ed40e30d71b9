## skynet-boot-starter-swagger (Swagger2 支持)

skynet-boot 采用的是 springfox-boot-starter v3.0

### pom.xml依赖

在目标Java项目 POM.xml 配置中 增加 `skynet-boot-starter-swagger` starter
它内部包含 swagger 相关的依赖， `springfox-boot-starter`

```xml
<dependency>
    <groupId>com.iflytek.skynet</groupId>
    <artifactId>skynet-boot-starter-swagger</artifactId>
</dependency>
```

### 开启Swagger2

在项目的 Springboot 启动类中增加，`@EnableSkynetSwagger2`  注解,
在需要开启Swagger2的API的 Controller 类上添加`@ExposeSwagger2` 注解.

示例:

```java
// SpringBoot 启动入口
import org.springframework.boot.autoconfigure.SpringBootApplication;
import skynet.boot.AppUtils;
import skynet.boot.annotation.EnableSkynetSwagger2;

@EnableSkynetSwagger2
@SpringBootApplication
public class MyBootApp {
    public static void main(String[] args) {
        AppUtils.run(MyBootApp.class, args);
    }
}
```

```java
// 具体的 RestController
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;

@ExposeSwagger2
@RestController
public class MyRestController {

    @RequestMapping(value = "/api/v1/test", method = RequestMethod.GET)
    public String test() throws Exception {
        ...
    }
}
```

### 相关配置

代码中已经开启了`Swagger2`，如果想禁用，可以通过如下配置。

```properties
# default true
springfox.documentation.enabled=true
```

> 备注：兼容以前的 `skynet.api.swagger2.enabled` 配置，优先级高于 `springfox.documentation.enabled`，默认为false

```properties
skynet.api.swagger2.enabled=true
```

### 访问入口

swagger3.0 访问地址与2.x不同，访问地址是：
`http://{ip:port}/swagger-ui/`

> 注意， skynetboot 为了API接口的安全性，建议 目标服务开启 [skynet-security](./security.html) 功能，如果
> 开启了表单验证和BaseAuth验证，swagger-ui 默认优先采用`form表单`验证，就是说只有登录后，才可以访问，否则，访问时会弹出
> base-auth 密码框登录认证。
