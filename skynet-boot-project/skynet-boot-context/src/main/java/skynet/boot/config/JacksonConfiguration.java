package skynet.boot.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "skynet.jackson.config.enabled")
public class JacksonConfiguration {

    private static final String FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";


    @Bean(name = "format")
    public DateTimeFormatter format() {
        return DateTimeFormatter.ofPattern(FORMAT_PATTERN);
    }

    /**
     * 统一 LocalDateTime, Instants 时间返回格式。
     */
    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public JavaTimeModule customJavaTimeModule(@Qualifier("format") DateTimeFormatter format) {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(format));
        javaTimeModule.addSerializer(Instant.class, new InstantCustomSerializer(format));
        javaTimeModule.addSerializer(Date.class, new DateSerializer(false, new SimpleDateFormat(FORMAT_PATTERN)));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(format));
        javaTimeModule.addDeserializer(Instant.class, new InstantCustomDeserializer(format));
        javaTimeModule.addDeserializer(Date.class, new DateCustomDeserializer(new SimpleDateFormat(FORMAT_PATTERN)));
        return javaTimeModule;
    }

    static class InstantCustomSerializer extends JsonSerializer<Instant> {
        private final DateTimeFormatter format;

        private InstantCustomSerializer(DateTimeFormatter formatter) {
            this.format = formatter;
        }

        @Override
        public void serialize(Instant instant, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            if (instant == null) {
                return;
            }
            String jsonValue = this.format.format(instant.atZone(ZoneId.systemDefault()));
            jsonGenerator.writeString(jsonValue);
        }
    }

    static class InstantCustomDeserializer extends JsonDeserializer<Instant> {
        private final DateTimeFormatter format;

        private InstantCustomDeserializer(DateTimeFormatter formatter) {
            this.format = formatter;
        }

        @Override
        public Instant deserialize(JsonParser jsonParser, DeserializationContext context) throws IOException {
            String value = jsonParser.getValueAsString();
            if (StringUtils.isBlank(value)) {
                return null;
            }
            LocalDateTime localDateTime = LocalDateTime.from(this.format.parse(value));
            ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, ZoneId.systemDefault());
            return Instant.from(zonedDateTime);
        }
    }

    static class DateCustomDeserializer extends JsonDeserializer<Date> {
        private final SimpleDateFormat format;

        private DateCustomDeserializer(SimpleDateFormat formatter) {
            this.format = formatter;
        }

        @Override
        public Date deserialize(JsonParser jsonParser, DeserializationContext context) throws IOException {
            String value = jsonParser.getValueAsString();
            if (StringUtils.isBlank(value)) {
                return null;
            }
            try {
                return this.format.parse(value);
            } catch (ParseException e) {
                return null;
            }
        }
    }
}
