## WebSocket支持

主要为了方便基于 `spring-boot-starter-websocket`  (tomcat-embed-websocket) 实现 WebSocket ServerEndpoint 时，不用重复定义
`ServerEndpointExporter` 和 `HttpSessionConfigurator` Bean，同时可以方便获取客户端IP而内置了相关功能的配置。

### 使用示例

默认自动 `@EnableSkynetWebSocket`

```java

// 核心重点：configurator = HttpSessionConfigurator.class
@EnableSkynetWebSocket
@ServerEndpoint(value = "/skynet/nls/v2/demo", configurator = HttpSessionConfigurator.class)
public class DemoServerEndpoint  {
 
    @Autowired
    private WebSocketConfig webSocketConfig;

    @OnOpen
    public final void openSession(Session session) throws IOException, NlsException {

        //通过webSocketConfig 获取客户端IP
        InetSocketAddress inetSocketAddress = webSocketConfig.getRemoteAddress(session);
        this.clientIp = String.valueOf(inetSocketAddress);
        log.debug("[nls-ws-open]sessionId= {};wsId= {};clientIp= {}", sessionId, session.getId(), clientIp);
    ...

    }

    @OnMessage
    public final void onMessage(Session session, byte[] bytes) throws IOException {
       //...
    }

    @OnMessage
    public final void onMessage(Session session, String text) throws IOException {
       //...
    }

    @OnClose
    public final synchronized void onClose(Session session) throws Exception {
       //...
    }

    @OnError
    public void onError(Session session, Throwable t) {
       //...
    }
}

```
