package skynet.boot.config;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJacksonValue;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.AbstractMappingJacksonResponseBodyAdvice;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;


/**
 * TODO： 需要验证 by lyhu
 *
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @date 2019-11-29 16:30
 */
@Slf4j
@ControllerAdvice(basePackages = {"com.*.controller", "skynet.*.controller"})
public class JsonpAdvice extends AbstractMappingJacksonResponseBodyAdvice {

    /**
     * Pattern for validating jsonp callback parameter values.
     */
    private static final Pattern CALLBACK_PARAM_PATTERN = Pattern.compile("[0-9A-Za-z_\\.]*");


    private final List<String> jsonpQueryParamNames = Arrays.asList("callback", "jsonp");

    /**
     * Validate the jsonp query parameter value. The default implementation
     * returns true if it consists of digits, letters, or "_" and ".".
     * Invalid parameter values are ignored.
     *
     * @param value the query param value, never {@code null}
     * @since 4.1.8
     */
    protected boolean isValidJsonpQueryParam(String value) {
        return CALLBACK_PARAM_PATTERN.matcher(value).matches();
    }

    /**
     * Return the content type to set the response to.
     * This implementation always returns "application/javascript".
     *
     * @param contentType the content type selected through content negotiation
     * @param request     the current request
     * @param response    the current response
     * @return the content type to set the response to
     */
    protected MediaType getContentType(MediaType contentType, ServerHttpRequest request, ServerHttpResponse response) {
        return new MediaType("application", "javascript");
    }

    @Override
    protected void beforeBodyWriteInternal(@NotNull MappingJacksonValue bodyContainer,
                                           @NotNull MediaType contentType,
                                           @NotNull MethodParameter returnType,
                                           @NotNull ServerHttpRequest request,
                                           @NotNull ServerHttpResponse response) {
        HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();

        for (String name : this.jsonpQueryParamNames) {
            String value = servletRequest.getParameter(name);
            if (value != null) {
                if (!isValidJsonpQueryParam(value)) {
                    log.debug("Ignoring invalid jsonp parameter value: {}", value);
                    continue;
                }
                MediaType contentTypeToUse = getContentType(contentType, request, response);
                response.getHeaders().setContentType(contentTypeToUse);

                bodyContainer.setValue(value);
                break;
            }
        }
    }
}
