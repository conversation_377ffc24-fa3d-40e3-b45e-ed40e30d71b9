package skynet.boot;

/**
 * Constants used throughout the Skynet framework
 * Contains feature flags, metadata keys, and MDC constants
 *
 * <AUTHOR>
 */
public class SkynetConsts {

    // ============================
    // Gateway Related Constants
    // ============================

    /**
     * Enable Nginx Gateway feature flag
     * Used to indicate if the service should be exposed through Nginx Gateway
     */
    public static final String ACTION_LABEL_NGINX_GATEWAY_CODE = "enableNginxGateway";

    /**
     * Enable API Gateway feature flag
     * Used to indicate if the service should be exposed through API Gateway
     */
    public static final String ACTION_LABEL_API_GATEWAY_CODE = "enableApiGateway";

    // ============================
    // Monitoring Related Constants
    // ============================

    /**
     * Enable Prometheus monitoring feature flag
     * Used to indicate if the service should expose metrics for Prometheus scraping
     */
    public static final String ACTION_LABEL_PROMETHEUS_CODE = "enablePrometheusTarget";

    /**
     * Enable Skywalking tracing feature flag
     * Used to indicate if the service should participate in distributed tracing
     */
    public static final String ACTION_LABEL_ENABLE_SKYWALKING = "enableSkywalking";

    // ============================
    // Stream Processing Constants
    // ============================

    /**
     * Enable Stream Processing feature flag
     * Used to indicate if the service should participate in distributed stream
     * processing
     */
    public static final String ACTION_LABEL_ENABLE_STREAM_LABEL = "enableStream";

    // ============================
    // JVM Related Constants
    // ============================

    /**
     * Disable JVM options append feature flag
     * When enabled, prevents additional JVM options from being appended
     * (Options list is determined by skynet.append.jvm.options.props)
     */
    public static final String ACTION_LABEL_DISABLE_APPEND_JVM_OPTIONS = "disableAppendJvmOptions";

    // ============================
    // Service Discovery Constants
    // ============================

    /**
     * Metadata key for Skynet plugin code in ZooKeeper service discovery
     * Used to identify the plugin type for service registration
     */
    public static final String DISCOVER_METADATA_SKYNET_PLUGIN_CODE_KEY = "SKYNET_PLUGIN_CODE";

    /**
     * Metadata key for Skynet action PID in ZooKeeper service discovery
     * Used to track the process ID of the service instance
     */
    public static final String DISCOVER_METADATA_SKYNET_ACTION_PID = "SKYNET_ACTION_PID";

    // ============================
    // MDC Logging Constants
    // ============================

    /**
     * MDC key for the current trace ID
     * Used in distributed tracing to correlate logs across services
     */
    public static final String MDC_CURRENT_TRACE_ID_KEY = "SKYNET_CURRENT_TRACE_ID";

    /**
     * MDC key for the current span ID
     * Used in distributed tracing to identify specific operations within a trace
     */
    public static final String MDC_CURRENT_SPAN_ID_KEY = "SKYNET_CURRENT_SPAN_ID";
}
