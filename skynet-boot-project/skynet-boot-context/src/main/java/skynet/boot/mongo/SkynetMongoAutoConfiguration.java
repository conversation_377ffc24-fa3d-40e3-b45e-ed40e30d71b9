package skynet.boot.mongo;

import org.bson.types.Decimal128;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.convert.converter.Converter;
import org.springframework.dao.DataAccessException;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;
import org.springframework.data.mapping.context.MappingContext;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.convert.DbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity;
import org.springframework.data.mongodb.core.mapping.MongoPersistentProperty;
import skynet.boot.annotation.EnableSkynetException;
import skynet.boot.annotation.EnableSkynetMongo;
import skynet.boot.exception.handler.ExceptionDescriptor;
import skynet.boot.exception.handler.SkynetExceptionHandler;
import skynet.boot.exception.message.ExceptionMessageFormatter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(MongoCustomConversions.class)
@ConditionalOnBean(annotation = EnableSkynetMongo.class)
@AutoConfigureBefore(MongoDataAutoConfiguration.class)
public class SkynetMongoAutoConfiguration {

    @Configuration(proxyBeanMethods = false)
    @EnableMongoAuditing
    class MongoConfiguration {

    }

    /**
     * 自定义 BigDecimal 转换器，防止保存 Mongo 时 BigDecimal 变 String 的问题
     * https://stackoverflow.com/questions/37950296/spring-data-mongodb-bigdecimal-support
     */
    @Bean
    @ConditionalOnClass(MongoCustomConversions.class)
    public MongoCustomConversions mongoCustomConversions(List<Converter> converters) {
        return new MongoCustomConversions(converters);
    }

    @Bean
    @ConditionalOnProperty(value = "skynet.mongodb.converter.decimal128.enabled", matchIfMissing = true)
    public Converter<BigDecimal, Decimal128> bigDecimalDecimal128Converter() {
        return new BigDecimalDecimal128Converter();
    }

    @Bean
    @ConditionalOnProperty(value = "skynet.mongodb.converter.decimal128.enabled", matchIfMissing = true)
    public Converter<Decimal128, BigDecimal> decimal128BigDecimalConverter() {
        return new Decimal128BigDecimalConverter();
    }

    @Bean
    @Primary
    @ConditionalOnClass(MappingMongoConverter.class)
    public MappingMongoConverter defaultMappingMongoConverter(MongoDatabaseFactory mongoDbFactory,
                                                              MappingContext<? extends MongoPersistentEntity<?>, MongoPersistentProperty> mappingContext,
                                                              MongoCustomConversions mongoCustomConversions) throws Exception {
        DbRefResolver dbRefResolver = new DefaultDbRefResolver(mongoDbFactory);

        MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mappingContext);
        //20210709从“_”改成“#”，避免控制台配置字段中的“_”被替换成“.”
        converter.setMapKeyDotReplacement("#");
        converter.setCustomConversions(mongoCustomConversions);
        converter.afterPropertiesSet();
        return converter;
    }

    @WritingConverter
    private static class BigDecimalDecimal128Converter implements Converter<BigDecimal, Decimal128> {
        @Override
        public Decimal128 convert(@NotNull BigDecimal source) {
            return new Decimal128(source);
        }
    }

    @ReadingConverter
    private static class Decimal128BigDecimalConverter implements Converter<Decimal128, BigDecimal> {
        @Override
        public BigDecimal convert(Decimal128 source) {
            return source.bigDecimalValue();
        }
    }

    @ConditionalOnClass({DataAccessException.class})
    @ConditionalOnBean(annotation = EnableSkynetException.class)
    class DataAccessExceptionHandlerConfiguration {
        @Bean
        public SkynetExceptionHandler<DataAccessException> dataAccessExceptionHandler(ExceptionMessageFormatter formatter) {
            return new SkynetExceptionHandler<DataAccessException>(formatter, () ->
                    new ExceptionDescriptor.ExceptionInfo(6010, "Data Access Exception")) {
            };
        }
    }

}