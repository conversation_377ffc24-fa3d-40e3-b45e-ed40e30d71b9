package skynet.boot.mongo;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonDocument;
import org.bson.BsonInt32;
import org.bson.BsonString;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * MongoUtils 是一个 MongoDB 工具类，主要用于在分片集群环境下自动创建并分片集合。
 * <p>
 * 该工具类支持通过 Spring Data MongoTemplate 或直接通过 MongoDB 连接 URI 操作集合的创建与分片。
 * 适用于需要自动化管理 MongoDB 分片集合的场景，简化分片集合的初始化流程。
 * <p>
 * 特性：
 * <ul>
 *     <li>自动检测集合是否存在，若不存在则创建并分片</li>
 *     <li>支持自定义初始分片块数（numInitialChunks）</li>
 *     <li>支持通过 Spring MongoTemplate 或 MongoDB URI 两种方式</li>
 * </ul>
 *
 * <AUTHOR>
@Slf4j
public class MongoUtils {

    /**
     * MongoDB 主键字段名
     */
    private static final String ID_FIELD = "_id";
    /**
     * 分片类型：哈希
     */
    private static final String HASHED_SHARD_TYPE = "hashed";
    /**
     * 管理数据库名
     */
    private static final String ADMIN_DB = "admin";
    /**
     * 分片集合命令
     */
    private static final String SHARD_COLLECTION_CMD = "shardCollection";
    /**
     * 分片键参数
     */
    private static final String KEY_PARAM = "key";
    /**
     * 初始分片块数参数
     */
    private static final String NUM_INITIAL_CHUNKS_PARAM = "numInitialChunks";

    /**
     * 默认初始分片块数
     */
    public static final int DEFAULT_NUM_INITIAL_CHUNKS = 4096;

    /**
     * 创建并分片集合（如果不存在），初始分片块数使用默认值。
     *
     * @param mongoTemplate Spring Data MongoTemplate 实例
     * @param collection    集合名称
     * @throws RuntimeException 创建或分片失败时抛出
     */
    public static void createCollectionIfNotExists(MongoTemplate mongoTemplate, String collection) {
        createCollectionIfNotExists(mongoTemplate, collection, DEFAULT_NUM_INITIAL_CHUNKS);
    }

    /**
     * 创建并分片集合（如果不存在）。
     *
     * @param mongoTemplate    Spring Data MongoTemplate 实例
     * @param collection       集合名称
     * @param numInitialChunks 初始分片块数，若为 0 则使用默认值
     * @throws RuntimeException 创建或分片失败时抛出
     */
    public static void createCollectionIfNotExists(MongoTemplate mongoTemplate, String collection, int numInitialChunks) {
        try {
            List<String> collectionNames = mongoTemplate.getDb().listCollectionNames().into(new ArrayList<>());
            if (!collectionNames.contains(collection)) {
                log.info("Collection {} does not exist. Creating and sharding...", collection);

                if (numInitialChunks == 0) {
                    numInitialChunks = DEFAULT_NUM_INITIAL_CHUNKS;
                }

                BsonDocument shardCommand = new BsonDocument(SHARD_COLLECTION_CMD,
                        new BsonString(String.format("%s.%s", mongoTemplate.getDb().getName(), collection)))
                        .append(KEY_PARAM, new BsonDocument(ID_FIELD, new BsonString(HASHED_SHARD_TYPE)))
                        .append(NUM_INITIAL_CHUNKS_PARAM, new BsonInt32(numInitialChunks));

                mongoTemplate.getMongoDatabaseFactory().getMongoDatabase(ADMIN_DB).runCommand(shardCommand);
                log.info("Collection {} created and sharded with {} initial chunks", collection, numInitialChunks);
            }
        } catch (Exception e) {
            log.error("Failed to create or shard collection: {}", collection, e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 创建并分片集合（如果不存在），初始分片块数使用默认值。建议调用层 可以使用缓存,避免多次判断创建
     *
     * @param uri
     * @param collectionFullName lynxiao_resource.DS03RZRY_002
     * @param numInitialChunks
     */
    public static void createCollectionIfNotExists(String uri, String collectionFullName, int numInitialChunks) {
        log.info("create collection if not exist, uri={}, shardCollection={}, numInitialChunks={}", uri, collectionFullName, numInitialChunks);

        try (MongoClient mongoClient = MongoClients.create(uri)) {
            String[] splitCollection = collectionFullName.split("\\.");
            String db = splitCollection[0];
            String collection = splitCollection[1];
            List<String> collectionNames = mongoClient.getDatabase(db).listCollectionNames().into(new ArrayList<>());
            if (collectionNames.contains(collection)) {
                log.warn("label collection already exist, collectionName=[{}]", collectionFullName);
                return;
            }

            if (numInitialChunks == 0) {
                numInitialChunks = DEFAULT_NUM_INITIAL_CHUNKS;
            }
            BsonDocument shardCommand = new BsonDocument(SHARD_COLLECTION_CMD, new BsonString(collectionFullName))
                    .append(KEY_PARAM, new BsonDocument(ID_FIELD, new BsonString(HASHED_SHARD_TYPE)))
                    .append(NUM_INITIAL_CHUNKS_PARAM, new BsonInt32(numInitialChunks));
            mongoClient.getDatabase(ADMIN_DB).runCommand(shardCommand);
            log.info("Collection created and sharded with initial chunks.");
        }
    }
}
