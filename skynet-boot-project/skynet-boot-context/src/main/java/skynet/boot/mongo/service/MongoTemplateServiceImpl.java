package skynet.boot.mongo.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.repository.MongoRepository;
import skynet.boot.common.dto.AbstractPatchDTO;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.TemplateService;
import skynet.boot.common.service.TenantAware;
import skynet.boot.common.service.filter.Filter;
import skynet.boot.exception.EntityNotFoundException;
import skynet.boot.mongo.domain.AbstractAuditingEntity;

import java.lang.reflect.Field;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于 Mongo 的 Service 基类
 */
@Slf4j
public class MongoTemplateServiceImpl<E extends AbstractAuditingEntity<ID>, D, ID> extends MongoQueryService implements TemplateService<E, D, ID> {

    private final MongoRepository<E, ID> mongoRepository;
    private final EntityMapper<D, E> entityMapper;
    private final MongoOperations mongoOperations;

    @Autowired(required = false)
    private TenantAware tenantAware;

    public MongoTemplateServiceImpl(MongoRepository<E, ID> mongoRepository, EntityMapper<D, E> entityMapper, MongoOperations mongoOperations) {
        this.mongoRepository = mongoRepository;
        this.entityMapper = entityMapper;
        this.mongoOperations = mongoOperations;
    }


    @Override
    public Page<D> findAllByCriteria(skynet.boot.common.service.Criteria criteria, Class<E> clazz, Pageable pageable) {
        Query query = new Query(createCriteria(criteria));
        long count = mongoOperations.count(query, clazz);
        List<D> list = mongoOperations.find(query.with(pageable), clazz).stream().map(entityMapper::toDto).collect(Collectors.toList());
        return new PageImpl<>(list, pageable, count);
    }


    @Override
    public List<D> findAllByCriteria(skynet.boot.common.service.Criteria criteria, Class<E> clazz) {
        Query query = new Query(createCriteria(criteria));
        return entityMapper.toDto(mongoOperations.find(query, clazz));
    }

    @Override
    public List<D> findAllByCriteria(skynet.boot.common.service.Criteria criteria, Class<E> clazz, Sort sort) {
        Query query = new Query(createCriteria(criteria));
        return entityMapper.toDto(mongoOperations.find(query.with(sort), clazz));
    }

    protected Criteria createCriteria(skynet.boot.common.service.Criteria criteria) {
        Criteria result = new Criteria();
        if (criteria != null) {
            Class<?> generatedCriteriaClass = criteria.getClass().getSuperclass();
            List<Field> fields = new ArrayList<>(Arrays.asList(criteria.getClass().getDeclaredFields()));
            fields.addAll(Arrays.asList(generatedCriteriaClass.getDeclaredFields()));

            for (Field field : fields) {
                try {
                    field.setAccessible(true);
                    Object fieldValue = field.get(criteria);
                    if (fieldValue instanceof Filter) {
                        result = buildCriteria(result, (Filter) fieldValue, field.getName());
                    }
                } catch (IllegalAccessException e) {
                    log.error("createCriteria error", e);
                }
            }
        }

        // 按租户过滤
        String tenantId = getCurrentTenant();
        if (StringUtils.isNotBlank(tenantId)) {
            result = result.and("tenantId").is(tenantId);
        }
        return result;
    }


    @Override
    public D findById(ID id) {
        E entity = mongoRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        return entityMapper.toDto(entity);
    }

    @Override
    public D save(D dto) {
        E entity = entityMapper.toEntity(dto);
        entity.setCreatedDate(Instant.now());
        if (getCurrentTenant() != null) {
            entity.setTenantId(getCurrentTenant());
        }
        return entityMapper.toDto(mongoRepository.save(entity));
    }

    @Override
    public D update(ID id, D dto) {
        E entity = mongoRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        E newEntity = entityMapper.toEntity(dto);
        newEntity.setId(entity.getId());
        newEntity.setCreatedDate(entity.getCreatedDate());
        newEntity.setCreatedBy(entity.getCreatedBy());
        newEntity.setLastModifiedDate(Instant.now());
        if (getCurrentTenant() != null) {
            newEntity.setTenantId(getCurrentTenant());
        }
        return entityMapper.toDto(mongoRepository.save(newEntity));
    }

    @Override
    public D patch(ID id, AbstractPatchDTO<ID> patchDTO) {
        E entity = mongoRepository.findById(id).orElseThrow(EntityNotFoundException::new);

        try {
            Class<?> entityClazz = entity.getClass();
            Class<?> patchDtoClass = patchDTO.getClass();
            Class<?> generatedPatchDtoClass = patchDtoClass.getSuperclass();

            List<Field> fields = new ArrayList<>(Arrays.asList(patchDtoClass.getDeclaredFields()));
            fields.addAll(Arrays.asList(generatedPatchDtoClass.getDeclaredFields()));
            for (Field dtoField : fields.stream().filter(field -> !"serialVersionUID".equals(field.getName())).collect(Collectors.toList())) {
                try {
                    Field entityField = entityClazz.getDeclaredField(dtoField.getName());
                    dtoField.setAccessible(true);
                    if (dtoField.get(patchDTO) != null) {
                        entityField.setAccessible(true);
                        entityField.set(entity, dtoField.get(patchDTO));
                    }
                } catch (Throwable e) {
                    log.error("更新{}字段出现异常！", dtoField.getName(), e);
                }
            }
        } catch (Throwable e) {
            log.error("更新部分字段出现异常！", e);
            return null;
        }
        entity.setLastModifiedDate(Instant.now());
        return entityMapper.toDto(mongoRepository.save(entity));
    }

    @Override
    public void delete(ID id) {
        mongoRepository.deleteById(id);
    }


    @Override
    public D copy(ID id, List<String> renameFields, Class<E> clazz) {
        E entity = mongoRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        entity.setId(null);
        entity.setCreatedDate(Instant.now());
        entity.setLastModifiedDate(Instant.now());
        if (getCurrentTenant() != null) {
            entity.setTenantId(getCurrentTenant());
        }
        for (String renameField : renameFields) {
            try {
                Field field = clazz.getDeclaredField(renameField);
                field.setAccessible(true);
                field.set(entity, generateCopy(clazz, renameField, String.valueOf(field.get(entity))));
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.error("copy error", e);
            }
        }
        return entityMapper.toDto(mongoRepository.save(entity));
    }

    /**
     * 针对某个表和字段，生成该字段值的一个拷贝，并保证值不会重复。
     *
     * @param <T>
     * @param clazz      表名
     * @param fieldName  字段名
     * @param fieldValue 字段值，动态生成改字段值的一个拷贝
     * @return 返回名称拷贝
     */
    private <T> String generateCopy(Class<T> clazz, String fieldName, String fieldValue) {
        Pair<String, Long> pair = initCopyPair(fieldValue);
        Long index = pair.getRight();
        Criteria criteria = Criteria.where(fieldName).is(pair.getLeft() + "-" + index);
        while (mongoOperations.exists(new Query(criteria), clazz)) {
            index++;
            criteria = Criteria.where(fieldName).is(pair.getLeft() + "-" + index);
        }
        return pair.getLeft() + "-" + index;
    }

    /**
     * 将名称分隔成 <head>-<tail> 两部分，其中 <head> 为原始名称，<tail> 为名称序号，并在序号上加一。
     * 如果名称中不存在序号，默认序号从一开始。
     */
    private Pair<String, Long> initCopyPair(String name) {
        int index = name.lastIndexOf("-");
        if (index >= 0) {
            String head = name.substring(0, index);
            String tail = name.substring(index + 1);
            if (!tail.isEmpty() && tail.chars().allMatch(Character::isDigit)) {
                return Pair.of(head, Long.parseLong(tail) + 1);
            }
        }
        return Pair.of(name, 1L);
    }

    /**
     * 获取当前的租户 ID
     */
    private String getCurrentTenant() {
        String tenantId = null;
        if (tenantAware != null) {
            tenantId = tenantAware.getCurrentTenant();
        }
        return tenantId;
    }
}