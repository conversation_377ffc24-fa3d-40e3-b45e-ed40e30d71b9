package skynet.boot.mongo.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.query.Criteria;
import skynet.boot.common.service.filter.BooleanFilter;
import skynet.boot.common.service.filter.Filter;
import skynet.boot.common.service.filter.RangeFilter;
import skynet.boot.common.service.filter.StringFilter;

import java.util.regex.Pattern;

/**
 * 用于构造 Mongo 查询条件
 */
public abstract class MongoQueryService {

    private final Pattern SPECIAL_REGEX_CHARS = Pattern.compile("[{}()\\[\\].+*?^$\\\\|]");

    /**
     * 根据 Filter 构造 Mongo 查询条件
     *
     * @param criteria
     * @param filter
     * @param field
     * @return
     */
    protected Criteria buildCriteria(Criteria criteria, Filter filter, String field) {
        if (filter instanceof StringFilter) {
            return buildCriteria(criteria, (StringFilter) filter, field);
        } else if (filter instanceof BooleanFilter) {
            return buildCriteria(criteria, (Bo<PERSON>anFilter) filter, field);
        } else {
            return buildCriteria(criteria, (RangeFilter) filter, field);
        }
    }

    /**
     * 根据 StringFilter 构造 Mongo 查询条件
     *
     * @param criteria
     * @param filter
     * @param field
     * @return
     */
    private Criteria buildCriteria(Criteria criteria, StringFilter filter, String field) {
        if (StringUtils.isNotBlank(filter.getEquals())) {
            criteria.and(field).is(filter.getEquals());
        } else if (filter.getIn() != null) {
            criteria.and(field).in(filter.getIn());
        } else if (filter.getNotIn() != null) {
            criteria.and(field).nin(filter.getNotIn());
        } else if (StringUtils.isNotBlank(filter.getContains())) {
            criteria.and(field).regex(".*" + escapeSpecialRegexChars(filter.getContains()) + ".*", "i");
        } else if (StringUtils.isNotBlank(filter.getDoesNotContain())) {
            criteria.and(field).not().regex(".*" + escapeSpecialRegexChars(filter.getDoesNotContain()) + ".*", "i");
        } else if (StringUtils.isNotBlank(filter.getNotEquals())) {
            criteria.and(field).ne(filter.getNotEquals());
        }
        return criteria;
    }

    // 对字符串中出现的正则特殊符合进行转义
    private String escapeSpecialRegexChars(String str) {
        return SPECIAL_REGEX_CHARS.matcher(str).replaceAll("\\\\$0");
    }

    /**
     * 根据 RangeFilter 构造 Mongo 查询条件
     *
     * @param criteriaOld
     * @param filter
     * @param field
     * @return
     */
    private Criteria buildCriteria(Criteria criteriaOld, RangeFilter filter, String field) {

        // 如果所有 filter 都为空，直接返回
        if (filter.getEquals() == null &&
                filter.getNotEquals() == null &&
                filter.getIn() == null &&
                filter.getNotIn() == null &&
                filter.getGreaterThan() == null &&
                filter.getGreaterThanOrEqual() == null &&
                filter.getLessThan() == null &&
                filter.getLessThanOrEqual() == null) {
            return criteriaOld;
        }

        Criteria criteria = criteriaOld.and(field);
        if (filter.getEquals() != null) {
            criteria.is(filter.getEquals());
        }
        if (filter.getNotEquals() != null) {
            criteria.ne(filter.getNotEquals());
        }
        if (filter.getIn() != null) {
            criteria.in(filter.getIn());
        }
        if (filter.getNotIn() != null) {
            criteria.nin(filter.getNotIn());
        }
        if (filter.getGreaterThan() != null) {
            criteria.gt(filter.getGreaterThan());
        }
        if (filter.getGreaterThanOrEqual() != null) {
            criteria.gte(filter.getGreaterThanOrEqual());
        }
        if (filter.getLessThan() != null) {
            criteria.lt(filter.getLessThan());
        }
        if (filter.getLessThanOrEqual() != null) {
            criteria.lte(filter.getLessThanOrEqual());
        }
        return criteria;
    }

    /**
     * 根据 BooleanFilter 构造 Mongo 查询条件
     *
     * @param criteria
     * @param filter
     * @param field
     * @return
     */
    private Criteria buildCriteria(Criteria criteria, BooleanFilter filter, String field) {
        if (filter.getEquals() != null) {
            criteria.and(field).is(filter.getEquals());
        } else if (filter.getNotEquals() != null) {
            criteria.and(field).ne(filter.getNotEquals());
        }
        return criteria;
    }
}
