package skynet.boot.mongo;

import lombok.extern.slf4j.Slf4j;
import org.bson.types.Binary;
import skynet.boot.common.GZipUtils;

import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * MongoDB文档压缩和解压缩工具类，用于优化大型文档的存储效率。
 *
 * <p><b>功能描述：</b></p>
 * 本工具类主要用于处理MongoDB文档中的大型字段压缩，通过GZIP算法实现数据压缩和解压缩。针对不同数据类型
 * 提供专门的压缩策略，可以显著减少存储空间占用。
 *
 * <p><b>支持的数据类型：</b></p>
 * <ul>
 *   <li>String - 对超过阈值的长字符串进行压缩</li>
 *   <li>double[], List&lt;Double&gt; - 压缩浮点数数组</li>
 *   <li>float[], List&lt;Float&gt; - 压缩单精度浮点数数组</li>
 *   <li>int[], List&lt;Integer&gt; - 压缩整数数组</li>
 *   <li>long[], List&lt;Long&gt; - 压缩长整型数组</li>
 * </ul>
 *
 * <p><b>重要注意事项：</b></p>
 * <ul>
 *   <li>性能考量：
 *     <ul>
 *       <li>压缩/解压缩操作会消耗额外的CPU资源</li>
 *       <li>建议仅对大型数据（超过阈值）进行压缩</li>
 *       <li>默认压缩阈值：数组200个元素，字符串100个字符</li>
 *     </ul>
 *   </li>
 *   <li>线程安全：
 *     <ul>
 *       <li>本类的实例是线程安全的，所有方法都是无状态的</li>
 *       <li>建议使用单例模式通过getDefault()方法获取实例</li>
 *     </ul>
 *   </li>
 *   <li>最佳实践：
 *     <ul>
 *       <li>根据实际数据特点合理设置压缩阈值</li>
 *       <li>对于频繁访问的小型文档，建议不要使用压缩</li>
 *       <li>压缩后的字段会带有特定后缀（如_str_gzip）</li>
 *     </ul>
 *   </li>
 * </ul>
 *
 * <p><b>代码示例：</b></p>
 * <pre>{@code
 * // 1. 使用默认配置
 * DocumentCompressor compressor = DocumentCompressor.getDefault();
 *
 * // 2. 自定义压缩阈值
 * DocumentCompressor customCompressor = new DocumentCompressor(500, 200); // 数组阈值500，字符串阈值200
 *
 * // 3. 实际使用示例
 * Map<String, Object> doc = new LinkedHashMap<>();
 * doc.put("description", "这是一个很长的描述文本...");     // 字符串字段
 * doc.put("scores", new float[]{98.5f, 92.0f, 88.5f}); // 浮点数组
 * doc.put("tags", Arrays.asList("tag1", "tag2", "tag3")); // 列表字段
 *
 * // 压缩文档
 * Map<String, Object> compressed = compressor.compress(doc);
 *
 * // 解压文档
 * Map<String, Object> decompressed = compressor.decompress(compressed);
 * }</pre>
 *
 * <p><b>监控指标建议：</b></p>
 * <ul>
 *   <li>压缩率：压缩前后文档大小的比例</li>
 *   <li>压缩耗时：压缩和解压缩操作的执行时间</li>
 *   <li>压缩命中率：实际被压缩的字段数量与总字段数量的比例</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2024/04/01
 * @since add v4.3.3
 */
@Slf4j
public class DocumentCompressor {
    public static final int DEFAULT_ARRAY_COMPRESSION_THRESHOLD = 200;
    public static final int DEFAULT_STRING_COMPRESSION_THRESHOLD = 100;

    public static final int LIMIT_COMPRESSION_THRESHOLD = 100;

    public static final String GZIP_SUFFIX = "_gzip";
    public static final String STRING_SUFFIX = "_str" + GZIP_SUFFIX;
    public static final String DOUBLE_ARRAY_SUFFIX = "_da" + GZIP_SUFFIX;
    public static final String FLOAT_ARRAY_SUFFIX = "_fa" + GZIP_SUFFIX;
    public static final String INT_ARRAY_SUFFIX = "_ia" + GZIP_SUFFIX;
    public static final String LONG_ARRAY_SUFFIX = "_la" + GZIP_SUFFIX;

    private static final DocumentCompressor defaultCompressor = new DocumentCompressor();
    private final int arrayCompressionThreshold;
    private final int stringCompressionThreshold;


    public DocumentCompressor() {
        this(DEFAULT_ARRAY_COMPRESSION_THRESHOLD, DEFAULT_STRING_COMPRESSION_THRESHOLD);
    }

    public DocumentCompressor(int arrayCompressionThreshold) {
        this(arrayCompressionThreshold, DEFAULT_STRING_COMPRESSION_THRESHOLD);
    }


    public DocumentCompressor(int arrayCompressionThreshold, int stringCompressionThreshold) {
        this.arrayCompressionThreshold = arrayCompressionThreshold;
        this.stringCompressionThreshold = stringCompressionThreshold;
    }

    public static DocumentCompressor getDefault() {
        return defaultCompressor;
    }

    /**
     * 压缩单个文档
     */
    public Map<String, Object> compress(Map<String, Object> document) {
        if (this.arrayCompressionThreshold < LIMIT_COMPRESSION_THRESHOLD || document == null) {
            log.debug("Skipping compression: threshold {} < limit {} or document is null", this.arrayCompressionThreshold, LIMIT_COMPRESSION_THRESHOLD);
            return document;
        }

        log.debug("Starting document compression with array len threshold={}; string len threshold={}", arrayCompressionThreshold, stringCompressionThreshold);
        Map<String, Object> result = new LinkedHashMap<>(document.size());
        for (Map.Entry<String, Object> entry : document.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            // 如果 key 以 "_gzip" 结尾，则不进行压缩处理
            if (key.endsWith(GZIP_SUFFIX)) {
                result.put(key, value);
            } else switch (value) {
                case String str when str.length() > stringCompressionThreshold -> {
                    log.debug("Compressing string field '{}' with length {}", key, str.length());
                    result.put(key + STRING_SUFFIX, GZipUtils.compress(str.getBytes(StandardCharsets.UTF_8)));
                }
                case double[] doubleArray when doubleArray.length > arrayCompressionThreshold -> {
                    log.debug("Compressing double array field '{}' with length {}", key, doubleArray.length);
                    result.put(key + DOUBLE_ARRAY_SUFFIX, GZipUtils.compress(doubleArrayToBinary(doubleArray)));
                }
                case float[] floatArray when floatArray.length > arrayCompressionThreshold -> {
                    log.debug("Compressing float array field '{}' with length {}", key, floatArray.length);
                    result.put(key + FLOAT_ARRAY_SUFFIX, GZipUtils.compress(floatArrayToBinary(floatArray)));
                }
                case int[] intArray when intArray.length > arrayCompressionThreshold -> {
                    log.debug("Compressing int array field '{}' with length {}", key, intArray.length);
                    result.put(key + INT_ARRAY_SUFFIX, GZipUtils.compress(intArrayToBinary(intArray)));
                }
                case long[] longArray when longArray.length > arrayCompressionThreshold -> {
                    log.debug("Compressing long array field '{}' with length {}", key, longArray.length);
                    result.put(key + LONG_ARRAY_SUFFIX, GZipUtils.compress(longArrayToBinary(longArray)));
                }
                case List<?> list when !list.isEmpty() && list.size() > arrayCompressionThreshold -> {
                    log.debug("Compressing list field '{}' with size {}", key, list.size());
                    compressList(key, list, result);
                }
                case Map map -> result.put(key, compress(map));
                case List<?> list -> result.put(key, compressNestedList(list));
                case null, default -> result.put(key, value);
            }
        }
        log.debug("Document compression completed");
        return result;
    }

    private void compressList(String key, List<?> list, Map<String, Object> result) {
        Object first = list.getFirst();
        switch (first) {
            case Double d -> {
                log.debug("Compressing double list '{}' to array", key);
                double[] array = new double[list.size()];
                for (int i = 0; i < list.size(); i++) {
                    array[i] = (Double) list.get(i);
                }
                result.put(key + DOUBLE_ARRAY_SUFFIX, GZipUtils.compress(doubleArrayToBinary(array)));
            }
            case Float v -> {
                log.debug("Compressing float list '{}' to array", key);
                float[] array = new float[list.size()];
                for (int i = 0; i < list.size(); i++) {
                    array[i] = (Float) list.get(i);
                }
                result.put(key + FLOAT_ARRAY_SUFFIX, GZipUtils.compress(floatArrayToBinary(array)));
            }
            case Integer i -> {
                log.debug("Compressing integer list '{}' to array", key);
                int[] array = list.stream().map(num -> (Integer) num).mapToInt(Integer::intValue).toArray();
                result.put(key + INT_ARRAY_SUFFIX, GZipUtils.compress(intArrayToBinary(array)));
            }
            case Long l -> {
                log.debug("Compressing long list '{}' to array", key);
                long[] array = list.stream().map(num -> (Long) num).mapToLong(Long::longValue).toArray();
                result.put(key + LONG_ARRAY_SUFFIX, GZipUtils.compress(longArrayToBinary(array)));
            }
            case null, default -> result.put(key, list);
        }
    }

    private List<Object> compressNestedList(List<?> list) {
        List<Object> newList = new ArrayList<>();
        for (Object item : list) {
            if (item instanceof Map map) {
                newList.add(compress(map));
            } else {
                newList.add(item);
            }
        }
        return newList;
    }

    /**
     * 解压单个文档
     */
    public Map<String, Object> decompress(Map<String, Object> document) {
        if (document == null) {
            log.debug("Skipping decompression: document is null");
            return null;
        }

        log.debug("Starting document decompression");
        Map<String, Object> result = new LinkedHashMap<>(document.size());
        for (Map.Entry<String, Object> entry : document.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (key.endsWith(STRING_SUFFIX)) {
                log.debug("Decompressing string field '{}'", key);
                result.put(removeCompressSuffix(key, STRING_SUFFIX), decompressValue(key, value));
            } else if (key.endsWith(DOUBLE_ARRAY_SUFFIX)) {
                log.debug("Decompressing double array field '{}'", key);
                result.put(removeCompressSuffix(key, DOUBLE_ARRAY_SUFFIX), decompressValue(key, value));
            } else if (key.endsWith(FLOAT_ARRAY_SUFFIX)) {
                log.debug("Decompressing float array field '{}'", key);
                result.put(removeCompressSuffix(key, FLOAT_ARRAY_SUFFIX), decompressValue(key, value));
            } else if (key.endsWith(INT_ARRAY_SUFFIX)) {
                log.debug("Decompressing int array field '{}'", key);
                result.put(removeCompressSuffix(key, INT_ARRAY_SUFFIX), decompressValue(key, value));
            } else if (key.endsWith(LONG_ARRAY_SUFFIX)) {
                log.debug("Decompressing long array field '{}'", key);
                result.put(removeCompressSuffix(key, LONG_ARRAY_SUFFIX), decompressValue(key, value));
            } else if (value instanceof Map map) {
                result.put(key, decompress(map));
            } else if (value instanceof List<?> list) {
                result.put(key, decompressNestedList(list));
            } else {
                result.put(key, value);
            }
        }
        log.debug("Document decompression completed");
        return result;
    }

    private String removeCompressSuffix(String key, String suffix) {
        return key.substring(0, key.length() - suffix.length());
    }

    private Object decompressValue(String key, Object value) {
        try {
            if (value instanceof byte[] || value instanceof Binary) {
                byte[] binary = value instanceof Binary ? ((Binary) value).getData() : (byte[]) value;
                if (key.endsWith(STRING_SUFFIX)) {
                    return new String(GZipUtils.decompress(binary), StandardCharsets.UTF_8);
                } else if (key.endsWith(DOUBLE_ARRAY_SUFFIX)) {
                    return binaryToDoubleArray(GZipUtils.decompress(binary));
                } else if (key.endsWith(FLOAT_ARRAY_SUFFIX)) {
                    return binaryToFloatArray(GZipUtils.decompress(binary));
                } else if (key.endsWith(INT_ARRAY_SUFFIX)) {
                    return binaryToIntArray(GZipUtils.decompress(binary));
                } else if (key.endsWith(LONG_ARRAY_SUFFIX)) {
                    return binaryToLongArray(GZipUtils.decompress(binary));
                }
            } else {
                log.warn("Failed to decompress field: value type is not byte[] or Binary [key={}, actualType={}]", key, value.getClass().getSimpleName());
            }
        } catch (Exception e) {
            log.error("Failed to decompress field: {}={}; error={}", key, value, e.getMessage());
            throw e;
        }
        return value;
    }

    private List<Object> decompressNestedList(List<?> list) {
        List<Object> newList = new ArrayList<>();
        for (Object item : list) {
            if (item instanceof Map map) {
                newList.add(decompress(map));
            } else {
                newList.add(item);
            }
        }
        return newList;
    }

    // =============== Binary Conversion Methods ===============

    public byte[] floatArrayToGzipBinary(float[] floatArray) {
        return GZipUtils.compress(floatArrayToBinary(floatArray));
    }

    public float[] gzipBinaryToFloatArray(byte[] gzipBinary) {
        return binaryToFloatArray(GZipUtils.decompress(gzipBinary));
    }

    public byte[] floatArrayToBinary(float[] floatArray) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(floatArray.length * Float.BYTES);
        FloatBuffer floatBuffer = byteBuffer.asFloatBuffer();
        floatBuffer.put(floatArray);
        return byteBuffer.array();
    }

    public float[] binaryToFloatArray(byte[] binary) {
        ByteBuffer buffer = ByteBuffer.wrap(binary);
        float[] vector = new float[binary.length / Float.BYTES];
        for (int i = 0; i < vector.length; i++) {
            vector[i] = buffer.getFloat();
        }
        return vector;
    }

    public byte[] intArrayToBinary(int[] intArray) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(intArray.length * Integer.BYTES);
        for (int value : intArray) {
            byteBuffer.putInt(value);
        }
        return byteBuffer.array();
    }

    public int[] binaryToIntArray(byte[] binary) {
        ByteBuffer buffer = ByteBuffer.wrap(binary);
        int[] array = new int[binary.length / Integer.BYTES];
        for (int i = 0; i < array.length; i++) {
            array[i] = buffer.getInt();
        }
        return array;
    }

    public byte[] longArrayToBinary(long[] longArray) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(longArray.length * Long.BYTES);
        for (long value : longArray) {
            byteBuffer.putLong(value);
        }
        return byteBuffer.array();
    }

    public long[] binaryToLongArray(byte[] binary) {
        ByteBuffer buffer = ByteBuffer.wrap(binary);
        long[] array = new long[binary.length / Long.BYTES];
        for (int i = 0; i < array.length; i++) {
            array[i] = buffer.getLong();
        }
        return array;
    }

    public byte[] doubleArrayToGzipBinary(double[] doubleArray) {
        return GZipUtils.compress(doubleArrayToBinary(doubleArray));
    }

    public double[] gzipBinaryToDoubleArray(byte[] gzipBinary) {
        return binaryToDoubleArray(GZipUtils.decompress(gzipBinary));
    }

    public byte[] doubleArrayToBinary(double[] doubleArray) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(doubleArray.length * Double.BYTES);
        for (double value : doubleArray) {
            byteBuffer.putDouble(value);
        }
        return byteBuffer.array();
    }

    public double[] binaryToDoubleArray(byte[] binary) {
        ByteBuffer buffer = ByteBuffer.wrap(binary);
        double[] array = new double[binary.length / Double.BYTES];
        for (int i = 0; i < array.length; i++) {
            array[i] = buffer.getDouble();
        }
        return array;
    }
}

