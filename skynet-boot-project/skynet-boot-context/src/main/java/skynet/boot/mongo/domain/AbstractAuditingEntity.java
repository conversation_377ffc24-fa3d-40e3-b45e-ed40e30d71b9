package skynet.boot.mongo.domain;

import jakarta.persistence.EntityListeners;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.mongodb.core.mapping.Field;
import skynet.boot.common.domain.Jsonable;

import java.io.Serializable;
import java.time.Instant;

/**
 * Base abstract class for entities which will hold definitions for created, last modified, created by,
 * last modified by attributes.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public abstract class AbstractAuditingEntity<ID> extends Jsonable implements Serializable {

    /**
     * 主键ID
     */
    @Id
    private ID id;

    @CreatedBy
    @Field(name = "created_by")
    private String createdBy;

    @CreatedDate
    @Field(name = "created_date")
    private Instant createdDate;

    @LastModifiedBy
    @Field(name = "last_modified_by")
    private String lastModifiedBy;

    @LastModifiedDate
    @Field(name = "last_modified_date")
    private Instant lastModifiedDate;

    @Field(name = "tenant_id")
    private String tenantId;
}
