package skynet.boot;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import skynet.boot.common.OsUtil;
import skynet.boot.common.domain.Jsonable;

/**
 * Skynet Framework Properties Configuration
 * 
 * This class manages the core configuration properties for the Skynet
 * framework.
 * It handles service identification, location, and runtime properties.
 *
 * Configuration example:
 * 
 * <pre>
 * skynet.action-id=ant-cloud-eureka-v10@ant
 * skynet.action-point=ant-cloud-eureka-v10@ant
 * skynet.action-title=[Base Service] Service Registry
 * skynet.action-desc=[Base Service] Service Registry - Eureka
 * </pre>
 *
 * <AUTHOR> [Oct 10, 2017 7:18:10 PM]
 */
@Getter
@Setter
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SkynetProperties extends Jsonable {

    // ============================
    // Static Configuration Keys
    // ============================

    public static final String BEAN_NAME = "skynet.properties";
    public static final String PROP_PREFIX = "skynet";

    /**
     * @deprecated Use {@link #SKYNET_ACTION_POINT_KEY} instead
     */
    @Deprecated
    public static final String SKYNET_ACTION_NAME_KEY = "skynet.actionName";
    public static final String SKYNET_ACTION_POINT_KEY = "skynet.actionPoint";
    public static final String SKYNET_ACTION_ID_KEY = "skynet.actionId";
    public static final String SKYNET_ACTION_TITLE_KEY = "skynet.actionTitle";
    public static final String SKYNET_ACTION_DESC_KEY = "skynet.actionDesc";
    public static final String SKYNET_HOME_KEY = "skynet.home";
    public static final String SKYNET_IP_KEY = "skynet.ipAddress";

    // ============================
    // Environment Configuration
    // ============================

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private final Environment environment;

    // ============================
    // Service Identity Properties
    // ============================

    /**
     * Managed service identifier
     * Defaults to actionPoint if not configured
     */
    @JSONField(ordinal = 10)
    @JsonProperty(index = 10)
    private String actionId;

    /**
     * Service coordinate name
     * Format: {service-name}@{plugin}
     * Example: ant-cloud-eureka-v10@ant
     */
    @JSONField(ordinal = 20)
    @JsonProperty(index = 20)
    private String actionPoint;

    /**
     * Service code extracted from actionPoint
     * Example: ant-cloud-eureka-v10
     */
    @JSONField(ordinal = 30)
    @JsonProperty(index = 30)
    private String actionCode;

    /**
     * Human-readable service title
     * Example: [Base Service] Service Registry
     */
    @JSONField(ordinal = 40)
    @JsonProperty(index = 40)
    private String actionTitle;

    /**
     * Detailed service description
     * Example: [Base Service] Service Registry - Eureka
     */
    @JSONField(ordinal = 50)
    @JsonProperty(index = 50)
    private String actionDesc;

    /**
     * Plugin code for the managed service
     * Example: ant
     */
    @JSONField(ordinal = 60)
    @JsonProperty(index = 60)
    private String plugin;

    // ============================
    // Network Properties
    // ============================

    /**
     * Current machine's IP address
     * Will be resolved automatically if not set
     */
    @JSONField(ordinal = 70)
    @JsonProperty(index = 70)
    private String ipAddress;

    /**
     * Current service port
     * Defaults to 8080 if not configured
     */
    @JSONField(ordinal = 80)
    @JsonProperty(index = 80)
    private int port;

    // ============================
    // Runtime Properties
    // ============================

    /**
     * Skynet platform deployment directory
     * Example: /iflytek/server/skynet
     */
    @JSONField(ordinal = 90)
    @JsonProperty(index = 90)
    private String home;

    /**
     * Debug mode flag
     * Enabled by --debug or --debug=true argument
     */
    @JSONField(ordinal = 100)
    @JsonProperty(index = 100)
    private boolean debugMode;

    /**
     * Constructs SkynetProperties with the given environment
     * Initializes core properties from environment variables and system properties
     *
     * @param environment Spring Environment object containing configuration
     *                    properties
     */
    public SkynetProperties(Environment environment) {
        this.environment = environment;
        this.home = environment.getProperty(SKYNET_HOME_KEY, "");
        this.ipAddress = environment.getProperty(SKYNET_IP_KEY);

        String actionPoint = environment.getProperty(
                SKYNET_ACTION_POINT_KEY,
                environment.getProperty(
                        SKYNET_ACTION_NAME_KEY,
                        environment.getProperty("spring.application.name", "unset") + "@skynet"));
        setActionPoint(actionPoint);

        this.port = Integer.parseInt(environment.getProperty("server.port", "8080"));
        String debugValue = environment.getProperty("debug");
        this.debugMode = debugValue != null && !"false".equals(debugValue);
    }

    /**
     * Gets the plugin home directory path
     * 
     * @return Plugin directory path (e.g.,
     *         /iflytek/server/skynet/plugin/tuling-ast)
     *         or empty string if plugin is not set
     */
    public String getPluginHome() {
        return StringUtils.hasText(plugin) ? String.format("%s/plugin/%s", home, plugin) : "";
    }

    /**
     * Gets the action ID, falling back to actionPoint if not set
     * 
     * @return The action ID or actionPoint as fallback
     */
    public String getActionId() {
        return StringUtils.hasText(actionId) ? actionId : actionPoint;
    }

    /**
     * Gets the IP address, attempting multiple resolution strategies
     * 1. Use configured IP if valid
     * 2. Try spring.cloud.client.ip-address
     * 3. Resolve using OS network interfaces
     * 
     * @return Resolved IP address
     */
    public String getIpAddress() {
        if (!isValidIpAddress(ipAddress)) {
            ipAddress = environment.getProperty("spring.cloud.client.ip-address");
            if (!StringUtils.hasText(ipAddress)) {
                ipAddress = OsUtil.getIPAddress();
            }
        }
        return ipAddress;
    }

    /**
     * Sets the action point and parses its components
     * Format: {actionCode}@{plugin}
     * 
     * @param actionPoint The action point string to parse
     * @throws RuntimeException if the actionPoint format is invalid
     */
    public void setActionPoint(String actionPoint) {
        this.actionPoint = actionPoint;
        if (StringUtils.hasText(actionPoint)) {
            String[] parts = actionPoint.split("@");
            if (parts.length > 1) {
                this.actionCode = parts[0];
                this.plugin = parts[1];
            } else {
                throw new RuntimeException("Invalid actionPoint format. Expected format: actionCode@pluginCode");
            }
        }
    }

    /**
     * Checks if the given IP address is valid (not localhost or empty)
     * 
     * @param ip IP address to validate
     * @return true if the IP is valid, false otherwise
     */
    private boolean isValidIpAddress(String ip) {
        return StringUtils.hasText(ip)
                && !"127.0.0.1".equals(ip)
                && !"0:0:0:0:0:0:0:1".equals(ip)
                && !"::1".equals(ip);
    }
}
