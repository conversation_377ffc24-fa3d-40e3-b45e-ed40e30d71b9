package skynet.boot.mysql.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import skynet.boot.common.dto.AbstractPatchDTO;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.TemplateService;
import skynet.boot.common.service.TenantAware;
import skynet.boot.exception.EntityNotFoundException;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于 MySQL 的 Service 基类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class MysqlTemplateServiceImpl<E extends AbstractAuditingEntity<ID>, D, ID extends Serializable> extends MySqlQueryService<E> implements TemplateService<E, D, ID> {

    private final JpaSpecificationExecutor<E> jpaSpecificationExecutor;
    private final JpaRepository<E, ID> jpaRepository;
    private final EntityMapper<D, E> entityMapper;

    @Autowired(required = false)
    private TenantAware tenantAware;

    public MysqlTemplateServiceImpl(EntityMapper<D, E> entityMapper, JpaSpecificationExecutor<E> jpaSpecificationExecutor, JpaRepository<E, ID> jpaRepository) {
        this.jpaSpecificationExecutor = jpaSpecificationExecutor;
        this.entityMapper = entityMapper;
        this.jpaRepository = jpaRepository;
    }

    @Override
    public Page<D> findAllByCriteria(Criteria criteria, Class<E> clazz, Pageable pageable) {
        Specification<E> specification = createSpecificationInner(criteria);
        return jpaSpecificationExecutor.findAll(specification, pageable).map(entityMapper::toDto);
    }

    @Override
    public List<D> findAllByCriteria(Criteria criteria, Class<E> clazz) {
        Specification<E> specification = createSpecificationInner(criteria);
        return entityMapper.toDto(jpaSpecificationExecutor.findAll(specification));
    }

    @Override
    public List<D> findAllByCriteria(Criteria criteria, Class<E> clazz, Sort sort) {
        Specification<E> specification = createSpecificationInner(criteria);
        return entityMapper.toDto(jpaSpecificationExecutor.findAll(specification, sort));
    }

    public abstract Specification<E> createSpecification(Criteria criteria);

    public Specification<E> createSpecificationInner(Criteria criteria) {
        Specification<E> specification = createSpecification(criteria);
        // 按租户过滤
        String tenantId = getCurrentTenant();
        if (StringUtils.isNotBlank(tenantId)) {
            specification = specification.and(
                    (root, query, builder) -> builder.equal(root.get("tenantId"), tenantId)
            );
        }
        return specification;
    }

    @Override
    public D findById(ID id) {
        E experiment = jpaRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        return entityMapper.toDto(experiment);
    }

    @Override
    public D save(D dto) {
        E entity = entityMapper.toEntity(dto);
        entity.setCreatedDate(Instant.now());
        if (getCurrentTenant() != null) {
            entity.setTenantId(getCurrentTenant());
        }
        return entityMapper.toDto(jpaRepository.save(entity));
    }

    @Override
    public D update(ID id, D dto) {
        E entity = jpaRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        E newEntity = entityMapper.toEntity(dto);
        newEntity.setId(entity.getId());
        newEntity.setCreatedDate(entity.getCreatedDate());
        newEntity.setCreatedBy(entity.getCreatedBy());
        newEntity.setLastModifiedDate(Instant.now());
        if (getCurrentTenant() != null) {
            newEntity.setTenantId(getCurrentTenant());
        }
        return entityMapper.toDto(jpaRepository.save(newEntity));
    }

    @Override
    public D patch(ID id, AbstractPatchDTO<ID> patchDTO) {
        E entity = jpaRepository.findById(id).orElseThrow(EntityNotFoundException::new);

        try {
            Class<?> entityClazz = entity.getClass();
            Class<?> patchDtoClass = patchDTO.getClass();
            Class<?> generatedPatchDtoClass = patchDtoClass.getSuperclass();

            List<Field> fields = new ArrayList<>(Arrays.asList(patchDtoClass.getDeclaredFields()));
            fields.addAll(Arrays.asList(generatedPatchDtoClass.getDeclaredFields()));
            for (Field dtoField : fields.stream().filter(field -> !"serialVersionUID".equals(field.getName())).collect(Collectors.toList())) {
                try {
                    Field entityField = entityClazz.getDeclaredField(dtoField.getName());
                    dtoField.setAccessible(true);
                    if (dtoField.get(patchDTO) != null) {
                        entityField.setAccessible(true);
                        entityField.set(entity, dtoField.get(patchDTO));
                    }
                } catch (Throwable e) {
                    log.error("更新{}字段出现异常！", dtoField.getName(), e);
                }
            }
        } catch (Throwable e) {
            log.error("更新部分字段出现异常！", e);
            return null;
        }
        entity.setLastModifiedDate(Instant.now());
        return entityMapper.toDto(jpaRepository.save(entity));
    }

    @Override
    public void delete(ID id) {
        jpaRepository.deleteById(id);
    }


    @Override
    public D copy(ID id, List<String> renameFields, Class<E> clazz) {
        E entity = jpaRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        E newEntity;
        try {
            newEntity = clazz.newInstance();
            BeanUtils.copyProperties(entity, newEntity);
            newEntity.setId(null);
            newEntity.setCreatedDate(Instant.now());
            newEntity.setLastModifiedDate(Instant.now());
            if (getCurrentTenant() != null) {
                newEntity.setTenantId(getCurrentTenant());
            }
        } catch (Throwable e) {
            log.error("Copy error", e);
            return null;
        }

        for (String renameField : renameFields) {
            try {
                Field field = clazz.getDeclaredField(renameField);
                field.setAccessible(true);
                field.set(newEntity, generateCopy(clazz, renameField, String.valueOf(field.get(newEntity))));
            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return entityMapper.toDto(jpaRepository.save(newEntity));
    }

    // TODO：生成复制名称
    private <T> String generateCopy(Class<T> clazz, String fieldName, String fieldValue) {
        return fieldValue;
    }

    /**
     * 获取当前的租户 ID
     */
    private String getCurrentTenant() {
        String tenantId = null;
        if (tenantAware != null) {
            tenantId = tenantAware.getCurrentTenant();
        }
        return tenantId;
    }
}