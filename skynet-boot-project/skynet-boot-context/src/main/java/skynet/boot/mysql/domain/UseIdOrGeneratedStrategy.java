package skynet.boot.mysql.domain;

import org.hibernate.id.IdentityGenerator;

/**
 * 自定义 id generate 策略，如果设置了 id 字段则直接使用，没有设置则自动生成
 *
 * <AUTHOR>
 */
public class UseIdOrGeneratedStrategy extends IdentityGenerator {

    //TODO:
//    @Override
//    public Serializable generate(SharedSessionContractImplementor session, Object obj) throws HibernateException {
//        if (obj == null) {
//            throw new HibernateException(new NullPointerException());
//        }
//
//        if ((((AbstractAuditingEntity<?>) obj).getId()) == null) {
//            return super.generate(session, obj);
//        } else {
//            return ((AbstractAuditingEntity<?>) obj).getId();
//        }
//    }
}
