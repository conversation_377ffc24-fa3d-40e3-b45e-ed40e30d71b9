package skynet.boot.mysql.domain;

import jakarta.annotation.Generated;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;

import java.time.Instant;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(AbstractAuditingEntity.class)
public class AbstractAuditingEntity_ {

    public static volatile SingularAttribute<AbstractAuditingEntity, Long> id;
    public static volatile SingularAttribute<AbstractAuditingEntity, String> createdBy;
    public static volatile SingularAttribute<AbstractAuditingEntity, Instant> createdDate;
    public static volatile SingularAttribute<AbstractAuditingEntity, String> lastModifiedBy;
    public static volatile SingularAttribute<AbstractAuditingEntity, Instant> lastModifiedDate;
    public static volatile SingularAttribute<AbstractAuditingEntity, String> tenantId;

    public static final String ID = "id";
    public static final String CREATEDBY = "createdBy";
    public static final String CREATEDDATE = "createdDate";
    public static final String LASTMODIFIEDBY = "lastModifiedBy";
    public static final String LASTMODIFIEDDATE = "lastModifiedDate";
    public static final String TENANTID = "tenantId";
}
