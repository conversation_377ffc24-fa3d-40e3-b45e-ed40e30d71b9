package skynet.boot.logging.config;

import jakarta.servlet.Filter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.source.ConfigurationPropertyName;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.env.Environment;
import org.springframework.web.filter.DelegatingFilterProxy;
import skynet.boot.annotation.EnableSkynetLogging;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.logging.LoggingCostAspect;
import skynet.boot.logging.SkynetLoggingEndpoint;
import skynet.boot.logging.debug.SkynetLoggingDebugContext;
import skynet.boot.logging.debug.SkynetLoggingDebugTurboFilter;
import skynet.boot.logging.debug.http.SkynetLoggingDebugSpringMvcFilter;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/28 18:05
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetLogging.class)
@ConditionalOnProperty(value = SkynetLoggingAutoConfiguration.SKYNET_DEBUGGING_PROP_PREFIX + ".enabled")
public class SkynetLoggingAutoConfiguration {

    public final static String SKYNET_DEBUGGING_PROP_PREFIX = "skynet.logging";
    private static final ConfigurationPropertyName LOGGING_DEBUG = ConfigurationPropertyName.of("skynet.logging.debug.enable");
    private static final Bindable<Map<String, Boolean>> STRING_LOG_DEBUG_MAP = Bindable.mapOf(String.class, Boolean.class);

    @Bean
    @ConditionalOnProperty(value = "skynet.logging.cost.enabled", matchIfMissing = true)
    public LoggingCostAspect loggingCostAspect() {
        return new LoggingCostAspect();
    }

    @Bean
    @RefreshScope
    @ConfigurationProperties(SKYNET_DEBUGGING_PROP_PREFIX)
    public SkynetLoggingProperties skynetLoggingProperties(Environment environment) {
        SkynetLoggingProperties properties = new SkynetLoggingProperties();
        properties.getDebug().setLoggers(Binder.get(environment).bind(LOGGING_DEBUG, STRING_LOG_DEBUG_MAP).orElseGet(Collections::emptyMap));
        return properties;
    }

    @Bean
    public SkynetLoggingDebugContext skynetLoggingDebugContext(SkynetLoggingProperties properties) {
        return new SkynetLoggingDebugContext(properties);
    }

    @Bean(SkynetLoggingDebugTurboFilter.BEAN_NAME)
    public SkynetLoggingDebugTurboFilter skynetLoggingDebugTurboFilter(SkynetLoggingDebugContext skynetLoggingDebugContext) {
        return new SkynetLoggingDebugTurboFilter(skynetLoggingDebugContext);
    }

    public static final String ELLIPSES_FILTER_BEAN_NAME = "skynetLoggingLargeObjectEllipsesFilter";

    /**
     * 日志简化 过滤器
     * add since by 4.0.13
     *
     * @return SkynetLoggingEllipsesTurboFilter
     */
    @Bean(ELLIPSES_FILTER_BEAN_NAME)
    public Jsonable.LargeObjectEllipsesFilter skynetLoggingLargeObjectEllipsesFilter(SkynetLoggingProperties properties) {
        return new Jsonable.LargeObjectEllipsesFilter(properties.getEllipses());
    }

    @Bean
    @ConditionalOnProperty(value = {SKYNET_DEBUGGING_PROP_PREFIX + ".debug.enabled", SKYNET_DEBUGGING_PROP_PREFIX + ".debug.springmvc.enabled"}, matchIfMissing = true)
    public SkynetLoggingDebugSpringMvcFilter skynetLoggingDebugSpringMvcFilter(SkynetLoggingDebugContext skynetLoggingDebugContext, SkynetLoggingProperties properties) {
        return new SkynetLoggingDebugSpringMvcFilter(skynetLoggingDebugContext, properties);
    }

//    @Bean
//    @ConditionalOnProperty(value = {SKYNET_DEBUGGING_PROP_PREFIX + ".debug.enabled", SKYNET_DEBUGGING_PROP_PREFIX + ".debug.springmvc.enabled"}, matchIfMissing = true)
//    public FilterRegistrationBean<Filter> springMvcDebuggingFilterProxy() {
//        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
//        registrationBean.setFilter(new DelegatingFilterProxy());
//        registrationBean.addInitParameter("targetBeanName", "skynetLoggingDebugSpringMvcFilter");
//        registrationBean.addInitParameter("targetFilterLifecycle", "true");
//        registrationBean.addUrlPatterns("/*");
//        registrationBean.setName("skynetLoggingDebugSpringMvcFilter");
//        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
//        return registrationBean;
//    }

    @Bean
    public SkynetLoggingEndpoint skynetLoggingEndpoint(SkynetLoggingProperties properties) {
        return new SkynetLoggingEndpoint(properties);
    }

//    @Configuration(proxyBeanMethods = false)
//    @ConditionalOnClass(name = "com.alibaba.dubbo.rpc.Filter")
//    class skynetDebuggingDubboFilterConfig {
//        @Bean
//        @ConditionalOnProperty(value = SKYNET_DEBUGGING_PROP_PREFIX + ".dubbo.enabled")
//        public SkynetLoggingDebugDubboFilter skynetLoggingDebugDubboFilter() {
//            return new SkynetLoggingDebugDubboFilter();
//        }
//    }
}

