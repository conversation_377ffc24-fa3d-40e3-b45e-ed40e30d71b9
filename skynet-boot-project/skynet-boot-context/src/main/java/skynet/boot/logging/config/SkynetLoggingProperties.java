package skynet.boot.logging.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.logging.debug.SkynetLoggingDebugContext;

import java.util.Map;
import java.util.TreeMap;

/**
 * 动态 debug  配置
 *
 * <AUTHOR>
 * @date 2022/8/8 15:30
 */
@Slf4j
@Getter
@Setter
public class SkynetLoggingProperties extends Jsonable {

    /**
     * 是否开启 logging，默认 false
     */
    private boolean enabled = true;

    private Debug debug = new Debug();

    /**
     * 大对象 日志输出省略简化处理配置
     * add since 4.0.13
     */
    private EllipsesSetting ellipses = new EllipsesSetting();


    /**
     * 大对象 日志输出省略简化处理配置
     */
    @Getter
    @Setter
    public static class EllipsesSetting extends Jsonable.LargeObjectEllipsesSetting {

        private boolean enabled = true;
    }

    @Getter
    @Setter
    public static class Debug {

        /**
         * 是否开启 动态 debugging，默认 false
         */
        private boolean enabled = false;

        /**
         * 请求头 标记Key，默认： x-skynet-debugging
         */
        private String headKey = SkynetLoggingDebugContext.FLAG_KEY;

        /**
         * 匹配表达式，支持JavaScript语法
         * <p>
         * http-api，dubbo-api 请求第一个入参；
         */
        private String expression = null;

        /**
         * dubbo 服务入口配置
         */
        private Dubbo dubbo = new Dubbo();

        /**
         * spring mvc 服务入口配置
         */
        private SpringMvc springmvc = new SpringMvc();

        /**
         * logger 名称
         */
        public void setLoggers(Map<String, Boolean> loggers) {
            this.loggers.putAll(loggers);
            this.root = loggers.getOrDefault("root", false);
            this.loggers.forEach((name, enabled) -> {
                log.info("{}={}", name, enabled);
            });
        }

        private Map<String, Boolean> loggers = new TreeMap<>((k1, k2) -> k1.length() > k2.length() ? -1 : 1);

        private Boolean root = false;

        @Getter
        @Setter
        public static class Dubbo {
            private boolean enabled = false;
        }

        @Getter
        @Setter
        public static class SpringMvc {
            private boolean enabled = false;
            private int order = 0;
            private String uriPattern = "/**/api/**";
        }
    }
}
