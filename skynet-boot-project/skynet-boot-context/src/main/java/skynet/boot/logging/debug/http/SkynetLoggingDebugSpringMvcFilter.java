package skynet.boot.logging.debug.http;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import skynet.boot.logging.config.SkynetLoggingProperties;
import skynet.boot.logging.debug.SkynetLoggingDebugContext;

import java.io.IOException;

/**
 * DebuggingSpringMvcFilter
 * 【日志染色】
 *
 * <AUTHOR>
 * @date 2022/7/29 09:54
 */
@Slf4j
public class SkynetLoggingDebugSpringMvcFilter implements Filter, Ordered {

    private final AntPathMatcher pathMatcher;
    private final SkynetLoggingProperties properties;
    private final SkynetLoggingDebugContext skynetLoggingDebugContext;

    public SkynetLoggingDebugSpringMvcFilter(SkynetLoggingDebugContext skynetLoggingDebugContext, SkynetLoggingProperties properties) {
        this.skynetLoggingDebugContext = skynetLoggingDebugContext;
        this.pathMatcher = new AntPathMatcher();
        this.pathMatcher.setCachePatterns(true);
        this.properties = properties;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        skynetLoggingDebugContext.disableDebugging();

        if (servletRequest instanceof HttpServletRequest) {

            HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
            //符合 URI请求范围的的。
            String uriPattern = properties.getDebug().getSpringmvc().getUriPattern();
            if (StringUtils.hasText(uriPattern) && pathMatcher.match(uriPattern, httpServletRequest.getRequestURI())) {

                //优先判断 请求头中带有 debug 标记
                String headerConfig = httpServletRequest.getHeader(properties.getDebug().getHeadKey());
                if (StringUtils.hasText(headerConfig)) {
                    if ("true".equalsIgnoreCase(headerConfig)) {
                        skynetLoggingDebugContext.enableDebugging();
                    }
                    filterChain.doFilter(servletRequest, servletResponse);
                    return;
                }

                //在判断条件表单式是否符合
                if (StringUtils.hasText(properties.getDebug().getExpression())) {
                    DebugHttpServletRequestWrapper requestWrapper = new DebugHttpServletRequestWrapper(httpServletRequest);
                    skynetLoggingDebugContext.enableDebugging(new String(requestWrapper.getBody()), properties.getDebug().getExpression());
                    filterChain.doFilter(requestWrapper, servletResponse);
                    return;
                }
            }
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public int getOrder() {
        return properties.getDebug().getSpringmvc().getOrder();
    }
}
