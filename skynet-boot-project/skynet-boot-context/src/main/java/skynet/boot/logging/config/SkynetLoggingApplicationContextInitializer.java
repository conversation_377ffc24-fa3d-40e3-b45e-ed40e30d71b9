package skynet.boot.logging.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;
import skynet.boot.logging.converter.SkynetLoggingMsgConverter4ActionId;
import skynet.boot.logging.converter.SkynetLoggingMsgConverter4Ellipsis;
import skynet.boot.logging.converter.SkynetLoggingMsgConverter4TraceId;

/**
 * <pre>
 * 通过 代码 注入 skynetLoggingDebugTurboFilter，同时主要是在 actuator refresh 刷新属性时重新配置。
 * </pre>
 *
 * <AUTHOR>
 * @date 2022年08月30日
 */
@Slf4j
public class SkynetLoggingApplicationContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext>, Ordered {

    static {

        boolean classExists;
        try {
            Class.forName("ch.qos.logback.classic.PatternLayout");
            classExists = true;
        } catch (ClassNotFoundException e) {
            classExists = false;
        }

        if (classExists) {
            //注入logback转化器
            try {
                log.info("Add PatternLayout.defaultConverterMap: msg,em,actionId,traceId...");
                ch.qos.logback.classic.PatternLayout.DEFAULT_CONVERTER_MAP.put("emsg", SkynetLoggingMsgConverter4Ellipsis.class.getTypeName());
                ch.qos.logback.classic.PatternLayout.DEFAULT_CONVERTER_MAP.put("em", SkynetLoggingMsgConverter4Ellipsis.class.getTypeName());
                ch.qos.logback.classic.PatternLayout.DEFAULT_CONVERTER_MAP.put("actionId", SkynetLoggingMsgConverter4ActionId.class.getTypeName());
                ch.qos.logback.classic.PatternLayout.DEFAULT_CONVERTER_MAP.put("traceId", SkynetLoggingMsgConverter4TraceId.class.getTypeName());
            } catch (Exception e) {
                log.error(" PatternLayout.defaultConverterMap error", e);
            }
        }
    }

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        if ("true".equalsIgnoreCase(applicationContext.getEnvironment().getProperty("skynet.logging.debug.enabled", "false"))) {
            ConfigLoggerContextTurboFilter.config();
        }
    }

    @Override
    public int getOrder() {
        // order 必须 在  PropertySourceBootstrapConfiguration 执行后面
        return Ordered.HIGHEST_PRECEDENCE + 20;
    }
}