package skynet.boot.logging.config;

import skynet.boot.common.SpringUtils;
import skynet.boot.logging.debug.SkynetLoggingDebugTurboFilter;

/**
 * 设置 SkynetLoggingDebugTurboFilter
 * <p>
 * 目的 从 ApplicationContextInitializer 移除出来，防止有的应用没有 使用 logback 日志组件，而出现 TurboFilter notfoundClass 异常
 *
 * <AUTHOR>
 */
public class ConfigLoggerContextTurboFilter {

    public static void config() {
        if (SpringUtils.getContext() != null) {
            if (SpringUtils.getContext().containsBean(SkynetLoggingDebugTurboFilter.BEAN_NAME)) {
                SpringUtils.getContext().getBean(SkynetLoggingDebugTurboFilter.class).config();
            }
        }
    }
}
