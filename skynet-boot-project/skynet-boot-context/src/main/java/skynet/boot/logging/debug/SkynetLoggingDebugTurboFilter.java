package skynet.boot.logging.debug;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import org.slf4j.TtlMDCAdapter;

/**
 * 【日志染色】
 * 判断当前日志是否输出。不受日志级别的控制
 *
 * <AUTHOR>
 * @date 2022/7/28 09:59
 */
@Slf4j
public class SkynetLoggingDebugTurboFilter extends TurboFilter {

    public final static String BEAN_NAME = "skynetLoggingDebugTurboFilter";

    private final SkynetLoggingDebugContext skynetLoggingDebugContext;

    public SkynetLoggingDebugTurboFilter(SkynetLoggingDebugContext skynetLoggingDebugContext) {
        this.skynetLoggingDebugContext = skynetLoggingDebugContext;
        this.config();
    }

    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String format, Object[] params, Throwable t) {
        if (skynetLoggingDebugContext.match(logger)) {
            return FilterReply.ACCEPT;
        }
        return FilterReply.NEUTRAL;
    }

    public void config() {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        loggerContext.addTurboFilter(this);
        TtlMDCAdapter.getInstance();
    }
}
