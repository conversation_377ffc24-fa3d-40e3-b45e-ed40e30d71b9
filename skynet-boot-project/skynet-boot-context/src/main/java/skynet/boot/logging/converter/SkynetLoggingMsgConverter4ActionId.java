package skynet.boot.logging.converter;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.SkynetProperties;
import skynet.boot.common.SpringUtils;

/**
 * <AUTHOR>  2023/9/5 16:45
 */
public class SkynetLoggingMsgConverter4ActionId extends MessageConverter {

    private String actionId;

    @Override
    public String convert(ILoggingEvent event) {
        if (actionId == null && SpringUtils.getContext() != null) {
            actionId = SpringUtils.getContext().getBean(SkynetProperties.class).getActionId();
        }
        return actionId != null ? actionId : StringUtils.EMPTY;
    }
}
