# skynet-boot-starter-logging 日志处理

## 主要功能列表

- skynet-logging-ellipses 日志省略简化
- skynet-logging-debugging 日志染色
- skynet-logging-cost 日志耗时记录

## 快速开始

1. 添加注解

pom.xml 依赖引入：

```xml
<dependency>
    <groupId>com.iflytek.skynet</groupId>
    <artifactId>skynet-boot-starter-logging</artifactId>
</dependency>
```

在springboot启动类上 添加 `@EnableSkynetLogging` 注解开启 `skynet-logging` 组件, 然后通过配置开启每项功能，这是基础。

```java

@EnableSkynetLogging
@SpringBootApplication
public class SpringBootApp {

    public static void main(String[] args) {
        AppUtils.run(SpringBootApp.class, args);
    }
}

```

2. 属性配置

```properties
# ---------------------------------------------------------#
skynet.logging.enabled=true
# ---------------------------------------------------------#
skynet.logging.ellipses.enabled=true
#...
skynet.logging.debug.enabled=true
#...
# ---------------------------------------------------------#
```

## 1.skynet-logging-ellipses 日志省略简化

### 应用场景

在输出日志中，如果包含了大对象，如base64编码的文件内容或长特征向量数组，会导致日志文件变得异常庞大。为了简化这些数据体积，我们希望能够只保留数据占位符，这样在排查问题时就能够非常方便地进行操作。

#### 属性配置

此功能开启只需要做出如下的配置，既可开启

```properties
# ---------------------------------------------------------#
skynet.logging.enabled=true
# ---------------------------------------------------------#
# 启动 ellipses ，默认：true
skynet.logging.ellipses.enabled=true
# 数组长度 默认 100
skynet.logging.ellipses.array-limit-len=100
# 字符串长度 默认 500
skynet.logging.ellipses.string-limit-len=500
# ---------------------------------------------------------#
```

logback配置文件中 修改输出 `PATTERN` 中的 `msg` 为 `emsg`(或`em`), 如果想覆盖，直接配置成 `msg` 或 `e`。

```properties
#优先推荐
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%traceId}]){cyan}%clr(:){faint} %emsg%n
logging.pattern.file=%d{MM-dd HH:mm:ss.SSS} -%5p 11245[%t] %-40.40logger{39}[%3line{3}][%traceId}]: %em%n
```

或

```xml
<property scope="context" name="APP_PATTERN"
          value='%d{yyyy-MM-dd HH:mm:ss.SSS}|%X{uuid}|%level|%M|%C\:%L|%thread|\t%emsg%n'/>
```

## 2.skynet-logging-debug 【日志染色】

### 背景

线上日志多为 INFO 级别。特定情况下想要观察所有日志，但无法将线上所有日志开放为 DEBUG 级别。
通过skynet-logging-debug 组件，可实现根据请求，动态设置指定命名空间下 所有的日志实现不区分级别打印,方便日志观察、问题排查。

#### 属性配置

```properties
# ---------------------------------------------------------#
skynet.logging.enabled=true
# ---------------------------------------------------------#
skynet.logging.debug.enabled=true
# 请求头Kye：默认 x-skynet-debugging，可以修改
skynet.logging.debug.head-key=x-skynet-debugging
# 请求入参 匹配表达式（springmvc 的是请求body，dubbo，是请求的第一个入参）
skynet.logging.debug.expression=$.user=="zhangsan"
# ---------------------------------------------------------#
skynet.logging.debug.dubbo.enabled=false
# ---------------------------------------------------------#
# 开启 springmvc filter，默认是 false
skynet.logging.debug.springmvc.enabled=true
#  springmvc 请求URL-PATTERN ，默认是 /**/api/**
skynet.logging.debug.springmvc.uri-pattern=/**/api/**
# ---------------------------------------------------------#
# logger 配置，true 表示开启，false 表示关闭
# skynet.logging.debug.enable.xxx.yyy  中的 xxx.yyy 表示命名空间
skynet.logging.debug.enable.root=false
skynet.logging.debug.enable.com.iflytek=true
skynet.logging.debug.enable.skynet=false
skynet.logging.debug.enable.skynet.boot=true
# ---------------------------------------------------------#
```

#### SpringMvc请求场景：

a. 通过 请求头 `x-skynet-debugging` = true，

```shell

curl -d "{\"user\":\"zhangsan\"}" -X GET -H 'x-skynet-debugging: true' http://10.3.121.3:7878/back/api/v1/test2

```

b. 通过 请求题条件匹配：

```shell

curl -d "{\"user\":\"zhangsan\"}" -X POST -H 'x-skynet-debugging: false' http://10.3.121.3:7878/back/api/v1/test2

```

```properties
skynet.logging.debug.expression=$.user=="zhangsan"
```

> 备注说明：
> springmvc 匹配规则：符合请求URI, http请求头\[x-skynet-debugging: true\] 优先级最高，请求参数，命名空间。
> 如果传入的body参数或dubbo 的第一个入参 json对象，可以使用 `$.obj` 引用入参


在dubbo使用时，需要在service定义时添加filter: skynetLoggingDebugDubboFilter。如：

```xml

<dubbo:service registry="app" ref="demoService" filter="skynetLoggingDebugDubboFilter"
               interface="com.xxx.service.DemoService"/>
```

#### dubbo 请求场景：

a. 设置 dubbo 接口 Filter
dubbo 请求的attachment 中含有 可在Dubbo的Filter设值。

```xml

<dubbo:service registry="app" ref="demoService" filter="skynetLoggingDebugDubboFilter"
               interface="com.xxx.service.DemoService"/>
```

b. 请求附属头 (根据场景可选)
通过 invocation attachment 参数 传入 head

```java
//TODO: 示例代码
invocation.getAttachments().put("x-skynet-debugging","true")
//head-key 通过下面属性修改
//skynet.logging.debug.head-key=x-skynet-debugging

```

// Runnable ttlRunnable = TtlRunnable.get(() -> log.error("2 ={}", FLAG_THREAD_LOCAL.get()));
// Future<?> submit2 = executorService.submit(ttlRunnable);

### 2.3 Dubbo 请求场景

a. 设置 dubbo 接口 Filter

在dubbo场景时，需要在service（服务端）定义时添加 `filter`: skynetLoggingDebugDubboFilter。如：

```xml
<dubbo:service registry="app" ref="SkylabExtServiceProvider"
               filter="skynetLoggingDebugDubboFilter"
               interface="com.iflytek.skylab.core.contract.service.SkylabExtService"/>
```

b. 通过 请求参数条件匹配：
> 与SpringMvc场景相似，通过第一个入参，设置匹配JS表达式。

c. 请求附属头 (根据场景可选)
> 通过 invocation attachment 参数 传入 head

首先编写客户端filter，

```java
@Slf4j
@Activate(group = {Constants.CONSUMER})
public class MyDebugFilter implements Filter {
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        if (RpcContext.getContext().isConsumerSide() ) {
            invocation.getAttachments().put("x-skynet-debugging","true");
        }
        return invoker.invoke(invocation);
    }
}

```

然后 设置对应的dubbo 配置

```xml

<dubbo:reference registry="app" id="SkylabDiagnoseServiceConsumer"
                 filter="myDebugFilter"
                 interface="com.iflytek.skylab.core.contract.service.SkylabExtService" check="false"
                 timeout="30000"/>

```

### 2.4 重要说明

由于此功能是通过Log logback 的 TurboFilter + 设置MDC中的状态 实现的，也就是
如果业务请求中，使用到了多线程技术，如果不做线程间的上下文同步，也就可能会出现非主线程的日志输出不了。
所以，建议，在使用的提交 Runnable或Callable给线程池执行时，通过阿里的TTL组件（TtlRunnable、TtlCallable）包装一下。
如：

```java
Runnable ttlRunnable = TtlRunnable.get(() -> log.debug("调试日志..."));
Future<?> future = executorService.submit(ttlRunnable);
```

`skynet-boot-starter-logging` 的maven依赖中已经引用了
阿里的TTL，更多参考大家可以访问它的[官网](https://github.com/alibaba/transmittable-thread-local)

```xml
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>transmittable-thread-local</artifactId>
</dependency>
```

### 2.5 其他

如果发现目前的`springmvc`和`dubbo`两种场景下的过滤器还不能满足业务需求，业务可以通过 复用 `skynet-logging`
中的内部组件，实现自定义的自己的过滤器，
具体可以参考 `skynet.boot.logging.debug.http.SkynetLoggingDebugSpringMvcFilter` 类，核心组件是
`SkynetLoggingDebugContext`



---

## 3.skynet-logging-cost 日志耗时记录

在方法上添加`@LoggingCost`注解，即可开启记录方法耗时功能，默认在 `info` 日志中输出耗时。

```java
public class IndexController {
    @LoggingCost
    @GetMapping("/")
    public String index() {

        log.error("hello world!");
        log.debug("hello world!");
        log.info("{}", appContext);

        return appContext.toString();
    }
}
```

输出日志示例：
日志级别命名空间是：`skynet.boot.logging.LoggingCostAspect`，

```
2022-07-11 17:53:50.027  INFO 12809 --- [nio-7878-exec-1] skynet.boot.logging.LoggingCostAspect    : class skynet.boot.springboot.controller.IndexController.Index.cost= 3 ms[00:00:00.003].
```

### 3.1 属性配置

代码中默认开启了`skynet.logging.cost.enabled`，如果想禁用，可以通过如下配置。

```properties
# default true
skynet.logging.cost.enabled=false
```