package skynet.boot.logging;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;


/**
 * <pre>
 * 记录方法耗时 注解
 * </pre>
 *
 * <AUTHOR>
 * @date 2019-12-09 18:03
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface LoggingCost {

    /**
     * 提示消息，如果没有填写，默认是 类型+方法名
     *
     * @return
     */
    @AliasFor("value")
    String message() default "";

    /**
     * 提示消息 必填
     *
     * @return
     */
    @AliasFor("message")
    String value() default "";
}
