package skynet.boot.logging.debug.dubbo;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.*;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import skynet.boot.logging.config.SkynetLoggingProperties;
import skynet.boot.logging.debug.SkynetLoggingDebugContext;

/**
 * * 【日志染色】
 *
 * <AUTHOR>
 * @date 2022/8/1 20:34
 */
@Slf4j
@Activate(group = {Constants.PROVIDER})
public class SkynetLoggingDebugDubboFilter implements Filter {

    private final AntPathMatcher pathMatcher;

    @Setter
    private SkynetLoggingProperties skynetLoggingProperties;
    @Setter
    private SkynetLoggingDebugContext skynetLoggingDebugContext;

    public SkynetLoggingDebugDubboFilter() {
        this.pathMatcher = new AntPathMatcher();
        this.pathMatcher.setCachePatterns(true);
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // 避免 epas-dubbo在功能未启用的情况下 自动应用此Filter，导致属性未注入，引起NPE
        if (skynetLoggingProperties == null || skynetLoggingDebugContext == null) {
            return invoker.invoke(invocation);
        }

        skynetLoggingDebugContext.disableDebugging();
        if (RpcContext.getContext().isProviderSide() && skynetLoggingProperties.getDebug().getDubbo().isEnabled()) {

            //优先判断 请求头中带有 debug 标记
            String headerConfig = invocation.getAttachment(skynetLoggingProperties.getDebug().getHeadKey());
            if (StringUtils.hasText(headerConfig)) {
                if ("true".equalsIgnoreCase(headerConfig)) {
                    skynetLoggingDebugContext.enableDebugging();
                }
            } else if (StringUtils.hasText(skynetLoggingProperties.getDebug().getExpression())) {
                //默认取第一个参数
                Object context = invocation.getArguments().length >= 1 ? invocation.getArguments()[0] : null;
                skynetLoggingDebugContext.enableDebugging(context, skynetLoggingProperties.getDebug().getExpression());
            }
        }
        return invoker.invoke(invocation);
    }
}