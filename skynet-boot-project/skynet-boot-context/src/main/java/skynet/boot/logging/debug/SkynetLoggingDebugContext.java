package skynet.boot.logging.debug;

import ch.qos.logback.classic.Logger;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import skynet.boot.logging.config.SkynetLoggingProperties;
import skynet.boot.script.javascript.JavaScriptBooleanExpressionExecutor;

import java.util.HashMap;
import java.util.Map;

/**
 * 【日志染色】
 *
 * <AUTHOR>
 * @date 2022/7/28 10:17
 */
@Slf4j
public class SkynetLoggingDebugContext {

    public final static String FLAG_KEY = "x-skynet-debugging";
    private final static TransmittableThreadLocal<Boolean> FLAG_THREAD_LOCAL = new TransmittableThreadLocal<>();
    private final JavaScriptBooleanExpressionExecutor javaScriptBooleanExpressionExecutor;
    private final SkynetLoggingProperties properties;

    static {
        FLAG_THREAD_LOCAL.set(false);
    }

    public SkynetLoggingDebugContext(SkynetLoggingProperties properties) {
        this.javaScriptBooleanExpressionExecutor = new JavaScriptBooleanExpressionExecutor();
        this.properties = properties;
    }

    public void enableDebugging(Object context, String expression) {
        try {
            JSONObject contextJsonObject = null;
            if (context != null) {
                if (context instanceof String) {
                    try {
                        contextJsonObject = (JSONObject) JSON.parse(context.toString());
                    } catch (Throwable e) {
                        log.debug("JSON.parse error={}", e.getMessage());
                        contextJsonObject = new JSONObject();
                        contextJsonObject.put("obj", context);
                    }
                } else {
                    contextJsonObject = (JSONObject) JSON.toJSON(context);
                }
            }
            if (StringUtils.hasText(expression) && javaScriptBooleanExpressionExecutor.eval(expression, contextJsonObject)) {
                enableDebugging();
            }
        } catch (Throwable e) {
            log.error("javaScriptBooleanExpressionExecutor.eval error. expression={}. error={}", expression, e.getMessage());
            log.trace("context={}", context);
        }
    }

    public void enableDebugging() {
        log.debug("enableDebugging");
        FLAG_THREAD_LOCAL.set(true);
    }

    public void disableDebugging() {
        FLAG_THREAD_LOCAL.set(false);
    }

    /**
     * 获取 请求头 x-skynet-debugging=true|false
     *
     * @return
     */
    public Map<String, String> getHeader() {
        Map<String, String> headerMap = new HashMap<>(1);
        headerMap.put(properties.getDebug().getHeadKey(), String.valueOf(FLAG_THREAD_LOCAL.get()));
        return headerMap;
    }

    /**
     * 是否匹配：开启了debug，并且当前logging符合范围
     *
     * @param logger
     * @return
     */
    public boolean match(Logger logger) {
        String loggerName = logger.getName();
        // 当前请求没有 设置 debugging 标志，直接返回
        if (FLAG_THREAD_LOCAL.get() == null || !FLAG_THREAD_LOCAL.get()) {
            return false;
        }

        for (Map.Entry<String, Boolean> item : properties.getDebug().getLoggers().entrySet()) {
            if (loggerName.startsWith(item.getKey())) {
                return item.getValue();
            }
        }
        return properties.getDebug().getRoot();
    }
}
