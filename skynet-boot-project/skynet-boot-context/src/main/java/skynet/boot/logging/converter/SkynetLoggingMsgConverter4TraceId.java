package skynet.boot.logging.converter;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import skynet.boot.SkynetConsts;

/**
 * <AUTHOR>  2023/9/5 16:45
 */
public class SkynetLoggingMsgConverter4TraceId extends MessageConverter {

    @Override
    public String convert(ILoggingEvent event) {
        String traceId = MDC.get(SkynetConsts.MDC_CURRENT_TRACE_ID_KEY);
        return traceId == null ? StringUtils.EMPTY : traceId;
    }
}
