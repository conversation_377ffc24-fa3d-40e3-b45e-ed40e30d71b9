package skynet.boot.logging;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;


/**
 * 记录方法耗时日志
 *
 * <AUTHOR>
 * @date 2019-12-09 18:03
 */
@Slf4j
@Aspect
public class LoggingCostAspect {


    /**
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("@annotation(loggingCost)")
    public Object audit(ProceedingJoinPoint joinPoint, LoggingCost loggingCost) throws Throwable {
        log.debug("audit begin ...");
        //Logger logger = LoggerFactory.getLogger(joinPoint.getTarget().getClass());
        StopWatch stopWatch = null;
        if (log.isInfoEnabled()) {
            stopWatch = new StopWatch();
            stopWatch.start();
        }
        try {
            return joinPoint.proceed();
        } finally {
            if (log.isInfoEnabled()) {
                String alias = loggingCost.message();
                if (StringUtils.isBlank(alias)) {
                    Method targetMethod = ((MethodSignature) joinPoint.getSignature()).getMethod();
                    alias = String.format("%s.%s", joinPoint.getTarget().getClass().getName(), targetMethod.getName());
                }
                assert stopWatch != null;
                stopWatch.stop();
                log.info("{}.cost= {} ms[{}].", alias, stopWatch.getTime(), stopWatch);
            }
            log.debug("audit end.");
        }
    }
}
