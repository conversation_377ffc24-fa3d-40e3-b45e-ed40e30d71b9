package skynet.boot.logging.converter;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.alibaba.fastjson2.JSON;
import org.slf4j.helpers.MessageFormatter;
import skynet.boot.common.SpringUtils;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.logging.config.SkynetLoggingAutoConfiguration;
import skynet.boot.logging.config.SkynetLoggingProperties;

/**
 * 自定义 大对象日志输出省略转换器
 * <p>
 * 使用场景 消息 大对象（base64的文件内容或特征向量数组）
 * <p>
 * <a href="https://www.cnblogs.com/duanxz/archive/2013/02/02/2890190.html">参考</a>
 *
 * <AUTHOR> 2023年08月31日
 * @since 4.0.13
 */
public class SkynetLoggingMsgConverter4Ellipsis extends MessageConverter {

    private SkynetLoggingProperties properties;

    @Override
    public String convert(ILoggingEvent event) {

        if (properties == null && SpringUtils.getContext() != null) {
            properties = SpringUtils.getContext().getBean(SkynetLoggingProperties.class);
        }

        if (properties != null && properties.getEllipses().isEnabled()) {
            Jsonable.LargeObjectEllipsesFilter filter = getFilter();
            if (event.getArgumentArray() != null) {
                //对 format 参数进行 省略处理
                Object[] argumentArray = new Object[event.getArgumentArray().length];
                boolean isEllipses = false;
                for (int index = 0; index < argumentArray.length; index++) {
                    argumentArray[index] = event.getArgumentArray()[index];
                    if (argumentArray[index] instanceof String && ((String) argumentArray[index]).length() > filter.getStringLimitLen()) {
                        argumentArray[index] = filter.ellipsesString((String) argumentArray[index]);
                        isEllipses = true;
                    } else if (!isPrimitiveType(argumentArray[index])) {
                        argumentArray[index] = JSON.toJSONString(argumentArray[index], filter);
                        isEllipses = true;
                    }
                }
                if (isEllipses) {
                    return MessageFormatter.arrayFormat(event.getMessage(), argumentArray).getMessage();
                }
            } else if (event.getMessage().length() > filter.getStringLimitLen()) {
                //只有 输出 message 时 也对长度进行省略处理
                return filter.ellipsesString(event.getMessage());
            }
        }
        //保持原样输出
        return event.getFormattedMessage();
    }

    /**
     * 由于 SkynetLoggingEllipsesMessageConverter 是通过配置文件注入到 logback中，由logback 自动创建实例，所以通过 静态类方法获取 SpringBean
     *
     * @return
     */
    private Jsonable.LargeObjectEllipsesFilter getFilter() {
        if (SpringUtils.getContext() != null) {
            if (SpringUtils.getContext().containsBean(SkynetLoggingAutoConfiguration.ELLIPSES_FILTER_BEAN_NAME)) {
                return SpringUtils.getContext().getBean(Jsonable.LargeObjectEllipsesFilter.class);
            }
        }
        return Jsonable.DEFAULT_LARGE_OBJECT_ELLIPSES_FILTER;
    }


    /**
     * 排除了 string
     *
     * @param obj
     * @return
     */
    private static boolean isPrimitiveType(Object obj) {
        return obj instanceof Byte
                || obj instanceof Short
                || obj instanceof Integer
                || obj instanceof Long
                || obj instanceof Float
                || obj instanceof Double
                || obj instanceof Character
                || obj instanceof Boolean;
    }
}
