package skynet.boot.logging;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Lazy;
import skynet.boot.logging.config.SkynetLoggingProperties;

/**
 * skynet-logging  Endpoint
 *
 * <AUTHOR>  2022年08月25日16:31:20
 */
@Slf4j
@Lazy
@Endpoint(id = "skynet-logging")
public class SkynetLoggingEndpoint {

    private final SkynetLoggingProperties skynetLoggingProperties;

    public SkynetLoggingEndpoint(SkynetLoggingProperties skynetLoggingProperties) {
        this.skynetLoggingProperties = skynetLoggingProperties;
    }

    @ReadOperation
    public Object invoke() {
        log.debug("do get skynet-logging...");
        Object targetObject = AopProxyUtils.getSingletonTarget(skynetLoggingProperties);
        if (targetObject == null) {
            targetObject = skynetLoggingProperties;
        }
        return targetObject;
    }
}