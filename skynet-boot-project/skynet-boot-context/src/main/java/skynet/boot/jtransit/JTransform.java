package skynet.boot.jtransit;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import skynet.boot.jtransit.domain.JTransformRule;
import skynet.boot.jtransit.domain.JTransformSchema;
import skynet.boot.script.CodeExecutor;

import java.math.BigDecimal;
import java.util.*;

/**
 * json转换器: 根据设置的转换模版（json），将输入的json数据，转成目标json
 * <p>
 * 注意：如果输入的json的key，在模版中没有配置，则会被忽略这个键值对
 * <p/>
 *
 * <pre>
 *  集成方式：
 *     1. 在resource目录下创建jtransit目录，并在此目录下创建一个或多个模版json文件，如xfyun_api_transfer_iat.json。
 *          也可以在通过系统属性配置skynet.jtransit=/xxx/jtransit，自定义json模版目录路径（优先级更高）
 *     2. 创建json转换管理类（建议单例）
 *          JTransformManager jTransformManager = new JTransformManager(new CodeExecutorBuilder("javascript").build());
 *     3. 调用json转换
 *          Optional<JTransform> transformOptional = apiTransferManager.getTransform("iat");
 *          transformOptional.get().transform(sessionParamObj);
 *
 *  转换方式：
 *      1. shift（可选） 指定输入json的key，将其value值转换
 *          1.1 匹配替换
 *          1.2 javascript脚本计算
 *      2. defValue 缺省值设置（可选）：如果没有shift或者shift之后没有合法值，用这个兜底
 *
 * 转换模版说明
 * $custom 为自定义的json key
 *  {
 *      //code非常重要，apiTransferManager.getTransform("iat")，这个方法的参数和code对应
 *      "code":"iat",
 *      "desc":"例子",
 *      "rule"：{
 *         "$custom": [
 *              {
 *                   // 需要生成的key，必填
 *                   "target": "",
 *
 *                   // 字段类型 必填（int，float，boolean， string）
 *                   "type": ""
 *
 *                   // 映射（可选）
 *                   "shift":{
 *
 *                           //原始json的key
 *                           "src": "",
 *
 *                           // 设置值方式一：替换原始值方式，优先级最高
 *                           "replace": [
 *                                   {"$srcValue": "$targetValue"}
 *                           ],
 *
 *                           // 设置值方式二：采用javascript脚本替换，srcValue为原始值
 *                           "script": "(($.srcValue + 500 ) / (500 + 500)) * 100"
 *                   },
 *
 *                   // 缺省值（可选），如果没有shift或者shift之后没有合法值，用这个兜底
 *                   "defValue": ""
 *                }
 *           ]
 *     }
 * }
 *
 * 案例
 * a. 直接设置默认值
 * <pre>
 *     // input
 *     {"name": "zhangsan", "age": "18"}
 *
 *     // rule
 *     {"business": [{"target": "myName", "type": "string", "defValue": "zs"}]}
 *
 *     //output
 *     {"business":{"myName": "zs"}}
 *
 * b. 改变值的类型
 *     // input
 *     {"name": "zhangsan", "age": "18"}
 *
 *     // rule
 *     {"business": [{"target": "myAge", "type": "int", "shift":{"src":"age"} }]}
 *
 *     //output
 *     {"business":{"myAge": 18}}
 *
 * c. 使用shift匹配替换值
 *     // input
 *     {"name": "zhangsan", "age": "18"}
 *
 *     // rule
 *     {"business": [{"target": "myName", "type": "string", "shift":{"src":"name", "replace":[{"zhangsan":"ZhangSan"}]} }]}
 *
 *     //output
 *     {"business":{"myName": "ZhangSan"}}
 *
 * d. 使用shift脚本替换值
 * <pre>
 *     // input
 *     {"name": "zhangsan", "age": "18"}
 *
 *     // rule
 *     {"business": [{"target": "myAge", "type": "string", "shift":{"src":"age", "script":{"$.srcValue < 18 ? '未成年' : '成年人'"}} }]}
 *
 *     //output
 *     {"business":{"myAge": "成年人"}}
 *
 * <AUTHOR>  2023/8/13 10:48
 */
@Slf4j
public class JTransform {

    private final CodeExecutor codeExecutor;
    private final JTransformSchema transformSchema;
    private final Map<String, List<JTransformRule>> transformRuleMap = new HashMap<>();


    public JTransform(CodeExecutor codeExecutor, JTransformSchema transformSchema) {
        Assert.notNull(transformSchema, "schemaJson error: transformSchema is null");
        Assert.hasText(transformSchema.getCode(), "schemaJson error: code is null");
        Assert.hasText(transformSchema.getDesc(), "schemaJson error: desc is null");

        JSONObject ruleObj = transformSchema.getRule();
        Assert.notNull(ruleObj, "schemaJson error: rule is null");
        Set<String> ruleKeySet = ruleObj.keySet();
        for (String key : ruleKeySet) {
            JSONArray ruleJsonArray = ruleObj.getJSONArray(key);
            Assert.isTrue(ruleJsonArray != null && !ruleJsonArray.isEmpty(), "schemaJson error: rule array is null");
            List<JTransformRule> ruleList = ruleJsonArray.toJavaList(JTransformRule.class);
            for (JTransformRule transformRule : ruleList) {
                Assert.notNull(transformRule, "schemaJson error: rule is null");
                Assert.hasText(transformRule.getTarget(), "schemaJson error: target is null");
                Assert.hasText(transformRule.getType(), "schemaJson error: type is null");
                if (transformRule.getShift() != null) {
                    Assert.hasText(transformRule.getShift().getSrc(), "schemaJson error: src is null");
                }
            }
            transformRuleMap.put(key, ruleList);
        }
        this.transformSchema = transformSchema;
        this.codeExecutor = codeExecutor;
    }


    public JTransformSchema getStatus() {
        return transformSchema;
    }

    /**
     * 转换
     *
     * @param srcJsonObj 原始json协议。需要扁平化json，不可以在jsonObj中嵌套jsonObj
     * @return 目标json协议
     */
    public JSONObject transform(JSONObject srcJsonObj) {
        log.debug("srcJsonObj = {}", srcJsonObj);

        JSONObject targetObj = new JSONObject();
        // 读取模版中的key，目前为 business 和 data
        this.transformRuleMap.forEach((k, v) -> {
            targetObj.put(k, transformRootObj(srcJsonObj, v));
        });
        log.debug("targetObj = {}", targetObj);
        return targetObj;
    }

    private JSONObject transformRootObj(JSONObject srcJsonObj, List<JTransformRule> transformRules) {
        if (CollectionUtils.isEmpty(transformRules)) {
            return new JSONObject();
        }
        //转换第一层（business / data / ...）
        JSONObject targetObj = new JSONObject();
        for (JTransformRule transformRule : transformRules) {
            // 转换每一个目标键值对
            transformItemObj(srcJsonObj, transformRule, targetObj);
        }
        return targetObj;
    }

    private void transformItemObj(JSONObject srcObj, JTransformRule transformRule, JSONObject targetObj) {
        //将原始的值转成目标值
        Optional<String> targetValue = convertValue(srcObj, transformRule);
        if (!targetValue.isPresent()) {
            // 原始的值，没有在匹配规则中找到，则无效（不设置，引擎服务会使用缺省值）
            return;
        }
        targetObj.put(transformRule.getTarget(), refreshValueType(transformRule.getType(), targetValue.get()));
    }

    private Optional<String> convertValue(JSONObject srcObj, JTransformRule transformRule) {
        Optional<String> shitValue = shiftValue(srcObj, transformRule);
        if (shitValue.isPresent()) {
            return shitValue;
        }
        // shift 没有合法值，则使用配置的缺省值
        return StringUtils.hasText(transformRule.getDefValue()) ? Optional.of(transformRule.getDefValue()) : Optional.empty();
    }

    private Optional<String> shiftValue(JSONObject srcObj, JTransformRule transformRule) {
        JTransformRule.Shift shift = transformRule.getShift();
        if (shift == null) {
            return Optional.empty();
        }

        String srcValue = srcObj.getString(shift.getSrc());
        if (!StringUtils.hasText(srcValue)) {
            // 模版中配置的key，在原始json中不存在，则忽略
            return Optional.empty();
        }

        // 优先使用replace规则
        JSONArray replace = shift.getReplace();
        if (replace != null && !replace.isEmpty()) {
            for (int i = 0; i < replace.size(); i++) {
                JSONObject replaceItem = replace.getJSONObject(i);
                if (replaceItem == null) {
                    continue;
                }
                if (replaceItem.containsKey(srcValue)) {
                    return Optional.of(replaceItem.getString(srcValue));
                }
            }
        }

        // 使用script
        if (StringUtils.hasText(shift.getScript())) {
            try {
                Object targetValue = codeExecutor.evalExpression(shift.getScript(), new JSONObject().fluentPut("srcValue", srcValue));
                return Optional.of(targetValue.toString());
            } catch (Exception e) {
                log.error("evalExpression:[{}}], error:{}.", shift.getScript(), e.getMessage());
            }
        }

        // 不变换值
        return Optional.of(srcValue);
    }

    private Object refreshValueType(String type, String targetValue) {
        // 刷新值的类型
        if ("int".equalsIgnoreCase(type)) {
            BigDecimal bigDecimal = new BigDecimal(targetValue);
            return bigDecimal.intValue();
        }
        if ("float".equalsIgnoreCase(type)) {
            return Float.valueOf(targetValue);
        }
        if ("boolean".equalsIgnoreCase(type)) {
            return Boolean.valueOf(targetValue);
        }
        return targetValue;
    }
}
