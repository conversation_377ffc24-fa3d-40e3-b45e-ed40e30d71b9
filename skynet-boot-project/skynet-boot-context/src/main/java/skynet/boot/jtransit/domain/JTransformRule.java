package skynet.boot.jtransit.domain;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * json 转换规则
 *
 * <AUTHOR>  2023/8/14 16:11
 */
@Setter
@Getter
public class JTransformRule extends Jsonable {

    /**
     * 需要生成的key，必填
     */
    @JSONField(name = "target")
    private String target;

    /**
     * 字段类型 必填（int，float，boolean，string）
     */
    @JSONField(name = "type")
    private String type;

    /**
     * 缺省值（可选），如果没有shift或者shift之后没有合法值，用这个兜底
     */
    @JSONField(name = "defValue")
    private String defValue;

    /**
     * 映射（可选）
     */
    @JSONField(name = "shift")
    private Shift shift;

    @Setter
    @Getter
    public static class Shift {

        /**
         * 原始json的key
         */
        @JSONField(name = "src")
        private String src;

        /**
         * 设置值方式一：替换原始值方式，优先级最高
         */
        @JSONField(name = "replace")
        private JSONArray replace;

        /**
         * 设置值方式二：采用脚本替换，srcValue为原始值
         */
        @JSONField(name = "script")
        private String script;
    }
}
