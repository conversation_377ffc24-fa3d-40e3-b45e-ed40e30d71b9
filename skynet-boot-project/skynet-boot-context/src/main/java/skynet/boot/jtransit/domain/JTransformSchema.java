package skynet.boot.jtransit.domain;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * <AUTHOR>  2023/8/15 10:54
 */
@Setter
@Getter
public class JTransformSchema extends Jsonable {

    /**
     * 转换规则 标识码
     */
    private String code;

    /**
     * 转换规则描述
     */
    private String desc;

    /**
     * 转换规则
     */
    private JSONObject rule;

}
