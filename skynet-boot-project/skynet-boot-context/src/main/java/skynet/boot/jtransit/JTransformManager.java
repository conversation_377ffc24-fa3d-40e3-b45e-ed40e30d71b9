package skynet.boot.jtransit;

import com.alibaba.fastjson2.JSONObject;
import io.micrometer.core.instrument.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.Assert;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import skynet.boot.jtransit.domain.JTransformSchema;
import skynet.boot.script.CodeExecutor;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * json转换管理器 单例
 *
 * <AUTHOR>  2023/8/15 09:42
 */
@Slf4j
public class JTransformManager implements AutoCloseable {

    private final CodeExecutor codeExecutor;
    private final Map<String, JTransform> transformMap = new ConcurrentHashMap<>();

    public JTransformManager(CodeExecutor codeExecutor) {
        this.codeExecutor = codeExecutor;
        scanAndRegister();
    }

    private void scanAndRegister() {
        //读取指定配置路径下的规则文件
        String schemaJsonDicPath = System.getProperty("skynet.jtransit");
        if (StringUtils.hasText(schemaJsonDicPath)) {
            File schemaJsonDir = new File(schemaJsonDicPath);
            Assert.isTrue(schemaJsonDir.isDirectory(), String.format("system property[skynet.jtransit={%s}] error: is not file directory", schemaJsonDir.getAbsolutePath()));
            log.info("jtransit resource:[{}]", schemaJsonDir);
            File[] files = schemaJsonDir.listFiles();
            if (files == null) {
                log.warn("jtransit directory is empty.");
                return;
            }
            for (File schemaFile : files) {
                try {
                    register(new String(FileCopyUtils.copyToByteArray(schemaFile), StandardCharsets.UTF_8));
                } catch (IOException e) {
                    log.error("read schemaFile:{} error:{}", schemaFile.getAbsolutePath(), e.getMessage());
                    throw new RuntimeException(e);
                }
            }
            return;
        }

        // 默认路径下的规则文件
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            Resource[] resources = resolver.getResources("classpath:jtransit/*.json");
            for (Resource resource : resources) {
                String schemaJson = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                register(schemaJson);
            }
        } catch (IOException e) {
            log.warn("read jtransit schema error:{}", e.getMessage());
        }
    }

    /**
     * 注册转换规则
     *
     * @param schemaJson 规则描述数据
     */
    public synchronized void register(String schemaJson) {
        log.debug("start register. current transform count:[{}]  schemaJson:{}", transformMap.size(), schemaJson);

        Assert.hasText(schemaJson, "schemaJson is null");
        JTransformSchema transformSchema = JSONObject.parseObject(schemaJson, JTransformSchema.class);
        transformMap.put(transformSchema.getCode(), new JTransform(codeExecutor, transformSchema));

        log.debug("register success. current transform count:[{}]", transformMap.size());
    }

    /**
     * 获取转换器
     *
     * @param code 规则标识码
     * @return 转换器
     */
    public Optional<JTransform> getTransform(String code) {
        Assert.hasText(code, "getTransform error: code is null");
        JTransform jsonTransform = transformMap.get(code);
        if (jsonTransform == null) {
            return Optional.empty();
        }
        return Optional.of(jsonTransform);
    }

    /**
     * 取消转换规则
     *
     * @param code 规则标识码
     */
    public void deregister(String code) {
        log.debug("start deregister. current transform count:[{}], code = {}", transformMap.size(), code);
        transformMap.remove(code);
        log.debug("deregister success. current transform count:[{}]", transformMap.size());
    }

    /**
     * 获取状态信息
     *
     * @return 状态信息
     */
    public List<JTransformSchema> getStatus() {
        return transformMap.values().stream().map(JTransform::getStatus).collect(Collectors.toList());
    }

    @Override
    public void close() throws Exception {
        log.info("close start...");
        transformMap.clear();
        log.info("close end.");
    }
}
