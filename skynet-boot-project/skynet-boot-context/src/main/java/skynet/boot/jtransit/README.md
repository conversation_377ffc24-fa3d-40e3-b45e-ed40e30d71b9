## jtransit:  json转换组件

（since v4.0.13）

jtransit是skynet提供一种json转换组件，通过配置json转换模版，可以将输入的json数据转成目标json，通常可用于协议的转换。典型的应用场景，如将私有化的语音识别服务数据协议转换成公有云服务协议，达到协议兼容的效果。

### 功能特性

- 支持json key的转换。
  将输入的key，转成目标的key


- 支持值的转换
  两种转换方式：1）匹配替换 2）脚本计算转换，支持groovy和JavaScript脚本


- 支持配置缺省值
  如果转换规则或转换之后没有合法值，用这个兜底

*注意：如果输入的**json**的**key**，在模版中没有配置，则会被忽略这个键值对*

### 模版说明

- 示例

```json
    {
       //code非常重要，apiTransferManager.getTransform("iat")，这个方法的参数和code对应
       "code":"iat",
       "desc":"例子",
       "rule"：{
          "$custom": [
               {
                    // 需要生成的key，必填
                    "target": "",
 
                    // 字段类型 必填（int，float，boolean， string）
                    "type": ""
 
                    // 映射（可选）
                    "shift":{
 
                            //原始json的key
                            "src": "",
 
                            // 设置值方式一：替换原始值方式，优先级最高
                            "replace": [
                                    {"$srcValue": "$targetValue"}
                            ],
 
                            // 设置值方式二：采用javascript脚本替换，srcValue为原始值
                            "script": "(($.srcValue + 500 ) / (500 + 500))  100"
                    },
 
                    // 缺省值（可选），如果没有shift或者shift之后没有合法值，用这个兜底
                    "defValue": ""
                 }
            ]
        }
    }
```

code为json转换模版的id，全局唯一；$custom 为自定义的json key。

### 模版注册

- 自动扫描

  在项目的**resource**目录下创建**jtransit**目录，并在此目录下创建一个或多个模版**json**文件，如*
  *xfyun_api_transfer_iat.json**

  也可以在通过系统属性配置**skynet.jtransit=/xxx/jtransit**，实现自定义**json**模版目录路径（优先级更高）。文件的后缀名必须为.json。


- 手动注册

  可以通过代码手动注册模版，示例如下：

  ```java
  JTransformManager jTransformManager = new JTransformManager(new CodeExecutorBuilder("javascript").build());
  String schemaJson = "{\"code\": \"iat\",\"desc\": \"例子\", \"rule\": {\"business\": [ { \"target\": \"myName\",\"type\": \"string\",\"shift\": {\"src\": \"name\", \"replace\": [ {\"zhangsan\": \"ZhangSan\"}]}}]}}";
  jTransformManager.register(schemaJson);
  ```

### 代码示例

- 创建json转换管理类（建议单例）

  ```java
  //这里使用javascript，也可以使用grovvy，用法参照skynet 脚本执行组件
  JTransformManager jTransformManager = new JTransformManager(new CodeExecutorBuilder("javascript").build());
  ```


- 调用json转换

  ```json
  //这里的参数为，json模版中配置的code
  Optional<JTransform> transformOptional = jTransformManager.getTransform("iat");
  transformOptional.get().transform(sessionParamObj);
  ```

### 真实案例

- 直接设置默认值

  输入

  ```json
   {"name": "zhangsan", "age": "18"}
  ```

  rule配置

  ```json
  {"business": [{"target": "myName", "type": "string", "defValue": "zs"}]}
  ```

  输出

  ```json
   {"business":{"myName": "zs"}}
  ```


- 改变值的类型

  输入

  ```json
   {"name": "zhangsan", "age": "18"}
  ```

  rule配置

  ```json
   {"business": [{"target": "myAge", "type": "int", "shift":{"src":"age"} }]}
  ```

  输出

  ```json
   {"business":{"myAge": 18}}
  ```


- 使用shift匹配替换值

  输入

  ```json
   {"name": "zhangsan", "age": "18"}
  ```

  rule配置

  ```json
  {
      "business": [
          {
              "target": "myName",
              "type": "string",
              "shift": {
                  "src": "name",
                  "replace": [
                      {
                          "zhangsan": "ZhangSan"
                      }
                  ]
              }
          }
      ]
  }
  ```

  输出

  ```json
    {"business":{"myName": "ZhangSan"}}
  ```


- 使用shift脚本替换值

  输入

  ```json
   {"name": "zhangsan", "age": "18"}
  ```

  rule配置

  ```json
  {
      "business": [
          {
              "target": "myAge",
              "type": "string",
              "shift": {
                  "src": "age",
                  "script": "$.srcValue < 18 ? '未成年' : '成年人'"
              }
          }
      ]
  }
  ```

  输出

  ```json
   {"business":{"myAge": "成年人"}}
  ```

### 状态获取

已使用actuator自定义端点，暴露已加载的json模版信息

  ```java
  /**
   * json转换 相关属性 Endpoint
   *
   * <AUTHOR>
   */
  @Slf4j
  @Lazy
  @Endpoint(id = "skynet-jtransit-status")
  public class SkynetJtransitEndpoint {
  
      private final JTransformManager jTransformManager;
  
      public SkynetJtransitEndpoint(JTransformManager jTransformManager) {
          this.jTransformManager = jTransformManager;
      }
  
      @ReadOperation
      public Object invoke() {
          log.debug("do get ServiceStatus...");
          Map<String, Object> map = new LinkedHashMap<>(1);
          map.put("skynet.jtransit", jTransformManager.getStatus());
          return map;
      }
  }
  ```

  

   
