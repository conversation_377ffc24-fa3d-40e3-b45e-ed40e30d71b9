package skynet.boot.jtransit.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Lazy;
import skynet.boot.jtransit.JTransformManager;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * json转换 相关属性 Endpoint
 *
 * <AUTHOR>
 */
@Slf4j
@Lazy
@Endpoint(id = "skynet-jtransit-status")
public class SkynetJtransitEndpoint {

    private final JTransformManager jTransformManager;

    public SkynetJtransitEndpoint(JTransformManager jTransformManager) {
        this.jTransformManager = jTransformManager;
    }

    @ReadOperation
    public Object invoke() {
        log.debug("do get ServiceStatus...");
        Map<String, Object> map = new LinkedHashMap<>(1);
        map.put("skynet.jtransit", jTransformManager.getStatus());
        return map;
    }
}