package skynet.boot.websocket;

import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpointConfig;
import java.net.InetSocketAddress;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket Configuration for Skynet Framework
 * 
 * This configuration class sets up WebSocket support with the following
 * features:
 * - Automatic endpoint registration
 * - HTTP session integration
 * - Client IP address tracking
 * - Component scanning for WebSocket endpoints
 *
 * The configuration is conditionally enabled when the required WebSocket
 * classes
 * are present on the classpath.
 *
 * Usage:
 * 1. Add this configuration to your Spring Boot application
 * 2. Create WebSocket endpoints using @ServerEndpoint annotation
 * 3. Use HttpSessionConfigurator for session management
 *
 * Example endpoint:
 * 
 * <pre>{@code
 * @ServerEndpoint(value = "/websocket/example", configurator = HttpSessionConfigurator.class)
 * public class ExampleEndpoint {
 *     // WebSocket endpoint implementation
 * }
 * }</pre>
 *
 * <AUTHOR> [2017-12-19 12:02:36]
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({ ServerEndpointExporter.class, ServerEndpointConfig.class })
@ServletComponentScan
@ComponentScan(basePackages = { "skynet.boot.websocket" })
public class WebSocketConfig {

    /**
     * Creates and configures the ServerEndpointExporter
     * This bean is responsible for registering WebSocket endpoints
     * annotated with @ServerEndpoint
     *
     * @return configured ServerEndpointExporter
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    /**
     * Creates the custom HTTP session configurator
     * This configurator enables HTTP session integration and
     * client IP address tracking for WebSocket connections
     *
     * @return configured HttpSessionConfigurator
     */
    @Bean
    public HttpSessionConfigurator customSpringConfigurator() {
        return new HttpSessionConfigurator();
    }

    /**
     * Retrieves the client's IP address from a WebSocket session
     * 
     * This method safely extracts the InetSocketAddress that was
     * stored during the WebSocket handshake by the HttpSessionConfigurator
     *
     * @param session The WebSocket session
     * @return The client's InetSocketAddress or null if not available
     */
    public static InetSocketAddress getRemoteAddressBySession(Session session) {
        if (session == null) {
            return null;
        }
        return (InetSocketAddress) session.getUserProperties()
                .get(HttpSessionConfigurator.CLIENT_IP_KEY);
    }
}
