package skynet.boot.websocket;

import jakarta.websocket.HandshakeResponse;
import jakarta.websocket.server.HandshakeRequest;
import jakarta.websocket.server.ServerEndpointConfig;
import jakarta.websocket.server.ServerEndpointConfig.Configurator;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * Custom WebSocket Endpoint Configurator
 * 
 * This configurator provides the following features:
 * 1. Spring dependency injection support for WebSocket endpoints
 * 2. HTTP headers propagation to WebSocket session
 * 3. Client IP address tracking
 * 
 * Key responsibilities:
 * - Manages WebSocket endpoint instantiation through Spring context
 * - Transfers HTTP headers to WebSocket user properties
 * - Captures and stores client IP address information
 * 
 * Usage example:
 * 
 * <pre>
 * {
 *     &#64;code
 *     &#64;ServerEndpoint(value = "/websocket/example", configurator = HttpSessionConfigurator.class)
 *     &#64;Component
 *     public class ExampleEndpoint {
 *         @Autowired
 *         private SomeService service; // Will be properly injected
 *     }
 * }
 * </pre>
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpSessionConfigurator extends Configurator implements ApplicationContextAware {

    /**
     * Key for storing client IP address in WebSocket session properties
     */
    public static final String CLIENT_IP_KEY = "skynet-client-ip";

    /**
     * Spring application context for dependency injection
     * Volatile ensures visibility across threads
     */
    private static volatile BeanFactory context;

    /**
     * Creates or retrieves a WebSocket endpoint instance from Spring context
     * Enables dependency injection in WebSocket endpoints
     *
     * @param <T>   The endpoint type
     * @param clazz The endpoint class
     * @return A Spring-managed instance of the endpoint
     * @throws InstantiationException if the endpoint cannot be instantiated
     */
    @Override
    public <T> T getEndpointInstance(Class<T> clazz) throws InstantiationException {
        return context.getBean(clazz);
    }

    /**
     * Stores the Spring application context for later use
     * Required for ApplicationContextAware implementation
     *
     * @param applicationContext The Spring application context
     * @throws BeansException if the context cannot be set
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        HttpSessionConfigurator.context = applicationContext;
    }

    /**
     * Modifies the WebSocket handshake process to:
     * 1. Copy HTTP headers to WebSocket session
     * 2. Store client IP address information
     *
     * @param config   The endpoint configuration
     * @param request  The handshake request
     * @param response The handshake response
     */
    @Override
    public void modifyHandshake(ServerEndpointConfig config,
            HandshakeRequest request,
            HandshakeResponse response) {
        super.modifyHandshake(config, request, response);

        if (config.getUserProperties() == null) {
            log.warn("User properties map is null in endpoint config");
            return;
        }

        // Copy HTTP headers to WebSocket session
        log.debug("Copying HTTP headers to WebSocket session properties");
        copyHttpHeaders(request, config);

        // Store client IP address
        storeClientIpAddress(config);
    }

    /**
     * Copies HTTP headers from the request to WebSocket session properties
     * Only copies headers that aren't already present in the properties
     *
     * @param request The handshake request
     * @param config  The endpoint configuration
     */
    private void copyHttpHeaders(HandshakeRequest request, ServerEndpointConfig config) {
        Map<String, List<String>> headers = request.getHeaders();
        Map<String, Object> properties = config.getUserProperties();

        headers.forEach((key, values) -> {
            if (!properties.containsKey(key) && !values.isEmpty()) {
                properties.put(key, values.get(0));
            }
        });
    }

    /**
     * Stores the client's IP address in the WebSocket session properties
     *
     * @param config The endpoint configuration
     */
    private void storeClientIpAddress(ServerEndpointConfig config) {
        InetSocketAddress clientAddress = WebServletListener.getCurrentInetSocketAddress();

        if (clientAddress != null) {
            log.debug("Storing client IP address: {}", clientAddress);
            config.getUserProperties().put(CLIENT_IP_KEY, clientAddress);
        } else {
            log.warn("Unable to determine client IP address");
        }
    }
}
