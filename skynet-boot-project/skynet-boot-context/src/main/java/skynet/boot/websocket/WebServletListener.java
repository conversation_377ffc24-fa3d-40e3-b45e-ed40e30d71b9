package skynet.boot.websocket;

import jakarta.servlet.ServletRequestEvent;
import jakarta.servlet.ServletRequestListener;
import jakarta.servlet.annotation.WebListener;
import java.net.InetSocketAddress;
import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket Client IP Address Tracking Listener
 * 
 * This listener captures and stores client IP address information for each
 * incoming
 * HTTP request in a ThreadLocal variable, making it available for WebSocket
 * handshakes.
 * 
 * Key features:
 * - Automatically captures client IP and port for each request
 * - Stores connection information in thread-local storage
 * - Provides thread-safe access to client address information
 * - Integrates with WebSocket handshake process
 * 
 * Usage:
 * The listener is automatically registered via @WebListener annotation.
 * Client IP information can be accessed during WebSocket handshake through
 * the getCurrentInetSocketAddress() method.
 *
 * <AUTHOR>
 * @since 2020-10-19
 */
@Slf4j
@WebListener
public class WebServletListener implements ServletRequestListener {

    /**
     * Thread-local storage for client address information
     * Ensures thread safety when handling multiple concurrent requests
     */
    private static final ThreadLocal<InetSocketAddress> THREAD_LOCAL = new ThreadLocal<>();

    /**
     * Captures client IP address information when a request is initialized
     * 
     * This method:
     * 1. Extracts the client's IP address and port from the request
     * 2. Creates an InetSocketAddress from the connection information
     * 3. Stores the address in thread-local storage for later use
     *
     * @param event The servlet request event containing request information
     */
    @Override
    public void requestInitialized(ServletRequestEvent event) {
        log.debug("Initializing request - capturing client IP information");

        try {
            String remoteAddr = event.getServletRequest().getRemoteAddr();
            int remotePort = event.getServletRequest().getRemotePort();

            InetSocketAddress clientAddress = new InetSocketAddress(remoteAddr, remotePort);

            if (log.isDebugEnabled()) {
                String remoteHost = event.getServletRequest().getRemoteHost();
                log.debug("Client connection details - Address: {}, Host: {}",
                        clientAddress, remoteHost);
            }

            THREAD_LOCAL.set(clientAddress);

        } catch (Exception e) {
            log.error("Failed to capture client IP information: {}", e.getMessage());
            if (log.isTraceEnabled()) {
                log.trace("Detailed error while capturing client IP", e);
            }
        }
    }

    /**
     * Retrieves the client's address information from thread-local storage
     * 
     * This method is typically called during WebSocket handshake to associate
     * the client's IP address with the WebSocket session.
     *
     * @return The client's InetSocketAddress or null if not available
     */
    public static InetSocketAddress getCurrentInetSocketAddress() {
        return THREAD_LOCAL.get();
    }

    /**
     * Cleans up thread-local storage when a request is destroyed
     * Prevents memory leaks by removing thread-local values
     *
     * @param event The servlet request event
     */
    @Override
    public void requestDestroyed(ServletRequestEvent event) {
        THREAD_LOCAL.remove();
        if (log.isDebugEnabled()) {
            log.debug("Request completed - cleared thread-local storage");
        }
    }
}