package skynet.boot.tlb.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;
import skynet.boot.common.InetAddressUtils;
import skynet.boot.common.domain.Jsonable;


@Getter
@Setter
@Accessors(chain = true)
public class ServerIPEndpoint extends Jsonable {

    private String serviceName;

    private String ip;

    private int port;

    /**
     * 获取  IPv4: IP:Port
     * <p>
     * IPv6 eg：  [fe80::de42:8cb:8db1:3366]:1377
     *
     * @return
     */
    public String getIPEndpoint() {
        return String.format(this.isIPv6() ? "[%s]:%s" : "%s:%s", this.ip, this.port);
    }

    /**
     * 是否是IPv6
     *
     * @return
     */
    public boolean isIPv6() {
        return StringUtils.hasText(ip) && InetAddressUtils.isIPv6Address(ip);
    }


    @Override
    public String toString() {
        return String.format("[svc:%s] %s:%d", this.serviceName, this.ip, this.port);
    }
}
