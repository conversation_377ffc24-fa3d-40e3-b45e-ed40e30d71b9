// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Lb.proto

// Protobuf Java Version: 3.25.1
package skynet.boot.tlb.core;

public final class Lb {
    private Lb() {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistry registry) {
        registerAllExtensions(
                (com.google.protobuf.ExtensionRegistryLite) registry);
    }

    public interface ServiceNameOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.ServiceName)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string ServiceName = 1;</code>
         *
         * @return The serviceName.
         */
        java.lang.String getServiceName();

        /**
         * <code>string ServiceName = 1;</code>
         *
         * @return The bytes for serviceName.
         */
        com.google.protobuf.ByteString
        getServiceNameBytes();
    }

    /**
     * Protobuf type {@code GetServer.ServiceName}
     */
    public static final class ServiceName extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.ServiceName)
            ServiceNameOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ServiceName.newBuilder() to construct.
        private ServiceName(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ServiceName() {
            serviceName_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ServiceName();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceName_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceName_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.ServiceName.class, skynet.boot.tlb.core.Lb.ServiceName.Builder.class);
        }

        public static final int SERVICENAME_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile java.lang.Object serviceName_ = "";

        /**
         * <code>string ServiceName = 1;</code>
         *
         * @return The serviceName.
         */
        @java.lang.Override
        public java.lang.String getServiceName() {
            java.lang.Object ref = serviceName_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                serviceName_ = s;
                return s;
            }
        }

        /**
         * <code>string ServiceName = 1;</code>
         *
         * @return The bytes for serviceName.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getServiceNameBytes() {
            java.lang.Object ref = serviceName_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                serviceName_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serviceName_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, serviceName_);
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serviceName_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, serviceName_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.ServiceName)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.ServiceName other = (skynet.boot.tlb.core.Lb.ServiceName) obj;

            if (!getServiceName()
                    .equals(other.getServiceName())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + SERVICENAME_FIELD_NUMBER;
            hash = (53 * hash) + getServiceName().hashCode();
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServiceName parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.ServiceName prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code GetServer.ServiceName}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.ServiceName)
                skynet.boot.tlb.core.Lb.ServiceNameOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceName_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceName_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.ServiceName.class, skynet.boot.tlb.core.Lb.ServiceName.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.ServiceName.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                serviceName_ = "";
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceName_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServiceName getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.ServiceName.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServiceName build() {
                skynet.boot.tlb.core.Lb.ServiceName result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServiceName buildPartial() {
                skynet.boot.tlb.core.Lb.ServiceName result = new skynet.boot.tlb.core.Lb.ServiceName(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.Lb.ServiceName result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.serviceName_ = serviceName_;
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.ServiceName) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.ServiceName) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.ServiceName other) {
                if (other == skynet.boot.tlb.core.Lb.ServiceName.getDefaultInstance()) return this;
                if (!other.getServiceName().isEmpty()) {
                    serviceName_ = other.serviceName_;
                    bitField0_ |= 0x00000001;
                    onChanged();
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 10: {
                                serviceName_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 10
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private java.lang.Object serviceName_ = "";

            /**
             * <code>string ServiceName = 1;</code>
             *
             * @return The serviceName.
             */
            public java.lang.String getServiceName() {
                java.lang.Object ref = serviceName_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    serviceName_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <code>string ServiceName = 1;</code>
             *
             * @return The bytes for serviceName.
             */
            public com.google.protobuf.ByteString
            getServiceNameBytes() {
                java.lang.Object ref = serviceName_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    serviceName_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <code>string ServiceName = 1;</code>
             *
             * @param value The serviceName to set.
             * @return This builder for chaining.
             */
            public Builder setServiceName(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                serviceName_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <code>string ServiceName = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearServiceName() {
                serviceName_ = getDefaultInstance().getServiceName();
                bitField0_ = (bitField0_ & ~0x00000001);
                onChanged();
                return this;
            }

            /**
             * <code>string ServiceName = 1;</code>
             *
             * @param value The bytes for serviceName to set.
             * @return This builder for chaining.
             */
            public Builder setServiceNameBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                serviceName_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.ServiceName)
        }

        // @@protoc_insertion_point(class_scope:GetServer.ServiceName)
        private static final skynet.boot.tlb.core.Lb.ServiceName DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.ServiceName();
        }

        public static skynet.boot.tlb.core.Lb.ServiceName getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ServiceName>
                PARSER = new com.google.protobuf.AbstractParser<ServiceName>() {
            @java.lang.Override
            public ServiceName parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ServiceName> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ServiceName> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServiceName getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface ServerParamOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.ServerParam)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The serverName.
         */
        java.lang.String getServerName();

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The bytes for serverName.
         */
        com.google.protobuf.ByteString
        getServerNameBytes();

        /**
         * <pre>
         * 要获取的服务地址数量
         * </pre>
         *
         * <code>int32 Number = 2;</code>
         *
         * @return The number.
         */
        int getNumber();

        /**
         * <pre>
         * UID用于个性化
         * </pre>
         *
         * <code>string UID = 3;</code>
         *
         * @return The uID.
         */
        java.lang.String getUID();

        /**
         * <pre>
         * UID用于个性化
         * </pre>
         *
         * <code>string UID = 3;</code>
         *
         * @return The bytes for uID.
         */
        com.google.protobuf.ByteString
        getUIDBytes();

        /**
         * <pre>
         * 扩展字段
         * </pre>
         *
         * <code>string Reserved = 4;</code>
         *
         * @return The reserved.
         */
        java.lang.String getReserved();

        /**
         * <pre>
         * 扩展字段
         * </pre>
         *
         * <code>string Reserved = 4;</code>
         *
         * @return The bytes for reserved.
         */
        com.google.protobuf.ByteString
        getReservedBytes();
    }

    /**
     * <pre>
     * 获取服务器信息时的参数
     * </pre>
     * <p>
     * Protobuf type {@code GetServer.ServerParam}
     */
    public static final class ServerParam extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.ServerParam)
            ServerParamOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ServerParam.newBuilder() to construct.
        private ServerParam(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ServerParam() {
            serverName_ = "";
            uID_ = "";
            reserved_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ServerParam();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParam_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParam_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.ServerParam.class, skynet.boot.tlb.core.Lb.ServerParam.Builder.class);
        }

        public static final int SERVERNAME_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile java.lang.Object serverName_ = "";

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The serverName.
         */
        @java.lang.Override
        public java.lang.String getServerName() {
            java.lang.Object ref = serverName_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                serverName_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The bytes for serverName.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getServerNameBytes() {
            java.lang.Object ref = serverName_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                serverName_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int NUMBER_FIELD_NUMBER = 2;
        private int number_ = 0;

        /**
         * <pre>
         * 要获取的服务地址数量
         * </pre>
         *
         * <code>int32 Number = 2;</code>
         *
         * @return The number.
         */
        @java.lang.Override
        public int getNumber() {
            return number_;
        }

        public static final int UID_FIELD_NUMBER = 3;
        @SuppressWarnings("serial")
        private volatile java.lang.Object uID_ = "";

        /**
         * <pre>
         * UID用于个性化
         * </pre>
         *
         * <code>string UID = 3;</code>
         *
         * @return The uID.
         */
        @java.lang.Override
        public java.lang.String getUID() {
            java.lang.Object ref = uID_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                uID_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * UID用于个性化
         * </pre>
         *
         * <code>string UID = 3;</code>
         *
         * @return The bytes for uID.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getUIDBytes() {
            java.lang.Object ref = uID_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                uID_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int RESERVED_FIELD_NUMBER = 4;
        @SuppressWarnings("serial")
        private volatile java.lang.Object reserved_ = "";

        /**
         * <pre>
         * 扩展字段
         * </pre>
         *
         * <code>string Reserved = 4;</code>
         *
         * @return The reserved.
         */
        @java.lang.Override
        public java.lang.String getReserved() {
            java.lang.Object ref = reserved_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                reserved_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * 扩展字段
         * </pre>
         *
         * <code>string Reserved = 4;</code>
         *
         * @return The bytes for reserved.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getReservedBytes() {
            java.lang.Object ref = reserved_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                reserved_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serverName_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, serverName_);
            }
            if (number_ != 0) {
                output.writeInt32(2, number_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(uID_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 3, uID_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(reserved_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 4, reserved_);
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serverName_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, serverName_);
            }
            if (number_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(2, number_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(uID_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, uID_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(reserved_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, reserved_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.ServerParam)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.ServerParam other = (skynet.boot.tlb.core.Lb.ServerParam) obj;

            if (!getServerName()
                    .equals(other.getServerName())) return false;
            if (getNumber()
                    != other.getNumber()) return false;
            if (!getUID()
                    .equals(other.getUID())) return false;
            if (!getReserved()
                    .equals(other.getReserved())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + SERVERNAME_FIELD_NUMBER;
            hash = (53 * hash) + getServerName().hashCode();
            hash = (37 * hash) + NUMBER_FIELD_NUMBER;
            hash = (53 * hash) + getNumber();
            hash = (37 * hash) + UID_FIELD_NUMBER;
            hash = (53 * hash) + getUID().hashCode();
            hash = (37 * hash) + RESERVED_FIELD_NUMBER;
            hash = (53 * hash) + getReserved().hashCode();
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerParam parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.ServerParam prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * <pre>
         * 获取服务器信息时的参数
         * </pre>
         * <p>
         * Protobuf type {@code GetServer.ServerParam}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.ServerParam)
                skynet.boot.tlb.core.Lb.ServerParamOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParam_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParam_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.ServerParam.class, skynet.boot.tlb.core.Lb.ServerParam.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.ServerParam.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                serverName_ = "";
                number_ = 0;
                uID_ = "";
                reserved_ = "";
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParam_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerParam getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.ServerParam.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerParam build() {
                skynet.boot.tlb.core.Lb.ServerParam result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerParam buildPartial() {
                skynet.boot.tlb.core.Lb.ServerParam result = new skynet.boot.tlb.core.Lb.ServerParam(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.Lb.ServerParam result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.serverName_ = serverName_;
                }
                if (((from_bitField0_ & 0x00000002) != 0)) {
                    result.number_ = number_;
                }
                if (((from_bitField0_ & 0x00000004) != 0)) {
                    result.uID_ = uID_;
                }
                if (((from_bitField0_ & 0x00000008) != 0)) {
                    result.reserved_ = reserved_;
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.ServerParam) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.ServerParam) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.ServerParam other) {
                if (other == skynet.boot.tlb.core.Lb.ServerParam.getDefaultInstance()) return this;
                if (!other.getServerName().isEmpty()) {
                    serverName_ = other.serverName_;
                    bitField0_ |= 0x00000001;
                    onChanged();
                }
                if (other.getNumber() != 0) {
                    setNumber(other.getNumber());
                }
                if (!other.getUID().isEmpty()) {
                    uID_ = other.uID_;
                    bitField0_ |= 0x00000004;
                    onChanged();
                }
                if (!other.getReserved().isEmpty()) {
                    reserved_ = other.reserved_;
                    bitField0_ |= 0x00000008;
                    onChanged();
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 10: {
                                serverName_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 10
                            case 16: {
                                number_ = input.readInt32();
                                bitField0_ |= 0x00000002;
                                break;
                            } // case 16
                            case 26: {
                                uID_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000004;
                                break;
                            } // case 26
                            case 34: {
                                reserved_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000008;
                                break;
                            } // case 34
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private java.lang.Object serverName_ = "";

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @return The serverName.
             */
            public java.lang.String getServerName() {
                java.lang.Object ref = serverName_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    serverName_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @return The bytes for serverName.
             */
            public com.google.protobuf.ByteString
            getServerNameBytes() {
                java.lang.Object ref = serverName_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    serverName_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @param value The serverName to set.
             * @return This builder for chaining.
             */
            public Builder setServerName(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                serverName_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearServerName() {
                serverName_ = getDefaultInstance().getServerName();
                bitField0_ = (bitField0_ & ~0x00000001);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @param value The bytes for serverName to set.
             * @return This builder for chaining.
             */
            public Builder setServerNameBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                serverName_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            private int number_;

            /**
             * <pre>
             * 要获取的服务地址数量
             * </pre>
             *
             * <code>int32 Number = 2;</code>
             *
             * @return The number.
             */
            @java.lang.Override
            public int getNumber() {
                return number_;
            }

            /**
             * <pre>
             * 要获取的服务地址数量
             * </pre>
             *
             * <code>int32 Number = 2;</code>
             *
             * @param value The number to set.
             * @return This builder for chaining.
             */
            public Builder setNumber(int value) {

                number_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 要获取的服务地址数量
             * </pre>
             *
             * <code>int32 Number = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearNumber() {
                bitField0_ = (bitField0_ & ~0x00000002);
                number_ = 0;
                onChanged();
                return this;
            }

            private java.lang.Object uID_ = "";

            /**
             * <pre>
             * UID用于个性化
             * </pre>
             *
             * <code>string UID = 3;</code>
             *
             * @return The uID.
             */
            public java.lang.String getUID() {
                java.lang.Object ref = uID_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    uID_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * UID用于个性化
             * </pre>
             *
             * <code>string UID = 3;</code>
             *
             * @return The bytes for uID.
             */
            public com.google.protobuf.ByteString
            getUIDBytes() {
                java.lang.Object ref = uID_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    uID_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * UID用于个性化
             * </pre>
             *
             * <code>string UID = 3;</code>
             *
             * @param value The uID to set.
             * @return This builder for chaining.
             */
            public Builder setUID(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                uID_ = value;
                bitField0_ |= 0x00000004;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * UID用于个性化
             * </pre>
             *
             * <code>string UID = 3;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearUID() {
                uID_ = getDefaultInstance().getUID();
                bitField0_ = (bitField0_ & ~0x00000004);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * UID用于个性化
             * </pre>
             *
             * <code>string UID = 3;</code>
             *
             * @param value The bytes for uID to set.
             * @return This builder for chaining.
             */
            public Builder setUIDBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                uID_ = value;
                bitField0_ |= 0x00000004;
                onChanged();
                return this;
            }

            private java.lang.Object reserved_ = "";

            /**
             * <pre>
             * 扩展字段
             * </pre>
             *
             * <code>string Reserved = 4;</code>
             *
             * @return The reserved.
             */
            public java.lang.String getReserved() {
                java.lang.Object ref = reserved_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    reserved_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * 扩展字段
             * </pre>
             *
             * <code>string Reserved = 4;</code>
             *
             * @return The bytes for reserved.
             */
            public com.google.protobuf.ByteString
            getReservedBytes() {
                java.lang.Object ref = reserved_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    reserved_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * 扩展字段
             * </pre>
             *
             * <code>string Reserved = 4;</code>
             *
             * @param value The reserved to set.
             * @return This builder for chaining.
             */
            public Builder setReserved(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                reserved_ = value;
                bitField0_ |= 0x00000008;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 扩展字段
             * </pre>
             *
             * <code>string Reserved = 4;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearReserved() {
                reserved_ = getDefaultInstance().getReserved();
                bitField0_ = (bitField0_ & ~0x00000008);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 扩展字段
             * </pre>
             *
             * <code>string Reserved = 4;</code>
             *
             * @param value The bytes for reserved to set.
             * @return This builder for chaining.
             */
            public Builder setReservedBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                reserved_ = value;
                bitField0_ |= 0x00000008;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.ServerParam)
        }

        // @@protoc_insertion_point(class_scope:GetServer.ServerParam)
        private static final skynet.boot.tlb.core.Lb.ServerParam DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.ServerParam();
        }

        public static skynet.boot.tlb.core.Lb.ServerParam getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ServerParam>
                PARSER = new com.google.protobuf.AbstractParser<ServerParam>() {
            @java.lang.Override
            public ServerParam parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ServerParam> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ServerParam> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServerParam getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface ServerParamExOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.ServerParamEx)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The serverName.
         */
        java.lang.String getServerName();

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The bytes for serverName.
         */
        com.google.protobuf.ByteString
        getServerNameBytes();

        /**
         * <pre>
         * 要获取的服务地址数量
         * </pre>
         *
         * <code>int32 Number = 2;</code>
         *
         * @return The number.
         */
        int getNumber();

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        int getServiceInfoCount();

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        boolean containsServiceInfo(
                java.lang.String key);

        /**
         * Use {@link #getServiceInfoMap()} instead.
         */
        @java.lang.Deprecated
        java.util.Map<java.lang.String, java.lang.String>
        getServiceInfo();

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        java.util.Map<java.lang.String, java.lang.String>
        getServiceInfoMap();

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        /* nullable */
        java.lang.String getServiceInfoOrDefault(
                java.lang.String key,
                /* nullable */
                java.lang.String defaultValue);

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        java.lang.String getServiceInfoOrThrow(
                java.lang.String key);
    }

    /**
     * Protobuf type {@code GetServer.ServerParamEx}
     */
    public static final class ServerParamEx extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.ServerParamEx)
            ServerParamExOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ServerParamEx.newBuilder() to construct.
        private ServerParamEx(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ServerParamEx() {
            serverName_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ServerParamEx();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParamEx_descriptor;
        }

        @SuppressWarnings({"rawtypes"})
        @java.lang.Override
        protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
                int number) {
            switch (number) {
                case 3:
                    return internalGetServiceInfo();
                default:
                    throw new RuntimeException(
                            "Invalid map field number: " + number);
            }
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParamEx_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.ServerParamEx.class, skynet.boot.tlb.core.Lb.ServerParamEx.Builder.class);
        }

        public static final int SERVERNAME_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile java.lang.Object serverName_ = "";

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The serverName.
         */
        @java.lang.Override
        public java.lang.String getServerName() {
            java.lang.Object ref = serverName_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                serverName_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * 要获取的服务名称
         * </pre>
         *
         * <code>string ServerName = 1;</code>
         *
         * @return The bytes for serverName.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getServerNameBytes() {
            java.lang.Object ref = serverName_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                serverName_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int NUMBER_FIELD_NUMBER = 2;
        private int number_ = 0;

        /**
         * <pre>
         * 要获取的服务地址数量
         * </pre>
         *
         * <code>int32 Number = 2;</code>
         *
         * @return The number.
         */
        @java.lang.Override
        public int getNumber() {
            return number_;
        }

        public static final int SERVICEINFO_FIELD_NUMBER = 3;

        private static final class ServiceInfoDefaultEntryHolder {
            static final com.google.protobuf.MapEntry<
                    java.lang.String, java.lang.String> defaultEntry =
                    com.google.protobuf.MapEntry
                            .<java.lang.String, java.lang.String>newDefaultInstance(
                                    skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParamEx_ServiceInfoEntry_descriptor,
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "",
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "");
        }

        @SuppressWarnings("serial")
        private com.google.protobuf.MapField<
                java.lang.String, java.lang.String> serviceInfo_;

        private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetServiceInfo() {
            if (serviceInfo_ == null) {
                return com.google.protobuf.MapField.emptyMapField(
                        ServiceInfoDefaultEntryHolder.defaultEntry);
            }
            return serviceInfo_;
        }

        public int getServiceInfoCount() {
            return internalGetServiceInfo().getMap().size();
        }

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        @java.lang.Override
        public boolean containsServiceInfo(
                java.lang.String key) {
            if (key == null) {
                throw new NullPointerException("map key");
            }
            return internalGetServiceInfo().getMap().containsKey(key);
        }

        /**
         * Use {@link #getServiceInfoMap()} instead.
         */
        @java.lang.Override
        @java.lang.Deprecated
        public java.util.Map<java.lang.String, java.lang.String> getServiceInfo() {
            return getServiceInfoMap();
        }

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        @java.lang.Override
        public java.util.Map<java.lang.String, java.lang.String> getServiceInfoMap() {
            return internalGetServiceInfo().getMap();
        }

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        @java.lang.Override
        public /* nullable */
        java.lang.String getServiceInfoOrDefault(
                java.lang.String key,
                /* nullable */
                java.lang.String defaultValue) {
            if (key == null) {
                throw new NullPointerException("map key");
            }
            java.util.Map<java.lang.String, java.lang.String> map =
                    internalGetServiceInfo().getMap();
            return map.containsKey(key) ? map.get(key) : defaultValue;
        }

        /**
         * <pre>
         * Map中基础信息如下：
         * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
         * "reqType"，请求类型
         *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
         *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
         *      空或"common"时，代表普通获取最佳服务请求。
         * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
         * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
         * </pre>
         *
         * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
         */
        @java.lang.Override
        public java.lang.String getServiceInfoOrThrow(
                java.lang.String key) {
            if (key == null) {
                throw new NullPointerException("map key");
            }
            java.util.Map<java.lang.String, java.lang.String> map =
                    internalGetServiceInfo().getMap();
            if (!map.containsKey(key)) {
                throw new java.lang.IllegalArgumentException();
            }
            return map.get(key);
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serverName_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, serverName_);
            }
            if (number_ != 0) {
                output.writeInt32(2, number_);
            }
            com.google.protobuf.GeneratedMessageV3
                    .serializeStringMapTo(
                            output,
                            internalGetServiceInfo(),
                            ServiceInfoDefaultEntryHolder.defaultEntry,
                            3);
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serverName_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, serverName_);
            }
            if (number_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(2, number_);
            }
            for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
                    : internalGetServiceInfo().getMap().entrySet()) {
                com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
                        serviceInfo__ = ServiceInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
                        .setKey(entry.getKey())
                        .setValue(entry.getValue())
                        .build();
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(3, serviceInfo__);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.ServerParamEx)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.ServerParamEx other = (skynet.boot.tlb.core.Lb.ServerParamEx) obj;

            if (!getServerName()
                    .equals(other.getServerName())) return false;
            if (getNumber()
                    != other.getNumber()) return false;
            if (!internalGetServiceInfo().equals(
                    other.internalGetServiceInfo())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + SERVERNAME_FIELD_NUMBER;
            hash = (53 * hash) + getServerName().hashCode();
            hash = (37 * hash) + NUMBER_FIELD_NUMBER;
            hash = (53 * hash) + getNumber();
            if (!internalGetServiceInfo().getMap().isEmpty()) {
                hash = (37 * hash) + SERVICEINFO_FIELD_NUMBER;
                hash = (53 * hash) + internalGetServiceInfo().hashCode();
            }
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.ServerParamEx prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code GetServer.ServerParamEx}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.ServerParamEx)
                skynet.boot.tlb.core.Lb.ServerParamExOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParamEx_descriptor;
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
                    int number) {
                switch (number) {
                    case 3:
                        return internalGetServiceInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapFieldReflectionAccessor internalGetMutableMapFieldReflection(
                    int number) {
                switch (number) {
                    case 3:
                        return internalGetMutableServiceInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParamEx_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.ServerParamEx.class, skynet.boot.tlb.core.Lb.ServerParamEx.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.ServerParamEx.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                serverName_ = "";
                number_ = 0;
                internalGetMutableServiceInfo().clear();
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerParamEx_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerParamEx getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.ServerParamEx.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerParamEx build() {
                skynet.boot.tlb.core.Lb.ServerParamEx result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerParamEx buildPartial() {
                skynet.boot.tlb.core.Lb.ServerParamEx result = new skynet.boot.tlb.core.Lb.ServerParamEx(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.Lb.ServerParamEx result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.serverName_ = serverName_;
                }
                if (((from_bitField0_ & 0x00000002) != 0)) {
                    result.number_ = number_;
                }
                if (((from_bitField0_ & 0x00000004) != 0)) {
                    result.serviceInfo_ = internalGetServiceInfo();
                    result.serviceInfo_.makeImmutable();
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.ServerParamEx) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.ServerParamEx) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.ServerParamEx other) {
                if (other == skynet.boot.tlb.core.Lb.ServerParamEx.getDefaultInstance()) return this;
                if (!other.getServerName().isEmpty()) {
                    serverName_ = other.serverName_;
                    bitField0_ |= 0x00000001;
                    onChanged();
                }
                if (other.getNumber() != 0) {
                    setNumber(other.getNumber());
                }
                internalGetMutableServiceInfo().mergeFrom(
                        other.internalGetServiceInfo());
                bitField0_ |= 0x00000004;
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 10: {
                                serverName_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 10
                            case 16: {
                                number_ = input.readInt32();
                                bitField0_ |= 0x00000002;
                                break;
                            } // case 16
                            case 26: {
                                com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
                                        serviceInfo__ = input.readMessage(
                                        ServiceInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
                                internalGetMutableServiceInfo().getMutableMap().put(
                                        serviceInfo__.getKey(), serviceInfo__.getValue());
                                bitField0_ |= 0x00000004;
                                break;
                            } // case 26
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private java.lang.Object serverName_ = "";

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @return The serverName.
             */
            public java.lang.String getServerName() {
                java.lang.Object ref = serverName_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    serverName_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @return The bytes for serverName.
             */
            public com.google.protobuf.ByteString
            getServerNameBytes() {
                java.lang.Object ref = serverName_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    serverName_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @param value The serverName to set.
             * @return This builder for chaining.
             */
            public Builder setServerName(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                serverName_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearServerName() {
                serverName_ = getDefaultInstance().getServerName();
                bitField0_ = (bitField0_ & ~0x00000001);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 要获取的服务名称
             * </pre>
             *
             * <code>string ServerName = 1;</code>
             *
             * @param value The bytes for serverName to set.
             * @return This builder for chaining.
             */
            public Builder setServerNameBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                serverName_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            private int number_;

            /**
             * <pre>
             * 要获取的服务地址数量
             * </pre>
             *
             * <code>int32 Number = 2;</code>
             *
             * @return The number.
             */
            @java.lang.Override
            public int getNumber() {
                return number_;
            }

            /**
             * <pre>
             * 要获取的服务地址数量
             * </pre>
             *
             * <code>int32 Number = 2;</code>
             *
             * @param value The number to set.
             * @return This builder for chaining.
             */
            public Builder setNumber(int value) {

                number_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 要获取的服务地址数量
             * </pre>
             *
             * <code>int32 Number = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearNumber() {
                bitField0_ = (bitField0_ & ~0x00000002);
                number_ = 0;
                onChanged();
                return this;
            }

            private com.google.protobuf.MapField<
                    java.lang.String, java.lang.String> serviceInfo_;

            private com.google.protobuf.MapField<java.lang.String, java.lang.String>
            internalGetServiceInfo() {
                if (serviceInfo_ == null) {
                    return com.google.protobuf.MapField.emptyMapField(
                            ServiceInfoDefaultEntryHolder.defaultEntry);
                }
                return serviceInfo_;
            }

            private com.google.protobuf.MapField<java.lang.String, java.lang.String>
            internalGetMutableServiceInfo() {
                if (serviceInfo_ == null) {
                    serviceInfo_ = com.google.protobuf.MapField.newMapField(
                            ServiceInfoDefaultEntryHolder.defaultEntry);
                }
                if (!serviceInfo_.isMutable()) {
                    serviceInfo_ = serviceInfo_.copy();
                }
                bitField0_ |= 0x00000004;
                onChanged();
                return serviceInfo_;
            }

            public int getServiceInfoCount() {
                return internalGetServiceInfo().getMap().size();
            }

            /**
             * <pre>
             * Map中基础信息如下：
             * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
             * "reqType"，请求类型
             *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
             *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
             *      空或"common"时，代表普通获取最佳服务请求。
             * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
             * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
             * </pre>
             *
             * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
             */
            @java.lang.Override
            public boolean containsServiceInfo(
                    java.lang.String key) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                return internalGetServiceInfo().getMap().containsKey(key);
            }

            /**
             * Use {@link #getServiceInfoMap()} instead.
             */
            @java.lang.Override
            @java.lang.Deprecated
            public java.util.Map<java.lang.String, java.lang.String> getServiceInfo() {
                return getServiceInfoMap();
            }

            /**
             * <pre>
             * Map中基础信息如下：
             * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
             * "reqType"，请求类型
             *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
             *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
             *      空或"common"时，代表普通获取最佳服务请求。
             * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
             * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
             * </pre>
             *
             * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
             */
            @java.lang.Override
            public java.util.Map<java.lang.String, java.lang.String> getServiceInfoMap() {
                return internalGetServiceInfo().getMap();
            }

            /**
             * <pre>
             * Map中基础信息如下：
             * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
             * "reqType"，请求类型
             *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
             *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
             *      空或"common"时，代表普通获取最佳服务请求。
             * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
             * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
             * </pre>
             *
             * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
             */
            @java.lang.Override
            public /* nullable */
            java.lang.String getServiceInfoOrDefault(
                    java.lang.String key,
                    /* nullable */
                    java.lang.String defaultValue) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                java.util.Map<java.lang.String, java.lang.String> map =
                        internalGetServiceInfo().getMap();
                return map.containsKey(key) ? map.get(key) : defaultValue;
            }

            /**
             * <pre>
             * Map中基础信息如下：
             * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
             * "reqType"，请求类型
             *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
             *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
             *      空或"common"时，代表普通获取最佳服务请求。
             * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
             * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
             * </pre>
             *
             * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
             */
            @java.lang.Override
            public java.lang.String getServiceInfoOrThrow(
                    java.lang.String key) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                java.util.Map<java.lang.String, java.lang.String> map =
                        internalGetServiceInfo().getMap();
                if (!map.containsKey(key)) {
                    throw new java.lang.IllegalArgumentException();
                }
                return map.get(key);
            }

            public Builder clearServiceInfo() {
                bitField0_ = (bitField0_ & ~0x00000004);
                internalGetMutableServiceInfo().getMutableMap()
                        .clear();
                return this;
            }

            /**
             * <pre>
             * Map中基础信息如下：
             * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
             * "reqType"，请求类型
             *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
             *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
             *      空或"common"时，代表普通获取最佳服务请求。
             * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
             * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
             * </pre>
             *
             * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
             */
            public Builder removeServiceInfo(
                    java.lang.String key) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                internalGetMutableServiceInfo().getMutableMap()
                        .remove(key);
                return this;
            }

            /**
             * Use alternate mutation accessors instead.
             */
            @java.lang.Deprecated
            public java.util.Map<java.lang.String, java.lang.String>
            getMutableServiceInfo() {
                bitField0_ |= 0x00000004;
                return internalGetMutableServiceInfo().getMutableMap();
            }

            /**
             * <pre>
             * Map中基础信息如下：
             * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
             * "reqType"，请求类型
             *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
             *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
             *      空或"common"时，代表普通获取最佳服务请求。
             * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
             * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
             * </pre>
             *
             * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
             */
            public Builder putServiceInfo(
                    java.lang.String key,
                    java.lang.String value) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                if (value == null) {
                    throw new NullPointerException("map value");
                }
                internalGetMutableServiceInfo().getMutableMap()
                        .put(key, value);
                bitField0_ |= 0x00000004;
                return this;
            }

            /**
             * <pre>
             * Map中基础信息如下：
             * "bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
             * "reqType"，请求类型
             *      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
             *      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
             *      空或"common"时，代表普通获取最佳服务请求。
             * "resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
             * "tag"  A&amp;&amp;B   A||B   !(A&amp;&amp;B)
             * </pre>
             *
             * <code>map&lt;string, string&gt; ServiceInfo = 3;</code>
             */
            public Builder putAllServiceInfo(
                    java.util.Map<java.lang.String, java.lang.String> values) {
                internalGetMutableServiceInfo().getMutableMap()
                        .putAll(values);
                bitField0_ |= 0x00000004;
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.ServerParamEx)
        }

        // @@protoc_insertion_point(class_scope:GetServer.ServerParamEx)
        private static final skynet.boot.tlb.core.Lb.ServerParamEx DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.ServerParamEx();
        }

        public static skynet.boot.tlb.core.Lb.ServerParamEx getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ServerParamEx>
                PARSER = new com.google.protobuf.AbstractParser<ServerParamEx>() {
            @java.lang.Override
            public ServerParamEx parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ServerParamEx> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ServerParamEx> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServerParamEx getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface ServerAddressOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.ServerAddress)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string ServerIP = 1;</code>
         *
         * @return The serverIP.
         */
        java.lang.String getServerIP();

        /**
         * <code>string ServerIP = 1;</code>
         *
         * @return The bytes for serverIP.
         */
        com.google.protobuf.ByteString
        getServerIPBytes();

        /**
         * <code>int32 ServerPort = 2;</code>
         *
         * @return The serverPort.
         */
        int getServerPort();
    }

    /**
     * <pre>
     * 单个服务的IP端口信息
     * </pre>
     * <p>
     * Protobuf type {@code GetServer.ServerAddress}
     */
    public static final class ServerAddress extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.ServerAddress)
            ServerAddressOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ServerAddress.newBuilder() to construct.
        private ServerAddress(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ServerAddress() {
            serverIP_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ServerAddress();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddress_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddress_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.ServerAddress.class, skynet.boot.tlb.core.Lb.ServerAddress.Builder.class);
        }

        public static final int SERVERIP_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile java.lang.Object serverIP_ = "";

        /**
         * <code>string ServerIP = 1;</code>
         *
         * @return The serverIP.
         */
        @java.lang.Override
        public java.lang.String getServerIP() {
            java.lang.Object ref = serverIP_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                serverIP_ = s;
                return s;
            }
        }

        /**
         * <code>string ServerIP = 1;</code>
         *
         * @return The bytes for serverIP.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getServerIPBytes() {
            java.lang.Object ref = serverIP_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                serverIP_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int SERVERPORT_FIELD_NUMBER = 2;
        private int serverPort_ = 0;

        /**
         * <code>int32 ServerPort = 2;</code>
         *
         * @return The serverPort.
         */
        @java.lang.Override
        public int getServerPort() {
            return serverPort_;
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serverIP_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, serverIP_);
            }
            if (serverPort_ != 0) {
                output.writeInt32(2, serverPort_);
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serverIP_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, serverIP_);
            }
            if (serverPort_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(2, serverPort_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.ServerAddress)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.ServerAddress other = (skynet.boot.tlb.core.Lb.ServerAddress) obj;

            if (!getServerIP()
                    .equals(other.getServerIP())) return false;
            if (getServerPort()
                    != other.getServerPort()) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + SERVERIP_FIELD_NUMBER;
            hash = (53 * hash) + getServerIP().hashCode();
            hash = (37 * hash) + SERVERPORT_FIELD_NUMBER;
            hash = (53 * hash) + getServerPort();
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.ServerAddress prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * <pre>
         * 单个服务的IP端口信息
         * </pre>
         * <p>
         * Protobuf type {@code GetServer.ServerAddress}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.ServerAddress)
                skynet.boot.tlb.core.Lb.ServerAddressOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddress_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddress_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.ServerAddress.class, skynet.boot.tlb.core.Lb.ServerAddress.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.ServerAddress.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                serverIP_ = "";
                serverPort_ = 0;
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddress_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerAddress getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.ServerAddress.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerAddress build() {
                skynet.boot.tlb.core.Lb.ServerAddress result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerAddress buildPartial() {
                skynet.boot.tlb.core.Lb.ServerAddress result = new skynet.boot.tlb.core.Lb.ServerAddress(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.Lb.ServerAddress result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.serverIP_ = serverIP_;
                }
                if (((from_bitField0_ & 0x00000002) != 0)) {
                    result.serverPort_ = serverPort_;
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.ServerAddress) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.ServerAddress) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.ServerAddress other) {
                if (other == skynet.boot.tlb.core.Lb.ServerAddress.getDefaultInstance()) return this;
                if (!other.getServerIP().isEmpty()) {
                    serverIP_ = other.serverIP_;
                    bitField0_ |= 0x00000001;
                    onChanged();
                }
                if (other.getServerPort() != 0) {
                    setServerPort(other.getServerPort());
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 10: {
                                serverIP_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 10
                            case 16: {
                                serverPort_ = input.readInt32();
                                bitField0_ |= 0x00000002;
                                break;
                            } // case 16
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private java.lang.Object serverIP_ = "";

            /**
             * <code>string ServerIP = 1;</code>
             *
             * @return The serverIP.
             */
            public java.lang.String getServerIP() {
                java.lang.Object ref = serverIP_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    serverIP_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <code>string ServerIP = 1;</code>
             *
             * @return The bytes for serverIP.
             */
            public com.google.protobuf.ByteString
            getServerIPBytes() {
                java.lang.Object ref = serverIP_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    serverIP_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <code>string ServerIP = 1;</code>
             *
             * @param value The serverIP to set.
             * @return This builder for chaining.
             */
            public Builder setServerIP(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                serverIP_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <code>string ServerIP = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearServerIP() {
                serverIP_ = getDefaultInstance().getServerIP();
                bitField0_ = (bitField0_ & ~0x00000001);
                onChanged();
                return this;
            }

            /**
             * <code>string ServerIP = 1;</code>
             *
             * @param value The bytes for serverIP to set.
             * @return This builder for chaining.
             */
            public Builder setServerIPBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                serverIP_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            private int serverPort_;

            /**
             * <code>int32 ServerPort = 2;</code>
             *
             * @return The serverPort.
             */
            @java.lang.Override
            public int getServerPort() {
                return serverPort_;
            }

            /**
             * <code>int32 ServerPort = 2;</code>
             *
             * @param value The serverPort to set.
             * @return This builder for chaining.
             */
            public Builder setServerPort(int value) {

                serverPort_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            /**
             * <code>int32 ServerPort = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearServerPort() {
                bitField0_ = (bitField0_ & ~0x00000002);
                serverPort_ = 0;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.ServerAddress)
        }

        // @@protoc_insertion_point(class_scope:GetServer.ServerAddress)
        private static final skynet.boot.tlb.core.Lb.ServerAddress DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.ServerAddress();
        }

        public static skynet.boot.tlb.core.Lb.ServerAddress getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ServerAddress>
                PARSER = new com.google.protobuf.AbstractParser<ServerAddress>() {
            @java.lang.Override
            public ServerAddress parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ServerAddress> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ServerAddress> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServerAddress getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface ServerAddressesOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.ServerAddresses)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * GetBestServer接口调用结果
         * </pre>
         *
         * <code>int32 ErrorCode = 1;</code>
         *
         * @return The errorCode.
         */
        int getErrorCode();

        /**
         * <pre>
         * 错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The errorInfo.
         */
        java.lang.String getErrorInfo();

        /**
         * <pre>
         * 错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The bytes for errorInfo.
         */
        com.google.protobuf.ByteString
        getErrorInfoBytes();

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        java.util.List<skynet.boot.tlb.core.Lb.ServerAddress>
        getAddressListList();

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        skynet.boot.tlb.core.Lb.ServerAddress getAddressList(int index);

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        int getAddressListCount();

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        java.util.List<? extends skynet.boot.tlb.core.Lb.ServerAddressOrBuilder>
        getAddressListOrBuilderList();

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        skynet.boot.tlb.core.Lb.ServerAddressOrBuilder getAddressListOrBuilder(
                int index);
    }

    /**
     * <pre>
     * 多个服务的IP端口信息
     * </pre>
     * <p>
     * Protobuf type {@code GetServer.ServerAddresses}
     */
    public static final class ServerAddresses extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.ServerAddresses)
            ServerAddressesOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ServerAddresses.newBuilder() to construct.
        private ServerAddresses(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ServerAddresses() {
            errorInfo_ = "";
            addressList_ = java.util.Collections.emptyList();
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ServerAddresses();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddresses_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddresses_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.ServerAddresses.class, skynet.boot.tlb.core.Lb.ServerAddresses.Builder.class);
        }

        public static final int ERRORCODE_FIELD_NUMBER = 1;
        private int errorCode_ = 0;

        /**
         * <pre>
         * GetBestServer接口调用结果
         * </pre>
         *
         * <code>int32 ErrorCode = 1;</code>
         *
         * @return The errorCode.
         */
        @java.lang.Override
        public int getErrorCode() {
            return errorCode_;
        }

        public static final int ERRORINFO_FIELD_NUMBER = 2;
        @SuppressWarnings("serial")
        private volatile java.lang.Object errorInfo_ = "";

        /**
         * <pre>
         * 错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The errorInfo.
         */
        @java.lang.Override
        public java.lang.String getErrorInfo() {
            java.lang.Object ref = errorInfo_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                errorInfo_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * 错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The bytes for errorInfo.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getErrorInfoBytes() {
            java.lang.Object ref = errorInfo_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                errorInfo_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int ADDRESSLIST_FIELD_NUMBER = 3;
        @SuppressWarnings("serial")
        private java.util.List<skynet.boot.tlb.core.Lb.ServerAddress> addressList_;

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        @java.lang.Override
        public java.util.List<skynet.boot.tlb.core.Lb.ServerAddress> getAddressListList() {
            return addressList_;
        }

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        @java.lang.Override
        public java.util.List<? extends skynet.boot.tlb.core.Lb.ServerAddressOrBuilder>
        getAddressListOrBuilderList() {
            return addressList_;
        }

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        @java.lang.Override
        public int getAddressListCount() {
            return addressList_.size();
        }

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServerAddress getAddressList(int index) {
            return addressList_.get(index);
        }

        /**
         * <pre>
         * 获取到的服务器信息列表
         * </pre>
         *
         * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
         */
        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServerAddressOrBuilder getAddressListOrBuilder(
                int index) {
            return addressList_.get(index);
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (errorCode_ != 0) {
                output.writeInt32(1, errorCode_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(errorInfo_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorInfo_);
            }
            for (int i = 0; i < addressList_.size(); i++) {
                output.writeMessage(3, addressList_.get(i));
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (errorCode_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(1, errorCode_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(errorInfo_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorInfo_);
            }
            for (int i = 0; i < addressList_.size(); i++) {
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(3, addressList_.get(i));
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.ServerAddresses)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.ServerAddresses other = (skynet.boot.tlb.core.Lb.ServerAddresses) obj;

            if (getErrorCode()
                    != other.getErrorCode()) return false;
            if (!getErrorInfo()
                    .equals(other.getErrorInfo())) return false;
            if (!getAddressListList()
                    .equals(other.getAddressListList())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
            hash = (53 * hash) + getErrorCode();
            hash = (37 * hash) + ERRORINFO_FIELD_NUMBER;
            hash = (53 * hash) + getErrorInfo().hashCode();
            if (getAddressListCount() > 0) {
                hash = (37 * hash) + ADDRESSLIST_FIELD_NUMBER;
                hash = (53 * hash) + getAddressListList().hashCode();
            }
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.ServerAddresses prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * <pre>
         * 多个服务的IP端口信息
         * </pre>
         * <p>
         * Protobuf type {@code GetServer.ServerAddresses}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.ServerAddresses)
                skynet.boot.tlb.core.Lb.ServerAddressesOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddresses_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddresses_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.ServerAddresses.class, skynet.boot.tlb.core.Lb.ServerAddresses.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.ServerAddresses.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                errorCode_ = 0;
                errorInfo_ = "";
                if (addressListBuilder_ == null) {
                    addressList_ = java.util.Collections.emptyList();
                } else {
                    addressList_ = null;
                    addressListBuilder_.clear();
                }
                bitField0_ = (bitField0_ & ~0x00000004);
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServerAddresses_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerAddresses getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.ServerAddresses.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerAddresses build() {
                skynet.boot.tlb.core.Lb.ServerAddresses result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServerAddresses buildPartial() {
                skynet.boot.tlb.core.Lb.ServerAddresses result = new skynet.boot.tlb.core.Lb.ServerAddresses(this);
                buildPartialRepeatedFields(result);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartialRepeatedFields(skynet.boot.tlb.core.Lb.ServerAddresses result) {
                if (addressListBuilder_ == null) {
                    if (((bitField0_ & 0x00000004) != 0)) {
                        addressList_ = java.util.Collections.unmodifiableList(addressList_);
                        bitField0_ = (bitField0_ & ~0x00000004);
                    }
                    result.addressList_ = addressList_;
                } else {
                    result.addressList_ = addressListBuilder_.build();
                }
            }

            private void buildPartial0(skynet.boot.tlb.core.Lb.ServerAddresses result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.errorCode_ = errorCode_;
                }
                if (((from_bitField0_ & 0x00000002) != 0)) {
                    result.errorInfo_ = errorInfo_;
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.ServerAddresses) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.ServerAddresses) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.ServerAddresses other) {
                if (other == skynet.boot.tlb.core.Lb.ServerAddresses.getDefaultInstance()) return this;
                if (other.getErrorCode() != 0) {
                    setErrorCode(other.getErrorCode());
                }
                if (!other.getErrorInfo().isEmpty()) {
                    errorInfo_ = other.errorInfo_;
                    bitField0_ |= 0x00000002;
                    onChanged();
                }
                if (addressListBuilder_ == null) {
                    if (!other.addressList_.isEmpty()) {
                        if (addressList_.isEmpty()) {
                            addressList_ = other.addressList_;
                            bitField0_ = (bitField0_ & ~0x00000004);
                        } else {
                            ensureAddressListIsMutable();
                            addressList_.addAll(other.addressList_);
                        }
                        onChanged();
                    }
                } else {
                    if (!other.addressList_.isEmpty()) {
                        if (addressListBuilder_.isEmpty()) {
                            addressListBuilder_.dispose();
                            addressListBuilder_ = null;
                            addressList_ = other.addressList_;
                            bitField0_ = (bitField0_ & ~0x00000004);
                            addressListBuilder_ =
                                    com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                                            getAddressListFieldBuilder() : null;
                        } else {
                            addressListBuilder_.addAllMessages(other.addressList_);
                        }
                    }
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 8: {
                                errorCode_ = input.readInt32();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 8
                            case 18: {
                                errorInfo_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000002;
                                break;
                            } // case 18
                            case 26: {
                                skynet.boot.tlb.core.Lb.ServerAddress m =
                                        input.readMessage(
                                                skynet.boot.tlb.core.Lb.ServerAddress.parser(),
                                                extensionRegistry);
                                if (addressListBuilder_ == null) {
                                    ensureAddressListIsMutable();
                                    addressList_.add(m);
                                } else {
                                    addressListBuilder_.addMessage(m);
                                }
                                break;
                            } // case 26
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private int errorCode_;

            /**
             * <pre>
             * GetBestServer接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @return The errorCode.
             */
            @java.lang.Override
            public int getErrorCode() {
                return errorCode_;
            }

            /**
             * <pre>
             * GetBestServer接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @param value The errorCode to set.
             * @return This builder for chaining.
             */
            public Builder setErrorCode(int value) {

                errorCode_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * GetBestServer接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearErrorCode() {
                bitField0_ = (bitField0_ & ~0x00000001);
                errorCode_ = 0;
                onChanged();
                return this;
            }

            private java.lang.Object errorInfo_ = "";

            /**
             * <pre>
             * 错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return The errorInfo.
             */
            public java.lang.String getErrorInfo() {
                java.lang.Object ref = errorInfo_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    errorInfo_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * 错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return The bytes for errorInfo.
             */
            public com.google.protobuf.ByteString
            getErrorInfoBytes() {
                java.lang.Object ref = errorInfo_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    errorInfo_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * 错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @param value The errorInfo to set.
             * @return This builder for chaining.
             */
            public Builder setErrorInfo(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                errorInfo_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearErrorInfo() {
                errorInfo_ = getDefaultInstance().getErrorInfo();
                bitField0_ = (bitField0_ & ~0x00000002);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @param value The bytes for errorInfo to set.
             * @return This builder for chaining.
             */
            public Builder setErrorInfoBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                errorInfo_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            private java.util.List<skynet.boot.tlb.core.Lb.ServerAddress> addressList_ =
                    java.util.Collections.emptyList();

            private void ensureAddressListIsMutable() {
                if (!((bitField0_ & 0x00000004) != 0)) {
                    addressList_ = new java.util.ArrayList<skynet.boot.tlb.core.Lb.ServerAddress>(addressList_);
                    bitField0_ |= 0x00000004;
                }
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    skynet.boot.tlb.core.Lb.ServerAddress, skynet.boot.tlb.core.Lb.ServerAddress.Builder, skynet.boot.tlb.core.Lb.ServerAddressOrBuilder> addressListBuilder_;

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public java.util.List<skynet.boot.tlb.core.Lb.ServerAddress> getAddressListList() {
                if (addressListBuilder_ == null) {
                    return java.util.Collections.unmodifiableList(addressList_);
                } else {
                    return addressListBuilder_.getMessageList();
                }
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public int getAddressListCount() {
                if (addressListBuilder_ == null) {
                    return addressList_.size();
                } else {
                    return addressListBuilder_.getCount();
                }
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public skynet.boot.tlb.core.Lb.ServerAddress getAddressList(int index) {
                if (addressListBuilder_ == null) {
                    return addressList_.get(index);
                } else {
                    return addressListBuilder_.getMessage(index);
                }
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder setAddressList(
                    int index, skynet.boot.tlb.core.Lb.ServerAddress value) {
                if (addressListBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureAddressListIsMutable();
                    addressList_.set(index, value);
                    onChanged();
                } else {
                    addressListBuilder_.setMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder setAddressList(
                    int index, skynet.boot.tlb.core.Lb.ServerAddress.Builder builderForValue) {
                if (addressListBuilder_ == null) {
                    ensureAddressListIsMutable();
                    addressList_.set(index, builderForValue.build());
                    onChanged();
                } else {
                    addressListBuilder_.setMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder addAddressList(skynet.boot.tlb.core.Lb.ServerAddress value) {
                if (addressListBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureAddressListIsMutable();
                    addressList_.add(value);
                    onChanged();
                } else {
                    addressListBuilder_.addMessage(value);
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder addAddressList(
                    int index, skynet.boot.tlb.core.Lb.ServerAddress value) {
                if (addressListBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureAddressListIsMutable();
                    addressList_.add(index, value);
                    onChanged();
                } else {
                    addressListBuilder_.addMessage(index, value);
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder addAddressList(
                    skynet.boot.tlb.core.Lb.ServerAddress.Builder builderForValue) {
                if (addressListBuilder_ == null) {
                    ensureAddressListIsMutable();
                    addressList_.add(builderForValue.build());
                    onChanged();
                } else {
                    addressListBuilder_.addMessage(builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder addAddressList(
                    int index, skynet.boot.tlb.core.Lb.ServerAddress.Builder builderForValue) {
                if (addressListBuilder_ == null) {
                    ensureAddressListIsMutable();
                    addressList_.add(index, builderForValue.build());
                    onChanged();
                } else {
                    addressListBuilder_.addMessage(index, builderForValue.build());
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder addAllAddressList(
                    java.lang.Iterable<? extends skynet.boot.tlb.core.Lb.ServerAddress> values) {
                if (addressListBuilder_ == null) {
                    ensureAddressListIsMutable();
                    com.google.protobuf.AbstractMessageLite.Builder.addAll(
                            values, addressList_);
                    onChanged();
                } else {
                    addressListBuilder_.addAllMessages(values);
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder clearAddressList() {
                if (addressListBuilder_ == null) {
                    addressList_ = java.util.Collections.emptyList();
                    bitField0_ = (bitField0_ & ~0x00000004);
                    onChanged();
                } else {
                    addressListBuilder_.clear();
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public Builder removeAddressList(int index) {
                if (addressListBuilder_ == null) {
                    ensureAddressListIsMutable();
                    addressList_.remove(index);
                    onChanged();
                } else {
                    addressListBuilder_.remove(index);
                }
                return this;
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public skynet.boot.tlb.core.Lb.ServerAddress.Builder getAddressListBuilder(
                    int index) {
                return getAddressListFieldBuilder().getBuilder(index);
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public skynet.boot.tlb.core.Lb.ServerAddressOrBuilder getAddressListOrBuilder(
                    int index) {
                if (addressListBuilder_ == null) {
                    return addressList_.get(index);
                } else {
                    return addressListBuilder_.getMessageOrBuilder(index);
                }
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public java.util.List<? extends skynet.boot.tlb.core.Lb.ServerAddressOrBuilder>
            getAddressListOrBuilderList() {
                if (addressListBuilder_ != null) {
                    return addressListBuilder_.getMessageOrBuilderList();
                } else {
                    return java.util.Collections.unmodifiableList(addressList_);
                }
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public skynet.boot.tlb.core.Lb.ServerAddress.Builder addAddressListBuilder() {
                return getAddressListFieldBuilder().addBuilder(
                        skynet.boot.tlb.core.Lb.ServerAddress.getDefaultInstance());
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public skynet.boot.tlb.core.Lb.ServerAddress.Builder addAddressListBuilder(
                    int index) {
                return getAddressListFieldBuilder().addBuilder(
                        index, skynet.boot.tlb.core.Lb.ServerAddress.getDefaultInstance());
            }

            /**
             * <pre>
             * 获取到的服务器信息列表
             * </pre>
             *
             * <code>repeated .GetServer.ServerAddress AddressList = 3;</code>
             */
            public java.util.List<skynet.boot.tlb.core.Lb.ServerAddress.Builder>
            getAddressListBuilderList() {
                return getAddressListFieldBuilder().getBuilderList();
            }

            private com.google.protobuf.RepeatedFieldBuilderV3<
                    skynet.boot.tlb.core.Lb.ServerAddress, skynet.boot.tlb.core.Lb.ServerAddress.Builder, skynet.boot.tlb.core.Lb.ServerAddressOrBuilder>
            getAddressListFieldBuilder() {
                if (addressListBuilder_ == null) {
                    addressListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                            skynet.boot.tlb.core.Lb.ServerAddress, skynet.boot.tlb.core.Lb.ServerAddress.Builder, skynet.boot.tlb.core.Lb.ServerAddressOrBuilder>(
                            addressList_,
                            ((bitField0_ & 0x00000004) != 0),
                            getParentForChildren(),
                            isClean());
                    addressList_ = null;
                }
                return addressListBuilder_;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.ServerAddresses)
        }

        // @@protoc_insertion_point(class_scope:GetServer.ServerAddresses)
        private static final skynet.boot.tlb.core.Lb.ServerAddresses DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.ServerAddresses();
        }

        public static skynet.boot.tlb.core.Lb.ServerAddresses getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ServerAddresses>
                PARSER = new com.google.protobuf.AbstractParser<ServerAddresses>() {
            @java.lang.Override
            public ServerAddresses parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ServerAddresses> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ServerAddresses> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServerAddresses getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface ServiceInfoListOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.ServiceInfoList)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * GetServiceInfo接口调用结果
         * </pre>
         *
         * <code>int32 ErrorCode = 1;</code>
         *
         * @return The errorCode.
         */
        int getErrorCode();

        /**
         * <pre>
         * 调用错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The errorInfo.
         */
        java.lang.String getErrorInfo();

        /**
         * <pre>
         * 调用错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The bytes for errorInfo.
         */
        com.google.protobuf.ByteString
        getErrorInfoBytes();

        /**
         * <pre>
         * JSON格式信息示例
         * {
         *    "asr_hf": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":150,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":33.3
         *        }
         *    ],
         *
         *    "tts_xtts": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":99.9,
         *            "mem":66.6
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        }
         *    ]
         * }
         * </pre>
         *
         * <code>string ServiceInfo = 3;</code>
         *
         * @return The serviceInfo.
         */
        java.lang.String getServiceInfo();

        /**
         * <pre>
         * JSON格式信息示例
         * {
         *    "asr_hf": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":150,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":33.3
         *        }
         *    ],
         *
         *    "tts_xtts": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":99.9,
         *            "mem":66.6
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        }
         *    ]
         * }
         * </pre>
         *
         * <code>string ServiceInfo = 3;</code>
         *
         * @return The bytes for serviceInfo.
         */
        com.google.protobuf.ByteString
        getServiceInfoBytes();
    }

    /**
     * <pre>
     * GetServiceInfo时获取到的服务信息列表
     * </pre>
     * <p>
     * Protobuf type {@code GetServer.ServiceInfoList}
     */
    public static final class ServiceInfoList extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.ServiceInfoList)
            ServiceInfoListOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ServiceInfoList.newBuilder() to construct.
        private ServiceInfoList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ServiceInfoList() {
            errorInfo_ = "";
            serviceInfo_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ServiceInfoList();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceInfoList_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceInfoList_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.ServiceInfoList.class, skynet.boot.tlb.core.Lb.ServiceInfoList.Builder.class);
        }

        public static final int ERRORCODE_FIELD_NUMBER = 1;
        private int errorCode_ = 0;

        /**
         * <pre>
         * GetServiceInfo接口调用结果
         * </pre>
         *
         * <code>int32 ErrorCode = 1;</code>
         *
         * @return The errorCode.
         */
        @java.lang.Override
        public int getErrorCode() {
            return errorCode_;
        }

        public static final int ERRORINFO_FIELD_NUMBER = 2;
        @SuppressWarnings("serial")
        private volatile java.lang.Object errorInfo_ = "";

        /**
         * <pre>
         * 调用错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The errorInfo.
         */
        @java.lang.Override
        public java.lang.String getErrorInfo() {
            java.lang.Object ref = errorInfo_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                errorInfo_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * 调用错误信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The bytes for errorInfo.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getErrorInfoBytes() {
            java.lang.Object ref = errorInfo_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                errorInfo_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int SERVICEINFO_FIELD_NUMBER = 3;
        @SuppressWarnings("serial")
        private volatile java.lang.Object serviceInfo_ = "";

        /**
         * <pre>
         * JSON格式信息示例
         * {
         *    "asr_hf": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":150,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":33.3
         *        }
         *    ],
         *
         *    "tts_xtts": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":99.9,
         *            "mem":66.6
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        }
         *    ]
         * }
         * </pre>
         *
         * <code>string ServiceInfo = 3;</code>
         *
         * @return The serviceInfo.
         */
        @java.lang.Override
        public java.lang.String getServiceInfo() {
            java.lang.Object ref = serviceInfo_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                serviceInfo_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * JSON格式信息示例
         * {
         *    "asr_hf": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":5090,
         *            "max_lic":150,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":33.3
         *        }
         *    ],
         *
         *    "tts_xtts": [
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":99.9,
         *            "mem":66.6
         *        },
         *        {
         *            "svr_ip":"***************",
         *            "svr_port":10006,
         *            "max_lic":200,
         *            "used_lic":55,
         *            "cpu":51.5,
         *            "mem":22.2
         *        }
         *    ]
         * }
         * </pre>
         *
         * <code>string ServiceInfo = 3;</code>
         *
         * @return The bytes for serviceInfo.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getServiceInfoBytes() {
            java.lang.Object ref = serviceInfo_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                serviceInfo_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (errorCode_ != 0) {
                output.writeInt32(1, errorCode_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(errorInfo_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorInfo_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serviceInfo_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 3, serviceInfo_);
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (errorCode_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(1, errorCode_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(errorInfo_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorInfo_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(serviceInfo_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, serviceInfo_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.ServiceInfoList)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.ServiceInfoList other = (skynet.boot.tlb.core.Lb.ServiceInfoList) obj;

            if (getErrorCode()
                    != other.getErrorCode()) return false;
            if (!getErrorInfo()
                    .equals(other.getErrorInfo())) return false;
            if (!getServiceInfo()
                    .equals(other.getServiceInfo())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
            hash = (53 * hash) + getErrorCode();
            hash = (37 * hash) + ERRORINFO_FIELD_NUMBER;
            hash = (53 * hash) + getErrorInfo().hashCode();
            hash = (37 * hash) + SERVICEINFO_FIELD_NUMBER;
            hash = (53 * hash) + getServiceInfo().hashCode();
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.ServiceInfoList prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * <pre>
         * GetServiceInfo时获取到的服务信息列表
         * </pre>
         * <p>
         * Protobuf type {@code GetServer.ServiceInfoList}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.ServiceInfoList)
                skynet.boot.tlb.core.Lb.ServiceInfoListOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceInfoList_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceInfoList_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.ServiceInfoList.class, skynet.boot.tlb.core.Lb.ServiceInfoList.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.ServiceInfoList.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                errorCode_ = 0;
                errorInfo_ = "";
                serviceInfo_ = "";
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_ServiceInfoList_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServiceInfoList getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.ServiceInfoList.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServiceInfoList build() {
                skynet.boot.tlb.core.Lb.ServiceInfoList result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.ServiceInfoList buildPartial() {
                skynet.boot.tlb.core.Lb.ServiceInfoList result = new skynet.boot.tlb.core.Lb.ServiceInfoList(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.Lb.ServiceInfoList result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.errorCode_ = errorCode_;
                }
                if (((from_bitField0_ & 0x00000002) != 0)) {
                    result.errorInfo_ = errorInfo_;
                }
                if (((from_bitField0_ & 0x00000004) != 0)) {
                    result.serviceInfo_ = serviceInfo_;
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.ServiceInfoList) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.ServiceInfoList) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.ServiceInfoList other) {
                if (other == skynet.boot.tlb.core.Lb.ServiceInfoList.getDefaultInstance()) return this;
                if (other.getErrorCode() != 0) {
                    setErrorCode(other.getErrorCode());
                }
                if (!other.getErrorInfo().isEmpty()) {
                    errorInfo_ = other.errorInfo_;
                    bitField0_ |= 0x00000002;
                    onChanged();
                }
                if (!other.getServiceInfo().isEmpty()) {
                    serviceInfo_ = other.serviceInfo_;
                    bitField0_ |= 0x00000004;
                    onChanged();
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 8: {
                                errorCode_ = input.readInt32();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 8
                            case 18: {
                                errorInfo_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000002;
                                break;
                            } // case 18
                            case 26: {
                                serviceInfo_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000004;
                                break;
                            } // case 26
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private int errorCode_;

            /**
             * <pre>
             * GetServiceInfo接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @return The errorCode.
             */
            @java.lang.Override
            public int getErrorCode() {
                return errorCode_;
            }

            /**
             * <pre>
             * GetServiceInfo接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @param value The errorCode to set.
             * @return This builder for chaining.
             */
            public Builder setErrorCode(int value) {

                errorCode_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * GetServiceInfo接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearErrorCode() {
                bitField0_ = (bitField0_ & ~0x00000001);
                errorCode_ = 0;
                onChanged();
                return this;
            }

            private java.lang.Object errorInfo_ = "";

            /**
             * <pre>
             * 调用错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return The errorInfo.
             */
            public java.lang.String getErrorInfo() {
                java.lang.Object ref = errorInfo_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    errorInfo_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * 调用错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return The bytes for errorInfo.
             */
            public com.google.protobuf.ByteString
            getErrorInfoBytes() {
                java.lang.Object ref = errorInfo_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    errorInfo_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * 调用错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @param value The errorInfo to set.
             * @return This builder for chaining.
             */
            public Builder setErrorInfo(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                errorInfo_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 调用错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearErrorInfo() {
                errorInfo_ = getDefaultInstance().getErrorInfo();
                bitField0_ = (bitField0_ & ~0x00000002);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 调用错误信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @param value The bytes for errorInfo to set.
             * @return This builder for chaining.
             */
            public Builder setErrorInfoBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                errorInfo_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            private java.lang.Object serviceInfo_ = "";

            /**
             * <pre>
             * JSON格式信息示例
             * {
             *    "asr_hf": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":150,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":33.3
             *        }
             *    ],
             *
             *    "tts_xtts": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":99.9,
             *            "mem":66.6
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        }
             *    ]
             * }
             * </pre>
             *
             * <code>string ServiceInfo = 3;</code>
             *
             * @return The serviceInfo.
             */
            public java.lang.String getServiceInfo() {
                java.lang.Object ref = serviceInfo_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    serviceInfo_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * JSON格式信息示例
             * {
             *    "asr_hf": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":150,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":33.3
             *        }
             *    ],
             *
             *    "tts_xtts": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":99.9,
             *            "mem":66.6
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        }
             *    ]
             * }
             * </pre>
             *
             * <code>string ServiceInfo = 3;</code>
             *
             * @return The bytes for serviceInfo.
             */
            public com.google.protobuf.ByteString
            getServiceInfoBytes() {
                java.lang.Object ref = serviceInfo_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    serviceInfo_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * JSON格式信息示例
             * {
             *    "asr_hf": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":150,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":33.3
             *        }
             *    ],
             *
             *    "tts_xtts": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":99.9,
             *            "mem":66.6
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        }
             *    ]
             * }
             * </pre>
             *
             * <code>string ServiceInfo = 3;</code>
             *
             * @param value The serviceInfo to set.
             * @return This builder for chaining.
             */
            public Builder setServiceInfo(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                serviceInfo_ = value;
                bitField0_ |= 0x00000004;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * JSON格式信息示例
             * {
             *    "asr_hf": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":150,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":33.3
             *        }
             *    ],
             *
             *    "tts_xtts": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":99.9,
             *            "mem":66.6
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        }
             *    ]
             * }
             * </pre>
             *
             * <code>string ServiceInfo = 3;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearServiceInfo() {
                serviceInfo_ = getDefaultInstance().getServiceInfo();
                bitField0_ = (bitField0_ & ~0x00000004);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * JSON格式信息示例
             * {
             *    "asr_hf": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":5090,
             *            "max_lic":150,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":33.3
             *        }
             *    ],
             *
             *    "tts_xtts": [
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":99.9,
             *            "mem":66.6
             *        },
             *        {
             *            "svr_ip":"***************",
             *            "svr_port":10006,
             *            "max_lic":200,
             *            "used_lic":55,
             *            "cpu":51.5,
             *            "mem":22.2
             *        }
             *    ]
             * }
             * </pre>
             *
             * <code>string ServiceInfo = 3;</code>
             *
             * @param value The bytes for serviceInfo to set.
             * @return This builder for chaining.
             */
            public Builder setServiceInfoBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                serviceInfo_ = value;
                bitField0_ |= 0x00000004;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.ServiceInfoList)
        }

        // @@protoc_insertion_point(class_scope:GetServer.ServiceInfoList)
        private static final skynet.boot.tlb.core.Lb.ServiceInfoList DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.ServiceInfoList();
        }

        public static skynet.boot.tlb.core.Lb.ServiceInfoList getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ServiceInfoList>
                PARSER = new com.google.protobuf.AbstractParser<ServiceInfoList>() {
            @java.lang.Override
            public ServiceInfoList parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ServiceInfoList> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ServiceInfoList> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.ServiceInfoList getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface LBVersionOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.LBVersion)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <code>string Version = 1;</code>
         *
         * @return The version.
         */
        java.lang.String getVersion();

        /**
         * <code>string Version = 1;</code>
         *
         * @return The bytes for version.
         */
        com.google.protobuf.ByteString
        getVersionBytes();
    }

    /**
     * Protobuf type {@code GetServer.LBVersion}
     */
    public static final class LBVersion extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.LBVersion)
            LBVersionOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use LBVersion.newBuilder() to construct.
        private LBVersion(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private LBVersion() {
            version_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new LBVersion();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_LBVersion_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_LBVersion_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.LBVersion.class, skynet.boot.tlb.core.Lb.LBVersion.Builder.class);
        }

        public static final int VERSION_FIELD_NUMBER = 1;
        @SuppressWarnings("serial")
        private volatile java.lang.Object version_ = "";

        /**
         * <code>string Version = 1;</code>
         *
         * @return The version.
         */
        @java.lang.Override
        public java.lang.String getVersion() {
            java.lang.Object ref = version_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                version_ = s;
                return s;
            }
        }

        /**
         * <code>string Version = 1;</code>
         *
         * @return The bytes for version.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getVersionBytes() {
            java.lang.Object ref = version_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                version_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(version_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 1, version_);
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(version_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, version_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.LBVersion)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.LBVersion other = (skynet.boot.tlb.core.Lb.LBVersion) obj;

            if (!getVersion()
                    .equals(other.getVersion())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + VERSION_FIELD_NUMBER;
            hash = (53 * hash) + getVersion().hashCode();
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.LBVersion parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.LBVersion prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code GetServer.LBVersion}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.LBVersion)
                skynet.boot.tlb.core.Lb.LBVersionOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_LBVersion_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_LBVersion_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.LBVersion.class, skynet.boot.tlb.core.Lb.LBVersion.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.LBVersion.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                version_ = "";
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_LBVersion_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.LBVersion getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.LBVersion.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.LBVersion build() {
                skynet.boot.tlb.core.Lb.LBVersion result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.LBVersion buildPartial() {
                skynet.boot.tlb.core.Lb.LBVersion result = new skynet.boot.tlb.core.Lb.LBVersion(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.Lb.LBVersion result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.version_ = version_;
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.LBVersion) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.LBVersion) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.LBVersion other) {
                if (other == skynet.boot.tlb.core.Lb.LBVersion.getDefaultInstance()) return this;
                if (!other.getVersion().isEmpty()) {
                    version_ = other.version_;
                    bitField0_ |= 0x00000001;
                    onChanged();
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 10: {
                                version_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 10
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private java.lang.Object version_ = "";

            /**
             * <code>string Version = 1;</code>
             *
             * @return The version.
             */
            public java.lang.String getVersion() {
                java.lang.Object ref = version_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    version_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <code>string Version = 1;</code>
             *
             * @return The bytes for version.
             */
            public com.google.protobuf.ByteString
            getVersionBytes() {
                java.lang.Object ref = version_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    version_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <code>string Version = 1;</code>
             *
             * @param value The version to set.
             * @return This builder for chaining.
             */
            public Builder setVersion(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                version_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <code>string Version = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearVersion() {
                version_ = getDefaultInstance().getVersion();
                bitField0_ = (bitField0_ & ~0x00000001);
                onChanged();
                return this;
            }

            /**
             * <code>string Version = 1;</code>
             *
             * @param value The bytes for version to set.
             * @return This builder for chaining.
             */
            public Builder setVersionBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                version_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.LBVersion)
        }

        // @@protoc_insertion_point(class_scope:GetServer.LBVersion)
        private static final skynet.boot.tlb.core.Lb.LBVersion DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.LBVersion();
        }

        public static skynet.boot.tlb.core.Lb.LBVersion getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<LBVersion>
                PARSER = new com.google.protobuf.AbstractParser<LBVersion>() {
            @java.lang.Override
            public LBVersion parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<LBVersion> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<LBVersion> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.LBVersion getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface EmptyMsgOrBuilder extends
            // @@protoc_insertion_point(interface_extends:GetServer.EmptyMsg)
            com.google.protobuf.MessageOrBuilder {
    }

    /**
     * Protobuf type {@code GetServer.EmptyMsg}
     */
    public static final class EmptyMsg extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:GetServer.EmptyMsg)
            EmptyMsgOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use EmptyMsg.newBuilder() to construct.
        private EmptyMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private EmptyMsg() {
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new EmptyMsg();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_EmptyMsg_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.Lb.internal_static_GetServer_EmptyMsg_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.Lb.EmptyMsg.class, skynet.boot.tlb.core.Lb.EmptyMsg.Builder.class);
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.Lb.EmptyMsg)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.Lb.EmptyMsg other = (skynet.boot.tlb.core.Lb.EmptyMsg) obj;

            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.Lb.EmptyMsg prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code GetServer.EmptyMsg}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:GetServer.EmptyMsg)
                skynet.boot.tlb.core.Lb.EmptyMsgOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_EmptyMsg_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_EmptyMsg_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.Lb.EmptyMsg.class, skynet.boot.tlb.core.Lb.EmptyMsg.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.Lb.EmptyMsg.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.Lb.internal_static_GetServer_EmptyMsg_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.EmptyMsg getDefaultInstanceForType() {
                return skynet.boot.tlb.core.Lb.EmptyMsg.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.EmptyMsg build() {
                skynet.boot.tlb.core.Lb.EmptyMsg result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.Lb.EmptyMsg buildPartial() {
                skynet.boot.tlb.core.Lb.EmptyMsg result = new skynet.boot.tlb.core.Lb.EmptyMsg(this);
                onBuilt();
                return result;
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.Lb.EmptyMsg) {
                    return mergeFrom((skynet.boot.tlb.core.Lb.EmptyMsg) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.Lb.EmptyMsg other) {
                if (other == skynet.boot.tlb.core.Lb.EmptyMsg.getDefaultInstance()) return this;
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:GetServer.EmptyMsg)
        }

        // @@protoc_insertion_point(class_scope:GetServer.EmptyMsg)
        private static final skynet.boot.tlb.core.Lb.EmptyMsg DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.Lb.EmptyMsg();
        }

        public static skynet.boot.tlb.core.Lb.EmptyMsg getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<EmptyMsg>
                PARSER = new com.google.protobuf.AbstractParser<EmptyMsg>() {
            @java.lang.Override
            public EmptyMsg parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<EmptyMsg> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<EmptyMsg> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.Lb.EmptyMsg getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_ServiceName_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_ServiceName_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_ServerParam_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_ServerParam_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_ServerParamEx_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_ServerParamEx_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_ServerParamEx_ServiceInfoEntry_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_ServerParamEx_ServiceInfoEntry_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_ServerAddress_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_ServerAddress_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_ServerAddresses_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_ServerAddresses_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_ServiceInfoList_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_ServiceInfoList_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_LBVersion_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_LBVersion_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_GetServer_EmptyMsg_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_GetServer_EmptyMsg_fieldAccessorTable;

    public static com.google.protobuf.Descriptors.FileDescriptor
    getDescriptor() {
        return descriptor;
    }

    private static com.google.protobuf.Descriptors.FileDescriptor
            descriptor;

    static {
        java.lang.String[] descriptorData = {
                "\n\010Lb.proto\022\tGetServer\"\"\n\013ServiceName\022\023\n\013" +
                        "ServiceName\030\001 \001(\t\"P\n\013ServerParam\022\022\n\nServ" +
                        "erName\030\001 \001(\t\022\016\n\006Number\030\002 \001(\005\022\013\n\003UID\030\003 \001(" +
                        "\t\022\020\n\010Reserved\030\004 \001(\t\"\247\001\n\rServerParamEx\022\022\n" +
                        "\nServerName\030\001 \001(\t\022\016\n\006Number\030\002 \001(\005\022>\n\013Ser" +
                        "viceInfo\030\003 \003(\0132).GetServer.ServerParamEx" +
                        ".ServiceInfoEntry\0322\n\020ServiceInfoEntry\022\013\n" +
                        "\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"5\n\rServerA" +
                        "ddress\022\020\n\010ServerIP\030\001 \001(\t\022\022\n\nServerPort\030\002" +
                        " \001(\005\"f\n\017ServerAddresses\022\021\n\tErrorCode\030\001 \001" +
                        "(\005\022\021\n\tErrorInfo\030\002 \001(\t\022-\n\013AddressList\030\003 \003" +
                        "(\0132\030.GetServer.ServerAddress\"L\n\017ServiceI" +
                        "nfoList\022\021\n\tErrorCode\030\001 \001(\005\022\021\n\tErrorInfo\030" +
                        "\002 \001(\t\022\023\n\013ServiceInfo\030\003 \001(\t\"\034\n\tLBVersion\022" +
                        "\017\n\007Version\030\001 \001(\t\"\n\n\010EmptyMsg2\364\002\n\013LoadBal" +
                        "ance\022E\n\rGetBestServer\022\026.GetServer.Server" +
                        "Param\032\032.GetServer.ServerAddresses\"\000\022P\n\026G" +
                        "etBestServerInstances\022\030.GetServer.Server" +
                        "ParamEx\032\032.GetServer.ServiceInfoList\"\000\022I\n" +
                        "\017GetBestServerEx\022\030.GetServer.ServerParam" +
                        "Ex\032\032.GetServer.ServerAddresses\"\000\022F\n\016GetS" +
                        "erviceInfo\022\026.GetServer.ServiceName\032\032.Get" +
                        "Server.ServiceInfoList\"\000\0229\n\nGetVersion\022\023" +
                        ".GetServer.EmptyMsg\032\024.GetServer.LBVersio" +
                        "n\"\000B\026\n\024skynet.boot.tlb.coreb\006proto3"
        };
        descriptor = com.google.protobuf.Descriptors.FileDescriptor
                .internalBuildGeneratedFileFrom(descriptorData,
                        new com.google.protobuf.Descriptors.FileDescriptor[]{
                        });
        internal_static_GetServer_ServiceName_descriptor =
                getDescriptor().getMessageTypes().get(0);
        internal_static_GetServer_ServiceName_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_ServiceName_descriptor,
                new java.lang.String[]{"ServiceName",});
        internal_static_GetServer_ServerParam_descriptor =
                getDescriptor().getMessageTypes().get(1);
        internal_static_GetServer_ServerParam_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_ServerParam_descriptor,
                new java.lang.String[]{"ServerName", "Number", "UID", "Reserved",});
        internal_static_GetServer_ServerParamEx_descriptor =
                getDescriptor().getMessageTypes().get(2);
        internal_static_GetServer_ServerParamEx_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_ServerParamEx_descriptor,
                new java.lang.String[]{"ServerName", "Number", "ServiceInfo",});
        internal_static_GetServer_ServerParamEx_ServiceInfoEntry_descriptor =
                internal_static_GetServer_ServerParamEx_descriptor.getNestedTypes().get(0);
        internal_static_GetServer_ServerParamEx_ServiceInfoEntry_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_ServerParamEx_ServiceInfoEntry_descriptor,
                new java.lang.String[]{"Key", "Value",});
        internal_static_GetServer_ServerAddress_descriptor =
                getDescriptor().getMessageTypes().get(3);
        internal_static_GetServer_ServerAddress_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_ServerAddress_descriptor,
                new java.lang.String[]{"ServerIP", "ServerPort",});
        internal_static_GetServer_ServerAddresses_descriptor =
                getDescriptor().getMessageTypes().get(4);
        internal_static_GetServer_ServerAddresses_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_ServerAddresses_descriptor,
                new java.lang.String[]{"ErrorCode", "ErrorInfo", "AddressList",});
        internal_static_GetServer_ServiceInfoList_descriptor =
                getDescriptor().getMessageTypes().get(5);
        internal_static_GetServer_ServiceInfoList_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_ServiceInfoList_descriptor,
                new java.lang.String[]{"ErrorCode", "ErrorInfo", "ServiceInfo",});
        internal_static_GetServer_LBVersion_descriptor =
                getDescriptor().getMessageTypes().get(6);
        internal_static_GetServer_LBVersion_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_LBVersion_descriptor,
                new java.lang.String[]{"Version",});
        internal_static_GetServer_EmptyMsg_descriptor =
                getDescriptor().getMessageTypes().get(7);
        internal_static_GetServer_EmptyMsg_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_GetServer_EmptyMsg_descriptor,
                new java.lang.String[]{});
    }

    // @@protoc_insertion_point(outer_class_scope)
}
