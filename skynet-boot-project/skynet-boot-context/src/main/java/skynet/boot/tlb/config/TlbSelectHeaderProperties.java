package skynet.boot.tlb.config;


import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * 选择服务，使用请求头参数
 */
@Setter
@Getter
public class TlbSelectHeaderProperties extends Jsonable {

    @J<PERSON>NField(serialize = false)
    public static final String DEFAULT_TAG_SELECTOR_KEY = "skynet-tlb-service-tag-selector";
    @JSONField(serialize = false)
    public static final String DEFAULT_BIZ_ID_KEY = "skynet-tlb-service-biz-id";
    @JSONField(serialize = false)
    public static final String DEFAULT_CHAIN_KEY = "skynet-tlb-invoke-chain";

    /**
     * 是否开启，从HTTP请求头，动态调整服务发现参数（tag-selector和 biz-id）
     */
    private boolean enabled = true;

    /**
     * tagSelector http 请求头 key，（根据tag获取最佳服务）
     */
    private String tagSelectorKey = DEFAULT_TAG_SELECTOR_KEY;

    /**
     * bizId http 请求头 key，（根据BizId获取最佳服务）
     */
    private String bizIdKey = DEFAULT_BIZ_ID_KEY;

    /**
     * 调用链上下文
     */
    private String chainKey = DEFAULT_CHAIN_KEY;
}