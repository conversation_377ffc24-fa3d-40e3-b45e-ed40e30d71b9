package skynet.boot.tlb;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import skynet.boot.tlb.client.TlbClient;
import skynet.boot.tlb.client.data.TlbServiceInstance;
import skynet.boot.tlb.client.data.TlbServiceInstanceResponse;
import skynet.boot.tlb.client.data.TlbStatus;
import skynet.boot.tlb.domain.*;
import skynet.boot.tlb.exception.TlbException;
import skynet.boot.tlb.exception.TlbServiceException;
import skynet.boot.tlb.service.TlbClientBuilder;

/**
 * TLB (Traffic Load Balancer) Selector for obtaining optimal service instances.
 * 
 * <p>
 * Key Features:
 * </p>
 * <ul>
 * <li>Get optimal service instances</li>
 * <li>Support service discovery</li>
 * <li>Support tag-based routing</li>
 * <li>Support business ID routing</li>
 * </ul>
 * 
 * <p>
 * Usage Example:
 * </p>
 * 
 * <pre>{@code
 * // Create TLB selector
 * TlbClientBuilder builder = new TlbClientBuilder();
 * TlbClientSelector selector = new TlbClientSelector(builder);
 * 
 * try {
 *     // Get optimal service instance
 *     ServerIPEndpoint endpoint = selector.getBestServer("userService");
 * 
 *     // With tag routing
 *     Tag tag = new Tag("version=1.0");
 *     endpoint = selector.getBestServer("userService", tag);
 * 
 *     // With business ID routing
 *     endpoint = selector.getBestServer("userService", "biz001");
 * 
 *     // Get service instance list
 *     List<TlbServiceInstance> instances = selector.getServiceInstances("userService");
 * } catch (TlbException e) {
 *     log.error("Failed to get service instance", e);
 * } finally {
 *     selector.close();
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class TlbClientSelector implements AutoCloseable {

    // Constants
    private static final String LOG_PREFIX = "[TlbClientSelector] ";

    // Core components
    private final TlbClientBuilder tlbClientBuilder;
    private volatile TlbClient tlbClient;

    // Thread safety
    private final ReentrantLock lock = new ReentrantLock();

    /**
     * Constructs a TLB selector.
     *
     * @param tlbClientBuilder TLB client builder, must not be null
     */
    public TlbClientSelector(TlbClientBuilder tlbClientBuilder) {
        Assert.notNull(tlbClientBuilder, "tlbClientBuilder must not be null");
        this.tlbClientBuilder = tlbClientBuilder;
    }

    /**
     * Gets the optimal service instance from the load balancer.
     *
     * @param serviceName Service name, must not be null or empty
     * @return ServerIPEndpoint or null if no available service
     * @throws TlbException if an error occurs during the operation
     */
    public ServerIPEndpoint getBestServer(String serviceName) throws TlbException {
        Assert.hasText(serviceName, "serviceName must not be empty");
        return getBestServer(serviceName, null, "");
    }

    /**
     * Gets the optimal service instance with tag routing.
     *
     * @param serviceName Service name, must not be null or empty
     * @param tag         Tag for routing, may be null
     * @return ServerIPEndpoint or null if no available service
     * @throws TlbException if an error occurs during the operation
     */
    public ServerIPEndpoint getBestServer(String serviceName, Tag tag) throws TlbException {
        Assert.hasText(serviceName, "serviceName must not be empty");
        return getBestServer(serviceName, "", true, tag);
    }

    /**
     * 从LB注册中心中 获取最优服务地址，如果没有可用服务或LB服务不存在，返回null
     *
     * @param serviceName 服务名称
     * @param bizId       业务id
     * @return ip port
     * @throws TlbException tlb 异常
     */
    public ServerIPEndpoint getBestServer(String serviceName, String bizId) throws TlbException {
        return getBestServer(serviceName, bizId, true);
    }

    /**
     * 从LB注册中心中 获取最优服务地址，如果没有可用服务或LB服务不存在，返回null
     *
     * @param serviceName                 服务名称
     * @param bizId                       业务id
     * @param isSelectServerDefault4BizId tlb 服务没找到服务实例是否兜底(针对bizId)
     * @return ip port
     * @throws TlbException tlb 异常
     */
    public ServerIPEndpoint getBestServer(String serviceName, String bizId, boolean isSelectServerDefault4BizId)
            throws TlbException {
        return getBestServer(serviceName, bizId, isSelectServerDefault4BizId, null);
    }

    /**
     * 从LB注册中心中 获取最优服务地址，如果没有可用服务或LB服务不存在，返回null
     *
     * @param serviceName                 服务名称
     * @param bizId                       业务id
     * @param tag                         tag 表达式 如： A&&B&&C A||B||C !(A&&B&&C)
     * @param isSelectServerDefault4BizId tlb 服务没找到服务实例是否兜底(针对bizId)
     * @return ip:port
     * @throws TlbException tlb 异常
     */
    public ServerIPEndpoint getBestServer(String serviceName, String bizId, boolean isSelectServerDefault4BizId,
            Tag tag) throws TlbException {
        if (isSelectServerDefault4BizId) {
            return getBestServer(serviceName, tag, bizId);
        }
        return getBestServer(serviceName, tag, bizId, LbFeature.DISABLED_SELECT_SERVER_DEFAULT);
    }

    /**
     * 从LB注册中心中 获取最优的服务地址，如果没有可用服务或LB服务不存在，返回null
     * <p>
     * 根据 lbServiceName、 bizId 向 LB 查询 ip:port
     * features 里的参数表示关闭 LB 的这些功能
     * 即 features 里的选项传给 LB 时值是: false
     * features 为空时传给 LB 值是默认值: true
     *
     * @param serviceName 服务名称
     * @param tag         服务标签
     * @param bizId       业务 id
     * @param features    待关闭的选项
     * @return ip:port
     * @throws TlbException tlb 异常
     */
    public ServerIPEndpoint getBestServer(String serviceName, Tag tag, String bizId, LbFeature... features)
            throws TlbException {
        List<ServerIPEndpoint> objList = this.getBestServers(serviceName, tag == null ? null : tag.toString(), bizId, 1,
                features);
        return !objList.isEmpty() ? objList.get(0) : null;
    }

    /**
     * 从LB注册中心中 获取最优服务地址列表，如果没有可用服务或LB服务不存在
     * <p>
     * 根据 lbServiceName、 bizId 向 LB 查询 ip:port
     * features 里的参数表示关闭 LB 的这些功能
     * 即 features 里的选项传给 LB 时值是: false
     * features 为空时传给 LB 值是默认值: true
     *
     * @param serviceName 服务名称
     * @param bizId       业务 id
     * @param tagSelector tag 表达式 如： A&&B&&C A||B||C !(A&&B&&C)
     * @param topN        最优的N个服务
     * @param features    待关闭的选项
     * @return ip:port
     * @throws TlbException tlb 异常
     */
    public List<ServerIPEndpoint> getBestServers(String serviceName, String tagSelector, String bizId, int topN,
            LbFeature... features) throws TlbException {

        BestServerParam bsp = buildBestServerParam(serviceName, tagSelector, bizId, topN, features);

        BestServerResult bestServerResult = this.getBestServer(bsp);
        if (null == bestServerResult) {
            log.error("bestServerResult is empty.[service name: {}]", serviceName);
            throw new TlbException(String.format("bestServerResult is empty.[tlb service name: %s]", serviceName));
        }
        log.debug("Get best {} service result= {}", serviceName, bestServerResult);

        if (bestServerResult.getErrorCode() != 0) {
            log.warn("getServiceInfo result serviceName={} code={}; info={}", serviceName,
                    bestServerResult.getErrorCode(), bestServerResult.getErrorInfo());
            return Collections.emptyList();
        }

        return bestServerResult.getAddressList() != null ? bestServerResult.getAddressList() : Collections.emptyList();
    }

    /**
     * 构建最优服务参数
     *
     * @param serviceName 服务名称
     * @param tagSelector 标签选择器
     * @param bizId       业务ID
     * @param topN        最优的N个服务
     * @param features    待关闭的选项
     * @return 最优服务参数
     * @throws TlbException tlb 异常
     */
    private BestServerParam buildBestServerParam(String serviceName, String tagSelector, String bizId, int topN,
            LbFeature... features) throws TlbException {
        Assert.hasText(serviceName, "serviceName is empty");
        log.debug("get topN={} best {} services...", topN, serviceName);

        int featureValue = LbFeature.of(features);
        // features 包含的字段放到 serviceInfo 里的 value 是 false
        // features 不包含的字段放到 serviceInfo 里的 value 是 true
        Map<String, String> serviceInfo = new HashMap<>(LbFeature.values().length);
        for (LbFeature feature : LbFeature.values()) {
            serviceInfo.put(feature.getExtKey(), String.valueOf(!LbFeature.isEnabled(featureValue, feature)));
        }

        return new BestServerParam().setNumber(topN).setServiceName(serviceName).setTagSelector(tagSelector)
                .setBizId(bizId).setServiceInfo(serviceInfo);
    }

    /**
     * 获取所有 serviceName 服务实例列表
     *
     * @param serviceName 服务名称
     * @return 服务实例列表
     * @throws TlbServiceException tlb 服务异常
     * @since 4.0.9
     */
    public List<TlbServiceInstance> getServiceInstances(String serviceName) throws TlbException {
        Assert.hasText(serviceName, "serviceName is empty");
        log.debug("get service info serviceName = {}", serviceName);

        TlbServiceInstanceResponse response = getServiceInfo(serviceName);
        if (response.getErrorCode() != 0) {
            log.warn("getServiceInstances result serviceName={} code={}; info={}", serviceName, response.getErrorCode(),
                    response.getErrorInfo());
        }
        return response.getServiceInstanceMap().getOrDefault(serviceName, Collections.emptyList());
    }

    /**
     * 获取所有 serviceName 服务列表
     *
     * @param serviceName 服务名称
     * @return 服务实例响应
     * @throws TlbException tlb 异常
     */
    public TlbServiceInstanceResponse getServiceInfo(String serviceName) throws TlbException {
        Assert.hasText(serviceName, "serviceName is empty");
        log.debug("get service info serviceName = {}", serviceName);
        return getTlbClient().getServiceInfo(serviceName);
    }

    /**
     * 获取最优服务信息
     *
     * @param bestServerParam 最优服务参数
     * @return 最优服务结果
     * @throws TlbException tlb 异常
     */
    public BestServerResult getBestServer(BestServerParam bestServerParam) throws TlbException {
        Assert.notNull(bestServerParam, "bestServerParam is empty");
        log.debug("getBestServer bestServerParam = {}", bestServerParam);
        return getTlbClient().getBestServer(bestServerParam);
    }

    /**
     * 获取最优服务实例信息
     *
     * @param serviceName 服务名称
     * @param tagSelector 标签选择器
     * @param bizId       业务ID
     * @param topN        最优的N个服务
     * @param features    待关闭的选项
     * @return 服务实例列表
     * @throws TlbException tlb 异常
     */
    public List<TlbServiceInstance> getBestServerInstances(String serviceName, String tagSelector, String bizId,
            int topN, LbFeature... features) throws TlbException {
        BestServerParam bsp = buildBestServerParam(serviceName, tagSelector, bizId, topN, features);
        return getBestServerInstances(bsp);
    }

    /**
     * 获取最优服务实例信息
     *
     * @param bestServerParam 最优服务参数
     * @return 服务实例列表
     * @throws TlbException tlb 异常
     */
    public List<TlbServiceInstance> getBestServerInstances(BestServerParam bestServerParam) throws TlbException {
        Assert.notNull(bestServerParam, "bestServerParam is empty");
        log.debug("get getBestServerInstances info bestServerParam = {}", bestServerParam);
        TlbServiceInstanceResponse response = getTlbClient().getBestServerInstances(bestServerParam);

        if (response.getErrorCode() != 0) {
            log.warn("getBestServerInstances result serviceName={} code={}; info={}", bestServerParam.getServiceName(),
                    response.getErrorCode(), response.getErrorInfo());
        }
        return response.getServiceInstanceMap().getOrDefault(bestServerParam.getServiceName(), Collections.emptyList());
    }

    /**
     * 获取所有服务信息列表
     *
     * @return 服务实例映射
     * @throws TlbException tlb 异常
     */
    public Map<String, List<TlbServiceInstance>> getServiceInstanceMap() throws TlbException {
        TlbServiceInstanceResponse response = getTlbClient().getServiceInfo();
        if (response.getErrorCode() != 0) {
            log.warn("getBestServerInstances result code={}; info={}", response.getErrorCode(),
                    response.getErrorInfo());
        }

        // getServiceInfo() 返回的信息中 tags 可能缺失，依次调用 getServiceInfo(svcName) 可以强制刷新 tags 字段
        Map<String, List<TlbServiceInstance>> result = new HashMap<>();
        if (response.getServiceInstanceMap() != null) {
            for (String key : response.getServiceInstanceMap().keySet()) {
                result.put(key, getServiceInstances(key));
            }
        }
        return result;
    }

    /**
     * Gets the TLB client instance, initializing it if necessary.
     * Thread-safe using ReentrantLock.
     *
     * @return TLB client instance
     */
    private TlbClient getTlbClient() {
        TlbClient localClient = tlbClient;
        if (localClient == null) {
            lock.lock();
            try {
                localClient = tlbClient;
                if (localClient == null) {
                    log.info("{}Initializing TLB client...", LOG_PREFIX);
                    localClient = tlbClientBuilder.newTlbClient();
                    tlbClient = localClient;
                    log.info("{}TLB client initialized successfully", LOG_PREFIX);
                }
            } finally {
                lock.unlock();
            }
        }
        return localClient;
    }

    /**
     * 获取TLB服务端状态
     *
     * @return TLB服务端状态
     */
    public TlbStatus getLbStatus() {
        log.debug("{}Checking TLB server status", LOG_PREFIX);
        TlbStatus status = getTlbClient().checkLbStatus();
        log.debug("{}TLB server status: {}", LOG_PREFIX, status);
        return status;
    }

    @Override
    public void close() throws Exception {
        lock.lock();
        try {
            log.info("{}Closing TLB client...", LOG_PREFIX);
            if (this.tlbClient != null) {
                try {
                    this.tlbClient.close();
                    this.tlbClient = null;
                    log.info("{}TLB client closed successfully", LOG_PREFIX);
                } catch (Exception e) {
                    log.error("{}Failed to close TLB client", LOG_PREFIX, e);
                    throw e;
                }
            }
        } finally {
            lock.unlock();
        }
    }
}