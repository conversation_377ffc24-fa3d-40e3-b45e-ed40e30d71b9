package skynet.boot.tlb.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.HashMap;
import java.util.Map;

/**
 * 最优服务参数.
 *
 * <AUTHOR> [2018年10月9日 上午11:47:47]
 */
@Getter
@Setter
@Accessors(chain = true)
public class BestServerParam extends Jsonable {

    /**
     * 要获取的服务名称.
     */
    private String serviceName;

    /**
     * 要获取的服务地址数量
     */
    private int number = 1;

    /**
     * BizId用于个性化.
     */
    private String bizId = "";

    private String tagSelector = "";


    private Map<String, String> serviceInfo = new HashMap<>();

    /**
     * 当 bizId 不为空时没找到对应服务实例是否兜底
     * true 表示兜底，随机返回一个没有 bizId 的实例 ip:port
     * false 表示不要兜底，没找到就返回错误
     */
    private boolean selectServerDefault = true;

    /**
     * 请求类型,取值为"preload"时，代表“预加载个性化资源”请求;
     * 取值为空字符串""或者"common"时，代表普通获取最佳服务请求。
     */
    private String reqType = "";

    /**
     * 是否在服务满时返回该服务，true(服务满时返回)|false(服务满时报错)
     */
    private boolean selectServerFull = true;

    public BestServerParam() {
    }

    public BestServerParam(String serviceName) {
        this(serviceName, 1);
    }

    public BestServerParam(String serviceName, int number) {
        this(serviceName, number, "");
    }

    public BestServerParam(String serviceName, int number, String uid) {
        this(serviceName, number, uid, new HashMap<>());
    }

    public BestServerParam(String serviceName, int number, String uid, Map<String, String> serviceInfo) {
        this(serviceName, number, uid, serviceInfo, "");
    }

    public BestServerParam(String serviceName, int number, String uid, Map<String, String> serviceInfo,
                           String reqType) {
        this.serviceName = serviceName;
        this.number = number;
        this.bizId = uid;
        this.serviceInfo = serviceInfo;
        this.reqType = reqType;
    }
}
