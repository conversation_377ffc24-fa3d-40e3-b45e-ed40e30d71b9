package skynet.boot.tlb.client;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import skynet.boot.tlb.exception.TlbException;

import javax.crypto.*;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 */
@Slf4j
public class CallCredentialUtil {
    private static final Charset CHARSET_UTF8 = StandardCharsets.UTF_8;
    private static final String ALGORITHM_NAME = "hmacsha256";

    public static String buildAuthCode(String rootSecret, String salt, String encryptedSecret) throws
            DecoderException, InvalidAlgorithmParameterException, IllegalBlockSizeException, NoSuchPaddingException,
            BadPaddingException, NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, NoSuchProviderException, TlbException {
        String plainSecret = decryptSecret(rootSecret, salt, encryptedSecret);
        String currentTime = String.format("%s", System.currentTimeMillis() / 1000);

        Mac mac = Mac.getInstance(ALGORITHM_NAME);
        SecretKeySpec spec = new SecretKeySpec(plainSecret.getBytes(CHARSET_UTF8), ALGORITHM_NAME);
        mac.init(spec);
        byte[] hexDigits = mac.doFinal(currentTime.getBytes(CHARSET_UTF8));

        String auth = String.format("%s-%s", Base64.getEncoder().encodeToString(hexDigits), currentTime);
        return Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
    }

    public static String decryptSecret(String rootSecret, String salt, String encryptedSecret) throws
            TlbException, DecoderException, IllegalBlockSizeException, BadPaddingException, NoSuchAlgorithmException,
            InvalidKeySpecException, InvalidAlgorithmParameterException, InvalidKeyException, NoSuchPaddingException, NoSuchProviderException {

        if (StringUtils.isBlank(rootSecret) || StringUtils.isBlank(salt) || StringUtils.isBlank(encryptedSecret)) {
            String msg = String.format("invalid params. rootSecret=%s, salt=%s, encryptedSecret=%s", rootSecret, salt, encryptedSecret);
            throw new TlbException(msg);
        }

        String[] splitEncryptedSecret = encryptedSecret.split("-");
        if (splitEncryptedSecret.length != 2) {
            throw new TlbException("split encrypted secret error. encryptedSecret=" + encryptedSecret);
        }

        byte[] iv = Hex.decodeHex(splitEncryptedSecret[0]);
        byte[] data = Hex.decodeHex(splitEncryptedSecret[1]);

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        // https://stackoverflow.com/questions/49755584/difference-between-ivparameterspec-and-gcmparameterspec-with-aes-gcm-nopadding
        cipher.init(Cipher.DECRYPT_MODE, getKeyFromPassword(rootSecret, salt), new GCMParameterSpec(128, iv));
        byte[] plainText = cipher.doFinal(data);
        return new String(plainText);
    }

    /**
     * https://www.baeldung.com/java-aes-encryption-decryption
     *
     * @param password
     * @param salt
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeySpecException
     */
    public static SecretKey getKeyFromPassword(String password, String salt)
            throws NoSuchAlgorithmException, InvalidKeySpecException {

        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
        KeySpec spec = new PBEKeySpec(password.toCharArray(), salt.getBytes(), 1000, 256);
        SecretKey secret = new SecretKeySpec(factory.generateSecret(spec)
                .getEncoded(), "AES");
        return secret;
    }

    public static void main(String[] args) {
        try {
            String rootKey = String.format("%s%s%s", "yVqsOYC8cgi5XiwjgjmGKyYhctLKZ6o18TdhKDwV084vVlE5mSSj05Fh0ITkK3YSeXWY7hae6kHH9VxGp\n",
                    "JbYPBxxLn8fR0gC5aLhmURiyM7mY7wfQHa7RwX6MoryljX2vY78C3kbzqZfj87MMFaJSdEWIGNX5m4WIFggJtUX\n",
                    "ONjOgjyLSJuLIDIvTzsOhDltVOwJ47l2Jp6zV9j8CzVGoJ98BKDwkmIFxS1wU3w8SzzQYnIcHqyQD7dn4pERPrpx");
            String decrypt = decryptSecret(rootKey, "b25fd269f0a7a5e2a835a2f4bd91e1d26895197648fb1129327da2936179ddd6",
                    "8d5aa658adac610021a7e90d-8b203b7197b26f67614b17da45933300a64543c4f5a1d8184593c73f3d1c13e9efdb76be06d6054ba36eb6011c3764a0");
            System.out.println(decrypt);
        } catch (Throwable e) {
            System.out.println(ExceptionUtils.getStackTrace(e));
        }
    }
}
