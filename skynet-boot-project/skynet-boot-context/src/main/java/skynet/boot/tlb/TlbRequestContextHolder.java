package skynet.boot.tlb;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.tlb.domain.TlbRequestHeader;


/**
 * 请求上下文持有者，用于存储和获取 TLB 请求头信息
 *
 * @date: 2023/5/9 17:19
 * @description:
 **/
@Slf4j
public class TlbRequestContextHolder {

    private static final TransmittableThreadLocal<TlbRequestHeader> HEADER_THREAD_LOCAL = new TransmittableThreadLocal<>();

    /**
     * 设置请求头信息
     *
     * @param chain    链信息
     * @param selector 选择器
     * @param bizId    业务ID
     */
    public static void setHeader(String chain, String selector, String bizId) {
        setHeader(new TlbRequestHeader(chain, selector, bizId));
    }

    /**
     * 设置请求头信息
     *
     * @param header 请求头信息
     */
    public static void setHeader(TlbRequestHeader header) {
        log.debug("setHeader header={}", header);
        HEADER_THREAD_LOCAL.set(header);
    }

    /**
     * 获取请求头信息
     *
     * @return 请求头信息
     */
    public static TlbRequestHeader getHeader() {
        return HEADER_THREAD_LOCAL.get();
    }

    /**
     * 更新链信息
     *
     * @param chain 链信息
     */
    public static void updateChain(String chain) {
        log.debug("updateChain={}", chain);
        TlbRequestHeader header = HEADER_THREAD_LOCAL.get();
        if (header != null) {
            header.setChain(chain);
        }
    }
} 