package skynet.boot.tlb.core;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * 服务向LB上报
 * </pre>
 */
@jakarta.annotation.Generated(
        value = "by gRPC proto compiler (version 1.64.0)",
        comments = "Source: Report.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ReportGrpc {

    private ReportGrpc() {
    }

    public static final java.lang.String SERVICE_NAME = "Report.Report";

    // Static method descriptors that strictly reflect the proto.
    private static volatile io.grpc.MethodDescriptor<skynet.boot.tlb.core.ReportOuterClass.ServiceInfo,
            skynet.boot.tlb.core.ReportOuterClass.ReportResponse> getReportServiceInfoMethod;

    @io.grpc.stub.annotations.RpcMethod(
            fullMethodName = SERVICE_NAME + '/' + "ReportServiceInfo",
            requestType = skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.class,
            responseType = skynet.boot.tlb.core.ReportOuterClass.ReportResponse.class,
            methodType = io.grpc.MethodDescriptor.MethodType.CLIENT_STREAMING)
    public static io.grpc.MethodDescriptor<skynet.boot.tlb.core.ReportOuterClass.ServiceInfo,
            skynet.boot.tlb.core.ReportOuterClass.ReportResponse> getReportServiceInfoMethod() {
        io.grpc.MethodDescriptor<skynet.boot.tlb.core.ReportOuterClass.ServiceInfo, skynet.boot.tlb.core.ReportOuterClass.ReportResponse> getReportServiceInfoMethod;
        if ((getReportServiceInfoMethod = ReportGrpc.getReportServiceInfoMethod) == null) {
            synchronized (ReportGrpc.class) {
                if ((getReportServiceInfoMethod = ReportGrpc.getReportServiceInfoMethod) == null) {
                    ReportGrpc.getReportServiceInfoMethod = getReportServiceInfoMethod =
                            io.grpc.MethodDescriptor.<skynet.boot.tlb.core.ReportOuterClass.ServiceInfo, skynet.boot.tlb.core.ReportOuterClass.ReportResponse>newBuilder()
                                    .setType(io.grpc.MethodDescriptor.MethodType.CLIENT_STREAMING)
                                    .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ReportServiceInfo"))
                                    .setSampledToLocalTracing(true)
                                    .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.getDefaultInstance()))
                                    .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.ReportOuterClass.ReportResponse.getDefaultInstance()))
                                    .setSchemaDescriptor(new ReportMethodDescriptorSupplier("ReportServiceInfo"))
                                    .build();
                }
            }
        }
        return getReportServiceInfoMethod;
    }

    /**
     * Creates a new async stub that supports all call types for the service
     */
    public static ReportStub newStub(io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<ReportStub> factory =
                new io.grpc.stub.AbstractStub.StubFactory<ReportStub>() {
                    @java.lang.Override
                    public ReportStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                        return new ReportStub(channel, callOptions);
                    }
                };
        return ReportStub.newStub(factory, channel);
    }

    /**
     * Creates a new blocking-style stub that supports unary and streaming output calls on the service
     */
    public static ReportBlockingStub newBlockingStub(
            io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<ReportBlockingStub> factory =
                new io.grpc.stub.AbstractStub.StubFactory<ReportBlockingStub>() {
                    @java.lang.Override
                    public ReportBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                        return new ReportBlockingStub(channel, callOptions);
                    }
                };
        return ReportBlockingStub.newStub(factory, channel);
    }

    /**
     * Creates a new ListenableFuture-style stub that supports unary calls on the service
     */
    public static ReportFutureStub newFutureStub(
            io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<ReportFutureStub> factory =
                new io.grpc.stub.AbstractStub.StubFactory<ReportFutureStub>() {
                    @java.lang.Override
                    public ReportFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                        return new ReportFutureStub(channel, callOptions);
                    }
                };
        return ReportFutureStub.newStub(factory, channel);
    }

    /**
     * <pre>
     * 服务向LB上报
     * </pre>
     */
    public interface AsyncService {

        /**
         * <pre>
         * 向LB上报服务器信息
         * </pre>
         */
        default io.grpc.stub.StreamObserver<skynet.boot.tlb.core.ReportOuterClass.ServiceInfo> reportServiceInfo(
                io.grpc.stub.StreamObserver<skynet.boot.tlb.core.ReportOuterClass.ReportResponse> responseObserver) {
            return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getReportServiceInfoMethod(), responseObserver);
        }
    }

    /**
     * Base class for the server implementation of the service Report.
     * <pre>
     * 服务向LB上报
     * </pre>
     */
    public static abstract class ReportImplBase
            implements io.grpc.BindableService, AsyncService {

        @java.lang.Override
        public final io.grpc.ServerServiceDefinition bindService() {
            return ReportGrpc.bindService(this);
        }
    }

    /**
     * A stub to allow clients to do asynchronous rpc calls to service Report.
     * <pre>
     * 服务向LB上报
     * </pre>
     */
    public static final class ReportStub
            extends io.grpc.stub.AbstractAsyncStub<ReportStub> {
        private ReportStub(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @java.lang.Override
        protected ReportStub build(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new ReportStub(channel, callOptions);
        }

        /**
         * <pre>
         * 向LB上报服务器信息
         * </pre>
         */
        public io.grpc.stub.StreamObserver<skynet.boot.tlb.core.ReportOuterClass.ServiceInfo> reportServiceInfo(
                io.grpc.stub.StreamObserver<skynet.boot.tlb.core.ReportOuterClass.ReportResponse> responseObserver) {
            return io.grpc.stub.ClientCalls.asyncClientStreamingCall(
                    getChannel().newCall(getReportServiceInfoMethod(), getCallOptions()), responseObserver);
        }
    }

    /**
     * A stub to allow clients to do synchronous rpc calls to service Report.
     * <pre>
     * 服务向LB上报
     * </pre>
     */
    public static final class ReportBlockingStub
            extends io.grpc.stub.AbstractBlockingStub<ReportBlockingStub> {
        private ReportBlockingStub(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @java.lang.Override
        protected ReportBlockingStub build(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new ReportBlockingStub(channel, callOptions);
        }
    }

    /**
     * A stub to allow clients to do ListenableFuture-style rpc calls to service Report.
     * <pre>
     * 服务向LB上报
     * </pre>
     */
    public static final class ReportFutureStub
            extends io.grpc.stub.AbstractFutureStub<ReportFutureStub> {
        private ReportFutureStub(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @java.lang.Override
        protected ReportFutureStub build(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new ReportFutureStub(channel, callOptions);
        }
    }

    private static final int METHODID_REPORT_SERVICE_INFO = 0;

    private static final class MethodHandlers<Req, Resp> implements
            io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
            io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
            io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
            io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
        private final AsyncService serviceImpl;
        private final int methodId;

        MethodHandlers(AsyncService serviceImpl, int methodId) {
            this.serviceImpl = serviceImpl;
            this.methodId = methodId;
        }

        @java.lang.Override
        @java.lang.SuppressWarnings("unchecked")
        public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
            switch (methodId) {
                default:
                    throw new AssertionError();
            }
        }

        @java.lang.Override
        @java.lang.SuppressWarnings("unchecked")
        public io.grpc.stub.StreamObserver<Req> invoke(
                io.grpc.stub.StreamObserver<Resp> responseObserver) {
            switch (methodId) {
                case METHODID_REPORT_SERVICE_INFO:
                    return (io.grpc.stub.StreamObserver<Req>) serviceImpl.reportServiceInfo(
                            (io.grpc.stub.StreamObserver<skynet.boot.tlb.core.ReportOuterClass.ReportResponse>) responseObserver);
                default:
                    throw new AssertionError();
            }
        }
    }

    public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
        return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
                .addMethod(
                        getReportServiceInfoMethod(),
                        io.grpc.stub.ServerCalls.asyncClientStreamingCall(
                                new MethodHandlers<
                                        skynet.boot.tlb.core.ReportOuterClass.ServiceInfo,
                                        skynet.boot.tlb.core.ReportOuterClass.ReportResponse>(
                                        service, METHODID_REPORT_SERVICE_INFO)))
                .build();
    }

    private static abstract class ReportBaseDescriptorSupplier
            implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
        ReportBaseDescriptorSupplier() {
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
            return skynet.boot.tlb.core.ReportOuterClass.getDescriptor();
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
            return getFileDescriptor().findServiceByName("Report");
        }
    }

    private static final class ReportFileDescriptorSupplier
            extends ReportBaseDescriptorSupplier {
        ReportFileDescriptorSupplier() {
        }
    }

    private static final class ReportMethodDescriptorSupplier
            extends ReportBaseDescriptorSupplier
            implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
        private final java.lang.String methodName;

        ReportMethodDescriptorSupplier(java.lang.String methodName) {
            this.methodName = methodName;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
            return getServiceDescriptor().findMethodByName(methodName);
        }
    }

    private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

    public static io.grpc.ServiceDescriptor getServiceDescriptor() {
        io.grpc.ServiceDescriptor result = serviceDescriptor;
        if (result == null) {
            synchronized (ReportGrpc.class) {
                result = serviceDescriptor;
                if (result == null) {
                    serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
                            .setSchemaDescriptor(new ReportFileDescriptorSupplier())
                            .addMethod(getReportServiceInfoMethod())
                            .build();
                }
            }
        }
        return result;
    }
}
