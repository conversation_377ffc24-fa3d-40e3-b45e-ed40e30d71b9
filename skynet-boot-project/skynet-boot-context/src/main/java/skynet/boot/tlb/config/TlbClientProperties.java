package skynet.boot.tlb.config;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.convert.DurationUnit;
import skynet.boot.common.domain.Jsonable;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * skynet.tlb.enabled=true
 * skynet.tlb.endpoints=172.31.234.57:3301,172.31.234.58:3301
 * skynet.tlb.http-endpoints=172.31.97.147:32101,172.31.97.147:32102
 * skynet.tlb.reportPeriod=3s
 * skynet.tlb.rpcTimeout=10s
 * skynet.tlb.smoothExitTimeout=60s
 *
 *
 * <p>
 * <p>
 * #TLS 的 是否开启
 * skynet.tlb.security.tlsEnabled=true
 * skynet.tlb.security.tlsAuthority=iflytek.com
 * #TSL 证书文件
 * skynet.tlb.security.tlsTrustCertCollectionFilePath=/........pem
 * <p>
 * #开启 call credentials 模式
 * skynet.tlb.security.authEnabled=false
 * # auth 用到的盐值
 * skynet.tlb.security.apiAuthSalt=xxxxxxxxxxxxxxx
 * #rootSecret file1 path
 * skynet.tlb.security.apiAuthRootSecret1FilePath=xxxxxxxx
 * rootSecret file2 path
 * skynet.tlb.security.apiAuthRootSecret2FilePath
 * <p>
 * rootSecret 的第三段和加密的 secret 在环境变量中，需要使用者自己 export 到环境变量里
 *
 * <AUTHOR>
 * @date 2019-11-22 09:53
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class TlbClientProperties extends Jsonable {

    /**
     * 是否启动 LB 客户端功能，默认：false
     */
    private boolean enabled = false;

    /**
     * LB服务的gPRC地址，如，172.31.97.147:33001,172.31.97.147:33002
     */
    private List<String> endpoints = new ArrayList<>(0);

    /**
     * LB服务的http地址，如，172.31.97.147:32101,172.31.97.147:32102
     */
    private List<String> httpEndpoints = new ArrayList<>(0);

    /**
     * 定时向LB汇报时间 间隔，默认3秒;(单位：秒)
     */
    @DurationUnit(ChronoUnit.SECONDS)
    private Duration reportPeriod = Duration.ofSeconds(3);

    /**
     * 同步接口请求超时时间,
     */
    @DurationUnit(ChronoUnit.SECONDS)
    private Duration rpcTimeout = Duration.ofSeconds(10);

    /**
     * 优雅退出超时时间，默认 60秒; (单位：秒)
     */
    @DurationUnit(ChronoUnit.SECONDS)
    private Duration smoothExitTimeout = Duration.ofSeconds(60);


    /**
     * 安全相关的配置
     */
    private LbSecurityProperties security = new LbSecurityProperties();


    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = true)
    public static class LbSecurityProperties extends Jsonable {

        /**
         * TLS 的 是否开启
         */
        private boolean tlsEnabled;

        /**
         * TLS 的 Authority
         */
        private String tlsAuthority;
        /**
         * TSL 证书文件
         */
        private String tlsTrustCertCollectionFilePath;

        /**
         * 开启 call credentials 模式
         */
        private boolean authEnabled;

        /**
         * auth 用到的盐值
         */
        private String apiAuthSalt;

        /**
         * rootSecret file1 path
         */
        private String apiAuthRootSecret1FilePath;
        /**
         * rootSecret file2 path
         */
        private String apiAuthRootSecret2FilePath;
    }
}
