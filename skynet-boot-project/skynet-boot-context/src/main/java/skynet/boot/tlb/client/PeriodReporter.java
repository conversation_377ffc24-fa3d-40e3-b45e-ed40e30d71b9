package skynet.boot.tlb.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import skynet.boot.tlb.client.data.TlbServiceInstance;

import java.time.Duration;


/**
 * 心跳汇报，防止 被LB server 认为超时 踢掉
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年11月20日 下午1:56:04]
 */
@Slf4j
class PeriodReporter implements AutoCloseable {

    /**
     * 控制线程是否运行变量
     */
    private volatile boolean isRun;
    private volatile TlbServiceInstance lastTlbServiceInstance;

    public PeriodReporter(OneTlbClient oneTlbClient, Duration timePeriod, TlbServiceInstance tlbServiceInstance) {

        Assert.notNull(oneTlbClient, "OneTlbClient is null.");
        Assert.notNull(timePeriod, "timePeriod is null.");
        Assert.notNull(tlbServiceInstance, "tlbServiceInstance is null.");
        log.debug("Periodically Report TimePeriod={}s, target={} Instance={}", timePeriod.getSeconds(), oneTlbClient.getUrl(), tlbServiceInstance);

        this.lastTlbServiceInstance = tlbServiceInstance;
        this.isRun = timePeriod.toMillis() > 0;

        if (this.isRun) {
            Thread thread = new Thread(() -> {
                while (isRun) {
                    try {
                        oneTlbClient.reportServiceInfoInnerWithCheckConnected(lastTlbServiceInstance);
                        log.debug("Periodically Report TimePeriod={}s, target={} Instance={}", timePeriod.getSeconds(), oneTlbClient.getUrl(), tlbServiceInstance);
                        Thread.sleep(timePeriod.toMillis());
                    } catch (Throwable e) {
                        log.error("Periodically report error ", e);
                    }
                }
                log.info("Stop report thread successfully.");
            });
            thread.setName("tlb-reporter-thread");
            thread.start();
        }
    }

    public void setDetailServiceInfo(TlbServiceInstance tlbServiceInstance) {
        this.lastTlbServiceInstance = tlbServiceInstance;
    }


    public TlbServiceInstance getLastDetailServiceInfo() {
        return lastTlbServiceInstance;
    }

    @Override
    public void close() throws Exception {
        this.isRun = false;
    }
}
