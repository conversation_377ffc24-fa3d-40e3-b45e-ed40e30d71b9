package skynet.boot.tlb.client.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;

/**
 * LB 状态类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class TlbStatus extends Jsonable {

    /**
     * 表示所有 LB 连接是否正常的状态
     * 全部 LB 连接正常时 status = true
     * 有 LB 连接不正常 status = false
     */
    private boolean up = true;

    /**
     * 保存连接不正常的 LB ip:port 信息
     * 当 status = true 时，badLbStatusList.size() = 0
     * 当 status = false 时，badLbStatusList.size() 表示不正常 LB 个数
     */
    private List<OneTlbStatus> endpoints = new ArrayList<>();

}
