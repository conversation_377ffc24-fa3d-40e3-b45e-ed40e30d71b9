package skynet.boot.tlb.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

@Getter
@Setter
@Accessors(chain = true)
public class Tag {

    private Set<String> tags;
    private TagLogic logic = TagLogic.AND;

    @Override
    public String toString() {
        if (tags == null) return null;

        switch (logic) {
            case AND:
                return StringUtils.join(tags, "&&");
            case OR:
                return StringUtils.join(tags, "||");
            case NOT:
                return String.format("!(%s)", StringUtils.join(tags, "&&"));
            default:
                return null;
        }
    }
}
