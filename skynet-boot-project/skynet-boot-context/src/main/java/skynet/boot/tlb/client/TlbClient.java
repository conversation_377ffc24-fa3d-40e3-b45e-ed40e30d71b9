package skynet.boot.tlb.client;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.Assert;
import skynet.boot.tlb.client.data.*;
import skynet.boot.tlb.domain.BestServerParam;
import skynet.boot.tlb.domain.BestServerResult;
import skynet.boot.tlb.exception.TlbException;
import skynet.boot.tlb.exception.TlbNotFoundSvcException;

import java.io.File;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

/**
 * 备注：LB 服务端通过 客户端连接 区分 不同服务名称的上报者，如果是同一个连接，汇报不同服务名称，路数会覆盖，所以 不同的服务名汇报者 需要不同的LBClient 实例。
 * <p>
 * The Class TlbClient.
 *
 * <AUTHOR>
 */
@Slf4j
public class TlbClient implements AutoCloseable {
    /**
     * Gets the tlb 地址,数量 >= 1,英文分号分隔.
     */
    @Getter
    private List<String> endpoints;

    /**
     * 默认定时上报时间间隔.
     */
    public static final Duration DEFAULT_TIME_PERIOD = Duration.ofMillis(1500);

    /**
     * 上报时间范围最大值
     */
    private static final long REPORT_MAX_TIME_PERIOD = 1500;

    /**
     * 上报时间范围最小值
     */
    private static final long REPORT_MIN_TIME_PERIOD_MS = 100;


    /**
     * lbClient 列表.
     */
    private final List<OneTlbClient> lbClientList = new ArrayList<>();

//    private final Set<String> bizIdSet = new java.util.concurrent.CopyOnWriteArraySet<>();

    /**
     * 记录上一次访问的 tlb 位置.
     */
    private volatile int lbIndex = 0;

    /**
     * 默认同步请求超时时间 10s
     */
    private static final Duration DEFAULT_RPC_TIMEOUT = Duration.ofSeconds(10);

    /**
     * <pre>
     *  LB 负载均衡 客户端
     *
     *  定时上报时间间隔, 缺省 3000 毫秒
     *  gRPC 缺省时间间隔 20s
     * </pre>
     *
     * @param endpoints 如 127.0.0.1:33000;127.0.0.1:33000
     * @throws TlbException 异常
     */
    public TlbClient(List<String> endpoints) throws TlbException {
        this(endpoints, DEFAULT_TIME_PERIOD);
    }

    /**
     * @param endpoints  如 127.0.0.1:33000;127.0.0.1:33000
     * @param timePeriod 定时上报时间间隔, 单位：毫秒, 如为0, 将不进行定时上报
     * @throws TlbException 异常
     */
    public TlbClient(List<String> endpoints, Duration timePeriod) throws TlbException {
        this(endpoints, timePeriod, DEFAULT_RPC_TIMEOUT);
    }

    /**
     * LB 负载均衡 客户端
     *
     * @param endpoints  如 127.0.0.1:33000;127.0.0.1:33000
     * @param timePeriod 定时上报时间间隔, 单位：毫秒, 如为0, 将不进行定时上报
     * @param rpcTimeout rpc 请求超时时间
     * @throws TlbException 异常
     */
    public TlbClient(List<String> endpoints, Duration timePeriod, Duration rpcTimeout) throws TlbException {
        TlbClientInitParam cip = new TlbClientInitParam().setTimePeriod(timePeriod)
                .setRpcTimeout(rpcTimeout);
        this.initLbClient(endpoints, cip);
    }

    /**
     * tls 的 LB 负载均衡 客户端
     *
     * @param endpoints                   如 127.0.0.1:33000;127.0.0.1:33000
     * @param trustCertCollectionFilePath 证书文件路径
     * @param authority
     * @throws TlbException 异常
     */
    public TlbClient(List<String> endpoints, String trustCertCollectionFilePath, String authority) throws TlbException {
        this(endpoints, DEFAULT_TIME_PERIOD, DEFAULT_RPC_TIMEOUT, trustCertCollectionFilePath, authority);
    }

    /**
     * tls 的 LB 负载均衡 客户端
     *
     * @param endpoints                   如 127.0.0.1:33000;127.0.0.1:33000
     * @param rpcTimeout                  rpc 请求超时时间
     * @param trustCertCollectionFilePath 证书文件路径
     * @param authority
     * @throws TlbException 异常
     */
    public TlbClient(List<String> endpoints, Duration rpcTimeout, String trustCertCollectionFilePath, String authority) throws TlbException {
        this(endpoints, DEFAULT_TIME_PERIOD, rpcTimeout, trustCertCollectionFilePath, authority);
    }

    /**
     * tls 的 LB 负载均衡 客户端
     *
     * @param endpoints                   如 127.0.0.1:33000;127.0.0.1:33000
     * @param timePeriod                  定时汇报间隔
     * @param rpcTimeout                  rpc 请求超时时间
     * @param trustCertCollectionFilePath 证书文件路径
     * @param authority
     * @throws TlbException 异常
     */
    public TlbClient(List<String> endpoints, Duration timePeriod, Duration rpcTimeout, String trustCertCollectionFilePath, String authority) throws TlbException {
        if (StringUtils.isBlank(authority)) {
            throw new TlbException("authority is blank");
        }
        log.info("[TlbClient with tls] url: {}, timePeriod: {}, rpcTimeout: {}, trustCertCollectionFilePath:" +
                " {}, authority: {}", endpoints, timePeriod, rpcTimeout, trustCertCollectionFilePath, authority);
        File trustCertCollectionFile = getValidTrustCertCollectionFileOrException(trustCertCollectionFilePath);
        TlbClientInitParam cip = new TlbClientInitParam().setTimePeriod(timePeriod)
                .setRpcTimeout(rpcTimeout).setAuthority(authority)
                .setTrustCertCollectionFile(trustCertCollectionFile);
        this.initLbClient(endpoints, cip);
    }

    public TlbClient(List<String> endpoints, TlbClientInitParam cip) throws TlbException {
        this.initLbClient(endpoints, cip);
    }

    public void initLbClient(List<String> endpoints, TlbClientInitParam cip) throws TlbException {
        Assert.notEmpty(endpoints, "endpoints is empty.");
        log.info("Init TlbClient with cip: {}", cip);
        cip.setTimePeriod(tuningTimePeriod(cip.getTimePeriod()));
        for (String endpoint : endpoints) {
            OneTlbClient oneTlbClient = new OneTlbClient(endpoint, cip);
            lbClientList.add(oneTlbClient);
        }

        /* 打印 有效的 lbClient 个数 */
        log.info("lbClientList size= {}", lbClientList.size());
        this.endpoints = endpoints;
    }


    /**
     * 调整用户传入的定时汇报时间间隔
     *
     * @param timePeriod 构造 TlbClient 传入的定时汇报时间间隔
     * @return 经过调整后的定时汇报时间间隔
     */
    private Duration tuningTimePeriod(Duration timePeriod) {
        /* timePeriod <= 0 表示不使用定时上报功能 */
        if (timePeriod.toMillis() > 0) {
            /* 自定义上报时间范围检测 范围之外时间取默认上报时间 */
            timePeriod = timePeriod.toMillis() >= REPORT_MIN_TIME_PERIOD_MS && timePeriod.toMillis() <= REPORT_MAX_TIME_PERIOD
                    ? timePeriod : DEFAULT_TIME_PERIOD;
            log.info("after tuning, timePeriod={}", timePeriod);
        }
        return timePeriod;
    }

    private File getValidTrustCertCollectionFileOrException(String trustCertCollectionFilePath) throws TlbException {
        if (StringUtils.isBlank(trustCertCollectionFilePath)) {
            log.error("trustCertCollectionFilePath is blank");
            throw new IllegalArgumentException("trustCertCollectionFilePath is blank.");
        }

        File trustCertCollectionFile = new File(trustCertCollectionFilePath);
        if (trustCertCollectionFile.exists() && trustCertCollectionFile.isFile()) {
            /* file 文件存在并且是文件时，检查通过 */
            return trustCertCollectionFile;
        }
        throw new TlbException(String.format("can't find valid trustCertCollectionFile, input trustCertCollectionFilePath: %s",
                trustCertCollectionFilePath));
    }

    /**
     * <p>
     * 获取 最优服务
     * </p>
     *
     * @param bestServerParam 最优服务参数
     * @return 最优服务信息
     * @throws TlbException 异常
     */
    public BestServerResult getBestServer(BestServerParam bestServerParam) throws TlbException {
        Assert.notNull(bestServerParam, "The bestServerParam is null");
        Assert.hasText(bestServerParam.getServiceName(), "The ServiceName is blank.");

        log.debug("TlbClient.getBestServer bestServerParam= {}", bestServerParam);

        int _lbIndex = lbIndex;
        for (int index = 0; index < lbClientList.size(); index++) {
            OneTlbClient oneTlbClient = getOneLbClient(_lbIndex + index);

            /* 过滤不可用 LB 服务 */
            if (oneTlbClient.isAvailable()) {
                try {
                    return oneTlbClient.getBestServer(bestServerParam);
                } catch (Throwable e) {
                    /* 标记 LB 服务不可达 */
                    oneTlbClient.setDisabled();
                    log.warn("getBestServer exception, set available=false, tlb url={}; err={}", oneTlbClient.getUrl(), e.getMessage());
                }
            } else {
                log.debug("skip unavailable LB service: {}", oneTlbClient.getUrl());
            }
        }

        /* oneLbClient 内部线程间隔 5s 把 oneLbClient 设置成可用，
         * 如果某时刻所有 LB 服务都不可用（如网路断开1s后又恢复），经过
         * 上面的循环所有 oneLbClient 都被标记不可用，但从网络断开的
         * 第 2s 到第 5s 实际上是可用。防止给 TlbClient 调用者造成"可
         * 用延迟"现象所以再添加一次循环。
         *
         * 下面的循环目的是，没找到可用 oneLbClient 时遍历所有 oneLbClient，
         * 但发现有不可用的 oneLbClient 时不要重复标记不可用，造成不可用时间延长。
         * */
        log.debug("There is no available OneTlbClient, start to find LB service from whole LB set.");

        _lbIndex = lbIndex;
        for (int index = 0; index < lbClientList.size(); index++) {
            OneTlbClient oneTlbClient = getOneLbClient(_lbIndex + index);
            try {
                return oneTlbClient.getBestServer(bestServerParam);
            } catch (Throwable e) {
                log.error(String.format("getBestServer exception, tlb endpoint= %s", oneTlbClient.getUrl()), e);
            }
        }
        log.warn("Not Found The Service [{}] From LB.", bestServerParam.getServiceName());
        throw new TlbNotFoundSvcException(bestServerParam.getServiceName());
    }


    /**
     * <p>
     * 获取 最优服务
     * </p>
     *
     * @param bestServerParam 最优服务参数
     * @return 最优服务信息
     * @throws TlbException 异常
     */
    public TlbServiceInstanceResponse getBestServerInstances(BestServerParam bestServerParam) throws TlbException {
        Assert.notNull(bestServerParam, "The bestServerParam is null");
        Assert.hasText(bestServerParam.getServiceName(), "The ServiceName is blank.");

        log.debug("TlbClient.getBestServerInstances bestServerParam= {}", bestServerParam);

        int _lbIndex = lbIndex;
        for (int index = 0; index < lbClientList.size(); index++) {
            OneTlbClient oneTlbClient = getOneLbClient(_lbIndex + index);

            /* 过滤不可用 LB 服务 */
            if (oneTlbClient.isAvailable()) {
                try {
                    return oneTlbClient.getBestServerInstances(bestServerParam);
                } catch (Throwable e) {
                    /* 标记 LB 服务不可达 */
                    oneTlbClient.setDisabled();
                    log.warn("getBestServer exception, set available=false, tlb url={}; err={}", oneTlbClient.getUrl(), e.getMessage());
                }
            } else {
                log.debug("skip unavailable LB service: {}", oneTlbClient.getUrl());
            }
        }

        /* oneLbClient 内部线程间隔 5s 把 oneLbClient 设置成可用，
         * 如果某时刻所有 LB 服务都不可用（如网路断开1s后又恢复），经过
         * 上面的循环所有 oneLbClient 都被标记不可用，但从网络断开的
         * 第 2s 到第 5s 实际上是可用。防止给 TlbClient 调用者造成"可
         * 用延迟"现象所以再添加一次循环。
         *
         * 下面的循环目的是，没找到可用 oneLbClient 时遍历所有 oneLbClient，
         * 但发现有不可用的 oneLbClient 时不要重复标记不可用，造成不可用时间延长。
         * */
        log.debug("There is no available OneTlbClient, start to find LB service from whole LB set.");

        _lbIndex = lbIndex;
        for (int index = 0; index < lbClientList.size(); index++) {
            OneTlbClient oneTlbClient = getOneLbClient(_lbIndex + index);
            try {
                return oneTlbClient.getBestServerInstances(bestServerParam);
            } catch (Throwable e) {
                log.error(String.format("getBestServer exception, tlb endpoint= %s", oneTlbClient.getUrl()), e);
            }
        }
        log.warn("Not Found The Service [{}] From LB.", bestServerParam.getServiceName());
        throw new TlbNotFoundSvcException(bestServerParam.getServiceName());
    }

    /**
     * <p>
     * 获取所有服务信息列表
     * </p>
     *
     * @return 服务信息
     * @throws TlbException 异常
     */
    public TlbServiceInstanceResponse getServiceInfo() throws TlbException {
        log.debug("TlbClient.getServiceInfo");
        return this.fetchServiceInfo("");
    }

    /**
     * <p>
     * 根据 服务名称 获取服务信息
     * </p>
     *
     * @param serviceName 服务名称，如 ast，如果为空，将返回所有的 服务节点信息
     * @return 服务信息
     * @throws TlbException 异常
     */
    public TlbServiceInstanceResponse getServiceInfo(String serviceName) throws TlbException {
        Assert.hasText(serviceName, "serviceName is empty");
        return this.fetchServiceInfo(serviceName);
    }

    private TlbServiceInstanceResponse fetchServiceInfo(String serviceName) throws TlbException {
        log.debug("TlbClient.fetchServiceInfo serviceName = {}", serviceName);
        // 避免线程安全问题，将 lbIndex 缓存下来，两个线程同时访问时，lbIndex 取值可能不是 0 1 2 按顺序，可能是 0 1 0
        int _lbIndex = lbIndex;
        for (int index = 0; index < lbClientList.size(); index++) {
            OneTlbClient oneTlbClient = getOneLbClient(_lbIndex + index);
            try {
                return oneTlbClient.getServiceInfo(serviceName);
            } catch (Throwable e) {
                log.error(String.format("getServiceInfo connect tlb exception, tlb url= %s", oneTlbClient.getUrl()), e);
            }
        }
        log.warn("no tlb available at present, input url= {}", endpoints);
        throw new TlbNotFoundSvcException(serviceName);
    }

    private synchronized OneTlbClient getOneLbClient(int index) throws TlbException {
        index = index % lbClientList.size();
        log.debug("You are about to access the {}th LB [url={}]", index, lbClientList.get(index).getUrl());
        return lbClientList.get(index);
    }

    /**
     * <p>
     * 汇报 服务信息
     * </p>
     *
     * @param tlbServiceInstance 需要上报的服务信息
     * @throws TlbException 异常
     */
    public void reportServiceInfo(TlbServiceInstance tlbServiceInstance) throws TlbException {
        log.debug("TlbClient.reportServiceInfo detailServiceInfo: {}", tlbServiceInstance);
        Assert.notNull(tlbServiceInstance, "The detailServiceInfo is null");
        Assert.hasText(tlbServiceInstance.getServiceName(), "The ServiceName is blank.");

        if (lbClientList.isEmpty()) {
            throw new TlbException(String.format("The endpoints is invalid , input endpoints= %s", endpoints));
        }

        for (OneTlbClient oneTlbClient : lbClientList) {
            try {
                oneTlbClient.reportServiceInfo(tlbServiceInstance);
            } catch (Throwable e) {
                log.error("reportServiceInfo connect tlb exception, tlb endpoint= {}", oneTlbClient.getUrl());
            }
        }
    }


    /**
     * 增加tag
     *
     * @param tag
     * @throws TlbException
     */
    public void addTag(String tag) throws TlbException {
        Assert.hasText(tag, "The tag is empty");
        log.debug("TlbClient.addTags tag: {}", tag);
        executeAction(client -> client.addTags(Collections.singletonList(tag)));
    }

    /**
     * 清理tag
     *
     * @throws TlbException
     */
    public void clearTag() throws TlbException {
        log.debug("TlbClient.clearTag.");
        executeAction(OneTlbClient::clearTags);
    }

    /**
     * 移除tag
     *
     * @param tag
     * @throws Exception
     */
    public void removeTag(String tag) throws Exception {
        Assert.hasText(tag, "The tag is empty");
        log.debug("TlbClient.removeTags tag: {}", tag);
        executeAction(client -> client.removeTags(Collections.singletonList(tag)));
    }

    public void addBizId(String bizId) throws TlbException {
        Assert.hasText(bizId, "The bizId is empty");
        log.debug("TlbClient.addBizId bizId: {}", bizId);
        executeAction(client -> client.addBizId(Collections.singletonList(bizId)));
    }

    public void clearBizId() throws TlbException {
        log.debug("TlbClient.clearBizId.");
        executeAction(OneTlbClient::clearBizId);
    }

    public void removeBizId(String bizId) throws Exception {
        Assert.hasText(bizId, "The bizId is empty");
        log.debug("TlbClient.removeBizId bizId: {}", bizId);
        executeAction(client -> client.removeBizId(Collections.singletonList(bizId)));
    }

    private void executeAction(Consumer<OneTlbClient> action) {
        for (OneTlbClient oneTlbClient : lbClientList) {
            try {
                action.accept(oneTlbClient);
            } catch (Throwable e) {
                log.error("executeAction connect tlb exception, tlb url= {}", oneTlbClient.getUrl());
            }
        }
    }

    public TlbStatus checkLbStatus() {
        List<OneTlbStatus> endpoints = new ArrayList<>();
        boolean up = true;
        for (OneTlbClient oneTlbClient : lbClientList) {
            OneTlbStatus oneTlbStatus = new OneTlbStatus(oneTlbClient.getUrl());
            try {
                String version = oneTlbClient.getVersion();
                oneTlbStatus.setVersion(version);
                //   TlbServiceInstanceResponse lbServiceInstanceResponse =  oneTlbClient.getServiceInfoForCheck("");
            } catch (Throwable e) {
                oneTlbStatus.setFailedReason(ExceptionUtils.getStackTrace(e));
                up = false;
            }
            endpoints.add(oneTlbStatus);
        }
        TlbStatus tlbStatus = new TlbStatus();
        tlbStatus.setEndpoints(endpoints);
        tlbStatus.setUp(up);
        return tlbStatus;
    }

    @Override
    public void close() throws Exception {
        for (OneTlbClient oneTlbClient : lbClientList) {
            oneTlbClient.close();
        }
    }
}