package skynet.boot.tlb.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import io.grpc.*;
import io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.NettyChannelBuilder;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import skynet.boot.common.SpringUtils;
import skynet.boot.tlb.client.data.ReportResult;
import skynet.boot.tlb.client.data.TlbClientInitParam;
import skynet.boot.tlb.client.data.TlbServiceInstance;
import skynet.boot.tlb.client.data.TlbServiceInstanceResponse;
import skynet.boot.tlb.config.TlbClientProperties;
import skynet.boot.tlb.core.Lb;
import skynet.boot.tlb.core.LoadBalanceGrpc;
import skynet.boot.tlb.core.ReportGrpc;
import skynet.boot.tlb.core.ReportOuterClass;
import skynet.boot.tlb.domain.*;
import skynet.boot.tlb.exception.TlbException;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * LB 负载均衡 客户端
 *
 * <AUTHOR> [2018年10月9日 下午12:07:29]
 */
@Slf4j
public class OneTlbClient implements AutoCloseable {

    private static final Pattern PATTERN = Pattern.compile("^\\{.*\\}$");

    /**
     * TuningAvailableThread 休眠间隔
     */
    private static long TUNING_AVAILABLE_THREAD_INTERVAL_MS = 1000;

    private static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(200, TimeUnit.SECONDS)
            .readTimeout(200, TimeUnit.SECONDS).build();

    private static final String HTTP_URL = "http://%s/monitor/getserviceinfo?svcname=%s&tags=%s";

    /**
     * tlb server 端 ip:port, 例如:127.0.0.1:33000
     */
    private final String url;
    /**
     * 初始化 Client 的参数，这个参数包含了初始化 OneTlbClient 的所有参数，放在一个类里方便将来初始化参数的扩展，
     * 这个类是在业务方提出接口级别的鉴权时加上的，代码中根据此对象是否为空来判断是否要开启接口鉴权，为了让接口兼容，
     * 没有把所有的构造方法传参都改成这个参数。
     */
    private final TlbClientInitParam cip;

    private final ManagedChannel channel;
    private final Set<String> currentBizIdSet = new CopyOnWriteArraySet<>();
    /**
     * 上报 bizId 一组的大小
     */
    private final int pageSize = 100;

    private final ReentrantLock lock = new ReentrantLock();

    private final AtomicInteger retryCount = new AtomicInteger(0);

    private static final int MAX_RETRY_COUNT = 10;

    /**
     * 布尔值表示 gRPC 连接中
     */
    private final AtomicBoolean isBreak = new AtomicBoolean(false);

    /**
     * tlb-sdk-restored-addBizId-thread 线程是否运行
     */
    private final AtomicBoolean isCheckRestore = new AtomicBoolean(true);

    /**
     * tlb-sdk-restored-addBizId-thread 线程休眠间隔
     */
    private final long RESTORED_PERIOD = 1500;

    /**
     * onError 方法休眠时间 目的是减少 gRPC 连接断开时 cpu 使用率
     */
    private static final long ON_ERROR_THREAD_SLEEP_MS = 1000;

    /**
     * 上一次打印 error 日志时间
     */
    private final AtomicLong lastPrintErrorTime = new AtomicLong(0);

    /**
     * onError 日志打印间隔, 30000ms
     */
    private final AtomicLong errLogPeriod = new AtomicLong(30000);

    /**
     * 标记对应的 LB 是否可用
     */
    private final AtomicBoolean available = new AtomicBoolean(true);

    /**
     * blockingStub.
     */
    private LoadBalanceGrpc.LoadBalanceBlockingStub loadBalanceBlockingStub;

    /**
     * grpc async stub.
     */
    private ReportGrpc.ReportStub reportAsyncStub;

    /**
     * grpc streamObserver.
     */
    private StreamObserver<ReportOuterClass.ReportResponse> responseObserver;

    /**
     * grpc requestObserver.
     */
    private StreamObserver<ReportOuterClass.ServiceInfo> requestObserver;

    /**
     * 定时汇报线程.
     */
    private PeriodReporter periodReporter;

    private final ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor();


    public OneTlbClient(String url, TlbClientInitParam cip) throws TlbException {
        Assert.hasText(url, "The url is empty.");
        Assert.notNull(cip, "The TlbClientInitParam is null.");

        log.debug("url: {}, TlbClientInitParam: {} ", url, cip);
        try {
            this.channel = buildManagedChannel(url, cip);
        } catch (Throwable e) {
            log.error("create channel exception", e);
            throw new TlbException(ExceptionUtils.getStackTrace(e));
        }

        this.cip = cip;
        this.url = url;
        this.initGrpcObserver();
        this.restoredAddBizIdThread();
        TUNING_AVAILABLE_THREAD_INTERVAL_MS = Long.parseLong(System.getProperty("skynet.boot.discovery.tlb.available.thread.interval.ms", "1000"));
        log.debug("TUNING_AVAILABLE_THREAD_INTERVAL_MS = {}ms", TUNING_AVAILABLE_THREAD_INTERVAL_MS);
    }


    private static ManagedChannel buildManagedChannel(String url, TlbClientInitParam cip) throws Exception {
        log.debug("created channel.");
        NettyChannelBuilder nettyChannelBuilder = NettyChannelBuilder.forTarget(url);
        if (cip.isEnableCallCredentials()) {
            nettyChannelBuilder = nettyChannelBuilder.overrideAuthority(cip.getAuthority());
            if (cip.getTrustCertCollectionFile() != null) {
                nettyChannelBuilder.sslContext(GrpcSslContexts.forClient().trustManager(cip.getTrustCertCollectionFile()).build());
            }
        } else {
            nettyChannelBuilder.usePlaintext();
        }
        Map<String, Object> retryPolicy = Map.of(
                "methodConfig", List.of(
                        Map.of(
                                "name", List.of(
                                        Map.of()  // 不指定 service 和 method，即为全局配置
                                ),
                                "retryPolicy", Map.of("maxAttempts", "5",  // 包括初始请求的最大重试次数
                                        "initialBackoff", "0.2s",  // 初始回退时间
                                        "maxBackoff", "1s",  // 最大回退时间
                                        "backoffMultiplier", 1.5,  // 回退倍数
                                        "retryableStatusCodes", List.of("UNAVAILABLE", "RESOURCE_EXHAUSTED")  // 触发重试的状态码
                                )
                        )));
        return nettyChannelBuilder.keepAliveTime(1, TimeUnit.SECONDS)  // 每 30 秒发送心跳包
                .keepAliveTimeout(10, TimeUnit.SECONDS) // 等待 10 秒确认
                .enableRetry()
                .defaultServiceConfig(retryPolicy).build();
    }

    /**
     * https://www.cnblogs.com/niejunlei/p/14995433.html
     *
     * @param cip
     * @return
     */
    private static CallCredentials buildCallCredentials(TlbClientInitParam cip) {
        return new CallCredentials() {
            @Override
            public void applyRequestMetadata(RequestInfo requestInfo, Executor appExecutor, MetadataApplier applier) {
                Metadata metadata = new Metadata();
                try {
                    // 认证信息
                    metadata.put(Metadata.Key.of("authcode", Metadata.ASCII_STRING_MARSHALLER),
                            CallCredentialUtil.buildAuthCode(cip.getRootSecret(), cip.getSalt(), cip.getEncryptedSecret()));
                } catch (Throwable e) {
                    log.error("add authcode to metadata failed.", e);
                }
                applier.apply(metadata);
            }

            @Override
            public void thisUsesUnstableApi() {
            }
        };
    }

    /**
     * <p>
     * 重连 tlb 之后,属性重新获取
     * </p>
     */
    private void initGrpcObserver() {
        if (!lock.tryLock()) {
            log.warn("initGrpcObserver is already running, skipping re-entry.");
            return;
        }
        try {
            this.loadBalanceBlockingStub = LoadBalanceGrpc.newBlockingStub(this.channel);
            this.reportAsyncStub = ReportGrpc.newStub(this.channel);

            // 授权模式
            if (this.cip != null && this.cip.isEnableCallCredentials()) {
                CallCredentials callCredentials = buildCallCredentials(cip);
                this.loadBalanceBlockingStub = this.loadBalanceBlockingStub.withCallCredentials(callCredentials);
                this.reportAsyncStub = this.reportAsyncStub.withCallCredentials(callCredentials);
            }

            log.info("initGrpcObserver version: {}", getVersion());

            this.responseObserver = new StreamObserver<>() {
                @Override
                public void onNext(ReportOuterClass.ReportResponse reportResponse) {
                    if (reportResponse != null) {
                        ReportResult reportResult = new ReportResult();
                        reportResult.setErrorCode(reportResponse.getErrorCode());
                        reportResult.setErrorInfo(reportResponse.getErrorInfo());
                        log.debug("StreamObserver onNext ReportResult = {}", reportResult);
                    } else {
                        log.error("reportResponse is null, error");
                    }
                }

                @Override
                public void onError(Throwable throwable) {
                    handleGrpcError(throwable);
                }

                @Override
                public void onCompleted() {
                    log.info("onCompleted");
                }
            };

            this.requestObserver = reportAsyncStub.reportServiceInfo(responseObserver);
            retryCount.set(0); // 成功初始化后重置重试计数
        } finally {
            lock.unlock();
        }
    }

    private void handleGrpcError(Throwable throwable) {
        if (throwable instanceof StatusRuntimeException) {
            if (System.currentTimeMillis() - lastPrintErrorTime.get() > errLogPeriod.get()) {
                lastPrintErrorTime.set(System.currentTimeMillis());
                log.error("gRPC connection error: ", throwable);
                log.error("tlb connect maybe failed, tlb url={}", url);
            }
            isBreak.set(true);

            if (retryCount.incrementAndGet() > MAX_RETRY_COUNT) {
                log.error("Reached max retry count, will not retry initGrpcObserver");
                return;
            }

            scheduledExecutor.schedule(this::initGrpcObserver, ON_ERROR_THREAD_SLEEP_MS, TimeUnit.MILLISECONDS);
        } else {
            log.error("Unknown gRPC error: ", throwable);
            log.error("tlb connect maybe failed, tlb url={}", url);
        }
    }

    private void restoredAddBizIdThread() {
        scheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                if (isBreak.get() && getGRPCConnected()) {
                    if (!currentBizIdSet.isEmpty()) {
                        try {
                            bizIdOpt(BizTypeEnum.bizId, BizIdCmdEnum.add, new ArrayList<>(currentBizIdSet));
                            isBreak.set(false);
                            log.info("after gRPC connection restored, addBizId() successful, tlb.url={}", url);
                        } catch (Throwable e) {
                            log.error("after gRPC connection restored, addBizId() exception, tlb.url={}", url, e);
                        }
                    } else {
                        isBreak.set(false);
                    }
                }
            } catch (Throwable e) {
                log.error("restored addBizId exception ", e);
            }
        }, 0, RESTORED_PERIOD, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取最优服务
     * <p>
     * 当：ErrorCode ==0 时 才获取成功。
     *
     * @param bestServerParam
     * @return
     * @throws Exception
     */
    public BestServerResult getBestServer(BestServerParam bestServerParam) throws Exception {
        try {
            final Lb.ServerAddresses[] serverAddresses = new Lb.ServerAddresses[1];
            Lb.ServerParamEx serverParamEx = buildServerParamEx(bestServerParam);
            long begin = System.currentTimeMillis();
            serverAddresses[0] = loadBalanceBlockingStub.withDeadlineAfter(cip.getRpcTimeout().toMillis(), TimeUnit.MILLISECONDS).getBestServerEx(serverParamEx);
            log.debug("getBestServerEx elapsed time={}ms", System.currentTimeMillis() - begin);
            return mappingServerAddressToServerResult(serverAddresses[0], bestServerParam.getServiceName());
        } catch (StatusRuntimeException e) {
            log.error("getBestServer gRPC StatusRuntimeException {}", bestServerParam, e);
            BestServerResult bestServer = getBestServer(bestServerParam.getServiceName(), bestServerParam.getTagSelector());
            log.debug("getBestServer gRPC bestServerResult {}", bestServer);
            return bestServer;
        } catch (Throwable e) {
            log.error("TlbClient getBestServer() function exception." + bestServerParam, e);
            throw e;
        }
    }

    /**
     * 获取最佳服务实例信息（包含meta，tag）
     *
     * @param bestServerParam
     * @return
     * @throws Exception
     */
    public TlbServiceInstanceResponse getBestServerInstances(BestServerParam bestServerParam) throws Exception {
        try {
            final Lb.ServiceInfoList[] serviceInfoList = new Lb.ServiceInfoList[1];
            Lb.ServerParamEx serverParamEx = buildServerParamEx(bestServerParam);
            long begin = System.currentTimeMillis();
            serviceInfoList[0] = loadBalanceBlockingStub.withDeadlineAfter(cip.getRpcTimeout().toMillis(), TimeUnit.MILLISECONDS).getBestServerInstances(serverParamEx);
            log.debug("getBestServerInstances elapsed time={}ms", System.currentTimeMillis() - begin);
            return convertLbServiceInstanceResponse(serviceInfoList[0]);
        } catch (StatusRuntimeException e) {
            log.error("getBestServerInstances gRPC StatusRuntimeException {}", bestServerParam, e);
            TlbServiceInstanceResponse tlbServiceInstanceResponse = getBestServerInstances(bestServerParam.getServiceName(), bestServerParam.getTagSelector());
            log.error("getBestServerInstances http tlbServiceInstanceResponse {}", tlbServiceInstanceResponse);
            return tlbServiceInstanceResponse;
        } catch (Throwable e) {
            log.error("TlbClient getBestServerInstances() function exception." + bestServerParam, e);
            throw e;
        }
    }

    /**
     * 获取最优服务
     * <p>
     * 当：ErrorCode ==0 时 才获取成功。
     *
     * @param bestServerParam
     * @return
     * @throws Exception
     */
    private Lb.ServerParamEx buildServerParamEx(BestServerParam bestServerParam) {
        Assert.notNull(bestServerParam, "The bestServerParam is null.");
        if (StringUtils.isBlank(bestServerParam.getServiceName())) {
            throw new TlbException("The ServiceName is blank.");
        }
        Lb.ServerParamEx.Builder builder = Lb.ServerParamEx.newBuilder().setServerName(bestServerParam.getServiceName()).setNumber(bestServerParam.getNumber())
                .putAllServiceInfo(bestServerParam.getServiceInfo())
                .putServiceInfo("bizId", StringUtils.isBlank(bestServerParam.getBizId()) ? "" : bestServerParam.getBizId().trim())
                .putServiceInfo("reqType", StringUtils.isBlank(bestServerParam.getReqType()) ? "" : bestServerParam.getReqType());

        if (StringUtils.isNotBlank(bestServerParam.getTagSelector())) {
            builder.putServiceInfo("tag", bestServerParam.getTagSelector().trim());
        }
        return builder.build();

    }

    private BestServerResult mappingServerAddressToServerResult(Lb.ServerAddresses serverAddresses, String serviceName) {
        log.debug("mappingServerAddressToServerResult serviceName= {}", serviceName);

        // 返回结果
        BestServerResult bestServerResult = new BestServerResult();
        if (serverAddresses == null) {
            log.error("serverAddress returned from server-side is null,return default value.");
            return bestServerResult;
        }

        for (Lb.ServerAddress serverAddress : serverAddresses.getAddressListList()) {
            ServerIPEndpoint serverIPEndpoint = new ServerIPEndpoint();
            serverIPEndpoint.setServiceName(serviceName);
            serverIPEndpoint.setIp(serverAddress.getServerIP());
            serverIPEndpoint.setPort(serverAddress.getServerPort());

            if (log.isDebugEnabled()) {
                log.debug("serverIPEndpoint: {}, serverName: {}", JSON.toJSONString(serverIPEndpoint), serviceName);
            }
            bestServerResult.getAddressList().add(serverIPEndpoint);
        }
        bestServerResult.setErrorCode(serverAddresses.getErrorCode());
        bestServerResult.setErrorInfo(serverAddresses.getErrorInfo());
        if (log.isDebugEnabled()) {
            log.debug("mappingServerAddressToServerResult bestServerResult: {}, serviceName: {}",
                    JSON.toJSONString(bestServerResult), serviceName);
        }

        return bestServerResult;
    }

    /**
     * <p>
     * 根据 服务名称 获取服务信息
     * </p>
     *
     * @param serviceName 服务名称，如 ast，如果为空，将返回所有的 服务节点信息
     * @return 服务信息
     * @throws Exception 异常
     */
    public TlbServiceInstanceResponse getServiceInfo(String serviceName) throws Exception {
        log.debug("getServiceInfo serviceName = {}", serviceName);
        // 返回结果
        TlbServiceInstanceResponse tlbServiceInstanceResponse;
        try {
            Lb.ServiceInfoList serviceInfoList = fetchServiceInfo(serviceName);
            tlbServiceInstanceResponse = convertLbServiceInstanceResponse(serviceInfoList);
        } catch (StatusRuntimeException e) {
            log.error("getServiceInfo gRPC StatusRuntimeException ", e);
            throw e;
        } catch (Throwable e) {
            log.error("TlbClient getServiceInfo() function exception", e);
            throw e;
        }
        log.debug("serviceInfoResult = {}, serviceName = {}", tlbServiceInstanceResponse, serviceName);
        return tlbServiceInstanceResponse;
    }

    private TlbServiceInstanceResponse convertLbServiceInstanceResponse(Lb.ServiceInfoList serviceInfoList) {
        Assert.notNull(serviceInfoList, "serviceInfoList is null.");
        // 返回结果
        TlbServiceInstanceResponse tlbServiceInstanceResponse = new TlbServiceInstanceResponse();
        log.debug("errorCode= {}, errorInfo= {}, serviceInfo= {}", serviceInfoList.getErrorCode(), serviceInfoList.getErrorInfo(), serviceInfoList.getServiceInfo());

        int errorCode = serviceInfoList.getErrorCode();
        String errorInfo = serviceInfoList.getErrorInfo();
        if (errorCode == 0) {
            tlbServiceInstanceResponse.setErrorCode(errorCode);
            tlbServiceInstanceResponse.setErrorInfo(errorInfo);
        } else {
            throw new TlbException(errorCode, String.format("errorCode = %s, errorInfo = %s", errorCode, errorInfo));
        }

        String serviceInfo = serviceInfoList.getServiceInfo();
        Matcher matcher = PATTERN.matcher(serviceInfo);
        if (matcher.matches()) {
            Map<String, List<TlbServiceInstance>> serviceInfoMap = JSON.parseObject(serviceInfo).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> JSON.parseArray(String.valueOf(entry.getValue()), TlbServiceInstance.class)));
            serviceInfoMap.values().forEach(x -> x.forEach(y -> y.setMetaData(StringUtils.isNotBlank(y.getMetaDataJson())
                    ? JSON.parseObject(y.getMetaDataJson(), new TypeReference<Map<String, String>>() {
            }) : new HashMap<>(0))));
            //置空，不让外围再序列化了 by lyhu
            serviceInfoMap.values().forEach(x -> x.forEach(y -> y.setMetaDataJson(null)));
            tlbServiceInstanceResponse.setServiceInstanceMap(serviceInfoMap);
        } else {
            // 若 serviceInfo 不是 {...} json 形式
            log.warn("No available service");
            log.warn("errorCode= {}, errorInfo= {}, serviceInfo= {}", serviceInfoList.getErrorCode(), serviceInfoList.getErrorInfo(), serviceInfo);
            tlbServiceInstanceResponse.setServiceInstanceMap(new HashMap<>());
        }

        return tlbServiceInstanceResponse;
    }

    /**
     * <p>
     * 汇报 服务信息
     * </p>
     *
     * @param tlbServiceInstance 需要上报的服务信息
     * @throws TlbException 异常
     */
    public void reportServiceInfo(TlbServiceInstance tlbServiceInstance) throws TlbException {
        log.debug("reportServiceInfo detailServiceInfo: {}", tlbServiceInstance);
        this.reportServiceInfoInnerWithCheckConnected(tlbServiceInstance);
        //启动定时汇报处理逻辑
        if (this.periodReporter == null) {
            log.debug("new PeriodReporter..");
            this.periodReporter = new PeriodReporter(this, this.cip.getTimePeriod(), tlbServiceInstance);
        }
        // 更新汇报信息
        this.periodReporter.setDetailServiceInfo(tlbServiceInstance);
    }

    /**
     * 执行上报动作的私有方法
     *
     * @param tlbServiceInstance 要上报的服务信息
     * @throws Exception 上报异常
     */
    private synchronized void reportServiceInfoInner(TlbServiceInstance tlbServiceInstance, BizTypeEnum bizType, BizIdCmdEnum bizIdCmdEnum, List<String> bizIdList) throws TlbException {
        if (log.isDebugEnabled()) {
            log.debug("detailServiceInfo: {}, bizIdCmdEnum: {}, bizIdList: {}", tlbServiceInstance, bizIdCmdEnum, JSON.toJSONString(bizIdList));
        }
        try {
            if (this.requestObserver == null) {
                requestObserver = reportAsyncStub.reportServiceInfo(responseObserver);
            }
            ReportOuterClass.ServiceInfo serviceInfo = newServiceInfo(tlbServiceInstance, bizType, bizIdCmdEnum, bizIdList);
            requestObserver.onNext(serviceInfo);
        } catch (StatusRuntimeException e) {
            log.error("tlb reportServiceInfo failed, tlb url= {}", url);
            log.error("reportServiceInfoInner gRPC StatusRuntimeException ", e);
            throw e;
        } catch (Throwable e) {
            log.error("tlb reportServiceInfo failed, tlb url= {}", url);
            log.error("reportServiceInfo() exception ", e);
            throw new TlbException(String.format("tlb reportServiceInfo failed, tlb url:%s", url));
        }
    }

    /**
     * 定时汇报的，不用 汇报  个性化id
     *
     * @param tlbServiceInstance
     * @throws TlbException
     */
    protected synchronized void reportServiceInfoInnerWithCheckConnected(TlbServiceInstance tlbServiceInstance) throws TlbException {
        reportServiceInfoInner(tlbServiceInstance, BizTypeEnum.bizId, null, null);
    }

    private static ReportOuterClass.ServiceInfo newServiceInfo(TlbServiceInstance tlbServiceInstance, BizTypeEnum bizType, BizIdCmdEnum bizIdCmdEnum, List<String> bizIdList) {
        ReportOuterClass.ServiceInfo serviceInfo;
        Map<String, String> info = new LinkedHashMap<>();
        // ——服务完整名称，如iat_hf
        // 服务IP，如**************
        // 服务端口号
        // 最大授权
        // 当前使用授权
        // CPU使用率百分比，50表示50%
        // 内存使用率百分比，60表示60%
        info.put("ServiceID", tlbServiceInstance.getServiceID());
        info.put("ServiceName", tlbServiceInstance.getServiceName());
        info.put("ServiceIP", tlbServiceInstance.getServiceIP());
        info.put("ServicePort", String.valueOf(tlbServiceInstance.getServicePort()));
        info.put("MaxLic", String.valueOf(tlbServiceInstance.getMaxLic()));
        info.put("UsedLic", String.valueOf(tlbServiceInstance.getUsedLic()));
        info.put("CPU", String.valueOf(tlbServiceInstance.getCpu()));
        info.put("Memory", String.valueOf(tlbServiceInstance.getMemory()));
        info.put("MetaData", JSON.toJSONString(tlbServiceInstance.getMetaData()));

        // bizIdCmdEnum——服务提供个性化功能时，向LB上报时要对某些用户ID做的操作[add,del,clr](添加用户ID关联，删除用户ID关联，清除所有关联)
        // bizId——服务提供个性化功能时关联的用户ID列表，使用英文逗号隔开
        if (bizIdCmdEnum != null && bizIdList != null) {
            info.put("bizType", bizType.toString());
            info.put("bizIdCmd", bizIdCmdEnum.toString());
            info.put("bizId", StringUtils.join(bizIdList, ","));
        }

        if (log.isDebugEnabled()) {
            log.debug("report serviceInfo= {}", JSON.toJSONString(info));
        }
        serviceInfo = ReportOuterClass.ServiceInfo.newBuilder().putAllInfo(info).build();
        return serviceInfo;
    }

    /**
     * Gets the tlb server 端 ip:port, 例如:127.
     *
     * @return the tlb server 端 ip:port, 例如:127
     */
    public String getUrl() {
        return url;
    }

    public void addBizId(List<String> bizIdList) throws TlbException {
        bizIdOpt(BizTypeEnum.bizId, BizIdCmdEnum.add, bizIdList);
        this.currentBizIdSet.addAll(bizIdList);
    }

    public void clearBizId() throws TlbException {
        bizIdOpt(BizTypeEnum.bizId, BizIdCmdEnum.clr, Collections.emptyList());
        this.currentBizIdSet.clear();
    }

    public void removeBizId(List<String> bizIdList) throws TlbException {
        bizIdOpt(BizTypeEnum.bizId, BizIdCmdEnum.del, bizIdList);
        bizIdList.forEach(this.currentBizIdSet::remove);
    }

    public void addTags(List<String> tagList) throws TlbException {
        bizIdOpt(BizTypeEnum.tag, BizIdCmdEnum.add, tagList);
    }

    public void clearTags() throws TlbException {
        bizIdOpt(BizTypeEnum.tag, BizIdCmdEnum.clr, Collections.emptyList());
    }

    public void removeTags(List<String> tagList) throws TlbException {
        bizIdOpt(BizTypeEnum.tag, BizIdCmdEnum.del, tagList);
    }

    private void bizIdOpt(BizTypeEnum bizType, BizIdCmdEnum bizIdCmdEnum, List<String> bizIdList) throws TlbException {
        if (log.isDebugEnabled()) {
            log.debug("bizIdCmdEnum: {}, bizIdList: {}", bizIdCmdEnum, JSON.toJSONString(bizIdList));
        }
        if (periodReporter != null && periodReporter.getLastDetailServiceInfo() != null) {
            if (bizIdList != null && !bizIdList.isEmpty()) {
                for (int index = 0; index < bizIdList.size(); index += pageSize) {
                    List<String> tempList = bizIdList.subList(index, Math.min(bizIdList.size(), index + pageSize));
                    reportServiceInfoInner(periodReporter.getLastDetailServiceInfo(), bizType, bizIdCmdEnum, tempList);
                }
            } else {
                reportServiceInfoInner(periodReporter.getLastDetailServiceInfo(), bizType, bizIdCmdEnum, bizIdList);
            }
        }
    }

    private BestServerResult getBestServer(String serviceName, String tag) throws IOException {
        TlbClientProperties tlbClientProperties = SpringUtils.getBean(TlbClientProperties.class);

        // 检查配置和端点是否为空
        if (ObjectUtils.isEmpty(tlbClientProperties) || ObjectUtils.isEmpty(tlbClientProperties.getHttpEndpoints())) {
            log.error("getBestServer {} http skynet.tlb.http-endpoints is null.", serviceName);
            return null;
        }

        // 构建 URI
        String uri = String.format(HTTP_URL, tlbClientProperties.getHttpEndpoints().getFirst(), serviceName, tag);
        Request request = new Request.Builder().url(uri).build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.body() == null) {
                log.warn("Response body is null for service: {}", serviceName);
                return null;
            }

            String tlbResponse = response.body().string();
            JSONObject tlbResponseJson = JSON.parseObject(tlbResponse);

            // 获取并解析服务实例列表
            String serverNameListStr = tlbResponseJson.getString(serviceName);
            if (ObjectUtils.isEmpty(serverNameListStr)) {
                log.warn("No server instances found for service: {}", serviceName);
                return null;
            }

            List<TlbServiceInstance> tlbServiceInstances = JSON.parseArray(serverNameListStr, TlbServiceInstance.class);
            if (tlbServiceInstances == null || tlbServiceInstances.isEmpty()) {
                log.warn("Parsed server instances list is empty for service: {}", serviceName);
                return null;
            }

            // 转换服务实例到结果对象
            BestServerResult bestServerResult = new BestServerResult();
            tlbServiceInstances.stream().map(tlbServiceInstance -> {
                ServerIPEndpoint endpoint = new ServerIPEndpoint();
                endpoint.setServiceName(tlbServiceInstance.getServiceName());
                endpoint.setIp(tlbServiceInstance.getServiceIP());
                endpoint.setPort(tlbServiceInstance.getServicePort());
                return endpoint;
            }).forEach(bestServerResult.getAddressList()::add);

            if (log.isDebugEnabled()) {
                log.debug("BestServerResult for service {}: {}", serviceName, JSON.toJSONString(bestServerResult));
            }

            return bestServerResult;

        } catch (IOException e) {
            log.error("Failed to getBestServer for service: {}", serviceName, e);
            throw e;
        }
    }

    private TlbServiceInstanceResponse getBestServerInstances(String serviceName, String tag) throws IOException {
        // 获取配置属性
        TlbClientProperties tlbClientProperties = SpringUtils.getBean(TlbClientProperties.class);
        if (ObjectUtils.isEmpty(tlbClientProperties) || ObjectUtils.isEmpty(tlbClientProperties.getHttpEndpoints())) {
            log.error("getBestServerInstances - http skynet.tlb.http-endpoints is null for service: {}", serviceName);
            return null;
        }

        // 构建请求 URI
        String uri = String.format(HTTP_URL, tlbClientProperties.getHttpEndpoints().getFirst(), serviceName, tag);
        Request request = new Request.Builder().url(uri).build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            // 检查响应体是否为空
            if (response.body() == null) {
                log.warn("Response body is null for service: {}", serviceName);
                return null;
            }

            // 解析响应体为 JSON
            String tlbResponse = response.body().string();
            JSONObject tlbResponseJson = JSON.parseObject(tlbResponse);
            String serverNameListStr = tlbResponseJson.getString(serviceName);

            // 处理服务实例列表
            if (ObjectUtils.isEmpty(serverNameListStr)) {
                log.warn("No server instances found for service: {}", serviceName);
                return null;
            }

            List<TlbServiceInstance> tlbServiceInstances = JSON.parseArray(serverNameListStr, TlbServiceInstance.class);
            if (tlbServiceInstances == null || tlbServiceInstances.isEmpty()) {
                log.warn("Parsed server instances list is empty for service: {}", serviceName);
                return null;
            }

            // 构造返回对象
            TlbServiceInstanceResponse tlbServiceInstanceResponse = new TlbServiceInstanceResponse();
            tlbServiceInstanceResponse.setServiceInstanceMap(Collections.singletonMap(serviceName, tlbServiceInstances));

            return tlbServiceInstanceResponse;

        } catch (IOException e) {
            log.error("getBestServerInstances - IOException occurred for service: {}", serviceName, e);
            throw e;
        }
    }

    /**
     * 获知当前 gRPC 是否连接
     *
     * @return true 表示连接中 false 表示连接断开
     */
    private synchronized boolean getGRPCConnected() {
        // 此处有一次 gRPC 请求
        final String[] channelStatus = new String[1];
        channelStatus[0] = channel.getState(true).toString();
        if (channelStatus[0].equals(ConnectivityState.CONNECTING.toString()) || channelStatus[0].equals(ConnectivityState.SHUTDOWN.toString())
                || channelStatus[0].equals(ConnectivityState.TRANSIENT_FAILURE.toString())) {
            log.info("Current gRPC status : {}, connection is disconnected. tlb.url={}", channelStatus[0], url);
            return false;
        }
        log.debug("Current gRPC status : {}, connection is connected. tlb.url={}", channelStatus[0], url);
        return true;
    }

    public boolean isAvailable() {
        return available.get();
    }

    public synchronized void setDisabled() {
        available.set(false);
        Thread thread = new Thread(() -> {
            try {
                Thread.sleep(TUNING_AVAILABLE_THREAD_INTERVAL_MS);
                log.info("tuning available = true, LB url: {}", url);
                available.set(true);
            } catch (Throwable e) {
                log.error("tuning available exception", e);
            }
        });
        thread.setName("TuningAvailableThread");
        thread.start();
    }

    public TlbServiceInstanceResponse getServiceInfoForCheck(String serviceName) {
        // 返回结果
        TlbServiceInstanceResponse tlbServiceInstanceResponse = new TlbServiceInstanceResponse();
        // 定义成 list 是为了从 lambda 表达式里回填值
        Lb.ServiceInfoList serviceInfoList = fetchServiceInfo(serviceName);
        if (serviceInfoList == null) {
            throw new TlbException("serviceInfoList returned from server-side is null,return default value.");
        }
        tlbServiceInstanceResponse.setErrorCode(serviceInfoList.getErrorCode());
        tlbServiceInstanceResponse.setErrorInfo(serviceInfoList.getErrorInfo());
        return tlbServiceInstanceResponse;
    }

    private synchronized Lb.ServiceInfoList fetchServiceInfo(String serviceName) {
        // 返回结果
        // 定义成 list 是为了从 lambda 表达式里回填值
        final Lb.ServiceInfoList[] serviceInfoList = new Lb.ServiceInfoList[1];
        Lb.ServiceName lbServiceName = Lb.ServiceName.newBuilder().setServiceName(serviceName).build();
        serviceInfoList[0] = loadBalanceBlockingStub.withDeadlineAfter(cip.getRpcTimeout().toMillis(), TimeUnit.MILLISECONDS).getServiceInfo(lbServiceName);
        return serviceInfoList[0];
    }

    /**
     * @return 获取服务端版本信息
     */
    public String getVersion() {
        try {
            Lb.LBVersion lbVersion = loadBalanceBlockingStub.getVersion(Lb.EmptyMsg.newBuilder().build());
            return lbVersion.getVersion();
        } catch (Exception e) {
            log.error("getBestServerInstances version exception", e);
        }
        return null;
    }


    /**
     * 1. 关闭定时汇报线程(如果线程存在)、 tlb-sdk-restored-addBizId-thread 线程、TuningAvailableThread 线程
     * 2. 完成事件 3. 关闭 channel
     *
     * @throws Exception 异常
     */
    @Override
    public synchronized void close() throws Exception {
        // 检查是否启动定时汇报线程,若启动则停止它
        if (this.periodReporter != null) {
            this.periodReporter.close();
        }
        if (this.requestObserver != null) {
            this.requestObserver.onCompleted();
        }

        /* 停止 tlb-sdk-restored-addBizId-thread 线程 */
        this.isCheckRestore.set(false);

        if (this.channel != null) {
            this.channel.shutdown();
        }
    }
}