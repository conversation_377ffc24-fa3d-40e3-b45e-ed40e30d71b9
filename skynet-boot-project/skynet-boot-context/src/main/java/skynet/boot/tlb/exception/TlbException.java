package skynet.boot.tlb.exception;

import skynet.boot.exception.SkynetException;

public class TlbException extends SkynetException {
    private static final long serialVersionUID = 1L;

    public TlbException(String arg0) {
        super(1000, arg0);
    }

    public TlbException(String message, Throwable arg1) {
        super(1000, message, arg1);
    }

    public TlbException(int code, String message) {
        super(code, message);
    }

    public TlbException(int code, String message, Throwable arg1) {
        super(code, message, arg1);
    }
}
