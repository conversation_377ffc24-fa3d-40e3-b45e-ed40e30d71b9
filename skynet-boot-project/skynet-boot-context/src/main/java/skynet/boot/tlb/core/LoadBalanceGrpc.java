package skynet.boot.tlb.core;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * 面向业务层的接口
 * </pre>
 */
@jakarta.annotation.Generated(
        value = "by gRPC proto compiler (version 1.64.0)",
        comments = "Source: Lb.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class LoadBalanceGrpc {

    private LoadBalanceGrpc() {
    }

    public static final java.lang.String SERVICE_NAME = "GetServer.LoadBalance";

    // Static method descriptors that strictly reflect the proto.
    private static volatile io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParam,
            skynet.boot.tlb.core.Lb.ServerAddresses> getGetBestServerMethod;

    @io.grpc.stub.annotations.RpcMethod(
            fullMethodName = SERVICE_NAME + '/' + "GetBestServer",
            requestType = skynet.boot.tlb.core.Lb.ServerParam.class,
            responseType = skynet.boot.tlb.core.Lb.ServerAddresses.class,
            methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
    public static io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParam,
            skynet.boot.tlb.core.Lb.ServerAddresses> getGetBestServerMethod() {
        io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParam, skynet.boot.tlb.core.Lb.ServerAddresses> getGetBestServerMethod;
        if ((getGetBestServerMethod = LoadBalanceGrpc.getGetBestServerMethod) == null) {
            synchronized (LoadBalanceGrpc.class) {
                if ((getGetBestServerMethod = LoadBalanceGrpc.getGetBestServerMethod) == null) {
                    LoadBalanceGrpc.getGetBestServerMethod = getGetBestServerMethod =
                            io.grpc.MethodDescriptor.<skynet.boot.tlb.core.Lb.ServerParam, skynet.boot.tlb.core.Lb.ServerAddresses>newBuilder()
                                    .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
                                    .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetBestServer"))
                                    .setSampledToLocalTracing(true)
                                    .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServerParam.getDefaultInstance()))
                                    .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServerAddresses.getDefaultInstance()))
                                    .setSchemaDescriptor(new LoadBalanceMethodDescriptorSupplier("GetBestServer"))
                                    .build();
                }
            }
        }
        return getGetBestServerMethod;
    }

    private static volatile io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParamEx,
            skynet.boot.tlb.core.Lb.ServiceInfoList> getGetBestServerInstancesMethod;

    @io.grpc.stub.annotations.RpcMethod(
            fullMethodName = SERVICE_NAME + '/' + "GetBestServerInstances",
            requestType = skynet.boot.tlb.core.Lb.ServerParamEx.class,
            responseType = skynet.boot.tlb.core.Lb.ServiceInfoList.class,
            methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
    public static io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParamEx,
            skynet.boot.tlb.core.Lb.ServiceInfoList> getGetBestServerInstancesMethod() {
        io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParamEx, skynet.boot.tlb.core.Lb.ServiceInfoList> getGetBestServerInstancesMethod;
        if ((getGetBestServerInstancesMethod = LoadBalanceGrpc.getGetBestServerInstancesMethod) == null) {
            synchronized (LoadBalanceGrpc.class) {
                if ((getGetBestServerInstancesMethod = LoadBalanceGrpc.getGetBestServerInstancesMethod) == null) {
                    LoadBalanceGrpc.getGetBestServerInstancesMethod = getGetBestServerInstancesMethod =
                            io.grpc.MethodDescriptor.<skynet.boot.tlb.core.Lb.ServerParamEx, skynet.boot.tlb.core.Lb.ServiceInfoList>newBuilder()
                                    .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
                                    .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetBestServerInstances"))
                                    .setSampledToLocalTracing(true)
                                    .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServerParamEx.getDefaultInstance()))
                                    .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServiceInfoList.getDefaultInstance()))
                                    .setSchemaDescriptor(new LoadBalanceMethodDescriptorSupplier("GetBestServerInstances"))
                                    .build();
                }
            }
        }
        return getGetBestServerInstancesMethod;
    }

    private static volatile io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParamEx,
            skynet.boot.tlb.core.Lb.ServerAddresses> getGetBestServerExMethod;

    @io.grpc.stub.annotations.RpcMethod(
            fullMethodName = SERVICE_NAME + '/' + "GetBestServerEx",
            requestType = skynet.boot.tlb.core.Lb.ServerParamEx.class,
            responseType = skynet.boot.tlb.core.Lb.ServerAddresses.class,
            methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
    public static io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParamEx,
            skynet.boot.tlb.core.Lb.ServerAddresses> getGetBestServerExMethod() {
        io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServerParamEx, skynet.boot.tlb.core.Lb.ServerAddresses> getGetBestServerExMethod;
        if ((getGetBestServerExMethod = LoadBalanceGrpc.getGetBestServerExMethod) == null) {
            synchronized (LoadBalanceGrpc.class) {
                if ((getGetBestServerExMethod = LoadBalanceGrpc.getGetBestServerExMethod) == null) {
                    LoadBalanceGrpc.getGetBestServerExMethod = getGetBestServerExMethod =
                            io.grpc.MethodDescriptor.<skynet.boot.tlb.core.Lb.ServerParamEx, skynet.boot.tlb.core.Lb.ServerAddresses>newBuilder()
                                    .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
                                    .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetBestServerEx"))
                                    .setSampledToLocalTracing(true)
                                    .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServerParamEx.getDefaultInstance()))
                                    .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServerAddresses.getDefaultInstance()))
                                    .setSchemaDescriptor(new LoadBalanceMethodDescriptorSupplier("GetBestServerEx"))
                                    .build();
                }
            }
        }
        return getGetBestServerExMethod;
    }

    private static volatile io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServiceName,
            skynet.boot.tlb.core.Lb.ServiceInfoList> getGetServiceInfoMethod;

    @io.grpc.stub.annotations.RpcMethod(
            fullMethodName = SERVICE_NAME + '/' + "GetServiceInfo",
            requestType = skynet.boot.tlb.core.Lb.ServiceName.class,
            responseType = skynet.boot.tlb.core.Lb.ServiceInfoList.class,
            methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
    public static io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServiceName,
            skynet.boot.tlb.core.Lb.ServiceInfoList> getGetServiceInfoMethod() {
        io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.ServiceName, skynet.boot.tlb.core.Lb.ServiceInfoList> getGetServiceInfoMethod;
        if ((getGetServiceInfoMethod = LoadBalanceGrpc.getGetServiceInfoMethod) == null) {
            synchronized (LoadBalanceGrpc.class) {
                if ((getGetServiceInfoMethod = LoadBalanceGrpc.getGetServiceInfoMethod) == null) {
                    LoadBalanceGrpc.getGetServiceInfoMethod = getGetServiceInfoMethod =
                            io.grpc.MethodDescriptor.<skynet.boot.tlb.core.Lb.ServiceName, skynet.boot.tlb.core.Lb.ServiceInfoList>newBuilder()
                                    .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
                                    .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetServiceInfo"))
                                    .setSampledToLocalTracing(true)
                                    .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServiceName.getDefaultInstance()))
                                    .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.ServiceInfoList.getDefaultInstance()))
                                    .setSchemaDescriptor(new LoadBalanceMethodDescriptorSupplier("GetServiceInfo"))
                                    .build();
                }
            }
        }
        return getGetServiceInfoMethod;
    }

    private static volatile io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.EmptyMsg,
            skynet.boot.tlb.core.Lb.LBVersion> getGetVersionMethod;

    @io.grpc.stub.annotations.RpcMethod(
            fullMethodName = SERVICE_NAME + '/' + "GetVersion",
            requestType = skynet.boot.tlb.core.Lb.EmptyMsg.class,
            responseType = skynet.boot.tlb.core.Lb.LBVersion.class,
            methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
    public static io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.EmptyMsg,
            skynet.boot.tlb.core.Lb.LBVersion> getGetVersionMethod() {
        io.grpc.MethodDescriptor<skynet.boot.tlb.core.Lb.EmptyMsg, skynet.boot.tlb.core.Lb.LBVersion> getGetVersionMethod;
        if ((getGetVersionMethod = LoadBalanceGrpc.getGetVersionMethod) == null) {
            synchronized (LoadBalanceGrpc.class) {
                if ((getGetVersionMethod = LoadBalanceGrpc.getGetVersionMethod) == null) {
                    LoadBalanceGrpc.getGetVersionMethod = getGetVersionMethod =
                            io.grpc.MethodDescriptor.<skynet.boot.tlb.core.Lb.EmptyMsg, skynet.boot.tlb.core.Lb.LBVersion>newBuilder()
                                    .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
                                    .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetVersion"))
                                    .setSampledToLocalTracing(true)
                                    .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.EmptyMsg.getDefaultInstance()))
                                    .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                                            skynet.boot.tlb.core.Lb.LBVersion.getDefaultInstance()))
                                    .setSchemaDescriptor(new LoadBalanceMethodDescriptorSupplier("GetVersion"))
                                    .build();
                }
            }
        }
        return getGetVersionMethod;
    }

    /**
     * Creates a new async stub that supports all call types for the service
     */
    public static LoadBalanceStub newStub(io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<LoadBalanceStub> factory =
                new io.grpc.stub.AbstractStub.StubFactory<LoadBalanceStub>() {
                    @java.lang.Override
                    public LoadBalanceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                        return new LoadBalanceStub(channel, callOptions);
                    }
                };
        return LoadBalanceStub.newStub(factory, channel);
    }

    /**
     * Creates a new blocking-style stub that supports unary and streaming output calls on the service
     */
    public static LoadBalanceBlockingStub newBlockingStub(
            io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<LoadBalanceBlockingStub> factory =
                new io.grpc.stub.AbstractStub.StubFactory<LoadBalanceBlockingStub>() {
                    @java.lang.Override
                    public LoadBalanceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                        return new LoadBalanceBlockingStub(channel, callOptions);
                    }
                };
        return LoadBalanceBlockingStub.newStub(factory, channel);
    }

    /**
     * Creates a new ListenableFuture-style stub that supports unary calls on the service
     */
    public static LoadBalanceFutureStub newFutureStub(
            io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<LoadBalanceFutureStub> factory =
                new io.grpc.stub.AbstractStub.StubFactory<LoadBalanceFutureStub>() {
                    @java.lang.Override
                    public LoadBalanceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                        return new LoadBalanceFutureStub(channel, callOptions);
                    }
                };
        return LoadBalanceFutureStub.newStub(factory, channel);
    }

    /**
     * <pre>
     * 面向业务层的接口
     * </pre>
     */
    public interface AsyncService {

        /**
         * <pre>
         * 获取最佳服务地址
         * </pre>
         */
        default void getBestServer(skynet.boot.tlb.core.Lb.ServerParam request,
                                   io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServerAddresses> responseObserver) {
            io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetBestServerMethod(), responseObserver);
        }

        /**
         * <pre>
         * 获取最佳服务实例信息（包含meta，tag）
         * </pre>
         */
        default void getBestServerInstances(skynet.boot.tlb.core.Lb.ServerParamEx request,
                                            io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServiceInfoList> responseObserver) {
            io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetBestServerInstancesMethod(), responseObserver);
        }

        /**
         * <pre>
         * 获取最佳服务地址(ServerParamEx中最后一个字段为Map)
         * </pre>
         */
        default void getBestServerEx(skynet.boot.tlb.core.Lb.ServerParamEx request,
                                     io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServerAddresses> responseObserver) {
            io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetBestServerExMethod(), responseObserver);
        }

        /**
         * <pre>
         * 获取服务信息
         * </pre>
         */
        default void getServiceInfo(skynet.boot.tlb.core.Lb.ServiceName request,
                                    io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServiceInfoList> responseObserver) {
            io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetServiceInfoMethod(), responseObserver);
        }

        /**
         * <pre>
         * 获取LB版本(用于检测与LB之前的连接)
         * </pre>
         */
        default void getVersion(skynet.boot.tlb.core.Lb.EmptyMsg request,
                                io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.LBVersion> responseObserver) {
            io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetVersionMethod(), responseObserver);
        }
    }

    /**
     * Base class for the server implementation of the service LoadBalance.
     * <pre>
     * 面向业务层的接口
     * </pre>
     */
    public static abstract class LoadBalanceImplBase
            implements io.grpc.BindableService, AsyncService {

        @java.lang.Override
        public final io.grpc.ServerServiceDefinition bindService() {
            return LoadBalanceGrpc.bindService(this);
        }
    }

    /**
     * A stub to allow clients to do asynchronous rpc calls to service LoadBalance.
     * <pre>
     * 面向业务层的接口
     * </pre>
     */
    public static final class LoadBalanceStub
            extends io.grpc.stub.AbstractAsyncStub<LoadBalanceStub> {
        private LoadBalanceStub(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @java.lang.Override
        protected LoadBalanceStub build(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new LoadBalanceStub(channel, callOptions);
        }

        /**
         * <pre>
         * 获取最佳服务地址
         * </pre>
         */
        public void getBestServer(skynet.boot.tlb.core.Lb.ServerParam request,
                                  io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServerAddresses> responseObserver) {
            io.grpc.stub.ClientCalls.asyncUnaryCall(
                    getChannel().newCall(getGetBestServerMethod(), getCallOptions()), request, responseObserver);
        }

        /**
         * <pre>
         * 获取最佳服务实例信息（包含meta，tag）
         * </pre>
         */
        public void getBestServerInstances(skynet.boot.tlb.core.Lb.ServerParamEx request,
                                           io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServiceInfoList> responseObserver) {
            io.grpc.stub.ClientCalls.asyncUnaryCall(
                    getChannel().newCall(getGetBestServerInstancesMethod(), getCallOptions()), request, responseObserver);
        }

        /**
         * <pre>
         * 获取最佳服务地址(ServerParamEx中最后一个字段为Map)
         * </pre>
         */
        public void getBestServerEx(skynet.boot.tlb.core.Lb.ServerParamEx request,
                                    io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServerAddresses> responseObserver) {
            io.grpc.stub.ClientCalls.asyncUnaryCall(
                    getChannel().newCall(getGetBestServerExMethod(), getCallOptions()), request, responseObserver);
        }

        /**
         * <pre>
         * 获取服务信息
         * </pre>
         */
        public void getServiceInfo(skynet.boot.tlb.core.Lb.ServiceName request,
                                   io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServiceInfoList> responseObserver) {
            io.grpc.stub.ClientCalls.asyncUnaryCall(
                    getChannel().newCall(getGetServiceInfoMethod(), getCallOptions()), request, responseObserver);
        }

        /**
         * <pre>
         * 获取LB版本(用于检测与LB之前的连接)
         * </pre>
         */
        public void getVersion(skynet.boot.tlb.core.Lb.EmptyMsg request,
                               io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.LBVersion> responseObserver) {
            io.grpc.stub.ClientCalls.asyncUnaryCall(
                    getChannel().newCall(getGetVersionMethod(), getCallOptions()), request, responseObserver);
        }
    }

    /**
     * A stub to allow clients to do synchronous rpc calls to service LoadBalance.
     * <pre>
     * 面向业务层的接口
     * </pre>
     */
    public static final class LoadBalanceBlockingStub
            extends io.grpc.stub.AbstractBlockingStub<LoadBalanceBlockingStub> {
        private LoadBalanceBlockingStub(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @java.lang.Override
        protected LoadBalanceBlockingStub build(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new LoadBalanceBlockingStub(channel, callOptions);
        }

        /**
         * <pre>
         * 获取最佳服务地址
         * </pre>
         */
        public skynet.boot.tlb.core.Lb.ServerAddresses getBestServer(skynet.boot.tlb.core.Lb.ServerParam request) {
            return io.grpc.stub.ClientCalls.blockingUnaryCall(
                    getChannel(), getGetBestServerMethod(), getCallOptions(), request);
        }

        /**
         * <pre>
         * 获取最佳服务实例信息（包含meta，tag）
         * </pre>
         */
        public skynet.boot.tlb.core.Lb.ServiceInfoList getBestServerInstances(skynet.boot.tlb.core.Lb.ServerParamEx request) {
            return io.grpc.stub.ClientCalls.blockingUnaryCall(
                    getChannel(), getGetBestServerInstancesMethod(), getCallOptions(), request);
        }

        /**
         * <pre>
         * 获取最佳服务地址(ServerParamEx中最后一个字段为Map)
         * </pre>
         */
        public skynet.boot.tlb.core.Lb.ServerAddresses getBestServerEx(skynet.boot.tlb.core.Lb.ServerParamEx request) {
            return io.grpc.stub.ClientCalls.blockingUnaryCall(
                    getChannel(), getGetBestServerExMethod(), getCallOptions(), request);
        }

        /**
         * <pre>
         * 获取服务信息
         * </pre>
         */
        public skynet.boot.tlb.core.Lb.ServiceInfoList getServiceInfo(skynet.boot.tlb.core.Lb.ServiceName request) {
            return io.grpc.stub.ClientCalls.blockingUnaryCall(
                    getChannel(), getGetServiceInfoMethod(), getCallOptions(), request);
        }

        /**
         * <pre>
         * 获取LB版本(用于检测与LB之前的连接)
         * </pre>
         */
        public skynet.boot.tlb.core.Lb.LBVersion getVersion(skynet.boot.tlb.core.Lb.EmptyMsg request) {
            return io.grpc.stub.ClientCalls.blockingUnaryCall(
                    getChannel(), getGetVersionMethod(), getCallOptions(), request);
        }
    }

    /**
     * A stub to allow clients to do ListenableFuture-style rpc calls to service LoadBalance.
     * <pre>
     * 面向业务层的接口
     * </pre>
     */
    public static final class LoadBalanceFutureStub
            extends io.grpc.stub.AbstractFutureStub<LoadBalanceFutureStub> {
        private LoadBalanceFutureStub(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @java.lang.Override
        protected LoadBalanceFutureStub build(
                io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new LoadBalanceFutureStub(channel, callOptions);
        }

        /**
         * <pre>
         * 获取最佳服务地址
         * </pre>
         */
        public com.google.common.util.concurrent.ListenableFuture<skynet.boot.tlb.core.Lb.ServerAddresses> getBestServer(
                skynet.boot.tlb.core.Lb.ServerParam request) {
            return io.grpc.stub.ClientCalls.futureUnaryCall(
                    getChannel().newCall(getGetBestServerMethod(), getCallOptions()), request);
        }

        /**
         * <pre>
         * 获取最佳服务实例信息（包含meta，tag）
         * </pre>
         */
        public com.google.common.util.concurrent.ListenableFuture<skynet.boot.tlb.core.Lb.ServiceInfoList> getBestServerInstances(
                skynet.boot.tlb.core.Lb.ServerParamEx request) {
            return io.grpc.stub.ClientCalls.futureUnaryCall(
                    getChannel().newCall(getGetBestServerInstancesMethod(), getCallOptions()), request);
        }

        /**
         * <pre>
         * 获取最佳服务地址(ServerParamEx中最后一个字段为Map)
         * </pre>
         */
        public com.google.common.util.concurrent.ListenableFuture<skynet.boot.tlb.core.Lb.ServerAddresses> getBestServerEx(
                skynet.boot.tlb.core.Lb.ServerParamEx request) {
            return io.grpc.stub.ClientCalls.futureUnaryCall(
                    getChannel().newCall(getGetBestServerExMethod(), getCallOptions()), request);
        }

        /**
         * <pre>
         * 获取服务信息
         * </pre>
         */
        public com.google.common.util.concurrent.ListenableFuture<skynet.boot.tlb.core.Lb.ServiceInfoList> getServiceInfo(
                skynet.boot.tlb.core.Lb.ServiceName request) {
            return io.grpc.stub.ClientCalls.futureUnaryCall(
                    getChannel().newCall(getGetServiceInfoMethod(), getCallOptions()), request);
        }

        /**
         * <pre>
         * 获取LB版本(用于检测与LB之前的连接)
         * </pre>
         */
        public com.google.common.util.concurrent.ListenableFuture<skynet.boot.tlb.core.Lb.LBVersion> getVersion(
                skynet.boot.tlb.core.Lb.EmptyMsg request) {
            return io.grpc.stub.ClientCalls.futureUnaryCall(
                    getChannel().newCall(getGetVersionMethod(), getCallOptions()), request);
        }
    }

    private static final int METHODID_GET_BEST_SERVER = 0;
    private static final int METHODID_GET_BEST_SERVER_INSTANCES = 1;
    private static final int METHODID_GET_BEST_SERVER_EX = 2;
    private static final int METHODID_GET_SERVICE_INFO = 3;
    private static final int METHODID_GET_VERSION = 4;

    private static final class MethodHandlers<Req, Resp> implements
            io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
            io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
            io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
            io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
        private final AsyncService serviceImpl;
        private final int methodId;

        MethodHandlers(AsyncService serviceImpl, int methodId) {
            this.serviceImpl = serviceImpl;
            this.methodId = methodId;
        }

        @java.lang.Override
        @java.lang.SuppressWarnings("unchecked")
        public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
            switch (methodId) {
                case METHODID_GET_BEST_SERVER:
                    serviceImpl.getBestServer((skynet.boot.tlb.core.Lb.ServerParam) request,
                            (io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServerAddresses>) responseObserver);
                    break;
                case METHODID_GET_BEST_SERVER_INSTANCES:
                    serviceImpl.getBestServerInstances((skynet.boot.tlb.core.Lb.ServerParamEx) request,
                            (io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServiceInfoList>) responseObserver);
                    break;
                case METHODID_GET_BEST_SERVER_EX:
                    serviceImpl.getBestServerEx((skynet.boot.tlb.core.Lb.ServerParamEx) request,
                            (io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServerAddresses>) responseObserver);
                    break;
                case METHODID_GET_SERVICE_INFO:
                    serviceImpl.getServiceInfo((skynet.boot.tlb.core.Lb.ServiceName) request,
                            (io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.ServiceInfoList>) responseObserver);
                    break;
                case METHODID_GET_VERSION:
                    serviceImpl.getVersion((skynet.boot.tlb.core.Lb.EmptyMsg) request,
                            (io.grpc.stub.StreamObserver<skynet.boot.tlb.core.Lb.LBVersion>) responseObserver);
                    break;
                default:
                    throw new AssertionError();
            }
        }

        @java.lang.Override
        @java.lang.SuppressWarnings("unchecked")
        public io.grpc.stub.StreamObserver<Req> invoke(
                io.grpc.stub.StreamObserver<Resp> responseObserver) {
            switch (methodId) {
                default:
                    throw new AssertionError();
            }
        }
    }

    public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
        return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
                .addMethod(
                        getGetBestServerMethod(),
                        io.grpc.stub.ServerCalls.asyncUnaryCall(
                                new MethodHandlers<
                                        skynet.boot.tlb.core.Lb.ServerParam,
                                        skynet.boot.tlb.core.Lb.ServerAddresses>(
                                        service, METHODID_GET_BEST_SERVER)))
                .addMethod(
                        getGetBestServerInstancesMethod(),
                        io.grpc.stub.ServerCalls.asyncUnaryCall(
                                new MethodHandlers<
                                        skynet.boot.tlb.core.Lb.ServerParamEx,
                                        skynet.boot.tlb.core.Lb.ServiceInfoList>(
                                        service, METHODID_GET_BEST_SERVER_INSTANCES)))
                .addMethod(
                        getGetBestServerExMethod(),
                        io.grpc.stub.ServerCalls.asyncUnaryCall(
                                new MethodHandlers<
                                        skynet.boot.tlb.core.Lb.ServerParamEx,
                                        skynet.boot.tlb.core.Lb.ServerAddresses>(
                                        service, METHODID_GET_BEST_SERVER_EX)))
                .addMethod(
                        getGetServiceInfoMethod(),
                        io.grpc.stub.ServerCalls.asyncUnaryCall(
                                new MethodHandlers<
                                        skynet.boot.tlb.core.Lb.ServiceName,
                                        skynet.boot.tlb.core.Lb.ServiceInfoList>(
                                        service, METHODID_GET_SERVICE_INFO)))
                .addMethod(
                        getGetVersionMethod(),
                        io.grpc.stub.ServerCalls.asyncUnaryCall(
                                new MethodHandlers<
                                        skynet.boot.tlb.core.Lb.EmptyMsg,
                                        skynet.boot.tlb.core.Lb.LBVersion>(
                                        service, METHODID_GET_VERSION)))
                .build();
    }

    private static abstract class LoadBalanceBaseDescriptorSupplier
            implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
        LoadBalanceBaseDescriptorSupplier() {
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
            return skynet.boot.tlb.core.Lb.getDescriptor();
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
            return getFileDescriptor().findServiceByName("LoadBalance");
        }
    }

    private static final class LoadBalanceFileDescriptorSupplier
            extends LoadBalanceBaseDescriptorSupplier {
        LoadBalanceFileDescriptorSupplier() {
        }
    }

    private static final class LoadBalanceMethodDescriptorSupplier
            extends LoadBalanceBaseDescriptorSupplier
            implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
        private final java.lang.String methodName;

        LoadBalanceMethodDescriptorSupplier(java.lang.String methodName) {
            this.methodName = methodName;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
            return getServiceDescriptor().findMethodByName(methodName);
        }
    }

    private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

    public static io.grpc.ServiceDescriptor getServiceDescriptor() {
        io.grpc.ServiceDescriptor result = serviceDescriptor;
        if (result == null) {
            synchronized (LoadBalanceGrpc.class) {
                result = serviceDescriptor;
                if (result == null) {
                    serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
                            .setSchemaDescriptor(new LoadBalanceFileDescriptorSupplier())
                            .addMethod(getGetBestServerMethod())
                            .addMethod(getGetBestServerInstancesMethod())
                            .addMethod(getGetBestServerExMethod())
                            .addMethod(getGetServiceInfoMethod())
                            .addMethod(getGetVersionMethod())
                            .build();
                }
            }
        }
        return result;
    }
}
