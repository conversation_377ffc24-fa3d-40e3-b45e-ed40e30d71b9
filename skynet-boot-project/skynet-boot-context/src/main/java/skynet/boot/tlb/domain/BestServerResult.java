package skynet.boot.tlb.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;

/**
 * 最优服务结果
 *
 * <AUTHOR> [2018年10月9日 上午11:45:27]
 */
@Getter
@Setter
@Accessors(chain = true)
public class BestServerResult extends Jsonable {

    /**
     * GetBestServer接口调用结果.
     */
    private int errorCode;

    /**
     * 错误信息.
     */
    private String errorInfo;

    /**
     * 获取到的服务器信息列表.
     */
    private List<ServerIPEndpoint> addressList = new ArrayList<>(0);

}
