//package skynet.boot.tlb;
//
//import lombok.extern.slf4j.Slf4j;
//import skynet.boot.tlb.config.TlbClientProperties;
//import sun.misc.Signal;
//import sun.misc.SignalHandler;
//
//import java.util.List;
//
/// **
// * 服务优雅下线处理器
// *
// * <pre>
// *
// *     kill -15 ${pid}
// *
// * </pre>
// *
// * <AUTHOR> */
//@Slf4j
//@Deprecated
//public class TlbSmoothDownHandler implements SignalHandler {
//
//    private final skynet.boot.tlb.config.TlbClientProperties tlbClientProperties;
//    private final List<TlbSmoothWorkerChecker> tlbSmoothWorkerCheckerList;
//    private final List<TlbClientRegister> tlbClientRegisters;
//
//    public TlbSmoothDownHandler(TlbClientProperties tlbClientProperties, List<TlbClientRegister> tlbClientRegisters, List<TlbSmoothWorkerChecker> tlbSmoothWorkerCheckers) {
//        this.tlbClientProperties = tlbClientProperties;
//        this.tlbClientRegisters = tlbClientRegisters;
//        this.tlbSmoothWorkerCheckerList = tlbSmoothWorkerCheckers;
//        Signal.handle(new Signal("TERM"), this);
//    }
//
//    public void registerChecker(List<TlbSmoothWorkerChecker> tlbSmoothWorkerCheckers) {
//        this.tlbSmoothWorkerCheckerList.addAll(tlbSmoothWorkerCheckers);
//    }
//
//    public void registerChecker(TlbSmoothWorkerChecker tlbSmoothWorkerChecker) {
//        this.tlbSmoothWorkerCheckerList.add(tlbSmoothWorkerChecker);
//    }
//
//
//    @Override
//    public void handle(Signal signal) {
//        log.info("receive signal name= {}", signal.getName());
//
//        try {
//            /* 断开与 LB 连接 */
//            this.tlbClientRegisters.forEach(r -> {
//                try {
//                    r.close();
//                } catch (Throwable e) {
//                    e.printStackTrace();
//                }
//            });
//
//            long current = System.currentTimeMillis();
//            for (TlbSmoothWorkerChecker tlbSmoothWorkerChecker : tlbSmoothWorkerCheckerList) {
//                while (!tlbSmoothWorkerChecker.isBusy()) {
//                    log.info("service is busy, waiting...");
//                    Thread.sleep(1000);
//                    if (System.currentTimeMillis() - current > tlbClientProperties.getSmoothExitTimeout().toMillis()) {
//                        log.info("timeout exit [timeout= {}]", tlbClientProperties.getSmoothExitTimeout());
//                        System.exit(0);
//                    }
//                }
//            }
//            log.info("exit");
//            System.exit(0);
//        } catch (Throwable e) {
//            log.error("shutdown service exception", e);
//        }
//    }
//}
