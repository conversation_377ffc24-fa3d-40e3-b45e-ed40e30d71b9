package skynet.boot.tlb.service;


import lombok.extern.slf4j.Slf4j;
import skynet.boot.common.Files;
import skynet.boot.tlb.client.TlbClient;
import skynet.boot.tlb.client.data.TlbClientInitParam;
import skynet.boot.tlb.config.TlbAutoConfiguration;
import skynet.boot.tlb.config.TlbClientProperties;
import skynet.boot.tlb.exception.TlbException;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * TlbClient 客户端初始化
 *
 * <pre>
 *
 *
 * </pre>
 *
 * <AUTHOR> [2019年1月9日 下午4:10:00]
 */
@Slf4j
public class TlbClientBuilder {

    private final TlbClientProperties tlbClientProperties;

    public TlbClientBuilder(TlbClientProperties tlbClientProperties) {
        this.tlbClientProperties = tlbClientProperties;
    }

    /**
     * 创建一个新的 TlbClient
     *
     * @throws TlbException
     */
    public synchronized TlbClient newTlbClient() throws TlbException {

        if (tlbClientProperties.getEndpoints().isEmpty()) {
            log.warn("Not set TLB endpoints[{}.endpoints].", TlbAutoConfiguration.CONFIG_PREFIX);
            throw new TlbException("Not set tlb endpoints.");
        }

        // 以启动时的在线TLBServer为中，后期启动的不加载
        List<String> endpoints = tlbClientProperties.getEndpoints();
        log.info("TLB action endpoints={}, TLB timePeriodMills={}", endpoints, tlbClientProperties.getReportPeriod());

        return (tlbClientProperties.getSecurity().isAuthEnabled()) ?
                buildAuthedLbClient(endpoints) : new TlbClient(endpoints, tlbClientProperties.getReportPeriod());
    }

    private TlbClient buildAuthedLbClient(List<String> endpoints) throws TlbException {

        File pemFile = new File(tlbClientProperties.getSecurity().getTlsTrustCertCollectionFilePath());
        File rootSecret1File = new File(tlbClientProperties.getSecurity().getApiAuthRootSecret1FilePath());
        File rootSecret2File = new File(tlbClientProperties.getSecurity().getApiAuthRootSecret2FilePath());
        if (!pemFile.exists() || !rootSecret1File.exists() || !rootSecret2File.exists()) {
            String msg = String.format("can't find trustCertCollectionFile=%s or rootSecret1File=%s or rootSecret2File=%s.",
                    tlbClientProperties.getSecurity().getTlsTrustCertCollectionFilePath(),
                    tlbClientProperties.getSecurity().getApiAuthRootSecret1FilePath(),
                    tlbClientProperties.getSecurity().getApiAuthRootSecret2FilePath());
            throw new TlbException(msg);
        }

        String rootSecret1;
        String rootSecret2;
        try {
            rootSecret1 = Files.readFileStringContent(rootSecret1File, StandardCharsets.US_ASCII);
            rootSecret2 = Files.readFileStringContent(rootSecret2File, StandardCharsets.US_ASCII);
        } catch (Throwable e) {
            String msg = String.format("read file failed. file1=%s, file2=%s", rootSecret1File.getAbsoluteFile(), rootSecret2File.getAbsoluteFile());
            throw new TlbException(msg);
        }

        String rootSecret3 = System.getenv("iflyInterfaceKey");
        String rootSecret = String.format("%s%s%s", rootSecret1, rootSecret2, rootSecret3);

        String encryptedSecret = System.getenv("secretIflytek");

        TlbClientInitParam cip = new TlbClientInitParam();
        cip.setEnableCallCredentials(true);
        cip.setSalt(tlbClientProperties.getSecurity().getApiAuthSalt());
        cip.setRpcTimeout(tlbClientProperties.getRpcTimeout());
        cip.setAuthority(tlbClientProperties.getSecurity().getTlsAuthority());
        cip.setRootSecret(rootSecret);
        cip.setEncryptedSecret(encryptedSecret);
        cip.setTrustCertCollectionFile(pemFile);
        cip.setTimePeriod(tlbClientProperties.getReportPeriod());
        log.debug("cip: {}", cip);

        return new TlbClient(endpoints, cip);
    }
}
