package skynet.boot.tlb.client.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;
import skynet.boot.common.domain.Jsonable;

import java.util.*;

/**
 * LB Services 实例
 */
@Getter
@Setter
@Accessors(chain = true)
public class TlbServiceInstance extends Jsonable {

    /**
     * 服务实例ID
     */
    @JSONField(ordinal = 5, name = "ServiceID")
    private String serviceID;

    /**
     * 服务完整名称,如 iat_hf.
     */
    @JSONField(ordinal = 10, name = "ServiceName")
    private String serviceName;

    /**
     * 服务IP,如**************
     */
    @JSONField(ordinal = 20, name = "ServiceIP")
    private String serviceIP;

    /**
     * 服务端口号.
     */
    @JSONField(ordinal = 30, name = "ServicePort")
    private int servicePort;

    /**
     * 当前使用授权.
     */
    @JSONField(ordinal = 40, name = "UsedLic")
    private int usedLic;


    /**
     * 最大授权.
     */
    @JSONField(ordinal = 50, name = "MaxLic")
    private int maxLic;

    /**
     * cpu 使用率百分比,50表示50%.
     */
    @JSONField(ordinal = 60, name = "CPU")
    private int cpu;


    /**
     * 内存使用率百分比,60表示60%.
     */
    @JSONField(ordinal = 70, name = "Memory")
    private int memory;

    /**
     * 扩展字段.
     */
    @JSONField(ordinal = 80, name = "MetaDataMap")
    private Map<String, String> metaData = new LinkedHashMap<>();

    @JSONField(ordinal = 85, name = "MetaData")
    private String metaDataJson;

    @JSONField(ordinal = 90)
    private String tags;

    public List<String> getTagList() {
        return StringUtils.hasText(tags) ? Arrays.asList(tags.split(",")) : new ArrayList<>(0);
    }
}
