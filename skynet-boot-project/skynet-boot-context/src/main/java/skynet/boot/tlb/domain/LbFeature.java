package skynet.boot.tlb.domain;

/**
 * <AUTHOR>
 */
public enum LbFeature {
    /**
     * 是否在服务满时返回该服务，true(服务满时返回)|false(服务满时报错)
     */
    DISABLED_SELECT_SERVER_FULL,

    /**
     * 当 bizId 不为空时没找到对应服务实例是否兜底
     * true 表示兜底，随机返回一个没有 bizId 的实例 ip:port
     * false 表示不要兜底，没找到就返回错误
     */
    DISABLED_SELECT_SERVER_DEFAULT,
    ;

    LbFeature() {
        mask = (1 << ordinal());
    }

    private final int mask;

    public final int getMask() {
        return mask;
    }

    public static boolean isEnabled(int features, LbFeature feature) {
        return (features & feature.mask) != 0;
    }

    public String getExtKey() {
        return this.toString().toUpperCase().replace("DISABLED_", "");
    }

    public static int of(LbFeature[] features) {
        if (features == null) {
            return 0;
        }

        int value = 0;

        for (LbFeature feature : features) {
            value |= feature.mask;
        }

        return value;
    }
}
