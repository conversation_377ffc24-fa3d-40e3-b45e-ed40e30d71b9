package skynet.boot.tlb.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.AppContext;
import skynet.boot.annotation.EnableSkynetTlbClient;
import skynet.boot.tlb.TlbClientRegister;
import skynet.boot.tlb.TlbClientSelector;
import skynet.boot.tlb.service.TlbClientBuilder;
import skynet.boot.tlb.service.TlbEndpoint;

/**
 * @since 4.0.9
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetTlbClient.class)
@ConditionalOnProperty(value = TlbAutoConfiguration.CONFIG_PREFIX + ".enabled", havingValue = "true")
public class TlbAutoConfiguration {

    public static final String CONFIG_PREFIX = "skynet.tlb";


    @Bean
    @ConfigurationProperties(prefix = CONFIG_PREFIX + ".select.header")
    public TlbSelectHeaderProperties tlbSelectHeaderProperties() {
        return new TlbSelectHeaderProperties();
    }

    /***
     * @since 4.0.9
     * @return
     */
    @Bean
    @ConfigurationProperties(prefix = CONFIG_PREFIX)
    @ConditionalOnMissingBean(TlbClientProperties.class)
    public TlbClientProperties lbClientProperties() {
        return new TlbClientProperties();
    }

    @Bean
    @ConditionalOnMissingBean(TlbClientBuilder.class)
    public TlbClientBuilder lbClientBuilder(TlbClientProperties tlbClientProperties) {
        return new TlbClientBuilder(tlbClientProperties);
    }

    @Bean
    //Remove from 4.0.9 by lyhu
    //    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    @ConditionalOnMissingBean(TlbClientRegister.class)
    public TlbClientRegister tlbClientRegister(TlbClientBuilder tlbClientBuilder, AppContext appContext) {
        return new TlbClientRegister(tlbClientBuilder, appContext);
    }

    @Bean
    @ConditionalOnMissingBean(TlbClientSelector.class)
    public TlbClientSelector tlbClientSelector(TlbClientBuilder tlbClientBuilder) {
        return new TlbClientSelector(tlbClientBuilder);
    }

//    /**
//     * 在 有 TlbSmoothWorkerChecker 对象实例的情况下，自动开启优雅退出模式
//     *
//     * @param tlbClientProperties
//     * @param tlbClientRegisters
//     * @param tlbSmoothWorkerCheckers
//     * @return
//     */
//    @Bean
//    @Deprecated
//    @ConditionalOnMissingBean(TlbSmoothDownHandler.class)
//    public TlbSmoothDownHandler lbSmoothDownHandler(TlbClientProperties tlbClientProperties,
//                                                    List<TlbClientRegister> tlbClientRegisters,
//                                                    List<TlbSmoothWorkerChecker> tlbSmoothWorkerCheckers) {
//        log.info("Build lbSmoothDownHandler");
//        return new TlbSmoothDownHandler(tlbClientProperties, tlbClientRegisters, tlbSmoothWorkerCheckers);
//    }

    @ConditionalOnClass({Endpoint.class})
    static class TlbEndpointConfiguration {
        @Bean
        @ConditionalOnMissingBean(TlbEndpoint.class)
        public TlbEndpoint tlbEndpoint(TlbClientProperties tlbClientProperties, TlbClientSelector tlbClientSelector, TlbClientRegister tlbClientRegister) {
            return new TlbEndpoint(tlbClientProperties, tlbClientSelector, tlbClientRegister);
        }
    }
}