package skynet.boot.tlb;

import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import skynet.boot.AppContext;
import skynet.boot.tlb.client.TlbClient;
import skynet.boot.tlb.client.data.TlbServiceInstance;
import skynet.boot.tlb.exception.TlbException;
import skynet.boot.tlb.service.TlbClientBuilder;

/**
 * TlbClientReg<PERSON> is a load balancing service registrar that reports the
 * status and information of local services
 * to the load balancing service. This class implements AutoCloseable interface
 * for automatic resource cleanup.
 *
 * <p>
 * Key features:
 * </p>
 * <ul>
 * <li>Initialize load balancing service instance</li>
 * <li>Report service usage and maximum authorization count</li>
 * <li>Add and remove tags, business identifiers</li>
 * <li>Report extended properties</li>
 * </ul>
 *
 * <p>
 * Important notes:
 * </p>
 * <ul>
 * <li>Must call {@link #initialize} method before use</li>
 * <li>Ensure load balancing service is started</li>
 * <li>Call {@link #close} method or use try-with-resources statement for
 * automatic closure</li>
 * </ul>
 *
 * <p>
 * Usage example:
 * </p>
 * 
 * <pre>
 * {@code
 * @Autowired
 * private TlbClientRegister tlbClientRegister;
 * 
 * try {
 *     // Initialize with service details and max licenses
 *     tlbClientRegister.initialize("MyService", 8080, 100);
 * 
 *     // Report usage and add metadata
 *     tlbClientRegister.reportUsage();
 *     tlbClientRegister.addTag("v1.0");
 * 
 *     Map<String, String> metadata = new HashMap<>();
 *     metadata.put("environment", "production");
 *     tlbClientRegister.reportMetaData(metadata);
 * 
 * } catch (TlbException e) {
 *     log.error("Failed to initialize TlbClientRegister: {}", e.getMessage(), e);
 * } finally {
 *     tlbClientRegister.close();
 * }
 * }
 * </pre>
 *
 * <AUTHOR> [Oct 19, 2017 2:14:00 PM]
 * @see AutoCloseable
 * @see TlbClientBuilder
 * @see TlbClient
 * @see TlbServiceInstance
 * @see TlbException
 */
@Slf4j
public class TlbClientRegister implements AutoCloseable {

    private static final String DEFAULT_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private final ReentrantLock lock = new ReentrantLock();
    private final AtomicInteger usedLicenseCount = new AtomicInteger();
    private final AppContext appContext;
    private final TlbClientBuilder tlbClientBuilder;

    @Getter
    private volatile TlbServiceInstance tlbServiceInstance;
    private volatile TlbClient tlbClient;

    public TlbClientRegister(TlbClientBuilder tlbClientBuilder, AppContext appContext) {
        Assert.notNull(tlbClientBuilder, "TlbClientBuilder must not be null");
        Assert.notNull(appContext, "AppContext must not be null");
        this.appContext = appContext;
        this.tlbClientBuilder = tlbClientBuilder;
    }

    /**
     * Reports the host Web service (service name as Skynet ActionPoint).
     *
     * @param maxLicenses Maximum number of concurrent licenses
     * @throws TlbException if initialization fails
     */
    public void initialize(int maxLicenses) throws TlbException {
        Assert.isTrue(maxLicenses > 0, "Maximum licenses must be greater than 0");
        this.initialize(appContext.getNodeName(), appContext.getPort(), maxLicenses);
    }

    /**
     * Initializes the service with the specified parameters and reports to the load
     * balancer.
     * Note: Load balancer service must be started before calling this method.
     *
     * @param serviceName Service name
     * @param port        Service port
     * @param maxLicenses Maximum number of licenses
     * @throws TlbException if initialization fails
     */
    public void initialize(String serviceName, int port, int maxLicenses) throws TlbException {
        Assert.hasText(serviceName, "Service name must not be empty");
        Assert.isTrue(port > 0, "Port must be greater than 0");
        Assert.isTrue(maxLicenses > 0, "Maximum licenses must be greater than 0");
        this.initialize(serviceName, appContext.getIpAddress(), port, maxLicenses);
    }

    /**
     * 初始化负载均衡服务实例。
     *
     * @param serviceName 服务的名称
     * @param ip          服务的 IP 地址
     * @param port        服务的端口号
     * @param maxLic      服务的最大许可数
     * @throws TlbException 如果初始化过程中发生错误
     */
    public void initialize(String serviceName, String ip, int port, int maxLic) throws TlbException {
        // 默认：以IP和AntId组合为 serviceId
        this.initialize(String.format("%s##%s", appContext.getAntId(), ip), serviceName, ip, port, maxLic);
    }

    /**
     * 初始化负载均衡服务实例。
     *
     * @param serviceId   服务的唯一标识符
     * @param serviceName 服务的名称
     * @param ip          服务的 IP 地址
     * @param port        服务的端口号
     * @param maxLic      服务的最大许可数
     * @throws TlbException 如果初始化过程中发生错误
     */
    public void initialize(String serviceId, String serviceName, String ip, int port, int maxLic) throws TlbException {
        this.initialize(serviceId, serviceName, ip, port, maxLic, null);
    }

    /**
     * 初始化负载均衡服务实例。
     *
     * @param serviceId   服务的唯一标识符
     * @param serviceName 服务的名称
     * @param ip          服务的 IP 地址
     * @param port        服务的端口号
     * @param maxLic      服务的最大许可数
     * @param metadata    服务的元数据，键值对形式
     * @throws TlbException 如果初始化过程中发生错误
     */
    public void initialize(String serviceId, String serviceName, String ip, int port, int maxLic,
            Map<String, String> metadata) throws TlbException {
        Assert.hasText(serviceName, "The serviceName is blank.");
        Assert.hasText(ip, "The ip is blank.");
        log.info("--------------------------------------------------");
        log.info("Init LbService: serviceId={}; serviceName={}; ip={}; port={}; maxLic={}. begin ...", serviceId,
                serviceName, ip, port, maxLic);
        TlbServiceInstance tlbServiceInstance = new TlbServiceInstance();
        tlbServiceInstance.setMaxLic(maxLic);
        tlbServiceInstance.setUsedLic(0);
        tlbServiceInstance.setServiceIP(ip);
        tlbServiceInstance.setServicePort(port);
        tlbServiceInstance.setServiceName(serviceName);
        tlbServiceInstance.setServiceID(serviceId);
        if (metadata != null) {
            tlbServiceInstance.getMetaData().putAll(metadata);
        }
        tlbServiceInstance.getMetaData().put("host", appContext.getHostName());
        tlbServiceInstance.getMetaData().put("start-time",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        tlbServiceInstance.getMetaData().put("start-stamp", String.valueOf(System.currentTimeMillis()));
        tlbServiceInstance.getMetaData().put("processors", String.valueOf(Runtime.getRuntime().availableProcessors()));

        tlbClient = tlbClientBuilder.newTlbClient();
        tlbClient.reportServiceInfo(tlbServiceInstance);
        log.info("Init LbService: serviceId={}; serviceName={}; ip={}; port={}; maxLic={}. end.", serviceId,
                serviceName, ip, port, maxLic);
        log.info("--------------------------------------------------");
        this.tlbServiceInstance = tlbServiceInstance;
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                this.close();
            } catch (Exception e) {
                log.error("Error during shutdown hook execution: {}", e.getMessage());
            }
        }));
    }

    /**
     * 是否初始化
     *
     * @return
     */
    public boolean isInitialize() {
        return tlbClient != null;
    }

    /**
     * 添加标签到负载均衡服务实例。
     *
     * @param tag 要添加的标签
     * @throws Exception 如果添加标签过程中发生错误
     */
    public void addTag(String tag) throws Exception {
        log.debug("LbService addTag, tag= {}", tag);
        this.checkInit();
        this.tlbClient.addTag(tag);
    }

    /**
     * 从负载均衡服务实例中移除标签。
     *
     * @param tag 要移除的标签
     * @throws Exception 如果移除标签过程中发生错误
     */
    public void removeTag(String tag) throws Exception {
        log.debug("LbService removeTag, tag= {}", tag);
        this.checkInit();
        this.tlbClient.removeTag(tag);
    }

    /**
     * 添加业务标识到负载均衡服务实例。
     *
     * @param bizId 要添加的业务标识
     * @throws Exception 如果添加业务标识过程中发生错误
     */
    public void addBizId(String bizId) throws Exception {
        log.debug("LbService addBizId, bizId= {}", bizId);
        this.checkInit();
        this.tlbClient.addBizId(bizId);
    }

    /**
     * 从负载均衡服务实例中移除业务标识。
     *
     * @param bizId 要移除的业务标识
     * @throws Exception 如果移除业务标识过程中发生错误
     */
    public void removeBizId(String bizId) throws Exception {
        log.debug("LbService removeBizId, bizId= {}", bizId);
        this.checkInit();
        this.tlbClient.removeBizId(bizId);
    }

    /**
     * 汇报已经使用的授权数。
     *
     * @param usedLic 已经使用的授权数
     * @throws TlbException 如果汇报过程中发生错误
     */
    public void reportUsedLic(int usedLic) throws TlbException {
        log.debug("LbService reportUsedLic, usedLic= {}", usedLic);
        this.checkInit();
        try {
            this.tlbServiceInstance.setUsedLic(usedLic);
            this.tlbClient.reportServiceInfo(this.tlbServiceInstance);
        } catch (TlbException e) {
            log.error(String.format("report detailServiceInfo to tlb service err= %s", e.getMessage()));
        }
    }

    /**
     * Reports usage of one license.
     * Thread-safe method to increment and report used license count.
     */
    public void reportUsage() {
        lock.lock();
        try {
            this.reportUsedLic(this.usedLicenseCount.incrementAndGet());
        } finally {
            lock.unlock();
        }
    }

    /**
     * Reports release of one license.
     * Thread-safe method to decrement and report used license count.
     */
    public void reportRelease() {
        lock.lock();
        try {
            this.reportUsedLic(this.usedLicenseCount.decrementAndGet());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 汇报最大授权数,汇报 -1 表示 LB 下线。
     *
     * @param maxLic 最大授权数
     * @throws TlbException 如果汇报过程中发生错误
     */
    public void reportMaxLic(int maxLic) throws TlbException {
        Assert.isTrue(maxLic >= -1, "maxLic must >=-1");
        log.debug("LbService reportUsedLic, usedLic= {}", maxLic);
        this.checkInit();
        try {
            this.tlbServiceInstance.setMaxLic(maxLic);
            this.tlbServiceInstance.setUsedLic(0);
            this.tlbClient.reportServiceInfo(tlbServiceInstance);
        } catch (TlbException e) {
            log.error("report reportMaxLic to tlb service err= {}", e.getMessage());
        }
    }

    /**
     * Reports metadata to the load balancer.
     *
     * @param metaData Map of metadata key-value pairs
     * @throws TlbException if reporting fails
     */
    public void reportMetaData(Map<String, String> metaData) throws TlbException {
        Assert.notNull(metaData, "Metadata must not be null");
        log.debug("Reporting extended fields to load balancer, metadata: {}", metaData);
        checkInit();
        try {
            lock.lock();
            try {
                this.tlbServiceInstance.setMetaData(metaData);
                this.tlbClient.reportServiceInfo(tlbServiceInstance);
            } finally {
                lock.unlock();
            }
        } catch (TlbException e) {
            log.error("Failed to report extended fields to load balancer service: {}", e.getMessage(), e);
            throw e;
        }
    }

    private void checkInit() {
        if (this.tlbClient == null) {
            throw new TlbException("TLB Service is not initialized");
        }
    }

    @PreDestroy
    @Override
    public void close() throws Exception {
        log.info("Closing TlbClientRegister and its associated client");
        if (this.tlbClient != null) {
            lock.lock();
            try {
                // Report no available licenses before closing the channel
                this.tlbServiceInstance.setMaxLic(-1);
                this.tlbServiceInstance.setUsedLic(-1);
                this.tlbClient.reportServiceInfo(tlbServiceInstance);
                this.tlbClient.close();
                this.tlbClient = null;
            } finally {
                lock.unlock();
            }
        }
    }
}
