// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Report.proto

// Protobuf Java Version: 3.25.1
package skynet.boot.tlb.core;

public final class ReportOuterClass {
    private ReportOuterClass() {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistry registry) {
        registerAllExtensions(
                (com.google.protobuf.ExtensionRegistryLite) registry);
    }

    public interface ReportResponseOrBuilder extends
            // @@protoc_insertion_point(interface_extends:Report.ReportResponse)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * 上报接口调用结果
         * </pre>
         *
         * <code>int32 ErrorCode = 1;</code>
         *
         * @return The errorCode.
         */
        int getErrorCode();

        /**
         * <pre>
         * 错误描述信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The errorInfo.
         */
        java.lang.String getErrorInfo();

        /**
         * <pre>
         * 错误描述信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The bytes for errorInfo.
         */
        com.google.protobuf.ByteString
        getErrorInfoBytes();
    }

    /**
     * Protobuf type {@code Report.ReportResponse}
     */
    public static final class ReportResponse extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:Report.ReportResponse)
            ReportResponseOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ReportResponse.newBuilder() to construct.
        private ReportResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ReportResponse() {
            errorInfo_ = "";
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ReportResponse();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ReportResponse_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ReportResponse_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.ReportOuterClass.ReportResponse.class, skynet.boot.tlb.core.ReportOuterClass.ReportResponse.Builder.class);
        }

        public static final int ERRORCODE_FIELD_NUMBER = 1;
        private int errorCode_ = 0;

        /**
         * <pre>
         * 上报接口调用结果
         * </pre>
         *
         * <code>int32 ErrorCode = 1;</code>
         *
         * @return The errorCode.
         */
        @java.lang.Override
        public int getErrorCode() {
            return errorCode_;
        }

        public static final int ERRORINFO_FIELD_NUMBER = 2;
        @SuppressWarnings("serial")
        private volatile java.lang.Object errorInfo_ = "";

        /**
         * <pre>
         * 错误描述信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The errorInfo.
         */
        @java.lang.Override
        public java.lang.String getErrorInfo() {
            java.lang.Object ref = errorInfo_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                errorInfo_ = s;
                return s;
            }
        }

        /**
         * <pre>
         * 错误描述信息
         * </pre>
         *
         * <code>string ErrorInfo = 2;</code>
         *
         * @return The bytes for errorInfo.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getErrorInfoBytes() {
            java.lang.Object ref = errorInfo_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                errorInfo_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (errorCode_ != 0) {
                output.writeInt32(1, errorCode_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(errorInfo_)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorInfo_);
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (errorCode_ != 0) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(1, errorCode_);
            }
            if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(errorInfo_)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorInfo_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.ReportOuterClass.ReportResponse)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.ReportOuterClass.ReportResponse other = (skynet.boot.tlb.core.ReportOuterClass.ReportResponse) obj;

            if (getErrorCode()
                    != other.getErrorCode()) return false;
            if (!getErrorInfo()
                    .equals(other.getErrorInfo())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
            hash = (53 * hash) + getErrorCode();
            hash = (37 * hash) + ERRORINFO_FIELD_NUMBER;
            hash = (53 * hash) + getErrorInfo().hashCode();
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.ReportOuterClass.ReportResponse prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code Report.ReportResponse}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:Report.ReportResponse)
                skynet.boot.tlb.core.ReportOuterClass.ReportResponseOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ReportResponse_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ReportResponse_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.ReportOuterClass.ReportResponse.class, skynet.boot.tlb.core.ReportOuterClass.ReportResponse.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.ReportOuterClass.ReportResponse.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                errorCode_ = 0;
                errorInfo_ = "";
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ReportResponse_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.ReportOuterClass.ReportResponse getDefaultInstanceForType() {
                return skynet.boot.tlb.core.ReportOuterClass.ReportResponse.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.ReportOuterClass.ReportResponse build() {
                skynet.boot.tlb.core.ReportOuterClass.ReportResponse result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.ReportOuterClass.ReportResponse buildPartial() {
                skynet.boot.tlb.core.ReportOuterClass.ReportResponse result = new skynet.boot.tlb.core.ReportOuterClass.ReportResponse(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.ReportOuterClass.ReportResponse result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.errorCode_ = errorCode_;
                }
                if (((from_bitField0_ & 0x00000002) != 0)) {
                    result.errorInfo_ = errorInfo_;
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.ReportOuterClass.ReportResponse) {
                    return mergeFrom((skynet.boot.tlb.core.ReportOuterClass.ReportResponse) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.ReportOuterClass.ReportResponse other) {
                if (other == skynet.boot.tlb.core.ReportOuterClass.ReportResponse.getDefaultInstance()) return this;
                if (other.getErrorCode() != 0) {
                    setErrorCode(other.getErrorCode());
                }
                if (!other.getErrorInfo().isEmpty()) {
                    errorInfo_ = other.errorInfo_;
                    bitField0_ |= 0x00000002;
                    onChanged();
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 8: {
                                errorCode_ = input.readInt32();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 8
                            case 18: {
                                errorInfo_ = input.readStringRequireUtf8();
                                bitField0_ |= 0x00000002;
                                break;
                            } // case 18
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private int errorCode_;

            /**
             * <pre>
             * 上报接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @return The errorCode.
             */
            @java.lang.Override
            public int getErrorCode() {
                return errorCode_;
            }

            /**
             * <pre>
             * 上报接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @param value The errorCode to set.
             * @return This builder for chaining.
             */
            public Builder setErrorCode(int value) {

                errorCode_ = value;
                bitField0_ |= 0x00000001;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 上报接口调用结果
             * </pre>
             *
             * <code>int32 ErrorCode = 1;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearErrorCode() {
                bitField0_ = (bitField0_ & ~0x00000001);
                errorCode_ = 0;
                onChanged();
                return this;
            }

            private java.lang.Object errorInfo_ = "";

            /**
             * <pre>
             * 错误描述信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return The errorInfo.
             */
            public java.lang.String getErrorInfo() {
                java.lang.Object ref = errorInfo_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    errorInfo_ = s;
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * 错误描述信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return The bytes for errorInfo.
             */
            public com.google.protobuf.ByteString
            getErrorInfoBytes() {
                java.lang.Object ref = errorInfo_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    errorInfo_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * 错误描述信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @param value The errorInfo to set.
             * @return This builder for chaining.
             */
            public Builder setErrorInfo(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                errorInfo_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 错误描述信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @return This builder for chaining.
             */
            public Builder clearErrorInfo() {
                errorInfo_ = getDefaultInstance().getErrorInfo();
                bitField0_ = (bitField0_ & ~0x00000002);
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 错误描述信息
             * </pre>
             *
             * <code>string ErrorInfo = 2;</code>
             *
             * @param value The bytes for errorInfo to set.
             * @return This builder for chaining.
             */
            public Builder setErrorInfoBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                checkByteStringIsUtf8(value);
                errorInfo_ = value;
                bitField0_ |= 0x00000002;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:Report.ReportResponse)
        }

        // @@protoc_insertion_point(class_scope:Report.ReportResponse)
        private static final skynet.boot.tlb.core.ReportOuterClass.ReportResponse DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.ReportOuterClass.ReportResponse();
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ReportResponse getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ReportResponse>
                PARSER = new com.google.protobuf.AbstractParser<ReportResponse>() {
            @java.lang.Override
            public ReportResponse parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ReportResponse> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ReportResponse> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.ReportOuterClass.ReportResponse getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    public interface ServiceInfoOrBuilder extends
            // @@protoc_insertion_point(interface_extends:Report.ServiceInfo)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        int getInfoCount();

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        boolean containsInfo(
                java.lang.String key);

        /**
         * Use {@link #getInfoMap()} instead.
         */
        @java.lang.Deprecated
        java.util.Map<java.lang.String, java.lang.String>
        getInfo();

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        java.util.Map<java.lang.String, java.lang.String>
        getInfoMap();

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        /* nullable */
        java.lang.String getInfoOrDefault(
                java.lang.String key,
                /* nullable */
                java.lang.String defaultValue);

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        java.lang.String getInfoOrThrow(
                java.lang.String key);
    }

    /**
     * <pre>
     * 上报给LB的服务信息
     * </pre>
     * <p>
     * Protobuf type {@code Report.ServiceInfo}
     */
    public static final class ServiceInfo extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:Report.ServiceInfo)
            ServiceInfoOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use ServiceInfo.newBuilder() to construct.
        private ServiceInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private ServiceInfo() {
        }

        @java.lang.Override
        @SuppressWarnings({"unused"})
        protected java.lang.Object newInstance(
                UnusedPrivateParameter unused) {
            return new ServiceInfo();
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ServiceInfo_descriptor;
        }

        @SuppressWarnings({"rawtypes"})
        @java.lang.Override
        protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
                int number) {
            switch (number) {
                case 1:
                    return internalGetInfo();
                default:
                    throw new RuntimeException(
                            "Invalid map field number: " + number);
            }
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
            return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ServiceInfo_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.class, skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.Builder.class);
        }

        public static final int INFO_FIELD_NUMBER = 1;

        private static final class InfoDefaultEntryHolder {
            static final com.google.protobuf.MapEntry<
                    java.lang.String, java.lang.String> defaultEntry =
                    com.google.protobuf.MapEntry
                            .<java.lang.String, java.lang.String>newDefaultInstance(
                                    skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ServiceInfo_InfoEntry_descriptor,
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "",
                                    com.google.protobuf.WireFormat.FieldType.STRING,
                                    "");
        }

        @SuppressWarnings("serial")
        private com.google.protobuf.MapField<
                java.lang.String, java.lang.String> info_;

        private com.google.protobuf.MapField<java.lang.String, java.lang.String>
        internalGetInfo() {
            if (info_ == null) {
                return com.google.protobuf.MapField.emptyMapField(
                        InfoDefaultEntryHolder.defaultEntry);
            }
            return info_;
        }

        public int getInfoCount() {
            return internalGetInfo().getMap().size();
        }

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        @java.lang.Override
        public boolean containsInfo(
                java.lang.String key) {
            if (key == null) {
                throw new NullPointerException("map key");
            }
            return internalGetInfo().getMap().containsKey(key);
        }

        /**
         * Use {@link #getInfoMap()} instead.
         */
        @java.lang.Override
        @java.lang.Deprecated
        public java.util.Map<java.lang.String, java.lang.String> getInfo() {
            return getInfoMap();
        }

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        @java.lang.Override
        public java.util.Map<java.lang.String, java.lang.String> getInfoMap() {
            return internalGetInfo().getMap();
        }

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        @java.lang.Override
        public /* nullable */
        java.lang.String getInfoOrDefault(
                java.lang.String key,
                /* nullable */
                java.lang.String defaultValue) {
            if (key == null) {
                throw new NullPointerException("map key");
            }
            java.util.Map<java.lang.String, java.lang.String> map =
                    internalGetInfo().getMap();
            return map.containsKey(key) ? map.get(key) : defaultValue;
        }

        /**
         * <pre>
         * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
         * ServiceName——服务完整名称，如iat_hf
         * ServiceIP——服务IP，如**************
         * ServicePort——服务端口号
         * MaxLic——最大授权
         * UsedLic——当前使用授权
         * CPU——CPU使用率百分比，50表示50%
         * Memory——内存使用率百分比，60表示60%
         * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
         * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
         * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
         * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
         * MetaData——扩展数据
         * </pre>
         *
         * <code>map&lt;string, string&gt; Info = 1;</code>
         */
        @java.lang.Override
        public java.lang.String getInfoOrThrow(
                java.lang.String key) {
            if (key == null) {
                throw new NullPointerException("map key");
            }
            java.util.Map<java.lang.String, java.lang.String> map =
                    internalGetInfo().getMap();
            if (!map.containsKey(key)) {
                throw new java.lang.IllegalArgumentException();
            }
            return map.get(key);
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            com.google.protobuf.GeneratedMessageV3
                    .serializeStringMapTo(
                            output,
                            internalGetInfo(),
                            InfoDefaultEntryHolder.defaultEntry,
                            1);
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
                    : internalGetInfo().getMap().entrySet()) {
                com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
                        info__ = InfoDefaultEntryHolder.defaultEntry.newBuilderForType()
                        .setKey(entry.getKey())
                        .setValue(entry.getValue())
                        .build();
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(1, info__);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof skynet.boot.tlb.core.ReportOuterClass.ServiceInfo)) {
                return super.equals(obj);
            }
            skynet.boot.tlb.core.ReportOuterClass.ServiceInfo other = (skynet.boot.tlb.core.ReportOuterClass.ServiceInfo) obj;

            if (!internalGetInfo().equals(
                    other.internalGetInfo())) return false;
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            if (!internalGetInfo().getMap().isEmpty()) {
                hash = (37 * hash) + INFO_FIELD_NUMBER;
                hash = (53 * hash) + internalGetInfo().hashCode();
            }
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(skynet.boot.tlb.core.ReportOuterClass.ServiceInfo prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * <pre>
         * 上报给LB的服务信息
         * </pre>
         * <p>
         * Protobuf type {@code Report.ServiceInfo}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:Report.ServiceInfo)
                skynet.boot.tlb.core.ReportOuterClass.ServiceInfoOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ServiceInfo_descriptor;
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
                    int number) {
                switch (number) {
                    case 1:
                        return internalGetInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            @SuppressWarnings({"rawtypes"})
            protected com.google.protobuf.MapFieldReflectionAccessor internalGetMutableMapFieldReflection(
                    int number) {
                switch (number) {
                    case 1:
                        return internalGetMutableInfo();
                    default:
                        throw new RuntimeException(
                                "Invalid map field number: " + number);
                }
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ServiceInfo_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.class, skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.Builder.class);
            }

            // Construct using skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.newBuilder()
            private Builder() {

            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);

            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                bitField0_ = 0;
                internalGetMutableInfo().clear();
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return skynet.boot.tlb.core.ReportOuterClass.internal_static_Report_ServiceInfo_descriptor;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.ReportOuterClass.ServiceInfo getDefaultInstanceForType() {
                return skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.getDefaultInstance();
            }

            @java.lang.Override
            public skynet.boot.tlb.core.ReportOuterClass.ServiceInfo build() {
                skynet.boot.tlb.core.ReportOuterClass.ServiceInfo result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public skynet.boot.tlb.core.ReportOuterClass.ServiceInfo buildPartial() {
                skynet.boot.tlb.core.ReportOuterClass.ServiceInfo result = new skynet.boot.tlb.core.ReportOuterClass.ServiceInfo(this);
                if (bitField0_ != 0) {
                    buildPartial0(result);
                }
                onBuilt();
                return result;
            }

            private void buildPartial0(skynet.boot.tlb.core.ReportOuterClass.ServiceInfo result) {
                int from_bitField0_ = bitField0_;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.info_ = internalGetInfo();
                    result.info_.makeImmutable();
                }
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof skynet.boot.tlb.core.ReportOuterClass.ServiceInfo) {
                    return mergeFrom((skynet.boot.tlb.core.ReportOuterClass.ServiceInfo) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(skynet.boot.tlb.core.ReportOuterClass.ServiceInfo other) {
                if (other == skynet.boot.tlb.core.ReportOuterClass.ServiceInfo.getDefaultInstance()) return this;
                internalGetMutableInfo().mergeFrom(
                        other.internalGetInfo());
                bitField0_ |= 0x00000001;
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 10: {
                                com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
                                        info__ = input.readMessage(
                                        InfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
                                internalGetMutableInfo().getMutableMap().put(
                                        info__.getKey(), info__.getValue());
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 10
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private com.google.protobuf.MapField<
                    java.lang.String, java.lang.String> info_;

            private com.google.protobuf.MapField<java.lang.String, java.lang.String>
            internalGetInfo() {
                if (info_ == null) {
                    return com.google.protobuf.MapField.emptyMapField(
                            InfoDefaultEntryHolder.defaultEntry);
                }
                return info_;
            }

            private com.google.protobuf.MapField<java.lang.String, java.lang.String>
            internalGetMutableInfo() {
                if (info_ == null) {
                    info_ = com.google.protobuf.MapField.newMapField(
                            InfoDefaultEntryHolder.defaultEntry);
                }
                if (!info_.isMutable()) {
                    info_ = info_.copy();
                }
                bitField0_ |= 0x00000001;
                onChanged();
                return info_;
            }

            public int getInfoCount() {
                return internalGetInfo().getMap().size();
            }

            /**
             * <pre>
             * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
             * ServiceName——服务完整名称，如iat_hf
             * ServiceIP——服务IP，如**************
             * ServicePort——服务端口号
             * MaxLic——最大授权
             * UsedLic——当前使用授权
             * CPU——CPU使用率百分比，50表示50%
             * Memory——内存使用率百分比，60表示60%
             * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
             * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
             * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
             * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
             * MetaData——扩展数据
             * </pre>
             *
             * <code>map&lt;string, string&gt; Info = 1;</code>
             */
            @java.lang.Override
            public boolean containsInfo(
                    java.lang.String key) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                return internalGetInfo().getMap().containsKey(key);
            }

            /**
             * Use {@link #getInfoMap()} instead.
             */
            @java.lang.Override
            @java.lang.Deprecated
            public java.util.Map<java.lang.String, java.lang.String> getInfo() {
                return getInfoMap();
            }

            /**
             * <pre>
             * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
             * ServiceName——服务完整名称，如iat_hf
             * ServiceIP——服务IP，如**************
             * ServicePort——服务端口号
             * MaxLic——最大授权
             * UsedLic——当前使用授权
             * CPU——CPU使用率百分比，50表示50%
             * Memory——内存使用率百分比，60表示60%
             * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
             * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
             * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
             * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
             * MetaData——扩展数据
             * </pre>
             *
             * <code>map&lt;string, string&gt; Info = 1;</code>
             */
            @java.lang.Override
            public java.util.Map<java.lang.String, java.lang.String> getInfoMap() {
                return internalGetInfo().getMap();
            }

            /**
             * <pre>
             * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
             * ServiceName——服务完整名称，如iat_hf
             * ServiceIP——服务IP，如**************
             * ServicePort——服务端口号
             * MaxLic——最大授权
             * UsedLic——当前使用授权
             * CPU——CPU使用率百分比，50表示50%
             * Memory——内存使用率百分比，60表示60%
             * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
             * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
             * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
             * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
             * MetaData——扩展数据
             * </pre>
             *
             * <code>map&lt;string, string&gt; Info = 1;</code>
             */
            @java.lang.Override
            public /* nullable */
            java.lang.String getInfoOrDefault(
                    java.lang.String key,
                    /* nullable */
                    java.lang.String defaultValue) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                java.util.Map<java.lang.String, java.lang.String> map =
                        internalGetInfo().getMap();
                return map.containsKey(key) ? map.get(key) : defaultValue;
            }

            /**
             * <pre>
             * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
             * ServiceName——服务完整名称，如iat_hf
             * ServiceIP——服务IP，如**************
             * ServicePort——服务端口号
             * MaxLic——最大授权
             * UsedLic——当前使用授权
             * CPU——CPU使用率百分比，50表示50%
             * Memory——内存使用率百分比，60表示60%
             * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
             * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
             * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
             * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
             * MetaData——扩展数据
             * </pre>
             *
             * <code>map&lt;string, string&gt; Info = 1;</code>
             */
            @java.lang.Override
            public java.lang.String getInfoOrThrow(
                    java.lang.String key) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                java.util.Map<java.lang.String, java.lang.String> map =
                        internalGetInfo().getMap();
                if (!map.containsKey(key)) {
                    throw new java.lang.IllegalArgumentException();
                }
                return map.get(key);
            }

            public Builder clearInfo() {
                bitField0_ = (bitField0_ & ~0x00000001);
                internalGetMutableInfo().getMutableMap()
                        .clear();
                return this;
            }

            /**
             * <pre>
             * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
             * ServiceName——服务完整名称，如iat_hf
             * ServiceIP——服务IP，如**************
             * ServicePort——服务端口号
             * MaxLic——最大授权
             * UsedLic——当前使用授权
             * CPU——CPU使用率百分比，50表示50%
             * Memory——内存使用率百分比，60表示60%
             * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
             * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
             * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
             * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
             * MetaData——扩展数据
             * </pre>
             *
             * <code>map&lt;string, string&gt; Info = 1;</code>
             */
            public Builder removeInfo(
                    java.lang.String key) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                internalGetMutableInfo().getMutableMap()
                        .remove(key);
                return this;
            }

            /**
             * Use alternate mutation accessors instead.
             */
            @java.lang.Deprecated
            public java.util.Map<java.lang.String, java.lang.String>
            getMutableInfo() {
                bitField0_ |= 0x00000001;
                return internalGetMutableInfo().getMutableMap();
            }

            /**
             * <pre>
             * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
             * ServiceName——服务完整名称，如iat_hf
             * ServiceIP——服务IP，如**************
             * ServicePort——服务端口号
             * MaxLic——最大授权
             * UsedLic——当前使用授权
             * CPU——CPU使用率百分比，50表示50%
             * Memory——内存使用率百分比，60表示60%
             * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
             * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
             * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
             * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
             * MetaData——扩展数据
             * </pre>
             *
             * <code>map&lt;string, string&gt; Info = 1;</code>
             */
            public Builder putInfo(
                    java.lang.String key,
                    java.lang.String value) {
                if (key == null) {
                    throw new NullPointerException("map key");
                }
                if (value == null) {
                    throw new NullPointerException("map value");
                }
                internalGetMutableInfo().getMutableMap()
                        .put(key, value);
                bitField0_ |= 0x00000001;
                return this;
            }

            /**
             * <pre>
             * ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
             * ServiceName——服务完整名称，如iat_hf
             * ServiceIP——服务IP，如**************
             * ServicePort——服务端口号
             * MaxLic——最大授权
             * UsedLic——当前使用授权
             * CPU——CPU使用率百分比，50表示50%
             * Memory——内存使用率百分比，60表示60%
             * bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
             * bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
             * bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
             * resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
             * MetaData——扩展数据
             * </pre>
             *
             * <code>map&lt;string, string&gt; Info = 1;</code>
             */
            public Builder putAllInfo(
                    java.util.Map<java.lang.String, java.lang.String> values) {
                internalGetMutableInfo().getMutableMap()
                        .putAll(values);
                bitField0_ |= 0x00000001;
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:Report.ServiceInfo)
        }

        // @@protoc_insertion_point(class_scope:Report.ServiceInfo)
        private static final skynet.boot.tlb.core.ReportOuterClass.ServiceInfo DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new skynet.boot.tlb.core.ReportOuterClass.ServiceInfo();
        }

        public static skynet.boot.tlb.core.ReportOuterClass.ServiceInfo getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        private static final com.google.protobuf.Parser<ServiceInfo>
                PARSER = new com.google.protobuf.AbstractParser<ServiceInfo>() {
            @java.lang.Override
            public ServiceInfo parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<ServiceInfo> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<ServiceInfo> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public skynet.boot.tlb.core.ReportOuterClass.ServiceInfo getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_Report_ReportResponse_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_Report_ReportResponse_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_Report_ServiceInfo_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_Report_ServiceInfo_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_Report_ServiceInfo_InfoEntry_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_Report_ServiceInfo_InfoEntry_fieldAccessorTable;

    public static com.google.protobuf.Descriptors.FileDescriptor
    getDescriptor() {
        return descriptor;
    }

    private static com.google.protobuf.Descriptors.FileDescriptor
            descriptor;

    static {
        java.lang.String[] descriptorData = {
                "\n\014Report.proto\022\006Report\"6\n\016ReportResponse" +
                        "\022\021\n\tErrorCode\030\001 \001(\005\022\021\n\tErrorInfo\030\002 \001(\t\"g" +
                        "\n\013ServiceInfo\022+\n\004Info\030\001 \003(\0132\035.Report.Ser" +
                        "viceInfo.InfoEntry\032+\n\tInfoEntry\022\013\n\003key\030\001" +
                        " \001(\t\022\r\n\005value\030\002 \001(\t:\0028\0012N\n\006Report\022D\n\021Rep" +
                        "ortServiceInfo\022\023.Report.ServiceInfo\032\026.Re" +
                        "port.ReportResponse\"\000(\001B\026\n\024skynet.boot.t" +
                        "lb.coreb\006proto3"
        };
        descriptor = com.google.protobuf.Descriptors.FileDescriptor
                .internalBuildGeneratedFileFrom(descriptorData,
                        new com.google.protobuf.Descriptors.FileDescriptor[]{
                        });
        internal_static_Report_ReportResponse_descriptor =
                getDescriptor().getMessageTypes().get(0);
        internal_static_Report_ReportResponse_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_Report_ReportResponse_descriptor,
                new java.lang.String[]{"ErrorCode", "ErrorInfo",});
        internal_static_Report_ServiceInfo_descriptor =
                getDescriptor().getMessageTypes().get(1);
        internal_static_Report_ServiceInfo_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_Report_ServiceInfo_descriptor,
                new java.lang.String[]{"Info",});
        internal_static_Report_ServiceInfo_InfoEntry_descriptor =
                internal_static_Report_ServiceInfo_descriptor.getNestedTypes().get(0);
        internal_static_Report_ServiceInfo_InfoEntry_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_Report_ServiceInfo_InfoEntry_descriptor,
                new java.lang.String[]{"Key", "Value",});
    }

    // @@protoc_insertion_point(outer_class_scope)
}
