package skynet.boot.tlb.client.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class TlbServiceInstanceResponse extends Jsonable {

    /**
     * GetServiceInfo接口调用结果
     */
    private int errorCode;

    /**
     * 调用错误信息
     */
    private String errorInfo;

    /**
     * 服务信息列表
     */
    private Map<String, List<TlbServiceInstance>> serviceInstanceMap = new HashMap<>();

}
