## 服务发现组件TLB - TLBClient （skynet-boot-starter-tlb)

（add v4.0.9+）

图聆LB客户端组件，主要包括

- TLB 服务注册
- TLB 服务发现
- 优雅平滑下线
- spring-cloud-discovery 规范实现

### pom.xml 依赖引入

```xml

<dependency>
    <groupId>com.iflytek.skynet</groupId>
    <artifactId>skynet-boot-starter-tlb</artifactId>
</dependency>
```

### SDK依赖研发使用

在使用的类上开启注解：

```java

import skynet.boot.annotation.EnableSkynetTlbClient;

@Slf4j
@EnableSkynetTlbClient
@RestController
@RequestMapping(value = "/tlb")
public class TlbTestController {
    
    private final TlbClientRegister tlbClientRegister;
    private final TlbClientSelector tlbClientSelector;

    public LbTestController(TlbClientRegister tlbClientRegister, TlbClientSelector tlbClientSelector) {
        this.tlbClientRegister = tlbClientRegister;
        this.tlbClientSelector = tlbClientSelector;
    }

    //....

}

```

#### 场景一：需要向TLB 服务注册

``` java    
    @Autowired
    private TlbClientRegister tlbClientRegister;

	tlbClientRegister.initialize(targetActionName, port, maxLic);
	//汇报 已经使用的授权数
	tlbClientRegister.reportUsedLic(usedLic); 
	////汇报最大授权数,汇报 -1 LB下线
	tlbClientRegister.reportMaxLic(maxLic); 
	
	tlbClientRegister.addBizId(bizId);
	tlbClientRegister.removeBizId(bizId); 
	//汇报扩展属性
	tlbClientRegister.reportExtendFields(Properties extendFields)
	
```

> 备注：
> `TlbClientSelector` `TlbClientRegister` 在 `LbAutoConfiguration`
> 中配置的都是单例模式，如果在同一个进程内部，需要个上报节点，建议手动创建 `TlbClientRegister`

#### 场景二：只需要向LB获取 最优可用服务信息

```java    

@Autowired
private TlbClientSelector tlbClientSelector

//从LB注册中心中 获取最优服务地址，如果没有可用服务或LB服务不存在，返回null
ServerIPEndpoint tlbClientSelector.getBestServer(String serviceName)
ServerIPEndpoint tlbClientSelector.getBestServer(String serviceName, String bizId, boolean isSelectServerDefault)

/**
 * 从LB注册中心中 获取最优服务地址，如果没有可用服务或LB服务不存在，返回null
 *
 * @param serviceName 服务名称
 * @param tag         tag
 * @return ip port
 * @throws LbException tlb 异常
 */
public ServerIPEndpoint getBestServer(String serviceName, Tag tag) throws LbException
        
//获取所有 serviceName 服务实例列表
List<TlbServiceInstance> tlbClientSelector.getServiceInstances(String serviceName)


/**
 * 从LB注册中心中 获取最优服务地址列表，如果没有可用服务或LB服务不存在
 * <p>
 * 根据 lbServiceName、 bizId 向 TLB 查询 ip:port
 * features 里的参数表示关闭 TLB 的这些功能
 * 即 features 里的选项传给 TLB 时值是: false
 * features 为空时传给 TLB 值是默认值: true
 *  add 4.0.8
 *
 * @param serviceName 服务名称
 * @param bizId       业务 id
 * @param tagSelector         tag 表达式  如： A&&B&&C   A||B||C  !(A&&B&&C)
 * @param topN        最优的N个服务
 * @param features    待关闭的选项
 * @return ip:port
 * @throws LbException
 */
public List<ServerIPEndpoint> getBestServers(String serviceName, String tagSelector, String bizId, int topN, LbFeature... features) throws LbException

/**
 * 获取最优服务实例信息
 *
 * @param bestServerParam
 * @return
 * @throws LbException
 */
public List<TlbServiceInstance> getBestServerInstances(String serviceName, String tagSelector, String bizId, int topN, LbFeature... features) throws LbException;
public List<TlbServiceInstance> getBestServerInstances(BestServerParam bestServerParam) throws LbException;


@Data
@Accessors(chain = true)
public class Tag {

    private Set<String> tags;
    private TagLogic logic = TagLogic.AND;

    @Override
    public String toString() {
        if (tags == null) return null;

        switch (logic) {
            case AND:
                return StringUtils.join(tags, "&&");
            case OR:
                return StringUtils.join(tags, "||");
            case NOT:
                return String.format("!(%s)", StringUtils.join(tags, "&&"));
            default:
                return null;
        }
    }
}
```

### LB服务配置

```properties
#-----------------------------------------------------------------
# LB的地址，多LB地址 用 半角下的逗号分隔
skynet.tlb.enabled=true
skynet.tlb.endpoints=172.31.234.57:3301,172.31.234.58:3301
skynet.tlb.reportPeriod=3s
skynet.tlb.rpcTimeout=10s
skynet.tlb.smoothExitTimeout=60s
#-----------------------------------------------------------------
# tlb tls 安全访问设置，LB服务端如果没有配置，不需要配置
#TLS 的 是否开启
skynet.tlb.security.tlsEnabled=true
skynet.tlb.security.tlsAuthority=iflytek.com
#TSL 证书文件
skynet.tlb.security.tlsTrustCertCollectionFilePath=/........pem
#开启 call credentials 模式
skynet.tlb.security.authEnabled=true
# auth 用到的盐值
skynet.tlb.security.apiAuthSalt=xxxxxxxxxxxxxxx
#rootSecret file1 path
skynet.tlb.security.apiAuthRootSecret1FilePath=xxxxxxxx
#rootSecret file2 path
skynet.tlb.security.apiAuthRootSecret2FilePath=xxxxxxxx

#rootSecret 的第三段和加密的 secret 在环境变量中，需要使用者自己 export 到环境变量里
#-----------------------------------------------------------------
```

### 服务优雅下线

方式一：

实现 `LbSmoothWorkerChecker` 接口（目的是定时检测是否满足条件，一般判断当前进程是否有请求在处理），让 Spring 托管。
示例：

```java
@Bean
public LbSmoothWorkerChecker tlbSmoothWorkerChecker(MyPool pool) {
    return new LbSmoothWorkerChecker() {
        @Override
        public boolean isBusy() {
            return pool.used() > 0;
        }
    };
}

//或：

@Bean
public LbSmoothWorkerChecker tlbSmoothWorkerChecker(MyPool pool) {
        return () -> pool.used > 0;
}
```

方式二：
通过 `LbSmoothDownHandler`  注册  `LbSmoothDownHandler`
示例：

```java
@Autowired(required = false)
private LbSmoothDownHandler lbSmoothDownHandler;

public void initialize(int port, int concurrency) throws Exception{
    //...
    lbSmoothDownHandler.registerChecker(()->itransGrpcHandlerPool.getFreeSize()==itransGrpcHandlerPool.getPoolSize());
    //...
}

```

### spring-cloud-discovery 规范实现

#### 功能开启

```java

import skynet.boot.annotation.EnableSkynetTlbDiscoveryClient

@EnableSkynetTlbDiscoveryClient
@Configuration(proxyBeanMethods = false)
class XxAppConfig{}
```

#### 属性配置

```properties
#-----------------------------------------------------------------
# 是否开启服务发现： 默认 true
spring.cloud.tlb.discovery.enabled=true
# 是否自动注册，默认false
spring.cloud.tlb.discovery.register=true
# 服务注册的一些元数据
spring.cloud.tlb.discovery.max-lic=100
spring.cloud.tlb.discovery.service-id=
spring.cloud.tlb.discovery.service-name=skyline-console
spring.cloud.tlb.discovery.service-host=
spring.cloud.tlb.discovery.service-port=0
spring.cloud.tlb.discovery.metadata.key1=value
spring.cloud.tlb.discovery.metadata.key2=value
# 服务发现，从tlb中获取的默认tag标签
spring.cloud.tlb.discovery.default-service-tag-selector=A

# 根据HTTP请求头，动态修改 TLB 服务发现参数 配置，默认配置如下,建议不要修改
spring.cloud.tlb.discovery.request-header.enabled=true
spring.cloud.tlb.discovery.request-header.tag-selector-key=skynet-tlb-service-tag-selector
spring.cloud.tlb.discovery.request-header.biz-id-key=skynet-tlb-service-biz-id
#-----------------------------------------------------------------
```

### 动态设置获取服务参数扩展

场景：Tlb DiscoveryClient 根据当前请求(如：HTTP) 动态设置获取服务的标签选择器 tagSelector。
> 如果通过`loadbalancerClient 调用，需要设置 `spring.cloud.loadbalancer.cache.enabled=false`，不然有缓存影响，导致设置
> TagSelector 后不生效。

实现 `TlbDiscoveryContextConfigurer` 配置接口, 如内置 根据HTTP请求头，动态修改 TLB 服务发现参数 的
TlbDiscoveryContextConfigurer 实现

```java

/**
 * 根据HTTP请求头，动态修改 TLB 服务发现参数
 */
@Slf4j
public class HttpRequestTlbDiscoveryContextConfigurer implements TlbDiscoveryContextConfigurer {

    private final TlbDiscoveryProperties tlbDiscoveryProperties;

    public HttpRequestTlbDiscoveryContextConfigurer(TlbDiscoveryProperties tlbDiscoveryProperties) {
        this.tlbDiscoveryProperties = tlbDiscoveryProperties;
    }

    @Override
    public void apply(TlbDiscoveryContext tlbDiscoveryContext) {
        if (!tlbDiscoveryProperties.getRequestHeader().isEnabled()) {
            return;
        }
        log.debug("tlbDiscoveryContext={}", tlbDiscoveryContext);
        HttpServletRequest request = getHttpServletRequest();
        if (request == null) {
            return;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String, String> header = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            header.put(headerName, headerValue);
        }
        String bizId = header.get(tlbDiscoveryProperties.getRequestHeader().getBizIdKey());
        String tagSelector = header.get(tlbDiscoveryProperties.getRequestHeader().getTagSelectorKey());
        if (StringUtils.hasText(bizId)) {
            tlbDiscoveryContext.setBizId(bizId);
        }
        if (StringUtils.hasText(tagSelector)) {
            tlbDiscoveryContext.setTagSelector(tagSelector);
        }
        log.debug("tlbDiscoveryContext={}", tlbDiscoveryContext);
    }

    private HttpServletRequest getHttpServletRequest() {
        if (RequestContextHolder.getRequestAttributes() instanceof ServletRequestAttributes) {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        }
        return null;
    }

    /**
     * 可以通过 order 修改 多个 TlbDiscoveryContextConfigurer 实现的顺序，可以达到覆盖的效果
     *
     * @return
     */
    @Override
    public int getOrder() {
        return 0;
    }
}

```

### TLB客户端与服务版本号查看端点

访问地址：
http://{IP:PORT}/actuator/skynet-tlb

结果示例：

```json
{
    "properties": {
        "enabled": true,
        "endpoints": [
            "************:33001"
        ],
        "reportPeriod": "PT3S",
        "rpcTimeout": "PT10S",
        "smoothExitTimeout": "PT1M",
        "security": {
            "tlsEnabled": false,
            "tlsAuthority": null,
            "tlsTrustCertCollectionFilePath": null,
            "authEnabled": false,
            "apiAuthSalt": null,
            "apiAuthRootSecret1FilePath": null,
            "apiAuthRootSecret2FilePath": null
        }
    },
    "tlb-server": {
        "up": true,
        "endpoints": [
            {
                "version": "*******",
                "endpoint": "************:33001",
                "failedReason": null
            }
        ]
    }
}
```

### TLB服务端相关说明

> 服务端口本身有2个，grpc 和 http
> 启动命令：

```shell
./LB --server.port=${grpc-port} --http.port=${http-port}  

```

#### GRPC 接口

grpc: 172.31.164.11:33000

#### 查询所有注册的HTTP服务接口：

http://172.31.164.11:33100/monitor/getserviceinfo

#### tag 查询接口（GET）

http://172.31.164.11:33100/tags?svcname=test&tag=pub||dev

#### tag 操作接口(POST)

http://172.31.164.11:33100/tags
> 需要设置 BaseAuth 验证

用户 taguser
密码 tagpassword

http 头：
Authorization Basic dGFndXNlcjp0YWdwYXNzd29yZA==

- tag 操作参数：

```json
 {
  "serviceName": "pandora-sample",
  "cmd": "add",
  "serviceIds": [
    "pandora-sample@skynet-pandora@@10.3.122.176",
    "pandora-sample@skynet-pandora@@10.3.122.104"
  ],
  "tags": [
    "dev",
    "gpu"
  ]
}

```

cmd：枚举有：

- add,（新增）
- del,（删除）
- clr  (清空)

### 备注 获取服务PB协议（TLB服务)

Lb.proto

```protobuf

// protobuf3

syntax = "proto3";

package GetServer;
option java_package = "skynet.boot.discovery.tlb.core";

message ServiceName{
  string ServiceName = 1;
}

//获取服务器信息时的参数
message ServerParam {
  string ServerName = 1;//要获取的服务名称
  int32 Number = 2;//要获取的服务地址数量
  string UID = 3;//UID用于个性化
  string Reserved = 4;//扩展字段
}

message ServerParamEx{
  string ServerName = 1;//要获取的服务名称
  int32 Number = 2;//要获取的服务地址数量
  //Map中基础信息如下：
  //"bizId"(以前叫UID)，用于个性化,注意大小写一定要一致
  //"reqType"，请求类型
  //      "preload":代表“预加载个性化资源”请求;注意大小写一定要一致
  //      "noXX": XX指代任意字符，例如"noDeeplink"。代表要获取未加载指定系统级热词服务的请求
  //      空或"common"时，代表普通获取最佳服务请求。
  //"resMd5" 当"reqType"为"noXX"，此字段必选，值为系统级热词的md5
  //"tag"  A&&B   A||B   !(A&&B)
  map<string, string> ServiceInfo = 3;
}

//单个服务的IP端口信息
message ServerAddress{
  string ServerIP = 1;
  int32 ServerPort = 2;
}

//多个服务的IP端口信息
message ServerAddresses{
  int32 ErrorCode = 1;//GetBestServer接口调用结果
  string ErrorInfo = 2;//错误信息
  repeated ServerAddress AddressList = 3;//获取到的服务器信息列表
}

//GetServiceInfo时获取到的服务信息列表
message ServiceInfoList{
  int32 ErrorCode = 1;//GetServiceInfo接口调用结果
  string ErrorInfo = 2;//调用错误信息

  //JSON格式信息示例
  //{
  //    "asr_hf": [
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":5090,
  //            "max_lic":200,
  //            "used_lic":55,
  //            "cpu":51.5,
  //            "mem":22.2
  //        },
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":5090,
  //            "max_lic":150,
  //            "used_lic":55,
  //            "cpu":51.5,
  //            "mem":33.3
  //        }
  //    ],
  //
  //    "tts_xtts": [
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":10006,
  //            "max_lic":200,
  //            "used_lic":55,
  //            "cpu":99.9,
  //            "mem":66.6
  //        },
  //        {
  //            "svr_ip":"***************",
  //            "svr_port":10006,
  //            "max_lic":200,
  //            "used_lic":55,
  //            "cpu":51.5,
  //            "mem":22.2
  //        }
  //    ]
  //}
  string ServiceInfo = 3;//JSON格式的服务信息列表
}

message LBVersion{
  string Version = 1;
}

message EmptyMsg{}

//面向业务层的接口
service LoadBalance {
  //获取最佳服务地址
  rpc GetBestServer(ServerParam) returns (ServerAddresses){}

  //获取最佳服务实例信息（包含meta，tag）
  rpc GetBestServerInstances(ServerParamEx) returns (ServiceInfoList){}

  //获取最佳服务地址(ServerParamEx中最后一个字段为Map)
  rpc GetBestServerEx(ServerParamEx) returns (ServerAddresses){}

  //获取服务信息
  rpc GetServiceInfo(ServiceName) returns (ServiceInfoList) {}

  //获取LB版本(用于检测与LB之前的连接)
  rpc GetVersion(EmptyMsg) returns(LBVersion){}
}
```

Report.proto

```protobuf
syntax = "proto3";
package Report;
option java_package = "skynet.boot.discovery.tlb.core";

message ReportResponse{
    int32 ErrorCode=1;//上报接口调用结果
    string ErrorInfo=2;//错误描述信息
}

//上报给LB的服务信息
message ServiceInfo{
    // ServiceID——服务实例ID,如果没有传，服务端 自动 用 ServiceName-ServiceIP-ServicePort
    // ServiceName——服务完整名称，如iat_hf
    // ServiceIP——服务IP，如**************
    // ServicePort——服务端口号
    // MaxLic——最大授权
    // UsedLic——当前使用授权
    // CPU——CPU使用率百分比，50表示50%
    // Memory——内存使用率百分比，60表示60%
    // bizType——个性化资源类型，为空或者"bizId"时对应原来的用户级个性化资源，为"deeplink"时对应系统级热词,为"tag"时对应标签
    // bizIdCmd——服务提供个性化功能时，向LB上报时要对某些资源ID做的操作[add,del,clr](添加ID关联，删除ID关联，清除所有关联)
    // bizId——服务提供个性化功能时关联的ID列表，使用英文逗号隔开
    // resMd5——系统级热词MD5列表，与系统级热词列表一一对应，使用英文逗号隔开
    // MetaData——扩展数据
    map<string,string> Info=1;
}
//服务向LB上报
service Report {
    //向LB上报服务器信息
    rpc ReportServiceInfo(stream ServiceInfo) returns (ReportResponse){}
}
```
