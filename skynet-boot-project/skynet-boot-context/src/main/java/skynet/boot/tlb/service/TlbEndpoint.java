package skynet.boot.tlb.service;

import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import skynet.boot.tlb.TlbClientRegister;
import skynet.boot.tlb.TlbClientSelector;
import skynet.boot.tlb.config.TlbClientProperties;

import java.util.Map;
import java.util.TreeMap;

@Endpoint(id = "skynet-tlb")
public class TlbEndpoint {

    private final TlbClientProperties tlbClientProperties;
    private final TlbClientSelector tlbClientSelector;
    private final TlbClientRegister tlbClientRegister;

    public TlbEndpoint(TlbClientProperties tlbClientProperties, TlbClientSelector tlbClientSelector, TlbClientRegister tlbClientRegister) {
        this.tlbClientProperties = tlbClientProperties;
        this.tlbClientSelector = tlbClientSelector;
        this.tlbClientRegister = tlbClientRegister;
    }

    @ReadOperation
    public Object invoke() {
        Map<String, Object> status = new TreeMap<>();
        status.put("properties", tlbClientProperties);
        status.put("tlb-server", tlbClientSelector.getLbStatus());
        status.put("tlb-register", tlbClientRegister.getTlbServiceInstance());
        return status;
    }
}
