package skynet.boot.tlb.client.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class ReportResult {

    /**
     * 上报接口调用结果
     */
    private int errorCode;

    /**
     * 错误描述信息
     */
    private String errorInfo;


    @Override
    public String toString() {
        return "ReportResult{" +
                "errorCode=" + errorCode +
                ", errorInfo='" + errorInfo + '\'' +
                '}';
    }
}
