package skynet.boot.tlb.client.data;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

/**
 * 单独一个 LB 的状态 和 请求失败信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class OneTlbStatus extends Jsonable {

    /**
     * TLB服务端的版本
     */
    private String version;
    /**
     * 一个 LB 的 ip:port 信息
     */
    private String endpoint;

    /**
     * 检查状态时请求失败的原因，异常信息
     */
    private String failedReason;

    public OneTlbStatus() {
    }

    public OneTlbStatus(String endpoint) {
        this.endpoint = endpoint;
    }
}
