package skynet.boot.tlb.domain;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/***
 * tag 操作参数：
 *
 * {
 *     "serviceName": "tts",
 *     "cmd": "add",
 *     "servers": [
 *         "172.31.164.11:33000",
 *         "172.31.164.12:33000"
 *     ],
 *     "tags": [
 *         "dev",
 *         "gpu"
 *     ]
 * }
 */
@Getter
@Setter
public class TagOptParam extends Jsonable {
    private String serviceName;

    private BizIdCmdEnum cmd = BizIdCmdEnum.add;
    private List<String> servers = new ArrayList<>();

    private Set<String> tags = new HashSet<>();
}
