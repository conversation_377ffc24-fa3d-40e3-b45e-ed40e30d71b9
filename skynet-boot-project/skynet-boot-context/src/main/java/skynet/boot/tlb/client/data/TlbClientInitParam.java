package skynet.boot.tlb.client.data;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.boot.convert.DurationUnit;
import skynet.boot.common.domain.Jsonable;

import java.io.File;
import java.time.Duration;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
public class TlbClientInitParam extends Jsonable {

    @JSONField(ordinal = 10)
    @DurationUnit(ChronoUnit.SECONDS)
    private Duration timePeriod = Duration.ofSeconds(3);

    @JSONField(ordinal = 20)
    @DurationUnit(ChronoUnit.SECONDS)
    private Duration rpcTimeout = Duration.ofSeconds(10);

    /**
     * tls 相关
     */
    @JSONField(ordinal = 30)
    private File trustCertCollectionFile;
    @JSONField(ordinal = 40)
    private String authority;

    /**
     * 根密匙
     */
    @JSONField(ordinal = 50)
    private String rootSecret;
    /**
     * 盐值
     */
    @JSONField(ordinal = 60)
    private String salt;
    /**
     * 加密后的 secret
     */
    @JSONField(ordinal = 70)
    private String encryptedSecret;

    /**
     * 开启接口级鉴权
     */
    @JSONField(ordinal = 80)
    private boolean enableCallCredentials;

}
