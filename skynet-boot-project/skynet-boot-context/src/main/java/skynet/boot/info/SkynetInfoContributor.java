package skynet.boot.info;

import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import skynet.boot.AppContext;

/**
 * <AUTHOR>
 * @date 2020/8/12 16:51
 */
@Lazy
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(InfoContributor.class)
public class SkynetInfoContributor implements InfoContributor {

    private final AppContext appContext;

    public SkynetInfoContributor(AppContext appContext) {
        this.appContext = appContext;
    }


    /**
     * 向 Info.Builder 添加项目版本信息的详细内容。
     * 该方法使用 appContext 中的项目版本列表来填充 Info.Builder 的 "project.version" 详情。
     *
     * @param builder Info.Builder 实例,用于构建包含项目版本信息的详细内容。
     */
    @Override
    public void contribute(Info.Builder builder) {
        builder.withDetail("project.version", appContext.getProjectVersionList());
    }
}