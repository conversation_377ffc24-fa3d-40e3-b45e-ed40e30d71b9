package skynet.boot.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

/**
 * MD5工具类
 *
 * <AUTHOR> qq:408365330
 */
@Slf4j
public class MD5Util {

    /**
     * 生成字符串的md5校验值
     * <p>
     * replace getMd5String
     *
     * @param s
     * @return
     */
    public static String getMd5String(String s) {
        return getMd5String(s.getBytes(StandardCharsets.UTF_8));
    }


    /**
     * 生成文件的md5校验值
     *
     * @param file
     * @return
     * @throws IOException
     */
    public static String getFileMd5String(File file) throws IOException {
        try (InputStream fis = Files.newInputStream(file.toPath())) {
            return getFileMd5String(fis);
        }
    }

    /**
     * 生成文件的md5校验值
     *
     * @param fis 输入流
     * @return
     * @throws IOException
     */
    public static String getFileMd5String(InputStream fis) throws IOException {
        return DigestUtils.md5DigestAsHex(fis);
    }

    /**
     * 生成字节 的 md5 校验
     *
     * @param bytes
     * @return
     */
    public static String getMd5String(byte[] bytes) {
        return DigestUtils.md5DigestAsHex(bytes);
    }
}
