package skynet.boot.common;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 建议使用  import org.apache.http.client.utils.URLEncodedUtils; 代替
 *
 * <AUTHOR>
 */
@Deprecated
public class QueryStringUtils {

    public static Map<String, String> regularParam2Map(String params) {
        String result = regularParam(params);
        String[] tokens = StringUtils.splitPreserveAllTokens(result, '&');
        if (tokens != null && tokens.length > 0) {
            Map<String, String> resultMap = new HashMap<>(tokens.length);
            String[] splits = null;
            for (String token : tokens) {
                splits = StringUtils.split(token, "=");
                if (splits != null && 2 == splits.length) {
                    resultMap.put(StringUtils.trim(splits[0]), StringUtils.trim(splits[1]));
                }
            }
            return resultMap;
        }
        return new HashMap<>(0);
    }

    private static String regularParam(String params) {
        if (StringUtils.isBlank(params)) {
            return StringUtils.EMPTY;
        }
        String result = StringUtils.EMPTY;
        try {
            result = StringUtils.trim(URLDecoder.decode(params, StandardCharsets.UTF_8.displayName()));
        } catch (Throwable ignored) {

        }
        result = StringUtils.replacePattern(result, "[&]{2,}", "&");
        result = StringUtils.replacePattern(result, "[=]{2,}", "=");
        return result;
    }


    public static <T> T decode(String queryString, Class<T> clazz) {
        Map<String, String> map = regularParam2Map(queryString);
        return JSONObject.parseObject(JSON.toJSONString(map), clazz);
    }

    @SuppressWarnings("unchecked")
    public static String encode(Object object) throws Exception {
        try {
            Map<String, String> map = JSONObject.parseObject(JSON.toJSONString(object), Map.class);

            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                sb.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), "utf-8")).append("&");
            }
            return sb.deleteCharAt(sb.length() - 1).toString();
        } catch (Exception e) {
            throw e;
        }
    }
}


//	public static void main(String[] args) throws Exception {
//		String tes = "user=lyhu&pwd=iflytek";
//		User user = decode(tes, User.class);
//		System.err.println(encode(user));
//	}
//
//	static class User extends Jsonable {
//		private String name;
//		private String pwd;
//
//		public String getName() {
//			return name;
//		}
//
//		public void setName(String name) {
//			this.name = name;
//		}
//
//		public String getPwd() {
//			return pwd;
//		}
//
//		public void setPwd(String pwd) {
//			this.pwd = pwd;
//		}
//	}
