package skynet.boot.common.disruptor;

import com.lmax.disruptor.EventHandler;

/**
 * 数据管道 事件处理器
 *
 * <pre>
 *     使用disruptor需要增加pom引用：
 *
 *          &lt;dependency&gt;
 *             &lt;groupId&gt;com.lmax&lt;/groupId&gt;
 *             &lt;artifactId&gt;disruptor&lt;/artifactId&gt;
 *         &lt;/dependency&gt;
 * </pre>
 *
 * @param <D> 管道中的数据类型
 * @param <P> 管道处理器的初始化参数类型
 * <AUTHOR> [2016年6月16日下午3:36:54]
 */
public interface DataEventHandler<D, P> extends EventHandler<DataEvent<D>>, AutoCloseable {
    void init(P param) throws Exception;
}
