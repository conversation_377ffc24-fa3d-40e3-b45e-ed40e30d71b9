package skynet.boot.common.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import skynet.boot.common.dto.AbstractPatchDTO;

import java.util.List;

/**
 * 通用 service 基类，包含常用的增删改查接口
 *
 * <AUTHOR>
 */
public interface TemplateService<E, D, ID> {

    /**
     * 查找列表-分页
     *
     * @param pageable
     * @return
     */
    Page<D> findAllByCriteria(Criteria criteria, Class<E> clazz, Pageable pageable);

    /**
     * 查找列表-不分页
     *
     * @return
     */
    List<D> findAllByCriteria(Criteria criteria, Class<E> clazz);

    /**
     * 查找列表-不分页
     *
     * @return
     */
    List<D> findAllByCriteria(Criteria criteria, Class<E> clazz, Sort sort);

    /**
     * 查找单条
     *
     * @param id
     * @return
     */
    D findById(ID id);

    /**
     * 创建
     *
     * @param dto
     * @return
     */
    D save(D dto);

    /**
     * 修改
     *
     * @param dto
     */
    D update(ID id, D dto);

    /**
     * 部分更新
     *
     * @param patchDTO
     */
    D patch(ID id, AbstractPatchDTO<ID> patchDTO);

    /**
     * 删除单条
     *
     * @param id
     */
    void delete(ID id);

    /**
     * 复制
     *
     * @param id
     */
    D copy(ID id, List<String> renameFields, Class<E> clazz);
}
