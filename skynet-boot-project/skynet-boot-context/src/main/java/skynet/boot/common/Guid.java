package skynet.boot.common;

import org.apache.commons.lang3.RandomStringUtils;

/**
 * GUID生成器
 *
 * <AUTHOR>
 */
public class Guid {

    /**
     * 得到随机GUID 26位
     *
     * @return 26位 随机guid
     */
    public static String randomGuid() {
        return RandomStringUtils.secure().nextAlphanumeric(26);
    }

    /**
     * 得到大写 16位随机 GUID
     *
     * <pre>
     * </pre>
     *
     * @return
     */
    public static String random() {
        return RandomStringUtils.secure().nextAlphanumeric(16).toUpperCase();
    }
}
