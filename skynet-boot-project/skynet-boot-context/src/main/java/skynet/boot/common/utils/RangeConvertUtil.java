package skynet.boot.common.utils;

import com.google.common.collect.Range;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RangeConvertUtil {
    /**
     * 一系列规则
     */
    private List<CustomRange> customRanges;
    private CustomRange targetInterval;

    // 将 CustomRange 转换为 Range<Integer>
    public List<Range<Double>> toRangeArray() {
        List<Range<Double>> ranges = new ArrayList<>();
        for (int i = 0; i < customRanges.size(); i++) {
            CustomRange cr = customRanges.get(i);
            ranges.add(convertToRange(cr));
        }
        return ranges;
    }

    /**
     * 将单个 CustomRange 转换为 Range<Double>。
     *
     * @param cr 需要转换的 CustomRange 对象。
     * @return 根据 CustomRange 的类型，返回对应的 Range<Double> 对象；如果类型无效，抛出异常。
     */
    public static Range<Double> convertToRange(CustomRange cr) {
        switch (cr.getType()) {
            case "closed":
                return Range.closed(cr.getStart(), cr.getEnd());
            case "open":
                return Range.open(cr.getStart(), cr.getEnd());
            case "closedOpen":
                return Range.closedOpen(cr.getStart(), cr.getEnd());
            case "openClosed":
                return Range.openClosed(cr.getStart(), cr.getEnd());
            case "greaterThan":
                return Range.greaterThan(cr.getStart());
            case "atLeast":
                return Range.atLeast(cr.getStart());
            case "lessThan":
                return Range.lessThan(cr.getEnd());
            case "atMost":
                return Range.atMost(cr.getEnd());
            case "all":
                return Range.all();
            default:
                throw new IllegalArgumentException("无效的类型: " + cr.getType());
        }
    }


    @Data
    public static class CustomRange {
        /**
         * 单个规则
         */
        private Double start;

        private Double end;
        /**
         * (a..b)	{x | a < x < b}	 open(C, C)
         * [a..b]	{x | a <= x <= b}	closed(C, C)
         * [a..b)	{x | a <= x < b}	closedOpen(C, C)
         * (a..b]	{x | a < x <= b}	openClosed(C, C)
         * (a..+∞)	{x | x > a}	    greaterThan(C)
         * [a..+∞)	{x | x >= a}	atLeast(C)
         * (-∞..b)	{x | x < b}	 lessThan(C)
         * (-∞..b]	{x | x <= b}	atMost(C)
         * (-∞..+∞)	all values	all()
         */
        private String type;

    }
}
