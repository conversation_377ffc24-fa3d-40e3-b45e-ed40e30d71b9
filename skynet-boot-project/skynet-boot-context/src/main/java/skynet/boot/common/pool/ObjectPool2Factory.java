package skynet.boot.common.pool;

/**
 * 对象池2工厂接口,用于创建特定类型的对象。
 * 该接口的实现类需要提供创建对象的方法。
 * <p>
 * 示例代码:
 * <pre>
 * public class MyObjectPool2Factory implements ObjectPool2Factory<MyObject> {
 *     private static final int MAX_INDEX = 100;
 *
 *     {@literal @}Override
 *     public MyObject makeObject(int index) throws Exception {
 *         Assert.isTrue(index >= 0 && index < MAX_INDEX, "Index must be between 0 and " + (MAX_INDEX - 1));
 *         return new MyObject(index);
 *     }
 * }
 * </pre>
 *
 * @param <T> 对象类型,该类型必须实现 AutoCloseable 接口
 * <AUTHOR>
 */
public interface ObjectPool2Factory<T extends AutoCloseable> {

    /**
     * 创建一个对象。
     *
     * @param index 对象的索引,用于标识对象在池中的位置。索引必须在有效范围内。
     * @return 创建的对象
     * @throws Exception 如果对象创建过程中发生错误,则抛出异常
     */
    T makeObject(int index) throws Exception;
}