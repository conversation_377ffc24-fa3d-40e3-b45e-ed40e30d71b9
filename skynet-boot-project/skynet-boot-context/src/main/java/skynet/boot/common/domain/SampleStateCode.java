package skynet.boot.common.domain;


import lombok.Getter;
import lombok.Setter;

/**
 * 错误码列表
 * * Deprecated  by lyhu 2022年06月17日13:39:54
 * <pre>
 *  参考：http://wiki.iflytek.com/pages/viewpage.action?pageId=235845978
 * </pre>
 * <p>
 * TODO：by lyhu
 *
 * <AUTHOR> [2019年1月24日 上午10:36:10]
 */
@Setter
@Getter
@Deprecated
public class SampleStateCode {

    // public final static SampleStateCode SUCCESS = new SampleStateCode("成功", 1);
    // public final static SampleStateCode ERROR = new SampleStateCode("失败", 0);
    public final static SampleStateCode NO_AUTHER = new SampleStateCode("UNAUTHORIZED", 401);

    public final static SampleStateCode TURING_SUCCESS = new SampleStateCode("SUCCESS", 0);
    public final static SampleStateCode TURING_ERROR_FAIL = new SampleStateCode("FAIL", -1);
    public final static SampleStateCode TURING_ERROR_EXCEPTION = new SampleStateCode("ERROR", -2);

    public final static SampleStateCode TURING_ERROR_GENERAL = new SampleStateCode("一般性错误", 10001);
    public final static SampleStateCode TURING_ERROR_OUT_OF_MEMORY = new SampleStateCode("内存越界", 10002);
    public final static SampleStateCode TURING_ERROR_FILE_NOT_FOUND = new SampleStateCode("找不到文件", 10003);
    public final static SampleStateCode TURING_ERROR_NOT_SUPPORT = new SampleStateCode("不支持", 10004);
    public final static SampleStateCode TURING_ERROR_NOT_IMPLEMENT = new SampleStateCode("没有实现", 10005);
    public final static SampleStateCode TURING_ERROR_ACCESS = new SampleStateCode("操作没有权限", 10006);
    public final static SampleStateCode TURING_ERROR_INVALID_PARA = new SampleStateCode("无效的参数名", 10007);
    public final static SampleStateCode TURING_ERROR_INVALID_PARA_VALUE = new SampleStateCode("无效的参数值", 10008);
    public final static SampleStateCode TURING_ERROR_INVALID_HANDLE = new SampleStateCode("无效的句柄", 10009);
    public final static SampleStateCode TURING_ERROR_INVALID_DATA = new SampleStateCode("无效的数据", 10010);
    public final static SampleStateCode TURING_ERROR_NO_LICENSE = new SampleStateCode("没有授权", 10011);
    public final static SampleStateCode TURING_ERROR_NOT_INIT = new SampleStateCode("没有初始化", 10012);
    public final static SampleStateCode TURING_ERROR_NULL_HANDLE = new SampleStateCode("空句柄", 10013);
    public final static SampleStateCode TURING_ERROR_OVERFLOW = new SampleStateCode("栈溢出", 10014);
    public final static SampleStateCode TURING_ERROR_TIME_OUT = new SampleStateCode("超时", 10015);
    public final static SampleStateCode TURING_ERROR_OPEN_FILE = new SampleStateCode("打开文件失败", 10016);
    public final static SampleStateCode TURING_ERROR_NOT_FOUND = new SampleStateCode("没有找到", 10017);
    public final static SampleStateCode TURING_ERROR_NO_ENOUGH_BUFFER = new SampleStateCode("没有足够的内存", 10018);
    public final static SampleStateCode TURING_ERROR_NO_DATA = new SampleStateCode("没有数据", 10019);
    public final static SampleStateCode TURING_ERROR_NO_MORE_DATA = new SampleStateCode("没有更多的数据", 10020);
    public final static SampleStateCode TURING_ERROR_NO_RESPONSE_DATA = new SampleStateCode("没有响应数据", 10021);
    public final static SampleStateCode TURING_ERROR_ALREADY_EXIST = new SampleStateCode("已经存在", 10022);
    public final static SampleStateCode TURING_ERROR_LOAD_MODULE = new SampleStateCode("加载模块失败", 10023);
    public final static SampleStateCode TURING_ERROR_BUSY = new SampleStateCode("系统忙碌", 10024);
    public final static SampleStateCode TURING_ERROR_INVALID_CONFIG = new SampleStateCode("无效的配置", 10025);
    public final static SampleStateCode TURING_ERROR_VERSION_CHECK = new SampleStateCode("版本错误", 10026);
    public final static SampleStateCode TURING_ERROR_CANCELED = new SampleStateCode("操作取消", 10027);
    public final static SampleStateCode TURING_ERROR_INVALID_MEDIA_TYPE = new SampleStateCode("无效的媒体类型", 10028);
    public final static SampleStateCode TURING_ERROR_CONFIG_INITIALIZE = new SampleStateCode("配置初始化失败", 10029);
    public final static SampleStateCode TURING_ERROR_CREATE_HANDLE = new SampleStateCode("创建句柄失败", 10030);
    public final static SampleStateCode TURING_ERROR_CODING_LIB_NOT_LOAD = new SampleStateCode("编解码库没有加载", 10031);
    public final static SampleStateCode TURING_ERROR_USER_CANCELLED = new SampleStateCode("用户取消", 10032);
    public final static SampleStateCode TURING_ERROR_INVALID_OPERATION = new SampleStateCode("无效的操作", 10033);
    public final static SampleStateCode TURING_ERROR_INVALID_UID = new SampleStateCode("无效的用户ID", 10034);
    public final static SampleStateCode TURING_ERROE_INVALID_PASSWORD = new SampleStateCode("无效的密码", 10035);
    public final static SampleStateCode TURING_ERROR_MSG_NOT_COMPLETE = new SampleStateCode("消息不完整", 10036);
    public final static SampleStateCode TURING_ERROR_MSG_NO_SESSION_ID = new SampleStateCode("消息中没有会话ID", 10037);
    public final static SampleStateCode TURING_ERROR_MSG_INVALID_SESSION_ID = new SampleStateCode("消息中会话ID无效", 10038);

    public final static SampleStateCode TURING_ERROR_NET_GENERAL = new SampleStateCode("一般性/未知网络错", 11001);
    public final static SampleStateCode TURING_ERROR_NET_OPENSOCK = new SampleStateCode("打开套接字失败  ", 11002);
    public final static SampleStateCode TURING_ERROR_NET_CONNECTSOCK = new SampleStateCode("网络连接失败", 11003);
    public final static SampleStateCode TURING_ERROR_NET_ACCEPTSOCK = new SampleStateCode("接受套接字失败", 11004);
    public final static SampleStateCode TURING_ERROR_NET_SENDSOCK = new SampleStateCode("网络发送失败", 11005);
    public final static SampleStateCode TURING_ERROR_NET_RECVSOCK = new SampleStateCode("网络接收失败", 11006);
    public final static SampleStateCode TURING_ERROR_NET_INVALIDSOCK = new SampleStateCode("无效的套接字", 11007);
    public final static SampleStateCode TURING_ERROR_NET_BADADDRESS = new SampleStateCode("无效的地址", 11008);
    public final static SampleStateCode TURING_ERROR_NET_BINDSEQUENCE = new SampleStateCode("绑定次序错误", 11009);
    public final static SampleStateCode TURING_ERROR_NET_NOTOPENSOCK = new SampleStateCode("套接字未打开", 11010);
    public final static SampleStateCode TURING_ERROR_NET_NOTBIND = new SampleStateCode("套接字未绑定", 11011);
    public final static SampleStateCode TURING_ERROR_NET_NOTLISTEN = new SampleStateCode("套接字未侦听", 11012);
    public final static SampleStateCode TURING_ERROR_NET_CONNECTCLOSE = new SampleStateCode("连接关闭", 11013);
    public final static SampleStateCode TURING_ERROR_NET_NOTDGRAMSOCK = new SampleStateCode("非数据报套接字", 11014);
    public final static SampleStateCode TURING_ERROR_NET_DNS = new SampleStateCode("DNS解析错误", 11015);
    public final static SampleStateCode TURING_ERROR_NET_INIT = new SampleStateCode("网络初始化失败", 11016);
    public final static SampleStateCode TURING_ERROR_NET_TIMEOUT = new SampleStateCode("网络超时", 11017);

    // 成员变量
    private String name;
    private int code;

    // 构造方法
    public SampleStateCode(String name, int code) {
        this.name = name;
        this.code = code;
    }

}