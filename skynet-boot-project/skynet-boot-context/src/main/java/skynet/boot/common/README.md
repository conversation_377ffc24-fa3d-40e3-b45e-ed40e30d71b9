## 其他常用工具

- OS工具
- GZip工具
- 文件压缩工具
- 高性能目录配置
- Boot启动工具

### 1.OS工具 - `OsUtil`

```java
public final static String LOCAL_CALLBACK_IP = "127.0.0.1";


public static Map<String, Object> getOs();

/**
 * 获取本机的HostName；
 *
 * @return
 */
public synchronized static String getHostName();

/**
 * 获取本机的 ip；
 * <p>
 *
 * @return 获取本机的IP
 * @throws InterruptedException
 */
public synchronized static String getIPAddress();

public static int getCurrentPid();

public static List<String> getAllIPs();

/**
 * 获取本机所有IPv6的地址 包含网卡名
 * <p>
 * 如：fe80:0:0:0:de42:8cb:8db1:3366%ens1f0
 *
 * @return
 */
public static List<String> getAllIPv6List();

/**
 * 是否是本机的IP地址，支持IPv4 和IPv6
 *
 * @param ip
 * @return
 */
public static boolean isLocalIP(String ip);

/**
 * 获取随机可用的端口号
 *
 * @return 随机可用的端口号
 */
public synchronized static int getRandomPort();

/**
 * 获取随机可用的端口号
 *
 * @param beginPort 开始端口序号(包含)
 * @param endPort   结束端口序号(包含)
 * @return
 */
public static int getRandomPort(int beginPort, int endPort);

/**
 * 检测端口是否被占用
 *
 * @param port 端口号
 * @return 端口被占用返回true，否则返回false
 */
public synchronized static boolean isUsedPort(int port);

/**
 * 检测端口号是否合法
 *
 * @param port 端口号
 * @return 合法：true | 不合法 false
 */
public static boolean isValidPort(int port);
```

### 2.GZip工具 - `GZipUtils`

```java
public final class GZipUtils {

    public static String compressString(String input) throws IOException;

    public static byte[] compress(byte[] input) throws IOException;

    public static byte[] decompress(byte[] input) throws IOException;
}

```

### 3.文件压缩工具 - `FileZipUtil`

```java

public abstract class FileZipUtil {

    /**
     * 压缩文件
     *
     * @param sourceFolder 需压缩文件 或者 文件夹 路径
     * @param zipFilePath  压缩文件输出路径
     * @throws Exception
     */
    public static void zip(String sourceFolder, String zipFilePath) throws Exception;

    /**
     * 压缩文件
     *
     * @param sourceFolders 一组 压缩文件夹 或 文件
     * @param zipFilePath   压缩文件输出路径
     * @throws Exception
     */
    public static void zip(String[] sourceFolders, String zipFilePath) throws Exception;

    /**
     * 递归压缩文件
     * 
     * @param parentFile
     * @param basePath
     * @param zos
     * @throws Exception
     */
    private static void zipFile(File parentFile, String basePath, ZipOutputStream zos) throws Exception;

    /**
     * 解压zip文件，默认采用 utf-8 编码，失败后，再用 gbk 重试
     *
     * @param zipFileName     待解压的zip文件路径，例如：c:\\a.zip
     * @param outputDirectory 解压目标文件夹,例如：c:\\a\
     */
    public static void unZip(String zipFileName, String outputDirectory) throws Exception;

    /**
     * 解压zip文件
     *
     * @param zipFileName     待解压的zip文件路径，例如：c:\\a.zip
     * @param outputDirectory 解压目标文件夹,例如：c:\\a\
     * @param charset         the {@linkplain java.nio.charset.Charset charset} to
     *                        be used to decode the ZIP entry name and comment that are not
     *                        encoded by using UTF-8 encoding (indicated by entry's general
     *                        purpose flag).
     */
    public static void unZip(String zipFileName, String outputDirectory, Charset charset) throws Exception;

    /**
     * 解压zip文件
     *
     * @param unZipFile       待解压的zip文件
     * @param outputDirectory 解压目标文件夹
     */
    public static void unZip(File unZipFile, File outputDirectory) throws Exception;

    /**
     * 创建目录
     *
     * @param directory
     * @param subDirectory
     */
    private static void createDirectory(String directory, String subDirectory) ;

    public static List<String> readFileNames(String zipFileName) throws Exception;

    /**
     * 无需解压直接读取Zip文件（包含目录和文件名）
     *
     * @param zipFileName 文件
     * @throws Exception
     */
    public static List<String> readFileNames(File zipFileName) throws Exception;

    /**
     * 获取 压缩文件的 顶级 目录和文件
     *
     * @param zipFileName
     * @return
     * @throws Exception
     */
    public static List<String> readTopFileNames(String zipFileName) throws Exception;

```

### 4.高性能目录配置 - `ShmDirUtils`

工具接口：

```java
public class ShmDirUtils {

    /**
     * 获取 skynet 的高性能目录
     *
     * @return
     */
    public File getShmDir4Skynet() ;


    /**
     * 获取 子目录名称 获取完整的 高性能目录
     *
     * @param child
     * @return
     * @throws IOException
     */
    public File getShmDir(String child) throws IOException ;

    /**
     * /dev/shm  或 skynet.dev.shm.path 配置后的目标
     * 非linux ， 将是 java.io.tmpdir 目录
     *
     * @return
     */
    public File getShmDir();
}

```

使用示例：

```java
// environment is org.springframework.core.env.Environment;
ShmDirUtils shmDirUtils =new ShmDirUtils(environment);
shmDirUtils.getShmDir4Skynet();
//TODO
```

### 5.Boot启动工具 - AppUtils

为了简化SpringBoot，统一的启动输出提示信息，抽取了AppUtils.run 工具类：

```java
@SpringBootApplication
public class AppBoot {
    public static void main(String[] args) {
        AppUtils.run(AppBoot.class, args);
    }
}
```

输出消息示例：

```text 
...
09-02 17:49:25.345  INFO [     main] s.p.m.server.ManagerZookeeperRegistry   [ 88]: Register to zookeeper discovery for namespace = /skynet/discovery/skylab-platform	id=APPL4OW9N4BQRK6H
09-02 17:49:25.370  INFO [     main] skynet.boot.AppUtils                    [ 35]: Start the ant-xmanager@ant. args = [--skynet.zookeeper.server_list=172.31.164.8:2181,--debug]
09-02 17:49:25.371  INFO [     main] skynet.boot.AppUtils                    [ 49]: 
------------------------------------------------------------------
	Application [ant-xmanager@ant] is running! [cost= 6.116 seconds]
	Local: 		http://lyhu.local:2230
	External: 	http://10.3.121.187:2230
	Profile(s): 	skynet-zk-config
------------------------------------------------------------------
```

