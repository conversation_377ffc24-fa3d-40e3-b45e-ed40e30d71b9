package skynet.boot.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * GZIP 压缩工具类
 *
 * <AUTHOR> [2016年11月13日下午2:34:53]
 */
@Slf4j
public final class GZipUtils {

    private GZipUtils() {
        // 私有构造器禁止实例化
    }

    /**
     * 压缩字符串为GZIP格式的Base64编码字符串
     *
     * @param input 需要压缩的字符串
     * @return Base64 编码的压缩字符串
     */
    public static String compressString(String input) {
        Assert.hasText(input, "Input string must not be null or empty");

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            GzipOutputer.writeToGzip(bos, input.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(bos.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException("Failed to compress string", e);
        }
    }

    /**
     * 解压Base64编码的GZIP字符串
     *
     * @param base64String Base64 编码的GZIP字符串
     * @return 原始字符串
     */
    public static String decompressString(String base64String) {
        Assert.hasText(base64String, "Base64 string must not be null or empty");

        try {
            byte[] compressedBytes = Base64.getDecoder().decode(base64String);
            if (compressedBytes.length == 0) {
                return "";
            }
            return new String(decompress(compressedBytes), StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid Base64 string", e);
        }
    }

    /**
     * 压缩字节数组到GZIP格式
     *
     * @param input 需要压缩的字节数组
     * @return GZIP 格式的字节数组
     */
    public static byte[] compress(byte[] input) {
        Assert.notNull(input, "Input array must not be null");
        if (input.length == 0) {
            return new byte[0];
        }
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            GzipOutputer.writeToGzip(bos, input);
            return bos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Failed to compress bytes", e);
        }
    }

    /**
     * 解压GZIP格式的字节数组
     *
     * @param compressed GZIP 格式的字节数组
     * @return 原始字节数组
     */
    public static byte[] decompress(byte[] compressed) {
        if (compressed == null || compressed.length == 0) {
            return new byte[0];
        }

        try (GZIPInputStream gis = new GZIPInputStream(new ByteArrayInputStream(compressed))) {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = gis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }

            return bos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Failed to decompress bytes", e);
        }
    }

    private static class GzipOutputer {
        private GzipOutputer() {
        }

        private static void writeToGzip(ByteArrayOutputStream bos, byte[] content) throws IOException {
            try (GZIPOutputStream gos = new GZIPOutputStream(bos)) {
                gos.write(content);
            }
        }
    }

//	public static void main(String[] args) throws IOException {
//		String unzip = "ABCDEFGHIABCDEFGHIABCDEFGHIABCDEFGHIABCDEFGHI 中国人";
//		StringBuilder sb = new StringBuilder();
//		for (int i = 0; i < 100; i++) {
//			sb.append(unzip);
//		}
//		unzip = sb.toString();
//
//		String file = "/Users/<USER>/wav/wavs/8k8bitALaw.wav";
//		byte[] bs = FileUtils.readFileToByteArray(new File(file));
//		byte[] zipbs = GZipUtils.compress(bs);
//
//		byte[] unzipbs = GZipUtils.decompress(zipbs);
//		System.out.println(String.format("bs:%d;\tzipbs:%d\tzunzipbs:%d", bs.length, zipbs.length, unzipbs.length));
//
//		String zipString = GZipUtils.compressString(unzip);
//		String unzipString = GZipUtils.decompressString(zipString);
//
//		System.out.println(unzip);
//		System.out.println(zipString);
//
//		System.out.println(unzipString);
//
//	}
}
