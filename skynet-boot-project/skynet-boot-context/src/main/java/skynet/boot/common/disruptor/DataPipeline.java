package skynet.boot.common.disruptor;

import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.util.DaemonThreadFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 数据管道 基类
 *
 *
 * <pre>
 * 管道大小缺省是 256,如果要修改，可以复写 getBufferSize() 方法。
 * </pre>
 * <pre>
 *     使用disruptor需要增加pom引用：
 *
 *          &lt;dependency&gt;
 *             &lt;groupId&gt;com.lmax&lt;/groupId&gt;
 *             &lt;artifactId&gt;disruptor&lt;/artifactId&gt;
 *         &lt;/dependency&gt;
 * </pre>
 *
 * @param <D> 管道内流的数据类型
 * @param <P> 管道初始化参数类型
 * <AUTHOR> [2016年12月2日下午2:59:16]
 */
@Slf4j
public abstract class DataPipeline<D, P> implements AutoCloseable {

    private Disruptor<DataEvent<D>> disruptor;
    public static final int DEFAULT_BUFFER_SIZE = 256;

    /**
     * 管道大小
     *
     * <pre>
     * Specify the size of the ring buffer, must be power of 2.
     * </pre>
     *
     * @return 管道大小
     */
    public int getBufferSize() {
        return DEFAULT_BUFFER_SIZE;
    }

    private List<DataEventHandler<D, P>> handlers = new ArrayList<>();

    /**
     * 初始化
     *
     * @param param 参数
     * @throws Exception Exception
     */
    protected abstract void onInit(P param) throws Exception;

    protected abstract List<DataEventHandler<D, P>> getHandlers();

    protected abstract void onClose() throws Exception;

    @SuppressWarnings("unchecked")
    public final void init(P param) throws Exception {

        // 初始化，并获取处理器
        this.onInit(param);
        this.handlers = getHandlers();

        if (handlers == null || handlers.isEmpty()) {
            throw new Exception("data event handlers is empty");
        }

        log.debug("init DataPipeline begin...");
        // Specify the size of the ring buffer, must be power of 2.
        int ringSize = (int) Math.pow(2, (int) (Math.log(Math.abs(this.getBufferSize())) / Math.log(2)));
        log.debug(String.format("set ringSize:%d,real ringBufferSize:%d", ringSize, this.getBufferSize()));

        // The factory for the event
        EventFactory<DataEvent<D>> factory = DataEvent::new;
        // Construct the Disruptor
        disruptor = new Disruptor<>(factory, ringSize, DaemonThreadFactory.INSTANCE);

        disruptor.handleEventsWith(handlers.toArray(new DataEventHandler[0]));

        // Start the Disruptor, starts all threads running
        disruptor.start();
        log.debug("init DataPipeline end");
    }

    /**
     * 向管道中提交数据
     *
     * @param data 数据
     */
    public final void onData(D data) {
        log.trace("DataPipeline receive record data: {}", data);

        RingBuffer<DataEvent<D>> ringBuffer = disruptor.getRingBuffer();
        // Grab the next sequence
        long sequence = ringBuffer.next();
        try {
            DataEvent<D> event = ringBuffer.get(sequence);
            // Fill with data
            event.setValue(data);
        } finally {
            ringBuffer.publish(sequence);
        }
    }

    /**
     * 关闭管道，直到 管道中所有的数据处理完成
     *
     * @throws Exception Exception
     */
    public void closeUntilComplete() throws Exception {
        closeUntilComplete(-1, TimeUnit.MILLISECONDS);
    }

    /**
     * 关闭管道，直到 管道中所有的数据处理完成
     *
     * @param timeout  超时时间
     * @param timeUnit 超时时间单位
     * @throws Exception Exception
     */
    public void closeUntilComplete(final long timeout, final TimeUnit timeUnit) throws Exception {
        log.debug("closeUntilComplete begin");
        if (disruptor != null) {
            disruptor.shutdown(timeout, timeUnit);
        }
        log.debug("closeUntilComplete end");
    }

    /**
     * 关闭管道， 立即停止管道中所有的数据处理
     */
    @Override
    public final void close() throws Exception {
        log.debug("close DataPipeline begin");
        onClose();
        disruptor.halt();

        for (DataEventHandler<D, P> dataEventHandler : handlers) {
            dataEventHandler.close();
        }
        handlers.clear();
        log.debug("close DataPipeline end");
    }
}
