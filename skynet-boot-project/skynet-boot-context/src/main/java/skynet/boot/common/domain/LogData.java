package skynet.boot.common.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Deprecated by lyhu  2022年06月17日13:40:55
 * 日志 结构化数据对象模型
 */
@Getter
@Setter
@Deprecated
public class LogData {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * -- GETTER --
     *
     * @return the trackId
     */
    @JSONField(ordinal = 10)
    private String trackId;
    /**
     * -- GETTER --
     *
     * @return the tag
     */
    @JSONField(ordinal = 20)
    private String tag;
    /**
     * -- GETTER --
     *
     * @return the context
     */
    @JSONField(ordinal = 40)
    private Object context;
    /**
     * -- GETTER --
     *
     * @return the ctxNum
     */
    @JSONField(ordinal = 50)
    private float ctxNum;
    /**
     * -- GETTER --
     *
     * @return the err
     */
    @JSONField(ordinal = 80)
    private Throwable err;

    public LogData() {
    }

    public LogData(Object data, String tag) {
        this(null, data, tag);
    }

    public LogData(String trackId, Object data, String tag) {
        this(trackId, data, tag, null);
    }

    public LogData(String trackId, Object data, String tag, Throwable t) {

        this.trackId = trackId;

        if (data != null) {
            if (data instanceof String) {
                this.context = String.valueOf(data);
            } else if ((data instanceof Integer) || (data instanceof Long) || (data instanceof Float) || (data instanceof Double)) {
                //str 也记录一次
                this.context = String.valueOf(data);
                this.ctxNum = Float.parseFloat(String.valueOf(data));
            } else if (data instanceof Date) {
                this.context = SDF.format((Date) data);
            } else {
                this.context = data;
            }
        }

        this.tag = tag;
        this.err = t;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this, JSONWriter.Feature.NotWriteDefaultValue);
    }

    /***
     * "trackId", "tag"
     */
    public static final List<String> PROP_NAMES = Arrays.asList("trackId", "tag", "context");

}
