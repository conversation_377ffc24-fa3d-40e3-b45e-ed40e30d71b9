package skynet.boot.common.pool;

import java.util.concurrent.ArrayBlockingQueue;

/**
 * 基于 ArrayBlockingQueue 实现的对象池存储类,遵循先进先出(FIFO)原则。
 * 该类实现了 ObjectPoolStore 接口,提供了对象的存取、清空和检查功能。
 *
 * @param <T> 存储在对象池中的对象类型
 * <AUTHOR>
 */
public class ArrayBlockingQueueStore<T> implements ObjectPoolStore<T> {

    /**
     * 用于存储对象的 ArrayBlockingQueue 实例。
     */
    private final ArrayBlockingQueue<T> objectQueue;

    /**
     * 构造函数,初始化对象池的大小。
     *
     * @param poolSize 对象池的最大容量
     */
    public ArrayBlockingQueueStore(int poolSize) {
        this.objectQueue = new ArrayBlockingQueue<>(poolSize);
    }

    /**
     * 将对象放入对象池的尾部。
     * 如果对象池已满,此方法将阻塞,直到有空间可用。
     *
     * @param object 要放入对象池的对象
     * @throws InterruptedException 如果当前线程在等待时被中断
     */
    @Override
    public void put(T object) throws InterruptedException {
        objectQueue.put(object);
    }

    /**
     * 从对象池的头部取出对象。
     * 如果对象池为空,此方法将阻塞,直到有对象可用。
     *
     * @return 从对象池取出的对象
     * @throws InterruptedException 如果当前线程在等待时被中断
     */
    @Override
    public T take() throws InterruptedException {
        return objectQueue.take();
    }

    /**
     * 清空对象池中的所有对象。
     */
    @Override
    public void clear() {
        objectQueue.clear();
    }

    /**
     * 检查对象池中是否包含指定的对象。
     *
     * @param object 要检查的对象
     * @return 如果对象池包含该对象, 则返回 true;否则返回 false
     */
    @Override
    public boolean contains(Object object) {
        return objectQueue.contains(object);
    }

}