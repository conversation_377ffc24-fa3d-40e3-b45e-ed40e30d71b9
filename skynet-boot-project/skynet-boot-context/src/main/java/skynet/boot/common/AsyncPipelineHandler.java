package skynet.boot.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import skynet.boot.common.concurrent.ManualResetEvent;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;

/**
 * Asynchronous Pipeline Handler
 *
 * <pre>
 * Features:
 * - Acts as both pipeline and handler
 * - Non-blocking processing
 * - One-to-one mapping between pipeline and handler
 *
 * Extension points:
 * Subclasses can override these methods for initialization and cleanup:
 * - onInit()  : Called during initialization
 * - onClose() : Called during shutdown
 * - onEvent() : Required implementation for event processing
 * </pre>
 *
 * @param <D> Type of data flowing through the pipeline
 * @param <P> Type of initialization parameters
 * <AUTHOR>
 */
public abstract class AsyncPipelineHandler<D, P> implements AutoCloseable {

    private static final Logger logger = LoggerFactory.getLogger(AsyncPipelineHandler.class);

    // Configuration constants
    private static final int MAX_CONSUMER_THREADS = 64;
    private static final int DEFAULT_SHUTDOWN_TIMEOUT_MS = 10_000;

    // Core components
    private ExecutorService executorService;
    private BlockingQueue<D> blockingQueue;

    // State tracking
    private final AtomicBoolean isBreak = new AtomicBoolean(false);
    private final LongAdder processCounter = new LongAdder();
    private final LongAdder errorCounter = new LongAdder();
    private final ManualResetEvent completionSignal = new ManualResetEvent(true);
    private final AtomicInteger threadId = new AtomicInteger();

    // Pipeline configuration
    private int queueCapacity = 8;

    /**
     * Initialize handler with custom parameters. Override this method to add custom
     * initialization logic.
     *
     * @param param Custom initialization parameters
     * @throws Exception if initialization fails
     */
    protected void onInit(P param) throws Exception {
        // Default implementation - no initialization required
    }

    /**
     * Process a single event. Must be implemented by concrete classes.
     *
     * @param event Event to process
     * @throws Exception if event processing fails
     */
    protected abstract void onEvent(D event) throws Exception;

    /**
     * Cleanup handler resources. Override this method to add custom cleanup logic.
     *
     * @throws Exception if cleanup fails
     */
    protected void onClose() throws Exception {
        // Default implementation - no cleanup required
    }

    /**
     * Initialize the pipeline with single consumer thread.
     *
     * @param queueSize Queue capacity
     * @param param     Initialization parameters
     * @throws Exception if initialization fails
     */
    public final void init(int queueSize, P param) throws Exception {
        init(queueSize, 1, param);
    }

    /**
     * Initialize the pipeline with multiple consumer threads.
     *
     * @param queueSize    Queue capacity
     * @param consumerSize Number of consumer threads
     * @param param        Initialization parameters
     * @throws Exception if initialization fails
     */
    public final void init(int queueSize, int consumerSize, P param) throws Exception {
        String className = this.getClass().getSimpleName();
        logger.info("Initializing {} with queue size={}, consumer threads={}", className, queueSize, consumerSize);

        validateConsumerSize(consumerSize);

        // Initialize handler
        onInit(param);
        isBreak.set(false);

        // Setup processing queue
        this.blockingQueue = new LinkedBlockingQueue<>(queueSize);
        this.queueCapacity = queueSize;

        // Initialize thread pool
        this.executorService = createExecutorService(consumerSize, className);
        startConsumerThreads(consumerSize, className);

        logger.info("Successfully initialized {}", className);
    }

    private void validateConsumerSize(int consumerSize) {
        if (consumerSize <= 0 || consumerSize > MAX_CONSUMER_THREADS) {
            throw new IllegalArgumentException(
                    String.format("Consumer thread count must be between 1 and %d (given: %d)",
                            MAX_CONSUMER_THREADS, consumerSize));
        }
    }


    private ExecutorService createExecutorService(int consumerSize, String className) {
        return Executors.newFixedThreadPool(
                consumerSize,
                r -> Thread.ofVirtual()
                        .name(String.format("%s-pool-t-%d", className, threadId.getAndIncrement()))
                        .unstarted(r)
        );
    }

    private void startConsumerThreads(int consumerSize, String className) {
        for (int i = 0; i < consumerSize; i++) {
            executorService.submit(() -> processEvents(className));
        }
    }

    private void processEvents(String className) {
        while (!isBreak.get()) {
            D data = null;
            try {
                data = blockingQueue.take();
                logger.debug("Retrieved event from queue");
            } catch (InterruptedException e) {
                errorCounter.increment();
                if (!isBreak.get()) {
                    logger.error("Failed to take event from queue: {}", e.getMessage());
                }
                Thread.currentThread().interrupt();
                continue;
            }
            processEvent(data, className);
        }
        logger.info("Consumer thread {} terminated", Thread.currentThread().getName());
    }

    private void processEvent(D data, String className) {
        try {
            onEvent(data);
            logger.debug("Successfully processed event");
        } catch (Exception e) {
            logger.error("Failed to process event in {}: {}", className, e.getMessage(), e);
        } finally {
            processCounter.decrement();
            if (processCounter.sum() == 0) {
                completionSignal.set();
            }
        }
    }

    /**
     * Submit data to the pipeline for processing.
     *
     * @param data Data to process (must not be null)
     * @throws InterruptedException if interrupted while waiting to submit
     */
    public void onData(D data) throws InterruptedException {
        if (data == null) {
            logger.warn("Attempted to submit null data to pipeline - ignored");
            return;
        }

        try {
            processCounter.increment();
            completionSignal.reset();
            blockingQueue.put(data);
            logger.debug("Successfully submitted data to pipeline");
        } catch (InterruptedException e) {
            processCounter.decrement();
            logger.error("Failed to submit data to pipeline: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * @return Number of events pending processing
     */
    public int getTodoSize() {
        return processCounter.intValue();
    }

    /**
     * @return Current size of the queue
     */
    public int getQueueSize() {
        return blockingQueue.size();
    }


    /**
     * @return Number of processing errors encountered
     */
    public long getEventErrCount() {
        return errorCounter.longValue();
    }

    /**
     * @return Maximum queue capacity
     */
    public int getPipelineSize() {
        return this.queueCapacity;
    }

    /**
     * Clear all pending events from the queue.
     */
    public void clear() {
        blockingQueue.clear();
        logger.info("Pipeline queue cleared");
    }

    /**
     * Shutdown the pipeline and cleanup resources.
     */
    @Override
    public final void close() throws Exception {
        String className = this.getClass().getName();
        logger.debug("Initiating shutdown of {}", className);

        try {
            // Wait for pending events to complete
            if (!completionSignal.waitOne(DEFAULT_SHUTDOWN_TIMEOUT_MS)) {
                logger.warn("Timed out waiting for event processing to complete");
            }

            isBreak.set(true);
            clear();

            if (executorService != null) {
                executorService.shutdownNow();
            }

            onClose();
            logger.info("Successfully shut down {}", className);
        } catch (InterruptedException e) {
            logger.error("Shutdown interrupted: {}", e.getMessage(), e);
            Thread.currentThread().interrupt();
            throw e;
        }
    }
}