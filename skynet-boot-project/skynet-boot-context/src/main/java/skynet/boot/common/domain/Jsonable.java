package skynet.boot.common.domain;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.filter.Filter;
import com.alibaba.fastjson2.filter.ValueFilter;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Jsonable
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class Jsonable {

    static {
        log.info("JSON.config(JSONWriter.Feature.WriteEnumsUsingName,true)");
        JSON.config(JSONWriter.Feature.WriteEnumsUsingName, true);
        log.info("JSON.config(JSONWriter.Feature.WriteEnumsUsingName,false)");
        JSON.config(JSONWriter.Feature.ReferenceDetection, false);
    }

    public String toJson() {
        return JSON.toJSONString(this);
    }

    /**
     * toJson String
     *
     * @param fast     JSONWriter.Feature.NotWriteDefaultValue
     * @param filters:
     * @return Json String
     */
    public String toJson(boolean fast, Filter... filters) {
        return fast ? JSON.toJSONString(this, filters, JSONWriter.Feature.NotWriteDefaultValue) : JSON.toJSONString(this, filters, JSONWriter.Feature.PrettyFormat);
    }

    /**
     * @return json串
     */
    @Override
    public String toString() {
        return this.toJson();
    }

    /**
     * @param isFilterLargeObject 是否过滤大对象（长文本只截取 前N和后N字符，数组只截取 前N和后N元素）
     * @return json串
     * <p>
     * add since by 4.0.13
     */
    public String toString(boolean isFilterLargeObject) {
        return isFilterLargeObject ? this.toString(DEFAULT_LARGE_OBJECT_ELLIPSES_FILTER) : this.toString();
    }

    /**
     * @param filters 过滤器
     * @return json串
     * <p>
     * add since by 4.0.13
     */
    public String toString(Filter... filters) {
        return JSON.toJSONString(this, filters);
    }

    /**
     * 深度 clone
     *
     * @param clazz 当前类型
     * @param <T>   当前类型
     * @return
     */
    public <T> T clone(Class<T> clazz) {
        return JSON.parseObject(this.toString(), clazz);
    }


    /**
     * Json 序列化过滤器，数组或字符串 超过 长度限制 就省略
     * add since by 4.0.13
     */
    public static class LargeObjectEllipsesFilter implements ValueFilter {

        public static final int CHAR_TOP_N = 20;
        public static final int ARRAY_TOP_N = 10;
        private final LargeObjectEllipsesSetting setting;

        /**
         * 数组长度限制，默认 100
         * 字符串长度，默认 500
         */
        public LargeObjectEllipsesFilter() {
            this(new LargeObjectEllipsesSetting());
        }

        /**
         * @param setting 数组和字符串长度门限 配置
         */
        public LargeObjectEllipsesFilter(LargeObjectEllipsesSetting setting) {
            this.setting = setting;
        }

        @Override
        public Object apply(Object object, String name, Object value) {
            log.debug("Apply LargeObjectEllipsesFilter ..");
            if (value == null) {
                return null;
            }
            if (value instanceof String) {
                return ellipsesString((String) value);
            }
            // 判断array类型字段值是否过长
            if (value instanceof Object[] && ((Object[]) value).length > setting.getArrayLimitLen() && ((Object[]) value).length > ARRAY_TOP_N << 1) {
                //返回TOP N的元素
                Object[] valueArray = (Object[]) value;
                Object[] temps = new Object[ARRAY_TOP_N << 1];
                System.arraycopy(valueArray, 0, temps, 0, ARRAY_TOP_N);
                System.arraycopy(valueArray, valueArray.length - ARRAY_TOP_N, temps, ARRAY_TOP_N, ARRAY_TOP_N);
                return temps;
            }

            // 判断array类型字段值是否过长
            if (value instanceof List && ((List<?>) value).size() > setting.getArrayLimitLen() && ((List<?>) value).size() > ARRAY_TOP_N << 1) {
                //返回TOP N的元素
                List<?> valueArray = ((List<?>) value);
                List<Object> temps = new ArrayList<>(ARRAY_TOP_N << 1);
                temps.addAll(valueArray.subList(0, ARRAY_TOP_N));
                temps.addAll(valueArray.subList(valueArray.size() - ARRAY_TOP_N, valueArray.size()));
                return temps;
            }
            return value;
        }

        public String ellipsesString(String value) {
            if (value == null) {
                return null;
            }
            if (value.length() >= setting.getStringLimitLen() && (long) value.length() > CHAR_TOP_N << 1) {
                return String.format("%s...ellipses %s char...%s", value.substring(0, CHAR_TOP_N), value.length() - CHAR_TOP_N << 1, value.substring(value.length() - CHAR_TOP_N));
            }
            return value;
        }

        public long getArrayLimitLen() {
            return setting.getArrayLimitLen();
        }

        public long getStringLimitLen() {
            return setting.getStringLimitLen();
        }
    }

    @Getter
    @Setter
    public static class LargeObjectEllipsesSetting extends Jsonable {

        private final static long DEFAULT_ARRAY_LIMIT_LEN = 100;
        private final static long DEFAULT_STRING_LIMIT_LEN = 500;

        public LargeObjectEllipsesSetting() {
            this(DEFAULT_ARRAY_LIMIT_LEN, DEFAULT_STRING_LIMIT_LEN);
        }

        public LargeObjectEllipsesSetting(long arrayLimitLen, long stringLimitLen) {
            this.arrayLimitLen = arrayLimitLen;
            this.stringLimitLen = stringLimitLen;
        }

        /**
         * 数组的长度门限
         */
        private long arrayLimitLen;
        /**
         * 字符串的长度门限
         */
        private long stringLimitLen;
    }

    /**
     * 默认大对象过滤器，
     * <p>
     * 数组或字符串的长度 门限：100
     * <p>
     * 长文本只截取 前20和后20字符，
     * 数组只截取 前10和后10元素
     */
    public static LargeObjectEllipsesFilter DEFAULT_LARGE_OBJECT_ELLIPSES_FILTER = new LargeObjectEllipsesFilter();

}
