package skynet.boot.common;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.jar.Manifest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.CRC32;

/**
 * 各种方法大杂烩
 *
 * <AUTHOR>
 */
@Deprecated
public class UtilAll {
    public static final String yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
    public static final String yyyy_MM_dd_HH_mm_ss_SSS = "yyyy-MM-dd#HH:mm:ss:SSS";
    public static final String yyyyMMddHHmmss = "yyyyMMddHHmmss";
    public static final String REGEX_FLOAT = "^(0|[1-9]\\d*)\\.{1}\\d+$";
    public static final String REGEX_INT = "^(0|[1-9]{1}\\d*)$";
    private static final Pattern PATTERN_FLOAT;
    private static final Pattern PATTERN_INT;

    static {
        PATTERN_FLOAT = Pattern.compile(REGEX_FLOAT);
        PATTERN_INT = Pattern.compile(REGEX_INT);
    }

    public static int getPid() {
        RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();
        String name = runtime.getName();
        // format: "pid@hostname"
        try {
            return Integer.parseInt(name.substring(0, name.indexOf('@')));
        } catch (Exception e) {
            return -1;
        }
    }

    public static String currentStackTrace() {
        StringBuilder sb = new StringBuilder();
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement ste : stackTrace) {
            sb.append("\n\t");
            sb.append(ste.toString());
        }

        return sb.toString();
    }

    private static final String MANIFEST_FILE_KEY = "META-INF/MANIFEST.MF";

    /**
     * 返回 .jar 文件中 Manifest 对象
     * 路径不存在、路径是文件夹、路径不是 zip 文件都会抛出异常
     *
     * @param jarFile jar file path
     * @return Manifest
     * @throws Exception ex
     */
    public static Manifest getManifest(File jarFile) throws Exception {
        if (!jarFile.exists()) {
            throw new Exception(String.format("file doesn't exist. %s", jarFile));
        }
        if (jarFile.isDirectory()) {
            throw new Exception(String.format("file is a directory. %s", jarFile));
        }
        try (JarFile jar = new JarFile(URLDecoder.decode(jarFile.toString(), "UTF-8"))) {
            Enumeration<JarEntry> entries = jar.entries();
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                if (entry.getName().equals(MANIFEST_FILE_KEY)) {
                    return new Manifest(jar.getInputStream(entry));
                }
            }
        }
        return null;
    }

    /**
     * 计算耗时操作，单位ms
     *
     * @param beginTime 开始时间
     * @return 单位ms
     */
    public static long computeEclipseTimeMilliseconds(final long beginTime) {
        return (System.currentTimeMillis() - beginTime);
    }

    public static boolean isItTimeToDo(final String when) {
        String[] whiles = when.split(";");
        if (whiles.length > 0) {
            Calendar now = Calendar.getInstance();
            for (String w : whiles) {
                int nowHour = Integer.parseInt(w);
                if (nowHour == now.get(Calendar.HOUR_OF_DAY)) {
                    return true;
                }
            }
        }

        return false;
    }

    public static String timeMillisToHumanString() {
        return timeMillisToHumanString(System.currentTimeMillis());
    }

    public static String timeMillisToHumanString(final long t) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(t);
        return String.format("%04d%02d%02d%02d%02d%02d%03d", cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, cal.get(Calendar.DAY_OF_MONTH), cal.get(Calendar.HOUR_OF_DAY),
                cal.get(Calendar.MINUTE), cal.get(Calendar.SECOND), cal.get(Calendar.MILLISECOND));
    }

    public static long computNextMorningTimeMillis() {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(System.currentTimeMillis());
        cal.add(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTimeInMillis();
    }

    public static long computNextMinutesTimeMillis() {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(System.currentTimeMillis());
        cal.add(Calendar.DAY_OF_MONTH, 0);
        cal.add(Calendar.HOUR_OF_DAY, 0);
        cal.add(Calendar.MINUTE, 1);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTimeInMillis();
    }

    public static long computNextHourTimeMillis() {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(System.currentTimeMillis());
        cal.add(Calendar.DAY_OF_MONTH, 0);
        cal.add(Calendar.HOUR_OF_DAY, 1);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTimeInMillis();
    }

    public static long computNextHalfHourTimeMillis() {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(System.currentTimeMillis());
        cal.add(Calendar.DAY_OF_MONTH, 0);
        cal.add(Calendar.HOUR_OF_DAY, 1);
        cal.set(Calendar.MINUTE, 30);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTimeInMillis();
    }

    public static String timeMillisToHumanString2(final long t) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(t);
        return String.format("%04d-%02d-%02d %02d:%02d:%02d,%03d",

                cal.get(Calendar.YEAR),

                cal.get(Calendar.MONTH) + 1,

                cal.get(Calendar.DAY_OF_MONTH),

                cal.get(Calendar.HOUR_OF_DAY),

                cal.get(Calendar.MINUTE),

                cal.get(Calendar.SECOND),

                cal.get(Calendar.MILLISECOND));
    }

    /**
     * 返回日期时间格式，精度到秒<br>
     * 格式如下：2013122305190000
     *
     * @param t millis the new time in UTC milliseconds from the epoch
     * @return 返回日期时间格式
     */
    public static String timeMillisToHumanString3(final long t) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(t);
        return String.format("%04d%02d%02d%02d%02d%02d", //
                cal.get(Calendar.YEAR), //
                cal.get(Calendar.MONTH) + 1, //
                cal.get(Calendar.DAY_OF_MONTH), //
                cal.get(Calendar.HOUR_OF_DAY), //
                cal.get(Calendar.MINUTE), //
                cal.get(Calendar.SECOND));
    }

    public static int crc32(byte[] array) {
        if (array != null) {
            return crc32(array, 0, array.length);
        }

        return 0;
    }

    public static int crc32(byte[] array, int offset, int length) {
        CRC32 crc32 = new CRC32();
        crc32.update(array, offset, length);
        return (int) (crc32.getValue() & 0x7FFFFFFF);
    }

    /**
     * 字节数组转化成16进制形式
     *
     * @param src 字节数组
     * @return 16进制形式
     */
    public static String bytes2string(byte[] src) {
        StringBuilder sb = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                sb.append(0);
            }
            sb.append(hv.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 16进制字符串转化成字节数组
     *
     * @param hexString 16进制字符串
     * @return 字节数组
     */
    public static byte[] string2bytes(String hexString) {
        if (hexString == null || "".equals(hexString)) {
            return null;
        }
        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        char[] hexChars = hexString.toCharArray();
        byte[] d = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }
        return d;
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }


    public static int asInt(String str, int defaultValue) {
        try {
            return Integer.parseInt(str);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static long asLong(String str, long defaultValue) {
        try {
            return Long.parseLong(str);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static String formatDate(Date date, String pattern) {
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        return df.format(date);
    }

    public static Date parseDate(String date, String pattern) {
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        try {
            return df.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    public static String responseCode2String(final int code) {
        return Integer.toString(code);
    }

    public static String frontStringAtLeast(final String str, final int size) {
        if (str != null) {
            if (str.length() > size) {
                return str.substring(0, size);
            }
        }

        return str;
    }

    public static boolean isBlank(String str) {
        int strLen;
        if (str == null || (strLen = str.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((!Character.isWhitespace(str.charAt(i)))) {
                return false;
            }
        }
        return true;
    }

    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * Returns the values from each provided array combined into a single array. For example, {@code concat(new byte[] {a, b}, new byte[] {}, new byte[] {c}} returns the array {@code {a, b, c}}.
     *
     * @param arrays zero or more {@code byte} arrays
     * @return a single array containing all the values from the source arrays, in order
     */
    public static byte[] concat(byte[]... arrays) {
        int length = 0;
        for (byte[] array : arrays) {
            length += array.length;
        }
        byte[] result = new byte[length];
        int pos = 0;
        for (byte[] array : arrays) {
            System.arraycopy(array, 0, result, pos, array.length);
            pos += array.length;
        }
        return result;
    }

    public static int byte2int(byte[] byteData, int offset) {
        return (byteData[offset] & 0xFF) << 24 | (byteData[(offset + 1)] & 0xFF) << 16 | (byteData[(offset + 2)] & 0xFF) << 8 | byteData[(offset + 3)] & 0xFF;
    }

    public static byte[] int2byte(int intData) {
        byte[] byteData = new byte[4];
        byteData[0] = ((byte) (0xFF & intData >> 24));
        byteData[1] = ((byte) (0xFF & intData >> 16));
        byteData[2] = ((byte) (0xFF & intData >> 8));
        byteData[3] = ((byte) (0xFF & intData));
        return byteData;
    }

    public static boolean isInt(String s) {
        Matcher m = PATTERN_INT.matcher(s);
        return m.matches();
    }

    public static boolean isFloat(String s) {
        Matcher m = PATTERN_FLOAT.matcher(s);
        return m.matches();
    }

    /**
     * 将字节数表达为KB/MB/GB/TB,保留两位小数
     *
     * @param size
     * @return
     */
    public static String getReadableBytes(long size) {
        double sizeInLowerUnit = size;
        if (size < 1024) {
            return size + "B";
        } else {
            size = size >> 10;
        }
        if (size < 1024) {
            double d = sizeInLowerUnit / 1024;
            return String.format("%.2fKB", d);
        } else {
            sizeInLowerUnit = size;
            size = size >> 10;
        }
        if (size < 1024) {
            double d = sizeInLowerUnit / 1024;
            return String.format("%.2fMB", d);
        } else {
            sizeInLowerUnit = size;
            size = size >> 10;
        }
        if (size < 1024) {
            double d = sizeInLowerUnit / 1024;
            return String.format("%.2fGB", d);
        } else {
            sizeInLowerUnit = size;
        }
        double d = sizeInLowerUnit / 1024;
        return String.format("%.2fTB", d);
    }
//    public static void main(String[] args) {
////		System.out.println(isInt("012"));
////		System.out.println(isInt("12"));
////		System.out.println(isFloat("0.12"));
////		System.out.println(isFloat(".12"));
////		System.out.println(isFloat("12."));
////		System.out.println(isFloat("02.3"));
////		System.out.println(isFloat("0.03"));
////		System.out.println(isFloat("a0.03"));
//        System.out.println(getReadableBytes(1248L));
//    }
}