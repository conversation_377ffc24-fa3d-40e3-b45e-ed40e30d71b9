package skynet.boot.common;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 实例实例统计器
 *
 * <AUTHOR>
 * @since 4.0.13
 */
public class InstanceCounter {
    private final AtomicInteger ATOMIC_OBJECT_INDEX = new AtomicInteger();
    private final AtomicInteger ATOMIC_OBJECT_COUNTER = new AtomicInteger();

    /**
     * 创建实例时
     *
     * @return 当前 实例的 序号（从0开始自增长）
     */
    public int onBuild() {
        this.ATOMIC_OBJECT_COUNTER.incrementAndGet();
        return ATOMIC_OBJECT_INDEX.incrementAndGet();
    }

    /**
     * 销毁实例时
     *
     * @return 剩下实例的数量
     */
    public int onDestroy() {
        return this.ATOMIC_OBJECT_COUNTER.decrementAndGet();
    }

    /**
     * 最大的序号
     *
     * @return
     */
    public int getLastIndex() {
        return ATOMIC_OBJECT_INDEX.get();
    }

    /**
     * 还剩下实例数量
     *
     * @return 还剩下实例数量
     */
    public int getLeftSize() {
        return this.ATOMIC_OBJECT_COUNTER.get();
    }
}