package skynet.boot.common.pool;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.util.Assert;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.LongAdder;

/**
 * 对象池2
 *
 * <p>
 * 这是一个线程安全的对象池实现,支持自定义对象工厂和存储机制。
 * </p>
 * <p>
 * 示例代码:
 * </p>
 *
 * <pre>
 * public static void main(String[] args) throws Exception {
 *     int poolSize = 2;
 *     int loopCount = 10;
 *     final ObjectPool2<Worker> workerPool = new ObjectPool2<>(poolSize, index -> new Worker());
 *
 *     List<Thread> threads = new ArrayList<>();
 *     for (int j = 0; j < loopCount; j++) {
 *         Thread t = Thread.ofVirtual()
 *                 .name("worker-" + j)
 *                 .start(() -> {
 *                     for (int i = 0; i < 10; i++) {
 *                         Worker worker = workerPool.borrowObject();
 *                         try {
 *                             if (worker != null) {
 *                                 worker.run(i);
 *                             }
 *                         } catch (Exception e) {
 *                             System.err.println("error: \t" + e.getMessage());
 *                         } finally {
 *                             workerPool.returnObject(worker);
 *                         }
 *                     }
 *                 });
 *         threads.add(t);
 *     }
 *     for (Thread thread : threads) {
 *         thread.join();
 *     }
 *     workerPool.close();
 *     System.out.println("------------end---------------");
 * }
 * </pre>
 *
 * @param <T> T
 * <AUTHOR>
 */
@Slf4j
public class ObjectPool2<T extends AutoCloseable> implements AutoCloseable {

    /**
     * 获取对象列表
     */
    @Getter
    private final List<T> objList = new CopyOnWriteArrayList<>();

    /**
     * 获取 对象池的大小
     */
    @Getter
    private final int poolSize;
    private final ObjectPool2Factory<T> objectPool2Factory;
    private final ObjectPoolStore<T> objectPoolStore;

    private final LongAdder freeInteger = new LongAdder();
    private final LongAdder usedInteger = new LongAdder();
    private final LongAdder waitingInteger = new LongAdder();

    /**
     * 初始化对象池,默认使用 ArrayBlockingQueueStore 存储池中的对象
     *
     * @param poolSize          如果为0,将是CPU 核数,最大 20000
     * @param objectPoolFactory 对象池工厂,用于创建对象
     * @throws Exception 如果初始化过程中发生错误
     */
    public ObjectPool2(int poolSize, ObjectPool2Factory<T> objectPoolFactory) throws Exception {
        this(poolSize, objectPoolFactory, new ArrayBlockingQueueStore<>(poolSize));
    }

    /**
     * 初始化对象池
     *
     * @param poolSize          如果为0,将是CPU 核数,最大 20000
     * @param objectPoolFactory 对象池工厂,用于创建对象
     * @param objectPoolStore   自定义对象池存储
     * @throws Exception 如果初始化过程中发生错误
     */
    public ObjectPool2(int poolSize, ObjectPool2Factory<T> objectPoolFactory, ObjectPoolStore<T> objectPoolStore)
            throws Exception {
        Assert.notNull(objectPoolFactory, "ObjectPool2Factory cannot be null");
        Assert.notNull(objectPoolStore, "ObjectPoolStore cannot be null");

        log.debug("ObjectPool [{}] size= {}", this.getClass(), poolSize);

        poolSize = Math.max(0, Math.min(20000, poolSize));

        if (poolSize == 0) {
            poolSize = Runtime.getRuntime().availableProcessors();
        }
        log.debug("ObjectPool [{}] size= {}", this.getClass(), poolSize);

        this.objectPoolStore = objectPoolStore;
        this.objectPool2Factory = objectPoolFactory;
        this.addObject(poolSize);
        this.freeInteger.add(poolSize);
        this.poolSize = poolSize;
    }

    /**
     * 借一个对象
     *
     * @return 借一个对象
     */
    public T borrowObject() {
        log.debug("borrowObject begin ...");
        boolean ok = true;
        try {
            waitingInteger.increment();
            return objectPoolStore.take();
        } catch (InterruptedException e) {
            log.error(String.format("%s borrowObject error:%s ", this.getClass(), e.getMessage()));
            ok = false;
        } finally {
            if (ok) {
                usedInteger.increment();
                waitingInteger.decrement();
                freeInteger.decrement();
            }
            log.debug("borrowObject end.");
        }
        return null;
    }

    /**
     * 归还对象
     *
     * @param obj 对象
     * @throws IllegalArgumentException 如果对象为null或已经存在于对象池中
     */
    public void returnObject(T obj) {
        Assert.notNull(obj, "Object to return cannot be null");
        log.debug("ReturnObject begin ...");

        if (objectPoolStore.contains(obj)) {
            throw new IllegalArgumentException("The obj is already contain.");
        }

        try {
            objectPoolStore.put(obj);
            freeInteger.increment();
            usedInteger.decrement();
        } catch (InterruptedException e) {
            log.error(String.format("ObjectPool [%s] returnObject error:%s ", this.getClass(), e.getMessage()));
        }
        log.debug("ReturnObject end.");
    }

    private synchronized void addObject(int poolSize) throws Exception {
        log.debug("ObjectPool [{}] will make {} object.", this.getClass(), poolSize);
        StopWatch sw = new StopWatch();
        sw.start();
        T tmp = null;
        for (int index = 0; index < poolSize; index++) {
            log.debug("ObjectPool [{}] make the No.{}/{} object.", tmp != null ? tmp.getClass().getName() : "", index,
                    poolSize);

            T obj = objectPool2Factory.makeObject(index);
            if (obj != null) {
                objectPoolStore.put(obj);
                objList.add(obj);
                tmp = obj;
            }
        }
        log.info("ObjectPool make {} object [{}]. cost={}", objList.size(), tmp != null ? tmp.getClass() : "", sw);
    }

    @Override
    public synchronized void close() throws Exception {
        log.debug("ObjectPool {} close begin.", this.getClass());

        // TODO: 考虑 objectPoolStore 是否全部还回来了 by lyhu
        for (T obj : objList) {
            obj.close();
        }
        objectPoolStore.clear();
        objList.clear();
        usedInteger.reset();
        freeInteger.reset();
        waitingInteger.reset();
        log.debug("ObjectPool {} close end.", this.getClass());
    }

    /**
     * 获取 池中 可用的对象数量
     *
     * @return 可用的对象数量
     */
    public int getFreeSize() {
        return Math.max(freeInteger.intValue(), 0);
    }

    /**
     * 对象池中对象使用次数
     *
     * @return 使用次数
     */
    public int getUsedSize() {
        return usedInteger.intValue();
    }

    /**
     * 借对象 等待数
     *
     * @return 等待数
     */
    public int getWaitingSize() {
        return waitingInteger.intValue();
    }
}
