package skynet.boot.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Stack;


/**
 * 文件工具类
 *
 * <AUTHOR> [Oct 15, 2017 9:23:37 PM]
 */
@Slf4j
public final class Files {

    private static void checkNotNull(String str) {
        Assert.hasText(str, "not null");
    }

    public static String getFileExtension(String fullName) {
        checkNotNull(fullName);
        String fileName = new File(fullName).getName();
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }

    /**
     * Returns the lexically cleaned form of the path name, <i>usually</i> (but not always) equivalent to the original. The following heuristics are used:
     *
     * <ul>
     * <li>empty string becomes .
     * <li>. stays as .
     * <li>fold out ./
     * <li>fold out ../ when possible
     * <li>collapse multiple slashes
     * <li>delete trailing slashes (unless the path is just "/")
     * </ul>
     *
     * <p>
     * These heuristics do not always match the behavior of the filesystem. In particular, consider the path {@code a/../b}, which {@code simplifyPath} will change to {@code b}. If {@code a} is a
     * symlink to {@code x}, {@code a/../b} may refer to a sibling of {@code x}, rather than the sibling of {@code a} referred to by {@code b}.
     * </p>
     *
     * @param path 路径
     * @return 合并后的路径
     */
    public static String simplifyPath(String path) {
        checkNotNull(path);
        StringBuilder result = new StringBuilder();
        String[] word = path.split("/");
        Stack<String> stk = new Stack<>();
        for (String s : word) {
            if ("..".equals(s)) {
                if (!stk.empty()) {
                    stk.pop();
                }
            } else if (".".equals(s) || "".equals(s)) {
                continue;
            } else {
                stk.push(s);
            }
        }
        for (String s : stk) {

            result.append("/").append(s);

        }
        return "".contentEquals(result) ? "/" : result.toString();
    }


    public static String getNameWithoutExtension(String file) {
        checkNotNull(file);
        String fileName = new File(file).getName();
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? fileName : fileName.substring(0, dotIndex);
    }

    /*
     * 文件大小转换
     */
    public static String getPrintSize(long size) {
        // 如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        double value = (double) size;
        if (value < 1024) {
            return value + "B";
        } else {
            value = new BigDecimal(value / 1024).setScale(2, RoundingMode.DOWN).doubleValue();
        }
        // 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
        // 因为还没有到达要使用另一个单位的时候
        // 接下去以此类推
        if (value < 1024) {
            return value + "KB";
        } else {
            value = new BigDecimal(value / 1024).setScale(2, RoundingMode.DOWN).doubleValue();
        }
        if (value < 1024) {
            return value + "MB";
        } else {
            // 否则如果要以GB为单位的，先除于1024再作同样的处理
            value = new BigDecimal(value / 1024).setScale(2, RoundingMode.DOWN).doubleValue();
            return value + "GB";
        }
    }

    public static String readFileStringContent(File file) throws IOException {
        return readFileStringContent(file, StandardCharsets.UTF_8);
    }

    /**
     * 获取文件字符串内容
     *
     * @param file
     * @return
     * @throws IOException
     */
    public static String readFileStringContent(File file, Charset cs) throws IOException {
        // 读取字节转换到字符
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            return readFileStringContent(fileInputStream, cs);
        }
    }

    public static String readFileStringContent(InputStream fileInputStream) throws IOException {
        return readFileStringContent(fileInputStream, StandardCharsets.UTF_8);
    }

    /**
     * 获取文件字符串内容
     *
     * @param fileInputStream 文件input 流
     * @return
     * @throws IOException
     */
    public static String readFileStringContent(InputStream fileInputStream, Charset cs) throws IOException {
        // 读取字节转换到字符
        StringBuilder builder = new StringBuilder();
        try (InputStreamReader reader = new InputStreamReader(fileInputStream, cs)) {
            char[] buf = new char[64];
            int count = 0;
            while ((count = reader.read(buf)) != -1) {
                builder.append(buf, 0, count);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.debug("readFileStringContent = {}", builder);
        return builder.toString();
    }

    /**
     * 向文件中写字符串内容
     *
     * @param file
     * @return
     * @throws IOException
     */
    public static void writeFileStringContent(File file, String content) throws IOException {
        // 写字符转换成字节流
        FileOutputStream fileWriter = new FileOutputStream(file);
        try (OutputStreamWriter writer = new OutputStreamWriter(fileWriter, StandardCharsets.UTF_8)) {
            writer.write(content);
            log.debug("writeFileStringContent:{}", content);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
