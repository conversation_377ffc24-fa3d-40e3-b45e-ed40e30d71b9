package skynet.boot.common.domain;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Deprecated  by lyhu 2022年06月17日13:39:54
 *
 * <AUTHOR>
 */
@Deprecated
public class SampleState extends Jsonable {

    private int code;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private Throwable exception;

    public SampleState() {
        this(SampleStateCode.TURING_SUCCESS);
    }

    public SampleState(SampleStateCode sampleStateCode) {
        this(sampleStateCode.getCode());
    }

    public SampleState(int code) {
        this.code = code;
    }

    public SampleState(String errMsg, Throwable throwable) {
        this(new Exception(errMsg, throwable));
    }

    public SampleState(Throwable ex) {
        this(SampleStateCode.TURING_ERROR_EXCEPTION);
        this.exception = ex;
    }

    @JSONField
    @JsonProperty
    public int getCode() {
        return code;
    }

    /**
     * @return 是否处理成功
     */
    @JSONField(ordinal = 10)
    @JsonProperty(index = 10)
    public boolean isSuccess() {
        return null == this.exception;
    }

    /**
     * 建议使用 code
     *
     * @return
     */
    @Deprecated
    @JSONField(name = "ok", ordinal = 20)
    @JsonProperty(value = "ok", index = 20)
    public int getOK() {
        return isSuccess() ? 1 : 0;
    }

    /**
     * 失败原因
     *
     * @return 失败原因
     */
    @JSONField(name = "failed", ordinal = 30)
    @JsonProperty(value = "failed", index = 30)
    public String getFailedMessage() {
        return ExceptionExt.getMergedMessage(this.exception);
    }


    @JSONField(name = "failed", ordinal = 30)
    @JsonProperty(value = "failed", index = 30)
    public void setFailedMessage(String failedMsg) {
        exception = StringUtils.isNotBlank(failedMsg) ? new Exception(failedMsg) : null;
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public Throwable getException() {
        return exception;
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public void setException(Throwable exception) {
        this.exception = exception;
    }

    @JSONField
    @JsonProperty
    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


    static class ExceptionExt {
        public static String getMergedMessage(Throwable exp) {
            if (exp == null) {
                return null;
            }

            List<String> msgList = new ArrayList<>(1);
            fillMessage(msgList, exp);
            return StringUtils.join(msgList, ";");
        }

        private static void fillMessage(List<String> msgList, Throwable exp) {
            if (exp == null) {
                return;
            }
            msgList.add(exp.getMessage());
            if (exp.getCause() == null) {
            } else {
                fillMessage(msgList, exp.getCause());
            }
        }
    }
}