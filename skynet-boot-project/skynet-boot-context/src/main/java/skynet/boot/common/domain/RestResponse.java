package skynet.boot.common.domain;


import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <pre>
 * Rest返回对象
 * </pre>
 *
 * @param <T>
 * <AUTHOR> [2018年4月19日 下午9:04:26]
 */
@Getter
@Setter
@Deprecated
@Schema(title = "RestResponse", description = "Rest返回对象")
public class RestResponse<T> extends Jsonable {

    /**
     * -- GETTER --
     *
     * @return the state
     */
    @JSONField(ordinal = 10)
    @JsonProperty(index = 10)
    private SampleState state;

    /**
     * -- GETTER --
     *
     * @return the body
     */
    @JSONField(ordinal = 20)
    @JsonProperty(index = 20)
    private T body;

    public RestResponse() {
        state = new SampleState();
    }

    public RestResponse(T inBody) {
        body = inBody;
        state = new SampleState();
    }

    public RestResponse(SampleState state) {
        this.state = state;
    }
}