package skynet.boot.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * Jackson-based JSON utility class for serialization and deserialization.
 * Thread-safe implementation with performance optimizations.
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonUtils implements ApplicationContextAware {

    // Thread-safe singleton ObjectMapper instance
    private static volatile ObjectMapper mapper;

    // Cache for JavaType objects to improve performance
    private static final ConcurrentHashMap<Class<?>, JavaType> TYPE_CACHE = new ConcurrentHashMap<>();

    // Empty string constant
    private static final String EMPTY_STRING = "";

    /**
     * Initialize the ObjectMapper with common configurations
     */
    static {
        initializeMapper();
    }

    /**
     * Creates and configures the default ObjectMapper
     */
    private static void initializeMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // Deserialization features
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_NUMERIC_LEADING_ZEROS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);

        // Serialization features
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        mapper = objectMapper;
    }

    /**
     * Converts an object to a JSON string
     *
     * @param obj Object to convert
     * @return JSON string representation or empty string if null
     */
    public static String toJSONString(Object obj) {
        return obj != null ? toJSONString(obj, () -> EMPTY_STRING, false) : EMPTY_STRING;
    }

    /**
     * Converts an object to a formatted (pretty-printed) JSON string
     *
     * @param obj Object to convert
     * @return Formatted JSON string representation or empty string if null
     */
    public static String toFormatJSONString(Object obj) {
        return obj != null ? toJSONString(obj, () -> EMPTY_STRING, true) : EMPTY_STRING;
    }

    /**
     * Converts an object to a JSON string with custom formatting and default value handling
     *
     * @param obj             Object to convert
     * @param defaultSupplier Supplier for default value if conversion fails
     * @param format          Whether to pretty-print the JSON
     * @return JSON string representation or default value if conversion fails
     */
    public static String toJSONString(Object obj, Supplier<String> defaultSupplier, boolean format) {
        if (obj == null) {
            return defaultSupplier.get();
        }

        try {
            // Handle primitive types directly for better performance
            if (obj instanceof String) {
                return obj.toString();
            }
            if (obj instanceof Number) {
                return obj.toString();
            }
            if (obj instanceof Boolean) {
                return obj.toString();
            }

            // Use Jackson for complex objects
            if (format) {
                return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
            }
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert object to JSON: {}", obj.getClass().getName(), e);
            return defaultSupplier.get();
        }
    }

    /**
     * Converts a JSON string to a Java object
     *
     * @param value  JSON string to convert
     * @param tClass Target class
     * @param <T>    Type parameter
     * @return Converted object or null if conversion fails
     */
    public static <T> T toJavaObject(String value, Class<T> tClass) {
        return StringUtils.isNotBlank(value) ? toJavaObject(value, tClass, () -> null) : null;
    }

    /**
     * Converts an object to a Java object of the specified type
     *
     * @param obj    Object to convert
     * @param tClass Target class
     * @param <T>    Type parameter
     * @return Converted object or null if conversion fails
     */
    public static <T> T toJavaObject(Object obj, Class<T> tClass) {
        if (obj == null) {
            return null;
        }

        // If object is already of the target type, return it directly
        if (tClass.isInstance(obj)) {
            return tClass.cast(obj);
        }

        return toJavaObject(toJSONString(obj), tClass, () -> null);
    }

    /**
     * Converts a JSON string to a Java object with custom default value handling
     *
     * @param value           JSON string to convert
     * @param tClass          Target class
     * @param defaultSupplier Supplier for default value if conversion fails
     * @param <T>             Type parameter
     * @return Converted object or default value if conversion fails
     */
    public static <T> T toJavaObject(String value, Class<T> tClass, Supplier<T> defaultSupplier) {
        if (StringUtils.isBlank(value)) {
            return defaultSupplier.get();
        }

        try {
            return mapper.readValue(value, tClass);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert JSON to object of type {}: {}", tClass.getName(),
                    StringUtils.abbreviate(value, 100), e);
            return defaultSupplier.get();
        }
    }

    /**
     * Converts a JSON string to a list of Java objects
     *
     * @param value  JSON string to convert
     * @param tClass Element class
     * @param <T>    Element type parameter
     * @return List of converted objects or null if conversion fails
     */
    public static <T> List<T> toJavaObjectList(String value, Class<T> tClass) {
        return StringUtils.isNotBlank(value) ? toJavaObjectList(value, tClass, () -> null) : null;
    }

    /**
     * Converts an object to a list of Java objects
     *
     * @param obj    Object to convert
     * @param tClass Element class
     * @param <T>    Element type parameter
     * @return List of converted objects or null if conversion fails
     */
    public static <T> List<T> toJavaObjectList(Object obj, Class<T> tClass) {
        if (obj == null) {
            return null;
        }

        // If object is already a List, try to cast it directly
        if (obj instanceof List<?>) {
            try {
                // Verify that all elements are of the correct type
                List<?> list = (List<?>) obj;
                if (list.isEmpty() || list.stream().allMatch(tClass::isInstance)) {
                    @SuppressWarnings("unchecked")
                    List<T> result = (List<T>) list;
                    return result;
                }
            } catch (Exception e) {
                log.debug("Could not cast List directly, falling back to JSON conversion", e);
            }
        }

        return toJavaObjectList(toJSONString(obj), tClass, () -> null);
    }

    /**
     * Converts a JSON string to a list of Java objects with custom default value handling
     *
     * @param value           JSON string to convert
     * @param tClass          Element class
     * @param defaultSupplier Supplier for default value if conversion fails
     * @param <T>             Element type parameter
     * @return List of converted objects or default value if conversion fails
     */
    public static <T> List<T> toJavaObjectList(String value, Class<T> tClass, Supplier<List<T>> defaultSupplier) {
        if (StringUtils.isBlank(value)) {
            return defaultSupplier.get();
        }

        try {
            // Get or create the JavaType for List<T>
            JavaType javaType = getListType(tClass);
            return mapper.readValue(value, javaType);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert JSON to list of type {}: {}", tClass.getName(),
                    StringUtils.abbreviate(value, 100), e);
            return defaultSupplier.get();
        }
    }

    /**
     * Creates a deep copy of an object using JSON serialization/deserialization
     *
     * @param obj    Object to copy
     * @param tClass Target class
     * @param <T>    Type parameter
     * @return Deep copy of the object or null if copying fails
     */
    public static <T> T jsonCopy(Object obj, Class<T> tClass) {
        return obj != null ? toJavaObject(toJSONString(obj), tClass) : null;
    }

    /**
     * Converts a JSON string to a Map
     *
     * @param value JSON string to convert
     * @return Map representation or null if conversion fails
     */
    public static Map<String, Object> toMap(String value) {
        return StringUtils.isNotBlank(value) ? toMap(value, () -> null) : null;
    }

    /**
     * Converts an object to a Map
     *
     * @param value Object to convert
     * @return Map representation or null if conversion fails
     */
    public static Map<String, Object> toMap(Object value) {
        return value != null ? toMap(value, () -> null) : null;
    }

    /**
     * Converts an object to a Map with custom default value handling
     *
     * @param value           Object to convert
     * @param defaultSupplier Supplier for default value if conversion fails
     * @return Map representation or default value if conversion fails
     */
    public static Map<String, Object> toMap(Object value, Supplier<Map<String, Object>> defaultSupplier) {
        if (value == null) {
            return defaultSupplier.get();
        }

        // If object is already a Map, return it directly
        if (value instanceof Map) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> result = (Map<String, Object>) value;
                return result;
            } catch (ClassCastException e) {
                log.debug("Could not cast Map directly, falling back to JSON conversion", e);
            }
        }

        return toMap(toJSONString(value), defaultSupplier);
    }

    /**
     * Converts a JSON string to a Map with custom default value handling
     *
     * @param value           JSON string to convert
     * @param defaultSupplier Supplier for default value if conversion fails
     * @return Map representation or default value if conversion fails
     */
    public static Map<String, Object> toMap(String value, Supplier<Map<String, Object>> defaultSupplier) {
        if (StringUtils.isBlank(value)) {
            return defaultSupplier.get();
        }

        try {
            TypeReference<LinkedHashMap<String, Object>> typeRef = new TypeReference<>() {};
            return mapper.readValue(value, typeRef);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert JSON to Map: {}", StringUtils.abbreviate(value, 100), e);
            return defaultSupplier.get();
        }
    }

    /**
     * Converts a JSON string to a List
     *
     * @param value JSON string to convert
     * @return List representation or null if conversion fails
     */
    public static List<Object> toList(String value) {
        return StringUtils.isNotBlank(value) ? toList(value, () -> null) : null;
    }

    /**
     * Converts an object to a List
     *
     * @param value Object to convert
     * @return List representation or null if conversion fails
     */
    public static List<Object> toList(Object value) {
        return value != null ? toList(value, () -> null) : null;
    }

    /**
     * Converts a JSON string to a List with custom default value handling
     *
     * @param value           JSON string to convert
     * @param defaultSupplier Supplier for default value if conversion fails
     * @return List representation or default value if conversion fails
     */
    public static List<Object> toList(String value, Supplier<List<Object>> defaultSupplier) {
        if (StringUtils.isBlank(value)) {
            return defaultSupplier.get();
        }

        try {
            TypeReference<List<Object>> typeRef = new TypeReference<>() {};
            return mapper.readValue(value, typeRef);
        } catch (JsonProcessingException e) {
            log.error("Failed to convert JSON to List: {}", StringUtils.abbreviate(value, 100), e);
            return defaultSupplier.get();
        }
    }

    /**
     * Converts an object to a List with custom default value handling
     *
     * @param value           Object to convert
     * @param defaultSupplier Supplier for default value if conversion fails
     * @return List representation or default value if conversion fails
     */
    public static List<Object> toList(Object value, Supplier<List<Object>> defaultSupplier) {
        if (value == null) {
            return defaultSupplier.get();
        }

        // If object is already a List, return it directly
        if (value instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> result = (List<Object>) value;
            return result;
        }

        return toList(toJSONString(value), defaultSupplier);
    }

    /**
     * Gets a long value from a Map
     *
     * @param map Map to get value from
     * @param key Key to get value for
     * @return Long value or 0 if not found or not a valid number
     */
    public static long getLong(Map<String, Object> map, String key) {
        if (map == null || map.isEmpty()) {
            return 0L;
        }

        Object value = map.get(key);
        if (value == null) {
            return 0L;
        }

        // Handle different types of values
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        String valueStr = String.valueOf(value);
        if (StringUtils.isBlank(valueStr) || !StringUtils.isNumeric(valueStr)) {
            return 0L;
        }

        try {
            return Long.parseLong(valueStr);
        } catch (NumberFormatException e) {
            log.debug("Could not parse '{}' as long", valueStr, e);
            return 0L;
        }
    }

    /**
     * Gets an int value from a Map
     *
     * @param map Map to get value from
     * @param key Key to get value for
     * @return Int value or 0 if not found or not a valid number
     */
    public static int getInt(Map<String, Object> map, String key) {
        if (map == null || map.isEmpty()) {
            return 0;
        }

        Object value = map.get(key);
        if (value == null) {
            return 0;
        }

        // Handle different types of values
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }

        String valueStr = String.valueOf(value);
        if (StringUtils.isBlank(valueStr) || !StringUtils.isNumeric(valueStr)) {
            return 0;
        }

        try {
            return Integer.parseInt(valueStr);
        } catch (NumberFormatException e) {
            log.debug("Could not parse '{}' as int", valueStr, e);
            return 0;
        }
    }

    /**
     * Gets a cached JavaType for List<T>
     *
     * @param elementClass Element class
     * @param <T>          Element type parameter
     * @return JavaType for List<T>
     */
    private static <T> JavaType getListType(Class<T> elementClass) {
        return TYPE_CACHE.computeIfAbsent(elementClass,
                clazz -> mapper.getTypeFactory().constructParametricType(List.class, clazz));
    }

    /**
     * Sets the ObjectMapper from the Spring application context
     *
     * @param applicationContext Spring application context
     * @throws BeansException if an error occurs while getting the ObjectMapper bean
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            ObjectMapper contextMapper = applicationContext.getBean(ObjectMapper.class);
            if (contextMapper != null) {
                mapper = contextMapper;
                // Clear type cache when mapper changes
                TYPE_CACHE.clear();
                log.info("Using ObjectMapper from Spring context");
            }
        } catch (BeansException e) {
            log.warn("Could not get ObjectMapper from Spring context, using default", e);
        }
    }
}

