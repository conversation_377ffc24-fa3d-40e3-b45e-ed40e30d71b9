package skynet.boot.common.utils;

import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
public class ListUtils {

    public static <T> List<T> getList(List<T> list, Integer pageIndex, Integer pageSize) {
        if (list.isEmpty()) {
            return list;
        }

        if (ObjectUtils.isEmpty(pageIndex)) {
            pageIndex = 0;
        }

        if (ObjectUtils.isEmpty(pageSize)) {
            pageSize = list.size();
        }

        if (pageIndex > pageSize && ObjectUtils.isEmpty(pageSize)) {
            return new ArrayList<>();
        }

        int fromIndex = pageIndex * pageSize;
        int index = (pageIndex + 1) * pageSize;


        int toIndex = Math.min(list.size(), index);
        return list.subList(fromIndex, toIndex);
    }


}
