package skynet.boot.common;

import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.lang.Nullable;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * 泛型参数工具类
 *
 * <AUTHOR>
 */
public final class GenericClassUtils {


    /**
     * 获取 父接口泛型参数
     * <p>
     * 示例：
     * static class MyList extends ArrayList<String> implements List<String> {
     * <p>
     * }
     * <p>
     * MyList myList = new MyList();
     * Class<?> myListClazz = GenericClassUtils.getGenericInterfacesClass(myList.getClass(), 0);
     * * class java.lang.String
     *
     * @param type  实现的接口子类类型
     * @param index 参数序号
     * @return
     */
    public static Class<?> getGenericInterfacesClass(Type type, int index) {
        if (type instanceof ParameterizedType) {
            return (Class<?>) ((ParameterizedType) type).getActualTypeArguments()[index];
        } else if (type instanceof Class) {
            return getGenericInterfacesClass(((Class<?>) type).getGenericInterfaces()[0], index);
        } else {
            throw new IllegalArgumentException("Unsupported type: " + type);
        }
    }

    /**
     * 获取 父接口泛型参数
     *
     * @param instance
     * @param index
     * @return
     */
    public static Class<?> getGenericInterfacesClassForInstance(@Nullable Object instance, int index) {

        Object targetObject = AopProxyUtils.getSingletonTarget(instance);
        if (targetObject == null) {
            targetObject = instance;
        }
        assert targetObject != null;
        return getGenericInterfacesClass(targetObject.getClass(), index);
    }


    public static Class<?> getGenericSuperclassForInstance(@Nullable Object instance, int index) {

        Object targetObject = AopProxyUtils.getSingletonTarget(instance);
        if (targetObject == null) {
            targetObject = instance;
        }
        assert targetObject != null;
        return (Class<?>) ((ParameterizedType) (targetObject.getClass().getGenericSuperclass())).getActualTypeArguments()[index];
    }


}
