package skynet.boot.common.disruptor;

import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.InsufficientCapacityException;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 管道处理器
 *
 * <pre>
 * - 自身是管道，同时又是处理器
 * - 非阻塞处理
 * - 一个管道对应一个处理器
 *
 * - 管道大小缺省是 256,如果要修改，可以复写 getBufferSize() 方法。
 * </pre>
 *
 *
 * <pre>
 *     使用disruptor需要增加pom引用：
 *
 *          &lt;dependency&gt;
 *             &lt;groupId&gt;com.lmax&lt;/groupId&gt;
 *             &lt;artifactId&gt;disruptor&lt;/artifactId&gt;
 *         &lt;/dependency&gt;
 * </pre>
 *
 * @param <D> 管道内流的数据类型
 * @param <P> 管道初始化参数类型
 * <AUTHOR> [2016年12月1日下午5:11:50]
 */
@Slf4j
public abstract class DataPipelineHandler<D, P> implements EventHandler<DataEvent<D>>, AutoCloseable {

    private static final AtomicInteger index = new AtomicInteger(0);

    private Disruptor<DataEvent<D>> disruptor;

    public static final int DEFAULT_BUFFER_SIZE = 256;

    private final LongAdder counter = new LongAdder();
    private CountDownLatch cancelDownLatch = new CountDownLatch(0);
    private CountDownLatch awaitDownLatch = new CountDownLatch(0);

    private ExecutorService executor;
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * Specify the size of the ring buffer, must be power of 2.
     *
     * <pre>
     * Default: 256
     * </pre>
     *
     * @return BUFFER_SIZE
     */
    public int getBufferSize() {
        return DEFAULT_BUFFER_SIZE;
    }

    protected abstract void onInit(P param) throws Exception;

    public abstract void onEvent(DataEvent<D> event, boolean endOfBatch) throws Exception;

    protected abstract void onClose() throws Exception;


    public final void init(P param) throws Exception {

        // 初始化，并获取处理器
        this.onInit(param);

        log.debug("init [{}] begin...", this.getClass());
        // Specify the size of the ring buffer, must be power of 2.
        int ringSize = (int) Math.pow(2, (int) (Math.log(Math.abs(this.getBufferSize())) / Math.log(2)));
        log.debug("[{}] set ringSize={},real ringBufferSize={}", this.getClass(), ringSize, this.getBufferSize());

        // The factory for the event
        EventFactory<DataEvent<D>> factory = DataEvent::new;

        this.executor = Executors.newSingleThreadExecutor(
                Thread.ofVirtual().name(String.format("skynet.DataPipelineHandler.thread-%s-%d", this.getClass(), index.getAndIncrement())).factory());


        // Construct the Disruptor
        // disruptor = new Disruptor<DataEvent<D>>(factory, ringSize, DaemonThreadFactory.INSTANCE);
        disruptor = new Disruptor<>(factory, ringSize, executor);

        // Connect the handler
        disruptor.handleEventsWith(this);
        // Start the Disruptor, starts all threads running
        disruptor.start();
        log.debug("init [{}] end.", this.getClass());
    }

    /**
     * 向管道中提交数据
     *
     * @param data 数据
     */
    public final void onData(D data) {
        counter.increment();
        RingBuffer<DataEvent<D>> ringBuffer = disruptor.getRingBuffer();
        try {
            // Grab the next sequence
            long sequence = ringBuffer.tryNext();
            DataEvent<D> event = ringBuffer.get(sequence);
            // Fill with data
            event.setValue(data);
            ringBuffer.publish(sequence);
        } catch (InsufficientCapacityException e) {
            counter.decrement();
            log.error("onData Err={}", e.getMessage());
        }
    }

    /**
     * 清除管道中的所有未处理的数据
     *
     * @throws InterruptedException InterruptedException
     */
    public void clear() throws InterruptedException {
        if (counter.intValue() > 0) {
            this.lock.writeLock().lock();
            if (counter.intValue() > 0) {

                cancelDownLatch = new CountDownLatch(1);
                // 解锁,等待处理
                this.lock.writeLock().unlock();

                cancelDownLatch.await();
                counter.reset();
            } else {
                this.lock.writeLock().unlock();
            }
        }
    }

    /**
     * 等待 管道中所有的数据处理完
     *
     * @throws InterruptedException InterruptedException
     */
    public void awaitComplete() throws InterruptedException {
        if (counter.intValue() > 0) {
            this.lock.writeLock().lock();
            if (counter.intValue() > 0) {
                log.debug("awaitComplete [{}] todo data count= {}", this.getClass(), counter.intValue());
                awaitDownLatch = new CountDownLatch(1);
                // 解锁,等待处理
                this.lock.writeLock().unlock();

                awaitDownLatch.await();
                counter.reset();
            } else {
                this.lock.writeLock().unlock();
            }
        }
    }

    /**
     * 关闭管道，直到 管道中所有的数据处理完成
     *
     * @throws Exception Exception
     */
    public void closeUntilComplete() throws Exception {
        closeUntilComplete(-1, TimeUnit.MILLISECONDS);
    }

    /**
     * 关闭管道，直到 管道中所有的数据处理完成
     *
     * @param timeout  超时时间
     * @param timeUnit 超时时间单位
     * @throws Exception Exception
     */
    public void closeUntilComplete(final long timeout, final TimeUnit timeUnit) throws Exception {
        log.debug("closeUntilComplete begin");
        if (disruptor != null) {
            disruptor.shutdown(timeout, timeUnit);
            disruptor.halt();
            disruptor = null;
        }
        if (executor != null) {
            executor.shutdown();
            executor = null;
        }
        log.debug("closeUntilComplete end");
    }

    @Override
    public final void onEvent(DataEvent<D> event, long sequence, boolean endOfBatch) throws Exception {
        this.lock.readLock().lock();
        try {
            // Reset 状态，忽略数据
            if (cancelDownLatch.getCount() > 0) {
                // 最后数据，释放锁
                if (endOfBatch) {
                    cancelDownLatch.countDown();
                }
            } else {
                this.onEvent(event, endOfBatch);
            }
        } catch (Throwable e) {
            log.error("[" + this.getClass() + "] onEvent error:" + e.getMessage(), e);
        } finally {
            try {
                counter.decrement();
                // 最后数据，释放锁
                if (endOfBatch && awaitDownLatch.getCount() > 0) {
                    awaitDownLatch.countDown();
                }
            } finally {
                this.lock.readLock().unlock();
            }
        }
    }

    /**
     * 关闭管道， 立即停止管道中所有的数据处理
     */
    @Override
    public final void close() throws Exception {
        log.debug("close DataPipelineHandler begin");
        onClose();

        this.closeUntilComplete();

        counter.reset();
        log.debug("close DataPipelineHandler end");
    }

    /**
     * 待处理的data 数量
     *
     * @return 待处理的data 数量
     */
    public int getTodoSize() {
        return counter.intValue();
    }

    /**
     * 管道大小
     *
     * @return 管道大小
     */
    public int getTotalSize() {
        return this.getBufferSize();
    }
}