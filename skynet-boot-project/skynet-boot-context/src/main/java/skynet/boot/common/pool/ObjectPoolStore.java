package skynet.boot.common.pool;

/**
 * 用于存储 ObjectPool 中的对象,提供对象的获取和存储功能。
 * <p>
 * 示例代码:
 * <pre>
 * public class MyObjectPoolStore implements ObjectPoolStore<MyObject> {
 *     private Queue<MyObject> queue = new LinkedList<>();
 *
 *     {@literal @}Override
 *     public void put(MyObject object) throws InterruptedException {
 *         Assert.notNull(object, "Object must not be null");
 *         queue.add(object);
 *     }
 *
 *     {@literal @}Override
 *     public MyObject take() throws InterruptedException {
 *         return queue.poll();
 *     }
 *
 *     {@literal @}Override
 *     public void clear() {
 *         queue.clear();
 *     }
 *
 *     {@literal @}Override
 *     public boolean contains(Object object) {
 *         return queue.contains(object);
 *     }
 * }
 * </pre>
 *
 * <AUTHOR>
 */
public interface ObjectPoolStore<T> {

    /**
     * 将对象放入存储池中。
     *
     * @param object 要放入存储池的对象,必须不为 null
     * @throws InterruptedException 如果线程在等待时被中断,则抛出此异常
     */
    void put(T object) throws InterruptedException;

    /**
     * 从存储池中取出一个对象。
     *
     * @return 从存储池中取出的对象
     * @throws InterruptedException 如果线程在等待时被中断,则抛出此异常
     */
    T take() throws InterruptedException;

    /**
     * 清空存储池中的所有对象。
     */
    void clear();

    /**
     * 检查存储池中是否包含指定的对象。
     *
     * @param object 要检查的对象
     * @return 如果存储池中包含该对象, 则返回 true;否则返回 false
     */
    boolean contains(Object object);
}