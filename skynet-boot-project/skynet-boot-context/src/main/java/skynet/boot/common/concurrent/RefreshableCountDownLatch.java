package skynet.boot.common.concurrent;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 可刷新等待时间的 {@code CountDownLatch}
 *
 * <p>基本功能与暴露的方法同{@code CountDownLatch}保持一致，
 * 额外提供{@code refresh()}方法，实现刷新阻塞等待线程的超时时间
 *
 * <AUTHOR>
 * @date 2024/12/10 15:00
 */
public class RefreshableCountDownLatch {

    private volatile long refreshTimestamp = 0L;
    private final CountDownLatch delegate;

    public RefreshableCountDownLatch(int count) {
        this.delegate = new CountDownLatch(count);
    }

    public void await() throws InterruptedException {
        this.delegate.await();
    }

    public boolean await(long timeout, TimeUnit unit) throws InterruptedException {
        boolean finish = delegate.await(timeout, unit);
        if (finish || this.refreshTimestamp == 0) {
            return finish;
        }

        long timeoutNanos = unit.toNanos(timeout);
        while (!finish) {
            long nanos = timeoutNanos - (System.nanoTime() - this.refreshTimestamp);
            if (nanos <= 0) {
                break;
            }
            finish = this.delegate.await(nanos, TimeUnit.NANOSECONDS);
        }
        return finish;
    }

    public synchronized void refresh() {
        this.refreshTimestamp = System.nanoTime();
    }

    public void countDown() {
        this.delegate.countDown();
    }

    public long getCount() {
        return this.delegate.getCount();
    }

    public String toString() {
        return super.toString() + "[Count = " + this.getCount() + ", refreshTimestamp = " + this.refreshTimestamp + "]";
    }
}
