package skynet.boot.common.concurrent;


import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * CompletableFuture 简单封装，
 * <p>
 * 简化 CompletableFuture 使用代码量
 *
 * @param <D>
 * <AUTHOR> 2024年08月06日14:57:58
 * @since 4.0.16
 */
@Slf4j
public class CompletableFutureService<D> {

    private final List<CompletableFuture<D>> dFutures = new ArrayList<>();
    private final List<CompletableFuture<List<D>>> lFutures = new ArrayList<>();
    private final List<CompletableFuture<Void>> vFutures = new ArrayList<>();
    private Executor executor;

    public CompletableFutureService() {
    }

    public CompletableFutureService(Executor executor) {
        this.executor = executor;
    }

    /**
     * 异步提供数据。相对于提交一次任务
     *
     * @param supplier 提供数据的供应者，其类型为Supplier<D>。
     */
    public void supplyAsync(Supplier<D> supplier) {
        dFutures.add(executor != null ? CompletableFuture.supplyAsync(supplier, executor) : CompletableFuture.supplyAsync(supplier));
    }

    /**
     * 异步提供列表。相对于提交一次任务
     *
     * @param supplier 一个供应器，用于生成D类型的列表。
     */
    public void supplyAsyncList(Supplier<List<D>> supplier) {
        lFutures.add(executor != null ? CompletableFuture.supplyAsync(supplier, executor) : CompletableFuture.supplyAsync(supplier));
    }

    /**
     * 异步运行给定的Runnable任务。
     *
     * @param runnable 需要异步执行的Runnable任务。
     */
    public void runAsync(Runnable runnable) {
        vFutures.add(executor != null ? CompletableFuture.runAsync(runnable, executor) : CompletableFuture.runAsync(runnable));
    }

    /**
     * 获取所有D类型的数据。
     *
     * @return 返回一个包含所有D类型数据的列表，该列表首先包含dFutures中的所有元素，然后是lFutures中的所有元素。
     */
    public void waitForCompletion() {
        vFutures.forEach(CompletableFuture::join);
        dFutures.forEach(CompletableFuture::join);
        lFutures.forEach(CompletableFuture::join);
    }

    /**
     * 阻塞等待所有任务完成，带超时。
     *
     * @param timeout 等待超时时间
     * @param unit    时间单位
     * @return 是否在超时前全部完成
     */
    public boolean waitForCompletion(long timeout, TimeUnit unit) {
        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(
                    Stream.concat(Stream.concat(dFutures.stream(), lFutures.stream()), vFutures.stream()
                    ).toArray(CompletableFuture[]::new)
            );
            allOf.get(timeout, unit);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;  // 超时或异常，返回false
        }
    }

    /**
     * 阻塞等待所有任务完成，带超时。
     *
     * @param timeout 等待超时时间
     * @return 是否在超时前全部完成
     */
    public boolean waitForCompletion(Duration timeout) {
        return waitForCompletion(timeout.toMillis(), TimeUnit.MILLISECONDS);
    }

    /**
     * 获取所有 D 类型的数据。
     *
     * @return 返回一个包含所有 D 类型数据的列表。
     */
    public List<D> getAll() {
        return getAllResults(true, 0, null);
    }

    /**
     * 获取所有 D 类型的数据，带超时。
     *
     * @param timeout 等待超时时间
     * @param unit    时间单位
     * @return 返回一个包含所有 D 类型数据的列表。如果超时，返回已完成的任务结果。
     */
    public List<D> getAll(long timeout, TimeUnit unit) {
        return getAllResults(false, timeout, unit);
    }

    /**
     * 私有方法：获取所有 D 类型的数据结果。
     *
     * @param waitForCompletion 是否等待所有任务完成
     * @param timeout           等待超时时间
     * @param unit              时间单位
     * @return 返回包含所有 D 类型数据的列表。
     */
    private List<D> getAllResults(boolean waitForCompletion, long timeout, TimeUnit unit) {
        if (!waitForCompletion) {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(
                    Stream.concat(
                            Stream.concat(dFutures.stream(), lFutures.stream()),
                            vFutures.stream()
                    ).toArray(CompletableFuture[]::new)
            );
            try {
                allOf.get(timeout, unit);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        } else {
            vFutures.forEach(CompletableFuture::join);
        }

        List<D> list = dFutures.stream()
                .map(future -> future.isDone() ? future.join() : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<List<D>> objList = lFutures.stream()
                .map(future -> future.isDone() ? future.join() : null)
                .filter(Objects::nonNull)
                .toList();

        objList.forEach(list::addAll);

        return list;
    }
}