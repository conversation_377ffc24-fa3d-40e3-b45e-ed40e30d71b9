package skynet.boot.common.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.exception.SkynetException;

/**
 * Skynet API 响应
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "服务响应")
public class SkynetResponse<T> extends Jsonable {

    @Schema(title = "状态码, 0:成功， 非0：失败")
    private int code;

    @Schema(title = "错误信息")
    private String message = "";

    @Schema(title = "数据")
    private T data;

    public SkynetResponse() {
    }

    public SkynetResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public SkynetResponse(int code, T data) {
        this.code = code;
        this.data = data;
    }


    public void setException(Exception e) {
        if (e instanceof SkynetException) {
            this.code = ((SkynetException) e).getCode();
            this.message = e.getMessage();
        } else {
            this.code = SkynetErrorCode.ERROR.getCode();
            this.message = SkynetErrorCode.ERROR.getMessage();
        }
    }

    public static <T> SkynetResponse<T> success() {
        return instance(SkynetErrorCode.SUCCESS.getCode(), SkynetErrorCode.SUCCESS.getMessage(), null);
    }

    public static <T> SkynetResponse<T> success(T data) {
        return instance(SkynetErrorCode.SUCCESS.getCode(), SkynetErrorCode.SUCCESS.getMessage(), data);
    }

    public static <T> SkynetResponse<T> success(String message, T data) {
        return instance(SkynetErrorCode.SUCCESS.getCode(), message, data);
    }

    public static <T> SkynetResponse<T> fail(int code, String message) {
        return instance(code, message, null);
    }

    public static <T> SkynetResponse<T> fail(Exception e) {
        String message = "";
        int code;
        if (e instanceof SkynetException) {
            code = ((SkynetException) e).getCode();
            message = e.getMessage();
        } else {
            code = SkynetErrorCode.ERROR.getCode();
            message = SkynetErrorCode.ERROR.getMessage();
        }
        return instance(code, message, null);
    }

    public static <T> SkynetResponse<T> fail(SkynetErrorCode e) {
        return instance(e.getCode(), e.getMessage(), null);
    }

    public static <T> SkynetResponse<T> instance(int code, String message, T data) {
        SkynetResponse<T> s = new SkynetResponse<>();
        s.setCode(code);
        s.setMessage(message);
        s.setData(data);
        return s;
    }
}
