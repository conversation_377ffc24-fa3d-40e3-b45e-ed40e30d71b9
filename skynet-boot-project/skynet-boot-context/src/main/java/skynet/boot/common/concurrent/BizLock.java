package skynet.boot.common.concurrent;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Business-specific lock utility for synchronizing operations on the same business ID.
 * <p>
 * This class provides a mechanism to ensure ordered processing of operations
 * related to the same business entity by using a unique business ID as the lock key.
 * It manages a pool of locks dynamically, creating them on demand and removing them
 * when no longer needed.
 * </p>
 * <p>
 * Usage example:
 * </p>
 * <pre>
 * BizLock bizLock = new BizLock();
 * String businessId = "order-123";
 *
 * try {
 *     bizLock.lock(businessId);
 *     // Perform thread-safe operations for this business ID
 *     processOrder(businessId);
 * } finally {
 *     bizLock.unlock(businessId);
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 2019-11-26
 */
@Slf4j
public class BizLock {

    /**
     * Map of business IDs to their corresponding lock objects.
     * Using ConcurrentHashMap for thread-safe access to the lock pool.
     */
    private final Map<String, BizLockItem> lockMap;

    /**
     * Creates a new BizLock instance with an empty lock pool.
     */
    public BizLock() {
        this.lockMap = new ConcurrentHashMap<>();
    }

    /**
     * Acquires a lock for the specified business ID.
     * If a lock doesn't exist for this ID, it will be created.
     *
     * @param bizId The business ID to lock on
     * @throws IllegalArgumentException if bizId is null
     */
    public void lock(String bizId) {
        if (bizId == null) {
            throw new IllegalArgumentException("Business ID cannot be null");
        }

        if (log.isDebugEnabled()) {
            log.debug("Acquiring lock for business ID: {} [current lock pool size: {}]", bizId, lockMap.size());
        }

        // Get or create the lock for this business ID
        BizLockItem lockItem = getLockItem(bizId);

        // Acquire the lock
        lockItem.lock();
    }

    /**
     * Attempts to acquire a lock for the specified business ID within the given timeout.
     *
     * @param bizId The business ID to lock on
     * @param timeout The maximum time to wait for the lock
     * @param unit The time unit of the timeout argument
     * @return true if the lock was acquired, false if the waiting time elapsed before the lock was acquired
     * @throws IllegalArgumentException if bizId is null
     */
    public boolean tryLock(String bizId, long timeout, TimeUnit unit) {
        if (bizId == null) {
            throw new IllegalArgumentException("Business ID cannot be null");
        }

        if (log.isDebugEnabled()) {
            log.debug("Attempting to acquire lock for business ID: {} with timeout: {} {}",
                    bizId, timeout, unit);
        }

        // Get or create the lock for this business ID
        BizLockItem lockItem = getLockItem(bizId);

        // Try to acquire the lock with timeout
        return lockItem.tryLock(timeout, unit);
    }

    /**
     * Releases the lock for the specified business ID.
     * If no threads are waiting for this lock, it will be removed from the pool.
     *
     * @param bizId The business ID to unlock
     * @throws IllegalArgumentException if bizId is null
     */
    public void unlock(String bizId) {
        if (bizId == null) {
            throw new IllegalArgumentException("Business ID cannot be null");
        }

        if (log.isDebugEnabled()) {
            log.debug("Releasing lock for business ID: {} [current lock pool size: {}]", bizId, lockMap.size());
        }

        // Synchronize on the map to ensure thread safety when checking and removing locks
        synchronized (lockMap) {
            BizLockItem lockItem = lockMap.get(bizId);
            if (lockItem != null) {
                lockItem.unlock();

                // If no threads are waiting for this lock, remove it from the map
                if (lockItem.getWaitCount() == 0) {
                    lockMap.remove(bizId);
                    if (log.isDebugEnabled()) {
                        log.debug("Removed unused lock for business ID: {} [new lock pool size: {}]",
                                bizId, lockMap.size());
                    }
                }
            } else {
                log.warn("Attempted to unlock non-existent lock for business ID: {}", bizId);
            }
        }
    }

    /**
     * Gets the current number of locks in the pool.
     *
     * @return The number of active locks
     */
    public int getOnlineLockSize() {
        return lockMap.size();
    }

    /**
     * Gets or creates a lock item for the specified business ID.
     * This method is synchronized to ensure thread safety when creating new lock items.
     *
     * @param bizId The business ID
     * @return The lock item for the business ID
     */
    private BizLockItem getLockItem(String bizId) {
        // First try to get the lock without synchronization for better performance
        BizLockItem lockItem = lockMap.get(bizId);
        if (lockItem != null) {
            return lockItem;
        }

        // If the lock doesn't exist, create it under synchronization
        synchronized (lockMap) {
            // Check again in case another thread created it while we were waiting
            return lockMap.computeIfAbsent(bizId, id -> {
                if (log.isDebugEnabled()) {
                    log.debug("Created new lock for business ID: {} [new lock pool size: {}]",
                            id, lockMap.size() + 1);
                }
                return new BizLockItem(id);
            });
        }
    }

    /**
     * Represents a lock for a specific business ID.
     * Tracks the number of threads waiting for or holding the lock.
     */
    static class BizLockItem {
        private final ReentrantLock lock;
        private final LongAdder waitCount;

        @Getter
        private final String bizId;

        /**
         * Creates a new lock item for the specified business ID.
         *
         * @param bizId The business ID
         */
        public BizLockItem(String bizId) {
            this.bizId = Objects.requireNonNull(bizId, "Business ID cannot be null");
            this.lock = new ReentrantLock(true); // Use fair ordering policy
            this.waitCount = new LongAdder();
        }

        /**
         * Gets the underlying ReentrantLock.
         *
         * @return The ReentrantLock
         */
        public ReentrantLock getLock() {
            return lock;
        }

        /**
         * Acquires the lock and increments the wait count.
         */
        public void lock() {
            waitCount.increment();
            if (log.isDebugEnabled()) {
                log.debug("Thread waiting for lock on business ID: {} [wait count: {}]", bizId, waitCount);
            }
            lock.lock();
        }

        /**
         * Attempts to acquire the lock within the given timeout and increments the wait count if successful.
         *
         * @param timeout The maximum time to wait for the lock
         * @param unit The time unit of the timeout argument
         * @return true if the lock was acquired, false if the waiting time elapsed before the lock was acquired
         */
        public boolean tryLock(long timeout, TimeUnit unit) {
            waitCount.increment();
            try {
                boolean acquired = lock.tryLock(timeout, unit);
                if (!acquired) {
                    // If we didn't get the lock, decrement the wait count
                    waitCount.decrement();
                }
                return acquired;
            } catch (InterruptedException e) {
                // If we were interrupted, decrement the wait count
                waitCount.decrement();
                Thread.currentThread().interrupt(); // Preserve interrupt status
                return false;
            }
        }

        /**
         * Releases the lock and decrements the wait count.
         * This method is synchronized to ensure thread safety when updating the wait count.
         */
        public void unlock() {
            waitCount.decrement();
            lock.unlock();
            if (log.isDebugEnabled()) {
                log.debug("Thread released lock on business ID: {} [wait count: {}]", bizId, waitCount);
            }
        }

        /**
         * Gets the current wait count.
         *
         * @return The number of threads waiting for or holding the lock
         */
        public int getWaitCount() {
            return waitCount.intValue();
        }
    }
}
