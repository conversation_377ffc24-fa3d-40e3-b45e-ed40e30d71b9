package skynet.boot.common.mapper;

import java.util.List;

/**
 * Contract for a generic dto to entity mapper.
 *
 * @param <D> - DTO type parameter.
 * @param <E> - Entity type parameter.
 * <AUTHOR>
 */

public interface EntityMapper<D, E> {

    /**
     * 控制层实体转换成数据层实体
     *
     * @param dto 控制层实体
     * @return 数据层实体
     */
    E toEntity(D dto);

    /**
     * 数据层实体转换成控制层实体
     *
     * @param entity 数据层实体
     * @return 控制层实体
     */
    D toDto(E entity);


    /**
     * 批量控制层实体转换成数据层实体
     *
     * @param dtoList 控制层实体
     * @return 数据层实体
     */
    List<E> toEntity(List<D> dtoList);

    /**
     * 批量数据层实体转换成控制层实体
     *
     * @param entityList 数据层实体
     * @return 控制层实体
     */
    List<D> toDto(List<E> entityList);
}
