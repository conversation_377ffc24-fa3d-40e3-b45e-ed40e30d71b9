package skynet.boot.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.net.*;
import java.util.*;

/**
 * 本地地址获取工具
 *
 * <AUTHOR> 2014年9月26日 下午3:43:57
 */
@Slf4j
public final class OsUtil {

    public final static String LOCAL_CALLBACK_IP = "127.0.0.1";
    private static String m_hostName;
    private static String mIP;
    private static String asyncTempIP = "";
    private static int pid_ = -1;

    public static Map<String, Object> getOs() {
        Map<String, Object> map = new LinkedHashMap<>(5);
        OperatingSystemMXBean oper = ManagementFactory.getOperatingSystemMXBean();
        map.put("name", oper.getName());
        map.put("arch", oper.getArch());
        map.put("version", oper.getVersion());
        map.put("systemLoad", oper.getSystemLoadAverage());
        map.put("availableProcessors", oper.getAvailableProcessors());
        return map;
    }

    /**
     * 获取本机的HostName；
     *
     * @return
     */
    public synchronized static String getHostName() {
        if (StringUtils.isBlank(m_hostName)) {
            Thread thread = Thread.ofVirtual()
                    .name("skynet-get-hostname")
                    .start(() -> {
                        try {
                            InetAddress inetAddress = InetAddress.getLocalHost();
                            m_hostName = inetAddress.getHostName();
                        } catch (UnknownHostException e) {
                            log.error("getHostName error= {}", e.getMessage());
                        }
                    });
            try {
                // 4秒超时
                thread.join(4000);
            } catch (InterruptedException e) {
                log.error("getIPAddress timeout, please check host config. err= {}", e.getMessage());
            } finally {
                thread.interrupt();
            }
            if (StringUtils.isBlank(m_hostName)) {
                return "UN_CONFIG_HOSTNAME";
            }
        }
        return m_hostName;
    }

    /**
     * 获取本机的 ip；
     * <p>
     *
     * @return 获取本机的IP
     * @throws InterruptedException
     */
    public synchronized static String getIPAddress() {
        log.debug("自动获取IP");
        if (StringUtils.isBlank(mIP)) {
            // +--------------------------------------------------------------------------------+
            // + llzhao4 2015-12-14 多 ip情况下， 获取hostname 映射的 ip，在docker容器化时有用
            // +--------------------------------------------------------------------------------+
            Thread thread = Thread.ofVirtual()
                    .name("skynet-get-ip")
                    .start(() -> {
                        try {
                            InetAddress inetAddress = InetAddress.getLocalHost();
                            log.info("Get current  ip: {}", inetAddress);
                            String ip = inetAddress.getHostAddress();
                            if (!StringUtils.isBlank(ip)) {
                                asyncTempIP = ip;
                            }
                        } catch (UnknownHostException e) {
                            log.error("getIPAddress error= {}", e.getMessage());
                        }
                    });
            try {
                // 4秒超时
                thread.join(4000);
            } catch (InterruptedException e) {
                log.error("getIPAddress timeout, please check host config. err= {}", e.getMessage());
            } finally {
                thread.interrupt();
            }
            if (StringUtils.isBlank(asyncTempIP)) {
                System.err.println("获取本地IP失败.");
            }

            // 如果获取地址超时，再从本机获取IP地址
            String tmpIp = asyncTempIP;
            log.debug("get async temp ip={}", tmpIp);
            if (StringUtils.isBlank(tmpIp)) {
                // 排除回调地址
                List<String> ips = getAllIPs();
                for (String ip : ips) {
                    if (!LOCAL_CALLBACK_IP.equals(ip)) {
                        log.debug("ip= {}", ip);
                        tmpIp = ip;
                        break;
                    }
                }
            }

            // 只有回调地址情况
            if (StringUtils.isBlank(tmpIp)) {
                tmpIp = LOCAL_CALLBACK_IP;
            }

            mIP = tmpIp;
            log.debug("The IP address was finally obtained：{}", mIP);
        }
        return mIP;
    }

    public static int getCurrentPid() {
        if (pid_ == -1) {
            Thread thread = Thread.ofVirtual()
                    .name("skynet-get-pid")
                    .start(() -> {
                        try {
                            // get name representing the running Java virtual machine.
                            String name = ManagementFactory.getRuntimeMXBean().getName();
                            String pid = name.split("@")[0];
                            pid_ = Integer.parseInt(pid);
                        } catch (Throwable e) {
                            log.error("getCurrentPid error= {}", e.getMessage());
                        }
                    });
            try {
                // 默认4秒超时
                String timeout = System.getProperty("skynet.system.pid.timeout", "4000");
                thread.join(Integer.parseInt(timeout));
            } catch (InterruptedException e) {
                log.error("getCurrentPid timeout, please check host config. err= {}", e.getMessage());
            } finally {
                thread.interrupt();
            }
            if (pid_ == -1) {
                System.out
                        .println("==================================================================================");
                System.out.println("Check the DNS Host configuration.  vim /etc/hosts. e.g ");
                System.out.println("127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4");
                System.out.println("::1         localhost localhost.localdomain localhost6 localhost6.localdomain6");
                System.out.println("# ${本机IP} ${本机hostname}，e.g:");
                System.out.println("************ SkynetServer");
                System.out
                        .println("==================================================================================");
                System.out.println("Current process will exit.");
                System.out
                        .println("==================================================================================");

                log.error("==================================================================================");
                log.error("Check the DNS Host configuration.  vim /etc/hosts. e.g ");
                log.error("127.0.0.1   localhost localhost.localdomain localhost4 localhost4.localdomain4");
                log.error("::1         localhost localhost.localdomain localhost6 localhost6.localdomain6");
                log.error("# ${本机IP} ${本机hostname}，e.g:");
                log.error("************ SkynetServer");
                log.error("==================================================================================");
                log.error("Current process will exit.");
                log.error("==================================================================================");

                System.exit(-1);
            }
        }
        return pid_;
    }

    @SuppressWarnings("rawtypes")
    public static List<String> getAllIPs() {
        List<String> ips = new ArrayList<>();
        Enumeration allNetInterfaces = null;
        try {
            allNetInterfaces = NetworkInterface.getNetworkInterfaces();
        } catch (java.net.SocketException e) {
            e.printStackTrace();
        }
        while (allNetInterfaces.hasMoreElements()) {
            NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
            log.debug(netInterface.getName());
            Enumeration addresses = netInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                InetAddress ip = (InetAddress) addresses.nextElement();
                if (ip != null && ip instanceof Inet4Address) {
                    ips.add(ip.getHostAddress());
                }
            }
        }
        log.debug("get all ip list={}", ips);
        return ips;
    }

    /**
     * 获取本机所有IPv6的地址 包含网卡名
     * <p>
     * 如：fe80:0:0:0:de42:8cb:8db1:3366%ens1f0
     *
     * @return
     */
    public static List<String> getAllIPv6List() {
        List<String> ips = new ArrayList<>();
        Enumeration<?> allNetInterfaces = null;
        try {
            allNetInterfaces = NetworkInterface.getNetworkInterfaces();
        } catch (java.net.SocketException e) {
            e.printStackTrace();
        }
        while (true) {
            assert allNetInterfaces != null;
            if (!allNetInterfaces.hasMoreElements()) {
                break;
            }
            NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
            log.debug(netInterface.getName());
            Enumeration<?> addresses = netInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                InetAddress ip = (InetAddress) addresses.nextElement();
                if (ip != null && ip instanceof Inet6Address) {
                    String ipAddr = ip.getHostAddress();
                    int index = ipAddr.indexOf('%');
                    if (index > 0) {
                        ipAddr = ipAddr.substring(0, index);
                    }
                    ips.add(IPv6Util.parseFullIPv6(ipAddr));
                }
            }
        }
        log.debug("get all IPv6 list={}", ips);
        return ips;
    }

    /**
     * 是否是本机的IP地址，支持IPv4 和IPv6
     *
     * @param ip
     * @return
     */
    public static boolean isLocalIP(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }

        List<String> ips = new ArrayList<>();
        if (InetAddressUtils.isIPv4Address(ip)) {
            ips.addAll(getAllIPs());
        } else if (InetAddressUtils.isIPv6Address(ip)) {
            // IPv6 转成 全写
            ip = IPv6Util.parseFullIPv6(ip);
            ips.addAll(getAllIPv6List());
        }
        return ips.contains(ip);
    }

    /**
     * 获取随机可用的端口号
     *
     * @return 随机可用的端口号
     */
    public synchronized static int getRandomPort() {
        return getRandomPort(0, 0xFFFF);
    }

    /**
     * 获取随机可用的端口号
     *
     * @param beginPort 开始端口序号(包含)
     * @param endPort   结束端口序号(包含)
     * @return
     */
    public static int getRandomPort(int beginPort, int endPort) {

        if (beginPort < 0 || beginPort > 0xFFFF) {
            throw new IllegalArgumentException("beginPort value out of range: " + beginPort);
        }
        if (endPort < 0 || endPort > 0xFFFF) {
            throw new IllegalArgumentException("endPort value out of range: " + endPort);
        }
        log.info("RandomPort:[{},{}]", Math.min(beginPort, endPort), Math.max(beginPort, endPort));
        int port = -1;
        while (port == -1) {
            port = RandomUtils.nextInt(Math.min(beginPort, endPort), Math.max(beginPort, endPort) + 1);
            ServerSocket serverSocket = null;
            try {
                serverSocket = new ServerSocket(port);
                return port;
            } catch (Throwable e) {
                port = -1;
            } finally {
                try {
                    if (serverSocket != null) {
                        serverSocket.close();
                    }
                } catch (IOException e) {
                    log.warn("RandomPort Err= {}", e.getMessage());
                }
            }
        }
        log.info("RandomPort:[{},{}]Port= {}", Math.min(beginPort, endPort), Math.max(beginPort, endPort), port);
        return port;
    }

    /**
     * 检测端口是否被占用
     *
     * @param port 端口号
     * @return 端口被占用返回true，否则返回false
     */
    public synchronized static boolean isUsedPort(int port) {
        ServerSocket socket = null;
        try {
            socket = new ServerSocket(port);
            return false;
        } catch (java.net.BindException ignored) {
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (socket != null) {
                    socket.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    /**
     * 检测端口号是否合法
     *
     * @param port 端口号
     * @return 合法：true | 不合法 false
     */
    public static boolean isValidPort(int port) {
        return port >= 1024 && port <= 65535;
    }

    public static void main(String[] args) throws InterruptedException {
        for (int index = 0; index < 100; index++) {
            int port = getRandomPort(10015, 10000);
            // System.out.println(port);
            port = getRandomPort();
            System.out.println(port);
        }

        String ip = OsUtil.getIPAddress();
        System.out.println(ip);
    }
}
