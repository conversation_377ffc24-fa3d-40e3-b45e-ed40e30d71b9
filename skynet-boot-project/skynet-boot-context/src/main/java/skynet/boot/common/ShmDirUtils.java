package skynet.boot.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.core.env.Environment;

import java.io.File;
import java.io.IOException;

/**
 * <pre>
 * 高性能临时工作目录,
 * </pre>
 *
 * <AUTHOR>
 * @date 2019-09-30 09:28
 */
@Slf4j
public class ShmDirUtils {

    private static File skynetShmDir;
    private static final Object syncObj = new Object();

    private final Environment environment;

    public ShmDirUtils(Environment environment) {
        this.environment = environment;
    }

    /**
     * 获取 skynet 的高性能目录
     *
     * @return
     */
    public File getShmDir4Skynet() {
        if (skynetShmDir == null) {
            synchronized (syncObj) {
                if (skynetShmDir == null) {
                    try {
                        skynetShmDir = getShmDir(".skynet");
                    } catch (Throwable e) {
                        log.error("INIT_SKYNET_SHM_DIR_ERR= {}", e.getMessage());
                        skynetShmDir = new File(System.getProperty("java.io.tmpdir"));
                    }

                    System.out.println("==============================");
                    log.info("skynet shm Folder:\t{}", skynetShmDir);
                    System.out.println("skynet  shm Folder:\t" + skynetShmDir);
                    System.out.println("==============================");
                }
            }
        }
        return skynetShmDir;
    }


    /**
     * 获取 子目录名称 获取完整的 高性能目录
     *
     * @param child
     * @return
     * @throws IOException
     */
    public File getShmDir(String child) throws IOException {
        File directory = new File(getShmDir(), child);
        if (!directory.exists() && !directory.mkdirs() && !directory.isDirectory()) {
            throw new IOException(String.format("Cannot create directory '%s'.", directory));
        }
        return directory;
    }

    /**
     * /dev/shm  或 skynet.dev.shm.path 配置后的目标
     * 非linux ， 将是 java.io.tmpdir 目录
     *
     * @return
     */
    public File getShmDir() {
        String devShmPath = environment.getProperty("skynet.dev.shm.path", "/dev/shm");
        devShmPath = StringUtils.isBlank(devShmPath) ? "/dev/shm" : devShmPath;
        return new File(SystemUtils.IS_OS_LINUX ? devShmPath : System.getProperty("java.io.tmpdir"));
    }
}
