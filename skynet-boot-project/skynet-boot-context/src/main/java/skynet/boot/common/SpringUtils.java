package skynet.boot.common;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * spring上下文工具类
 *
 * <AUTHOR>
 * @title SpringUtils
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public final class SpringUtils implements ApplicationContextAware, Ordered {
    /**
     * spring上下文
     * -- GETTER --
     *  获取ApplicationContext.
     *
     * @return ApplicationContext

     */
    @Getter
    private static ApplicationContext context;

    /**
     * @param applicationContext 上下文
     *                           实现ApplicationContextAware接口的context注入函数, 将其存入静态变量
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        log.debug("Init ApplicationContext ：{}", applicationContext);
        SpringUtils.context = applicationContext;
    }

    /**
     * 从ApplicationContext中取得Bean
     *
     * @param clazz bean
     * @return 对象
     */
    public static <T> T getBean(Class<T> clazz) {
        return getContext().getBean(clazz);
    }

    private SpringUtils() {
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}
