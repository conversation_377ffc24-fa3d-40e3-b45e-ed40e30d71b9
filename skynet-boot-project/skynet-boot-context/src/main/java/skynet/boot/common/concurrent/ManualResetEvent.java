package skynet.boot.common.concurrent;

import lombok.Getter;

/**
 * 手动信号量实现，提供一个线程间的同步机制，允许一个或多个线程等待事件的发生。
 * 可以通过`set()`方法将事件状态设置为打开状态，所有等待的线程都会被放行；
 * 通过`reset()`方法可以将事件状态重置为阻塞状态。
 * <p>
 * 示例代码：
 * <pre>
 * {@code
 * ManualResetEvent event = new ManualResetEvent(false);
 *
 * Thread waitingThread = new Thread(() -> {
 *     try {
 *         event.waitOne();
 *         System.out.println("Thread is released");
 *     } catch (InterruptedException e) {
 *         Thread.currentThread().interrupt();
 *     }
 * });
 * waitingThread.start();
 *
 * // Simulate some work
 * Thread.sleep(1000);
 * event.set();  // Release the waiting thread
 * }
 * </pre>
 *
 * <AUTHOR>
 */
public class ManualResetEvent {
    private final Object monitor = new Object();

    /**
     * 信号量状态，`true`表示放行状态，`false`表示阻塞状态。
     */
    @Getter
    private volatile boolean open;

    /**
     * 初始化ManualResetEvent实例。
     *
     * @param initialState 初始状态：`false`表示阻塞，`true`表示放行
     */
    public ManualResetEvent(boolean initialState) {
        this.open = initialState;
    }

    /**
     * 阻塞当前线程，直到信号量状态变为放行状态。
     *
     * @return 当前信号量的状态，`true`表示放行，`false`表示阻塞
     * @throws InterruptedException 如果线程在等待时被中断
     */
    public boolean waitOne() throws InterruptedException {
        synchronized (monitor) {
            while (!open) {
                monitor.wait();
            }
            return open;
        }
    }

    /**
     * 阻塞当前线程，直到信号量状态变为放行状态或超时。
     *
     * @param timeout 等待的最大毫秒数
     * @return 当前信号量的状态，`true`表示放行，`false`表示阻塞
     * @throws InterruptedException 如果线程在等待时被中断
     */
    public boolean waitOne(long timeout) throws InterruptedException {
        synchronized (monitor) {
            if (!open) {
                monitor.wait(timeout);
            }
            return open;
        }
    }

    /**
     * 将信号量状态设置为放行状态，并通知所有等待的线程。
     */
    public void set() {
        synchronized (monitor) {
            open = true;
            monitor.notifyAll();
        }
    }

    /**
     * 重置信号量状态为阻塞状态。
     */
    public void reset() {
        synchronized (monitor) {
            open = false;
        }
    }
}