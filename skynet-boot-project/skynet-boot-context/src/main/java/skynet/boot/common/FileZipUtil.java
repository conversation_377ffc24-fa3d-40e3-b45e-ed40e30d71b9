package skynet.boot.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

/**
 * <pre>
 * ZIP 文件压缩  工具类
 * </pre>
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class FileZipUtil {

    /**
     * 使用GBK编码可以避免压缩中文文件名乱码
     */
    public static final Charset CHARSET_GBK = Charset.forName("GBK");
    public static final Charset CHARSET_UTF8 = StandardCharsets.UTF_8;

    /**
     * 文件读取缓冲区大小
     */
    private static final int CACHE_SIZE = 1024;

    /**
     * <pre>
     * 压缩文件
     * </pre>
     *
     * @param sourceFolder 需压缩文件 或者 文件夹 路径
     * @param zipFilePath  压缩文件输出路径
     * @throws Exception
     */
    public static void zip(String sourceFolder, String zipFilePath) throws Exception {
        log.info("--zip [{}] to [{}]", sourceFolder, zipFilePath);

        try (OutputStream out = Files.newOutputStream(Paths.get(zipFilePath))) {
            try (BufferedOutputStream bos = new BufferedOutputStream(out)) {
                try (ZipOutputStream zos = new ZipOutputStream(bos)) {
                    // 解决中文文件名乱码
                    //zos.setEncoding(CHINESE_CHARSET);
                    File file = new File(sourceFolder);
                    String basePath = null;
                    if (file.isDirectory()) {
                        basePath = file.getPath();
                    } else {
                        basePath = file.getParent();
                    }
                    zipFile(file, basePath, zos);
                    zos.closeEntry();
                }
            }
        }
        log.info("--zip [{}] done.", sourceFolder);
    }

    /**
     * <p>
     * 压缩文件
     * </p>
     *
     * @param sourceFolders 一组 压缩文件夹 或 文件
     * @param zipFilePath   压缩文件输出路径
     * @throws Exception
     */
    public static void zip(String[] sourceFolders, String zipFilePath) throws Exception {
        try (OutputStream out = Files.newOutputStream(Paths.get(zipFilePath))) {
            try (BufferedOutputStream bos = new BufferedOutputStream(out)) {
                try (ZipOutputStream zos = new ZipOutputStream(bos)) {
                    // 解决中文文件名乱码
                    //zos.setEncoding(CHINESE_CHARSET);
                    for (String sourceFolder : sourceFolders) {
                        log.debug("zip [{}] to [{}]", sourceFolder, zipFilePath);
                        File file = new File(sourceFolder);
                        String basePath = null;
                        basePath = file.getParent();
                        zipFile(file, basePath, zos);
                    }
                    zos.closeEntry();
                }
            }
        }
    }

    /**
     * <p>
     * 递归压缩文件
     * </p>
     *
     * @param parentFile
     * @param basePath
     * @param zos
     * @throws Exception
     */
    private static void zipFile(File parentFile, String basePath, ZipOutputStream zos) throws Exception {

        File[] files = new File[0];
        if (parentFile.isDirectory()) {
            files = parentFile.listFiles();
        } else {
            files = new File[1];
            files[0] = parentFile;
        }
        String pathName;

        byte[] cache = new byte[CACHE_SIZE];
        assert files != null;
        for (File file : files) {
            if (file.isDirectory()) {
                log.debug("directory：{}", file.getPath());
                basePath = basePath.replace('\\', '/');
                if ("/".equals(basePath.substring(basePath.length() - 1))) {
                    pathName = file.getPath().substring(basePath.length()) + "/";
                } else {
                    pathName = file.getPath().substring(basePath.length() + 1) + "/";
                }

                zos.putNextEntry(new ZipEntry(pathName));
                zipFile(file, basePath, zos);
            } else {
                pathName = file.getPath().substring(basePath.length());
                pathName = pathName.replace('\\', '/');
                if ("/".equals(pathName.substring(0, 1))) {
                    pathName = pathName.substring(1);
                }

                log.debug("压缩：{}", pathName);
                try (InputStream is = Files.newInputStream(file.toPath())) {
                    try (BufferedInputStream bis = new BufferedInputStream(is)) {
                        zos.putNextEntry(new ZipEntry(pathName));
                        int nRead = 0;
                        while ((nRead = bis.read(cache, 0, CACHE_SIZE)) != -1) {
                            zos.write(cache, 0, nRead);
                        }
                    }
                }
            }
        }
    }

    /**
     * 解压zip文件，默认采用 utf-8 编码，失败后，再用 gbk 重试
     *
     * @param zipFileName     待解压的zip文件路径，例如：c:\\a.zip
     * @param outputDirectory 解压目标文件夹,例如：c:\\a\
     */
    public static void unZip(String zipFileName, String outputDirectory) throws Exception {
        //默认采用 utf-8 编码，失败后，再用 gbk 重试
        try {
            unZip(zipFileName, outputDirectory, CHARSET_UTF8);
        } catch (IllegalArgumentException e) {
            unZip(zipFileName, outputDirectory, CHARSET_GBK);
        }
    }

    /**
     * 解压zip文件
     *
     * @param zipFileName     待解压的zip文件路径，例如：c:\\a.zip
     * @param outputDirectory 解压目标文件夹,例如：c:\\a\
     * @param charset         the {@linkplain java.nio.charset.Charset charset} to
     *                        be used to decode the ZIP entry name and comment that are not
     *                        encoded by using UTF-8 encoding (indicated by entry's general
     *                        purpose flag).
     */
    public static void unZip(String zipFileName, String outputDirectory, Charset charset) throws Exception {
        log.debug("--unzip [{}] to [{}]", zipFileName, outputDirectory);
        try (ZipFile zipFile = new ZipFile(zipFileName, charset)) {
            Enumeration<?> e = zipFile.entries();
            ZipEntry zipEntry = null;
            createDirectory(outputDirectory, "");
            while (e.hasMoreElements()) {
                zipEntry = (ZipEntry) e.nextElement();
                log.debug("unzip：{}", zipEntry.getName());
                if (zipEntry.isDirectory()) {
                    String name = zipEntry.getName();
                    name = name.substring(0, name.length() - 1);
                    File f = new File(outputDirectory + File.separator + name);
                    f.mkdir();
                    log.debug("mkdir: {}{}{}", outputDirectory, File.separator, name);
                } else {
                    String fileName = zipEntry.getName();
                    fileName = fileName.replace('\\', '/');
                    if (fileName.contains("/")) {
                        createDirectory(outputDirectory, fileName.substring(0, fileName.lastIndexOf("/")));
                        fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                    }
                    File f = new File(outputDirectory + File.separator + zipEntry.getName());
                    f.createNewFile();

                    try (InputStream in = zipFile.getInputStream(zipEntry)) {
                        try (FileOutputStream out = new FileOutputStream(f)) {
                            byte[] by = new byte[1024];
                            int c;
                            while ((c = in.read(by)) != -1) {
                                out.write(by, 0, c);
                            }
                        }
                    }
                }
            }
            log.debug("--unzip [{}] done.--------------", zipFileName);
        } catch (Throwable ex) {
            String msg = String.format("The file=%s with charset=%s unzip error=%s", zipFileName, charset, ex.getMessage());
            log.error(msg);
            throw ex instanceof IllegalArgumentException ? new IllegalArgumentException(msg) : new Exception(msg, ex);
        }
    }

    /**
     * 解压zip文件
     *
     * @param unZipFile       待解压的zip文件
     * @param outputDirectory 解压目标文件夹
     */
    public static void unZip(File unZipFile, File outputDirectory) throws Exception {
        unZip(unZipFile.getAbsolutePath(), outputDirectory.getAbsolutePath());
    }

    /**
     * 创建目录
     *
     * @param directory
     * @param subDirectory
     * <AUTHOR>
     * @Time 2017年7月28日 下午7:10:05
     */
    private static void createDirectory(String directory, String subDirectory) {
        String[] dir;
        File fl = new File(directory);
        try {
            if (StringUtils.isBlank(subDirectory) && !fl.exists()) {
                fl.mkdir();
            } else if (StringUtils.isNoneBlank(subDirectory)) {
                dir = subDirectory.replace('\\', '/').split("/");
                StringBuilder directoryBuilder = new StringBuilder(directory);
                for (String s : dir) {
                    File subFile = new File(directoryBuilder + File.separator + s);
                    if (!subFile.exists()) {
                        subFile.mkdir();
                    }
                    directoryBuilder.append(File.separator).append(s);
                }
                directory = directoryBuilder.toString();
            }
        } catch (Throwable ex) {
            log.error("createDirectory error:" + ex.getMessage(), ex);
        }
    }

    public static List<String> readFileNames(String zipFileName) throws Exception {
        return readFileNames(new File(zipFileName));
    }

    /**
     * 无需解压直接读取Zip文件（包含目录和文件名）
     *
     * @param zipFileName 文件
     * @throws Exception
     * <AUTHOR>
     * @Time 2017年7月28日 下午3:23:10
     */
    public static List<String> readFileNames(File zipFileName) throws Exception {
        List<String> fileNames = new ArrayList<>();
        try (InputStream in = new BufferedInputStream(new FileInputStream(zipFileName))) {
            try (java.util.zip.ZipInputStream zin = new java.util.zip.ZipInputStream(in)) {
                java.util.zip.ZipEntry ze;
                while ((ze = zin.getNextEntry()) != null) {
                    fileNames.add(ze.getName());
                }
                log.debug("The compressed file is encoded by default.");
                zin.closeEntry();
            } catch (IllegalArgumentException ex) {
                log.warn("The compressed file is not in the default encoding, try using GBK encoding.[{}]", zipFileName);
                fileNames.clear();
                try (InputStream in2 = new BufferedInputStream(Files.newInputStream(zipFileName.toPath()))) {
                    try (java.util.zip.ZipInputStream zin = new java.util.zip.ZipInputStream(in2, CHARSET_GBK)) {
                        java.util.zip.ZipEntry ze;
                        while ((ze = zin.getNextEntry()) != null) {
                            fileNames.add(ze.getName());
                        }
                        zin.closeEntry();
                    }
                }
                log.debug("The compressed file is encoded in GBK.");
            }
        } catch (Throwable e) {
            log.error("Read zip file [{}] err= {}", zipFileName, e.getMessage());
            throw e;
        }
        Collections.sort(fileNames);
        return fileNames;
    }

    /**
     * 获取 压缩文件的 顶级 目录和文件
     *
     * @param zipFileName
     * @return
     * @throws Exception
     */
    public static List<String> readTopFileNames(String zipFileName) throws Exception {
        List<String> fileNames = readFileNames(zipFileName);
        List<String> topFiles = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        for (String item : fileNames) {
            String parent = new File(item).getParent();
            if (StringUtils.isBlank(parent)) {
                topFiles.add(item);
            } else {
                map.put(parent, parent);
            }
        }
        topFiles.addAll(map.keySet());
        return topFiles;
    }
}
