package skynet.boot.common.pool;

import java.util.concurrent.LinkedBlockingDeque;

/**
 * 基于 LinkedBlockingDeque 实现的对象池存储类,遵循先进后出(LIFO)的原则。
 * 该类实现了 ObjectPoolStore 接口,提供了线程安全的对象存储和检索功能。
 */
public class LinkedBlockingDequeStore<T> implements ObjectPoolStore<T> {

    // 内部使用的 LinkedBlockingDeque 实例,用于存储对象池中的对象
    private final LinkedBlockingDeque<T> objectDeque;

    /**
     * 构造函数,初始化对象池的大小。
     *
     * @param poolSize 对象池的最大容量
     */
    public LinkedBlockingDequeStore(int poolSize) {
        this.objectDeque = new LinkedBlockingDeque<>(poolSize);
    }

    /**
     * 将对象放入对象池的头部。
     * 如果对象池已满,此方法将阻塞,直到有空间可用。
     *
     * @param object 要放入对象池的对象
     * @throws InterruptedException 如果当前线程在等待时被中断
     */
    @Override
    public void put(T object) throws InterruptedException {
        objectDeque.putFirst(object);
    }

    /**
     * 从对象池的头部取出一个对象。
     * 如果对象池为空,此方法将阻塞,直到有对象可用。
     *
     * @return 从对象池中取出的对象
     * @throws InterruptedException 如果当前线程在等待时被中断
     */
    @Override
    public T take() throws InterruptedException {
        return objectDeque.take();
    }

    /**
     * 清空对象池中的所有对象。
     */
    @Override
    public void clear() {
        objectDeque.clear();
    }

    /**
     * 检查对象池中是否包含指定的对象。
     *
     * @param object 要检查的对象
     * @return 如果对象池中包含该对象, 则返回 true;否则返回 false
     */
    @Override
    public boolean contains(Object object) {
        return objectDeque.contains(object);
    }
}
