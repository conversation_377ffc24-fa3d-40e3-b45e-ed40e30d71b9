package skynet.boot.common;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * IPv6  工具类
 *
 * <AUTHOR>
 * @date 2022/5/29 20:21
 */
public class IPv6Util {

    /**
     * 将一个IPv6地址转为全写格式，全写中的前导0省略
     * 例：将1ade:03da:0::转为1ade:3da:0:0:0:0:0:0
     *
     * @param ipv6Str
     * @return fullIPv6
     */
    public static String parseFullIPv6(String ipv6Str) {
        Assert.hasText(ipv6Str, "The IPv6Str is blank.");
        // 判断IPv6地址的格式是否正确
        if (!InetAddressUtils.isIPv6Address(ipv6Str)) {
            throw new RuntimeException("The IPv6Str format invalid.[" + ipv6Str + "]");
        }

        String[] arr = new String[]{"0", "0", "0", "0", "0", "0", "0", "0"};

        // 将IPv6地址用::分开
        // 如果IPv6地址为::，tempArr.length==0
        // 如果不包含::或以::结尾，tempArr.length==1
        // 如果以::开头或::在中间，tempArr.length==2
        String[] tempArr = ipv6Str.split("::");

        // tempArr[0]用:分开，填充到arr前半部分
        if (tempArr.length > 0) {
            // new String[0]为空数组，因为"".split(":")为{""}，如果tempArr[0]==""，此时数组包含一个元素
            String[] tempArr0 = tempArr[0].isEmpty() ? new String[0] : tempArr[0].split(":");
            for (int i = 0; i < tempArr0.length; i++) {
                // 如果是纯数字，用parseInt去除前导0，如果包含字母，用正则去除前导0
                arr[i] = tempArr0[i].matches("\\d+")
                        ? (String.valueOf(Integer.parseInt(tempArr0[i])))
                        : tempArr0[i].replaceAll("^(0+)", "");
            }
        }

        // tempArr[1]用:分开，填充到arr后半部分
        if (tempArr.length > 1) {
            String[] tempArr1 = tempArr[1].isEmpty() ? new String[0] : tempArr[1].split(":");
            for (int i = 0; i < tempArr1.length; i++) {
                arr[i + arr.length - tempArr1.length] = tempArr1[i].matches("\\d+")
                        ? (String.valueOf(Integer.parseInt(tempArr1[i])))
                        : tempArr1[i].replaceAll("^(0+)", "");
            }
        }

        return StringUtils.join(arr, ":");
    }

    /**
     * 将一个IPv6地址转为简写格式
     * 例：将1ade:03da:0::转为1ade:3da::
     *
     * @param iPv6Str
     * @return AbbrIPv6
     */
    public static String parseAbbrIPv6(String iPv6Str) {
        // 将一个IPv6地址转为全写格式，全写中的前导0省略
        String fullIpV6 = parseFullIPv6(iPv6Str);
        // 如果IPv6地址的格式不正确
        if (fullIpV6.isEmpty()) {
            throw new RuntimeException("The IPv6Str format invalid.[" + iPv6Str + "]");
        }

        // 将1ade:3da:0:0:0:0:0:99转为{"-","-","0","0","0","0","0","-"}
        String[] arr = fullIpV6.split(":");
        for (int i = 0, len = arr.length; i < len; i++) {
            if (!"0".equals(arr[i])) {
                arr[i] = "-";
            }
        }

        // 找到最长的连续的0
        Pattern pattern = Pattern.compile("0{2,}");
        Matcher matcher = pattern.matcher(StringUtils.join(arr, ""));
        String maxStr = "";
        int start = -1;
        int end = -1;
        while (matcher.find()) {
            if (maxStr.length() < matcher.group().length()) {
                maxStr = matcher.group();
                start = matcher.start();
                end = matcher.end();
            }
        }

        // 合并
        arr = fullIpV6.split(":");
        if (maxStr.isEmpty()) {
            for (int i = start; i < end; i++) {
                arr[i] = ":";
            }
        }
        return StringUtils.join(arr, ":").replaceAll(":{2,}", "::");
    }
}
