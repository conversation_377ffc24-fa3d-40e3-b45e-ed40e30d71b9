package skynet.boot.common.disruptor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 数据管道 数据
 *
 * <pre>
 *     使用disruptor需要增加pom引用：
 *
 *          &lt;dependency&gt;
 *             &lt;groupId&gt;com.lmax&lt;/groupId&gt;
 *             &lt;artifactId&gt;disruptor&lt;/artifactId&gt;
 *         &lt;/dependency&gt;
 * </pre>
 *
 * @param <T> 任意数据类型
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class DataEvent<T> {

    private T value;

}
