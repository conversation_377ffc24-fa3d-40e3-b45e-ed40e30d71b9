package skynet.boot.common.utils;

import com.google.common.collect.BoundType;
import com.google.common.collect.Range;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * 原理:利用groovy的Range类，实现区间判断的功能
 */
@Slf4j
public class RangeCheckerUtil {
    /**
     * 判断给的区间是否连续和不重叠
     *
     * @param ranges
     * @return
     */
    public static boolean areRangesContinuousAndNonOverlapping(List<Range<Double>> ranges) {
        if (CollectionUtils.isEmpty(ranges)) {
            throw new RuntimeException("ranges cannot be null");
        }

        // 对区间进行排序
        ranges.sort(Comparator.comparingDouble(range -> range.hasLowerBound() ? range.lowerEndpoint() : Double.MIN_VALUE));

        for (int i = 0; i < ranges.size() - 1; i++) {
            Range<Double> current = ranges.get(i);
            Range<Double> next = ranges.get(i + 1);

            if (!isContinuousNonOverlapping(current, next)) {
                log.warn("Intervals are discontinuous or overlapping at.: {} and {}", current, next);
                return false;
            }


        }
        return true;
    }

    /**
     * 判断端点值是否重合
     *
     * @param current
     * @param next
     * @return
     */
    private static boolean isContinuousNonOverlapping(Range<Double> current, Range<Double> next) {
        if (current.upperBoundType() == BoundType.CLOSED && next.lowerBoundType() == BoundType.CLOSED) {
            return false;
        }

        if (current.upperBoundType() == BoundType.OPEN && next.lowerBoundType() == BoundType.OPEN) {
            return false;
        }

        return current.upperEndpoint().equals(next.lowerEndpoint());
    }

    /**
     * 传入区间是否包含了指定区间,并且判断提供的区间不连续或有重叠
     *
     * @param ranges
     * @param targetInterval
     * @return
     */
    public static boolean doRangesCoverWholeInterval(List<Range<Double>> ranges, Range<Double> targetInterval) {
        if (ranges.isEmpty()) {
            log.info("No intervals were provided. It is impossible to cover the specified range.");
            return false;
        }
        // 检查区间是否连续且不重叠
        if (!areRangesContinuousAndNonOverlapping(ranges)) {
            log.warn("The provided intervals are discontinuous or overlapping. They cannot cover the specified range.");
            return false;
        }


        // 对区间进行排序
        ranges.sort(Comparator.comparingDouble(Range::lowerEndpoint));
        Range<Double> rangeMax = ranges.get(ranges.size() - 1);
        Range<Double> rangeMin = ranges.get(0);
        BoundType lowerBoundType = rangeMin.lowerBoundType();
        BoundType upperBoundType = rangeMax.upperBoundType();
        Double lowerEndpoint = rangeMin.lowerEndpoint();
        Double upperEndpoint = rangeMax.upperEndpoint();
        Range<Double> combinedRange;
        // 根据边界类型构建新区间
        if (lowerBoundType == BoundType.CLOSED && upperBoundType == BoundType.CLOSED) {
            combinedRange = Range.closed(lowerEndpoint, upperEndpoint);
        } else if (lowerBoundType == BoundType.OPEN && upperBoundType == BoundType.CLOSED) {
            combinedRange = Range.openClosed(lowerEndpoint, upperEndpoint);
        } else if (lowerBoundType == BoundType.CLOSED && upperBoundType == BoundType.OPEN) {
            combinedRange = Range.closedOpen(lowerEndpoint, upperEndpoint);
        } else {
            combinedRange = Range.open(lowerEndpoint, upperEndpoint);
        }
        // 创建一个大的区间
        // 检查大区间是否包含目标区间
        if (combinedRange.encloses(targetInterval)) {
            return true;
        } else {
            log.warn("The merged intervals did not fully cover the target interval: {}", targetInterval);
            return false;
        }
    }

}