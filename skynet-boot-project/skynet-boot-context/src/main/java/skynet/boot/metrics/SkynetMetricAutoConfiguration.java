package skynet.boot.metrics;

import io.micrometer.core.aop.CountedAspect;
import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetMetrics;
import skynet.boot.common.OsUtil;

/**
 * <pre>
 * 指标管理
 * </pre>
 *
 * <AUTHOR>
 * @date 2019-12-04 09:23
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnBean(annotation = EnableSkynetMetrics.class)
@ConditionalOnClass(PrometheusMetricsExportAutoConfiguration.class)
@AutoConfigureAfter({PrometheusMetricsExportAutoConfiguration.class})
public class SkynetMetricAutoConfiguration {

    private final MeterRegistry meterRegistry;

    public SkynetMetricAutoConfiguration(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    @Bean
    @ConditionalOnMissingBean(TimedAspect.class)
    public TimedAspect defaultTimedAspect() {
        return new TimedAspect(meterRegistry);
    }

    @Bean
    @ConditionalOnMissingBean(CountedAspect.class)
    public CountedAspect defaultCountedAspect() {
        return new CountedAspect(meterRegistry);
    }

    @Bean
    public SkynetPoolMetrics skynetPoolMetrics(ApplicationContext applicationContext) {
        return new SkynetPoolMetrics(applicationContext, meterRegistry);
    }

    @Bean
    public SkynetMetricsService skynetMetricsService(SkynetPoolMetrics skynetPoolMetrics) {
        return new SkynetMetricsService(meterRegistry, skynetPoolMetrics);
    }

    @Bean
    @ConditionalOnProperty("skynet.metric.tag.pid.enabled")
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config().commonTags("pid", String.valueOf(OsUtil.getCurrentPid()));
    }
}
