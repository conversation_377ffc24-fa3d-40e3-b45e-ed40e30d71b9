package skynet.boot.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import skynet.boot.common.pool.ObjectPool2;
import skynet.boot.metrics.domain.MetricsLabel;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * SkynetMetrics收集器
 *
 * <pre>
 * 包括：如 某个指标 带有标签 属性，和不带标签的，适配  prometheus
 * </pre>
 *
 * <AUTHOR> 2019年05月30日13:33:47
 */
public class SkynetMetricsService {


    private final MeterRegistry meterRegistry;
    private final SkynetPoolMetrics skynetPoolMetrics;
    private static final List<Tag> EMPTY_LIST = new ArrayList<>(0);

    public SkynetMetricsService(MeterRegistry meterRegistry, SkynetPoolMetrics skynetPoolMetrics) {
        this.meterRegistry = meterRegistry;
        this.skynetPoolMetrics = skynetPoolMetrics;
    }

    /**
     * 请使用 timer 代替
     *
     * @param metricName
     * @param metricValue
     */
    @Deprecated
    public void aggregate(String metricName, long metricValue) {
        this.meterRegistry.timer(metricName).record(metricValue, TimeUnit.MILLISECONDS);
    }

    /**
     * 注册 ObjectPool2
     *
     * @param metricName metric 名称
     * @param pool
     * @param tags       扩展的tag 标签
     *                   add  since  4.0.13  by lyhu  2023年09月18日17:55:42
     */
    public void registerObjectPool2(String metricName, ObjectPool2<?> pool, String... tags) {
        this.skynetPoolMetrics.addObjectPool2(metricName, pool, tags);
    }


    /**
     * 提交 Metrics 累计 指标
     *
     * <pre>
     * 永远不会移除, （Prometheus中为counter数据类型）
     * </pre>
     *
     * @param metricName
     * @param value         增加的偏移量 也可以是负数（表示减少）
     * @param metricsLabels
     */
    public void counter(String metricName, long value, MetricsLabel... metricsLabels) {
        Iterable<Tag> tags = (metricsLabels == null || metricsLabels.length == 0) ? EMPTY_LIST : Arrays.asList(metricsLabels);
        meterRegistry.counter(metricName, tags).increment(value);
    }

    /**
     * timer
     *
     * @param metricName
     * @param value
     * @param tags
     */
    public void timer(String metricName, Duration value, String... tags) {
        this.meterRegistry.timer(metricName, tags).record(value);
    }


    /**
     * timer
     *
     * @param metricName
     * @param timeMillis
     * @param tags
     */
    public void timer(String metricName, long timeMillis, String... tags) {
        this.meterRegistry.timer(metricName, tags).record(Duration.ofMillis(timeMillis));
    }

    /**
     * @param metricName
     * @param timeMillis
     * @param tags
     */
    public void timer(String metricName, long timeMillis, Iterable<Tag> tags) {
        this.meterRegistry.timer(metricName, tags).record(Duration.ofMillis(timeMillis));
    }

    /**
     * 提交 Metrics 累计 + 1
     *
     * <pre>
     * 永远不会移除, （Prometheus中为counter数据类型）
     * </pre>
     *
     * @param metricName
     * @param metricsLabels
     */
    public void counterIncrement(String metricName, MetricsLabel... metricsLabels) {
        this.counter(metricName, 1, metricsLabels);
    }

    /**
     * 提交 Metrics 累计 - 1
     *
     * <pre>
     * 永远不会移除, （Prometheus中为counter数据类型）
     * </pre>
     *
     * @param metricName
     * @param metricsLabels
     */
    public void counterDecrement(String metricName, MetricsLabel... metricsLabels) {
        this.counter(metricName, -1, metricsLabels);
    }


    /**
     * 提交 Metrics 即时 指标
     *
     * <pre>
     * 永远不会移除, （Prometheus中为gauge数据类型）
     * </pre>
     *
     * @param metricName
     * @param value
     * @param metricsLabels
     */
    public void gauge(String metricName, long value, MetricsLabel... metricsLabels) {
        Iterable<Tag> tags = (metricsLabels == null || metricsLabels.length == 0) ? EMPTY_LIST : Arrays.asList(metricsLabels);
        meterRegistry.gauge(metricName, tags, value);
    }
}


