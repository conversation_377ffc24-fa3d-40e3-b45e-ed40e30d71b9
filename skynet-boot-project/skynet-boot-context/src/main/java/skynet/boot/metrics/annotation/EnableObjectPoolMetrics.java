package skynet.boot.metrics.annotation;

import java.lang.annotation.*;

/**
 * 启用 ObjectPool2<?> 对象的 Metrics
 * <p>
 * 使用方法  放到 放到具体的 ObjectPool2的 属性上，同时 此类 需要添加 @EnableSkynetMetrics 注解。
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2019年6月15日 上午8:09:54]
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface EnableObjectPoolMetrics {
    /**
     * 池对象名称
     *
     * <pre>
     * 如： skynet.mq.handler
     * </pre>
     *
     * @return
     */
    String value();
}
