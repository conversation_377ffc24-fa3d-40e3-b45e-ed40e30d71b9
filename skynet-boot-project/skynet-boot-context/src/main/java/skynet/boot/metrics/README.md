## skynet-boot-starter-metrics 指标组件

- 基于 MeterRegistry实现的提供 `SkynetMetricsService` 指标记录接口

> 与 MeterRegistry 对比，主要是新增了 聚合统计接口（最近150条 metric 的Max,Min,Avg, Avg10 Avg50, Avg150）

- 简洁Metric 注解：`@Timed`,`@Counted`

### 快速开始

pom.xml 依赖引入：

```xml
<dependency>
    <groupId>com.iflytek.skynet</groupId>
    <artifactId>skynet-boot-starter-metrics</artifactId>
</dependency>
```

Springboot启动类，增加 `@EnableSkynetMetrics` 开启Metric支持。

目标代码 注解引用 `SkynetMetricsService`，

```java
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.annotation.Counted;

@Slf4j
public class FuncHandlerPool<IN extends IApiRequest, OUT extends IApiResponse, H extends PandoraFuncHandler<IN, OUT>> implements AutoCloseable {

    private final PandoraLbService pandoraLbService;

    private final ApplicationContext springContext;
    private String func;

    private ObjectPool2<H> handlerPool;

    @Autowired(required = false)
    private SkynetMetricsService skynetMetricsService;

    public FuncHandlerPool(PandoraLbService pandoraLbService, ApplicationContext springContext) {
        this.pandoraLbService = pandoraLbService;
        this.springContext = springContext;
    }

    @LoggingCost
    @Timed
    public void initialize(PandoraProperties pandoraProperties, Class<?> funcHandlerType) throws Exception {
        log.info("Initialize FuncHandlerPool pandoraProperties={}", pandoraProperties);
        ObjectPool2Factory<H> objectPool2Factory = index -> {
            H pandoraFuncHandler = (H) springContext.getBean(funcHandlerType);
            pandoraFuncHandler.initialize();
            return pandoraFuncHandler;
        };
        this.handlerPool = new ObjectPool2<H>(pandoraProperties.getConcurrency(), objectPool2Factory);
        skynetMetricsService.registerObjectPool2("skynet.pandora.func.handler", this.handlerPool);
        this.func = StringUtils.isBlank(pandoraProperties.getLbName()) ? "UNSET" : pandoraProperties.getLbName();
    }

    @LoggingCost
    @Counted
    public OUT recognize(IN apiRequest) {

        OUT apiResponse = null;
        pandoraLbService.beforeReport();
        H handler = this.handlerPool.borrowObject();

        boolean ok = false;
        StopWatch stopwatch = new StopWatch();
        stopwatch.start();

        try {
            apiResponse = handler.run(apiRequest);
            log.debug("handler run cost= {} output= {}", stopwatch, apiResponse);
            ok = true;
        } catch (PandoraException e) {
            throw e;
        } catch (Exception e) {
            log.error("func run exception", e);
            throw new PandoraException("func run exception", e);
        } finally {
            this.handlerPool.returnObject(handler);
            pandoraLbService.afterReport();
            stopwatch.stop();
            if (skynetMetricsService != null) {
                skynetMetricsService.timer("skynet.pandora.func.cost", stopwatch.getLastTaskTimeMillis(), "func", func, "ok", String.valueOf(ok));
            }

        }
        return apiResponse;
    }


    @Override
    @LoggingCost
    public void close() {
        log.debug("Close func handler pool ...");
        try {
            if (this.handlerPool != null) {
                this.handlerPool.close();
            }
        } catch (Exception e) {
            log.error("Close func handler pool exception.", e);
        }
        log.debug("Close func handler pool.");
    }
}
```

### 其他说明

`skynet-boot-starter-metrics` 主要引用了以下2个依赖

```xml
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
</dependency>

<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```
