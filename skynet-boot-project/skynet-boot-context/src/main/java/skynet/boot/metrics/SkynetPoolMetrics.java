package skynet.boot.metrics;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.util.Assert;
import skynet.boot.annotation.EnableSkynetMetrics;
import skynet.boot.common.pool.ObjectPool2;
import skynet.boot.metrics.annotation.EnableObjectPoolMetrics;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * skynet.object pool Metrics 统一处理
 *
 * <pre>
 * 目前包含：ObjectPool Metric 度量指标
 * </pre>
 *
 * <AUTHOR> [2018年3月29日 上午11:35:33]
 */
@Slf4j
public class SkynetPoolMetrics implements ApplicationListener<ApplicationReadyEvent> {

    private static final Map<String, String> metricMap = new ConcurrentHashMap<>();

    private final ApplicationContext applicationContext;
    private final MeterRegistry meterRegistry;

    public SkynetPoolMetrics(ApplicationContext applicationContext, MeterRegistry meterRegistry) {
        this.applicationContext = applicationContext;
        this.meterRegistry = meterRegistry;
    }

    @Override
    public void onApplicationEvent(@NotNull ApplicationReadyEvent applicationReadyEvent) {
        // 没有从 applicationReadyEvent 中获取 applicationContext 原因:
        // 由于 applicationReadyEvent事件会触发多次，统一采用显式注解Bean来实现。 by lyhu
        if (applicationContext == null) {
            return;
        }
        //利用 ApplicationReadyEvent 事件 时机来注册 meterRegistry 回调。
        Map<String, List<ObjectPool2<?>>> objectPoolMap = getObjectPoolMap();
        for (Map.Entry<String, List<ObjectPool2<?>>> metric : objectPoolMap.entrySet()) {
            metric.getValue().forEach(x -> {
                if (!metricMap.containsKey(metric.getKey())) {
                    addObjectPool2(metric.getKey(), x);
                    metricMap.put(metric.getKey(), metric.getKey());
                }
            });
        }
    }

    public void addObjectPool2(String metricName, ObjectPool2<?> pool, String... tags) {
        Assert.hasText(metricName, "metricName is blank");
        if (meterRegistry != null) {
            Gauge.builder(String.format("%s.pool.size", metricName), pool, ObjectPool2::getPoolSize).tags(tags).register(meterRegistry);
            Gauge.builder(String.format("%s.pool.activity", metricName), pool, m -> m.getPoolSize() - m.getFreeSize()).tags(tags).register(meterRegistry);
            Gauge.builder(String.format("%s.pool.idle", metricName), pool, ObjectPool2::getFreeSize).tags(tags).register(meterRegistry);
            Gauge.builder(String.format("%s.pool.waiting", metricName), pool, ObjectPool2::getWaitingSize).tags(tags).register(meterRegistry);
            Gauge.builder(String.format("%s.pool.total", metricName), pool, ObjectPool2::getUsedSize).tags(tags).register(meterRegistry);
        }
    }

    private synchronized Map<String, List<ObjectPool2<?>>> getObjectPoolMap() {

        long start = System.currentTimeMillis();
        Map<String, Object> beansWithAnnotationMap = applicationContext.getBeansWithAnnotation(EnableSkynetMetrics.class);
        Map<String, List<ObjectPool2<?>>> objectPoolMap = new ConcurrentHashMap<>();

        Class<? extends Object> clazz = null;
        for (Map.Entry<String, Object> entry : beansWithAnnotationMap.entrySet()) {
            Object targetObject = AopProxyUtils.getSingletonTarget(entry.getValue());
            if (targetObject == null) {
                targetObject = entry.getValue();
            }
            clazz = targetObject.getClass();
            log.debug("Load EnableObjectPoolMetrics Object Pool= {}", clazz);
            for (Field field : clazz.getDeclaredFields()) {
                EnableObjectPoolMetrics enableObjectPoolMetrics = field.getAnnotation(EnableObjectPoolMetrics.class);
                if (enableObjectPoolMetrics != null) {
                    field.setAccessible(true);
                    try {
                        Object objValue = field.get(targetObject);
                        if (objValue instanceof ObjectPool2<?>) {
                            String metricsName = enableObjectPoolMetrics.value();
                            List<ObjectPool2<?>> poolList = objectPoolMap.getOrDefault(metricsName, new ArrayList<>(1));
                            poolList.add((ObjectPool2<?>) objValue);
                            objectPoolMap.put(metricsName, poolList);
                        }
                    } catch (Throwable e) {
                        log.error("get Object error= {}", e.getMessage());
                    }
                }
            }
        }
        log.debug("Load EnableObjectPoolMetrics Object Pool size= {}.cost= {}ms", objectPoolMap.size(), System.currentTimeMillis() - start);
        return objectPoolMap;
    }
}
