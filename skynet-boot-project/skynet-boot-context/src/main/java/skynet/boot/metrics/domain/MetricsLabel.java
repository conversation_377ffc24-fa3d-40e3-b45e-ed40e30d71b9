package skynet.boot.metrics.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.lang.Nullable;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class MetricsLabel implements Tag {
    private final String key;
    private final String value;

    public MetricsLabel(String key, String value) {
        Objects.requireNonNull(key);
        Objects.requireNonNull(value);
        this.key = key;
        this.value = value;
    }

    @Override
    public String getKey() {
        return key;
    }

    /**
     * @return the value
     */
    @Override
    public String getValue() {
        return value;
    }


    @JSONField(serialize = false, deserialize = false)
    public static final MetricsLabel OK_TRUE = new MetricsLabel("ok", "true");
    @JSONField(serialize = false, deserialize = false)
    public static final MetricsLabel OK_FALSE = new MetricsLabel("ok", "false");

    @Override
    public boolean equals(@Nullable Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            Tag that = (Tag) o;
            return Objects.equals(this.key, that.getKey()) && Objects.equals(this.value, that.getValue());
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        int result = this.key.hashCode();
        result = 31 * result + this.value.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "tag(" + this.key + "=" + this.value + ")";
    }
}
