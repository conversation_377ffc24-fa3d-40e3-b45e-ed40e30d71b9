package skynet.boot.i18n;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import skynet.boot.common.SpringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Locale;


/**
 * 获取i18n资源文件
 *
 * <p>
 * - 默认配置 spring.messages.basename=messages
 * - 配置示例：设置默认系统语言为中文：skynet.i18n.language=zh_CN
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class MessageUtils {

    /**
     * 根据消息键和参数 获取消息 委托给spring messageSource
     * <p>
     * - 默认配置 spring.messages.basename=messages
     * - 配置示例：设置默认系统语言为中文：skynet.i18n.language=zh_CN
     * <p>
     *
     * @param code 消息键
     * @param args 参数
     * @return 获取国际化翻译值
     */
    public static String getMessage(String code, Object... args) {
        MessageSource messageSource = SpringUtils.getBean(MessageSource.class);

        //优先根据 请求语言 获取 message，否则，返回系统配置默认的
        if (RequestContextHolder.getRequestAttributes() != null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            List<Locale> locales = Collections.list(request.getLocales());
            log.debug("request.locales={}", locales);
            for (Locale locale : locales) {
                try {
                    return messageSource.getMessage(code, args, locale);
                } catch (Exception e) {
                    log.warn("messageSource.getMessage locale={};error={}", locale, e.getMessage());
                }
            }
        }
        return messageSource.getMessage(code, args, getDefaultLocale());
    }

    public static Locale getDefaultLocale() {
        //获取系统默认的配置属性
        String i18nLanguage = SpringUtils.getBean(Environment.class).getProperty("skynet.i18n.language");
        if (StringUtils.hasText(i18nLanguage)) {
            String[] language = i18nLanguage.split("_");
            return new Locale(language[0], language[1]);
        }
        return LocaleContextHolder.getLocale();
    }
}
