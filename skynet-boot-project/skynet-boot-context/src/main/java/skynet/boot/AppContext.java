package skynet.boot;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import skynet.boot.common.InetAddressUtils;
import skynet.boot.common.OsUtil;
import skynet.boot.common.domain.Jsonable;

import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.*;

/**
 * Skynet App上下文信息：
 * <p>
 * 主要包括:
 * <ul>
 * <li>当前服务器的IP，当前进程Id，对外状态端口号，节点名称等</li>
 * <li>Spring容器， Ant 配置API等</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Slf4j
public final class AppContext extends Jsonable {

    public static final String BEAN_NAME = "skynet.appContext";
    private static final Date START_TIME = new Date();

    private final ApplicationContext applicationContext;

    private final SkynetProperties skynetProperties;
    private final String hostName;
    private final Map<String, Object> os;
    private final String uuid;
    private final int pid;
    private final boolean ssl;
    private final String contextPath;
    private final boolean ipv6;

    private List<String> projectVersionList = null;
    private Map<String, String> manifestMap;
    private long startCost;


    public AppContext(ApplicationContext applicationContext, SkynetProperties skynetProperties, Environment environment) throws Exception {
        this.applicationContext = applicationContext;
        this.skynetProperties = skynetProperties;
        this.hostName = OsUtil.getHostName();
        this.os = OsUtil.getOs();
        this.pid = OsUtil.getCurrentPid();
        this.uuid = "APP" + RandomStringUtils.randomAlphanumeric(13).toUpperCase();

        // 暂时采用 server.ssl.key-store 属性来 确定 是否是 ssl
        this.ssl = StringUtils.hasText(environment.getProperty("server.ssl.key-store"));

        String ipAddress = skynetProperties.getIpAddress();
        if ("true".equalsIgnoreCase(environment.getProperty("skynet.check-ip.enabled", "true"))) {
            if (!skynetProperties.isDebugMode() && !OsUtil.isLocalIP(ipAddress)) {
                throw new Exception(String.format("The ip %s is not local server.(this server ips=%s)", ipAddress, OsUtil.getAllIPs()));
            }
        }
        this.ipv6 = InetAddressUtils.isIPv6Address(ipAddress);
        ServerProperties serverProperties = applicationContext.getBean(ServerProperties.class);
        String contextPath = serverProperties.getServlet().getContextPath();
        if (StringUtils.hasText(contextPath)) {
            contextPath = contextPath.trim().startsWith("/") ? contextPath.trim() : String.format("/%s", contextPath.trim());
        } else {
            contextPath = "";
        }
        this.contextPath = contextPath;
    }


    @JSONField(ordinal = 5)
    @JsonProperty(index = 5)
    public String getUuid() {
        return uuid;
    }

    @JSONField(name = "ip", ordinal = 10)
    @JsonProperty(index = 10)
    public String getIpAddress() {
        return skynetProperties.getIpAddress();
    }


    @JSONField(ordinal = 20)
    @JsonProperty(index = 20)
    public int getPort() {
        return skynetProperties.getPort();
    }

    @JSONField(ordinal = 25)
    @JsonProperty(index = 25)
    public String getContextPath() {
        return this.contextPath;
    }

    /**
     * 获取IPEndpoint
     *
     * <pre>
     *  如：**************:8080
     * </pre>
     *
     * @return
     */
    @JSONField(serialize = false)
    @JsonIgnore
    public String getIpEndpoint() {
        return String.format(ipv6 ? "[%s]:%d" : "%s:%d", skynetProperties.getIpAddress(), skynetProperties.getPort());
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public URI getUri() {
        return URI.create(String.format("%s://%s%s", ssl ? "https" : "http", getIpEndpoint(), contextPath));
    }

    @JSONField(ordinal = 30)
    @JsonProperty(index = 30)
    public int getPid() {
        return pid;
    }

    /**
     * @return the ssl
     */
    @JSONField(ordinal = 40)
    @JsonProperty(index = 40)
    public boolean isSsl() {
        return ssl;
    }

    /**
     * 是否IPv6
     *
     * @return
     */
    @JSONField(ordinal = 45)
    @JsonProperty(index = 45)
    public boolean isIpv6() {
        return ipv6;
    }

    /**
     * name : rest-app-v1.0@sample
     *
     * @return
     */
    @JSONField(name = "name", ordinal = 50)
    @JsonProperty(index = 50)
    public String getNodeName() {
        return skynetProperties.getActionPoint();
    }

    /**
     * @return the antId
     */
    @JSONField(ordinal = 60)
    @JsonProperty(index = 60)
    public String getAntId() {
        return skynetProperties.getActionId();
    }

    @JSONField(ordinal = 70)
    @JsonProperty(index = 70)
    public String getHostName() {
        return hostName;
    }

    @JSONField(ordinal = 80)
    @JsonProperty(index = 80)
    public Map<String, Object> getOs() {
        return os;
    }


    /**
     * 获取程序 project_version
     *
     * @return project_version
     */
    @JSONField(ordinal = 90)
    @JsonProperty(index = 90)
    public List<String> getProjectVersionList() {
        try {
            if (projectVersionList == null || projectVersionList.isEmpty()) {
                Enumeration<URL> resources = getClass().getClassLoader().getResources("META-INF/MANIFEST.MF");
                projectVersionList = new ArrayList<>();
                while (resources.hasMoreElements()) {
                    Properties props = new Properties();
                    URL url = resources.nextElement();
                    props.load(url.openStream());
                    if (props.containsKey("project-name") && props.containsKey("build-time")) {
                        projectVersionList.add(String.format("%s:%s[branch:%s,num:%s](%s)"
                                , props.get("project-name")
                                , props.get("project-version")
                                , props.get("build-branch")
                                , props.get("build-number")
                                , props.get("build-time")));
                    }
                }
                Collections.sort(projectVersionList);
            }
        } catch (IOException e) {
            log.error("getProjectVersionList error", e);
        }
        return projectVersionList;
    }


    @Deprecated
    @JSONField(ordinal = 100)
    @JsonProperty(index = 100)
    public Map<String, String> getManifestItems() {
        try {
            if (manifestMap == null || manifestMap.isEmpty()) {
                URL resource = getClass().getClassLoader().getResource("META-INF/MANIFEST.MF");
                Map<String, String> manifestMap = new TreeMap<>();
                Properties props = new Properties();
                assert resource != null;
                props.load(resource.openStream());
                for (Map.Entry<Object, Object> item : props.entrySet()) {
                    manifestMap.put(String.valueOf(item.getKey()).toLowerCase(), String.valueOf(item.getValue()));
                }
                this.manifestMap = manifestMap;
            }
        } catch (IOException e) {
            log.error("getManifestItems error", e);
        }
        return this.manifestMap;
    }


    /**
     * SKYNET 部署的根目录路径
     * <p>
     *
     * <pre>
     * 例如：
     *  /iflytek/server/skynet
     * </pre>
     *
     * @return the skynetHome
     */
    @JSONField(ordinal = 110)
    @JsonProperty(index = 110)
    public String getSkynetHome() {
        return skynetProperties.getHome();
    }


    /**
     * @return the skynetProperties
     */
    @JSONField(ordinal = 120)
    @JsonProperty(index = 120)
    public SkynetProperties getSkynetProperties() {
        return skynetProperties;
    }


    @JSONField(format = "yyyy-MM-dd HH:mm:ss", ordinal = 130)
    @JsonProperty(index = 130)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    public Date getStartTime() {
        return START_TIME;
    }

    @JSONField(ordinal = 140)
    @JsonProperty(index = 140)
    public long getStartCost() {
        return startCost;
    }

    void setStartCost(long startCost) {
        this.startCost = startCost;
    }


    @JSONField(deserialize = false)
    @JsonIgnore
    public String getFrom() {
        return String.format("%s[ip:%s;pid:%d;time:%s]", skynetProperties.getActionPoint(), skynetProperties.getIpAddress(), OsUtil.getCurrentPid(),
                DateFormatUtils.format(new Date(), "yy-MM-dd HH:mm:ss"));
    }


    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public ApplicationContext getSpringContext() {
        return applicationContext;
    }
}
