package org.springframework.cloud.tlb.extension.config;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.tlb.discovery.ConditionalOnTlbDiscoveryEnabled;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryAutoConfiguration;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.cloud.tlb.extension.TlbRestTemplateInitializer;
import org.springframework.cloud.tlb.extension.TlbRestTemplateInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import skynet.boot.annotation.EnableSkynetTlbExtension;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

/**
 * RestTemplate拦截器配置类
 *
 * @author: leitong
 * @date: 2023/5/17 21:16
 * @description:
 **/
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(RestTemplate.class)
@ConditionalOnTlbDiscoveryEnabled
@ConditionalOnBean(annotation = EnableSkynetTlbExtension.class, value = {TlbDiscoveryProperties.class, TlbSelectHeaderProperties.class})
@AutoConfigureAfter(TlbDiscoveryAutoConfiguration.class)
public class TlbRestTemplateAutoConfiguration {

    @Bean
    public TlbRestTemplateInterceptor tlbRestTemplateInterceptor(TlbDiscoveryProperties properties, TlbSelectHeaderProperties tlbSelectHeaderProperties) {
        return new TlbRestTemplateInterceptor(properties, tlbSelectHeaderProperties);
    }

    @Bean
    public TlbRestTemplateInitializer tlbRestTemplateInitializer(TlbRestTemplateInterceptor tlbRestTemplateInterceptor) {
        return new TlbRestTemplateInitializer(tlbRestTemplateInterceptor);
    }

}