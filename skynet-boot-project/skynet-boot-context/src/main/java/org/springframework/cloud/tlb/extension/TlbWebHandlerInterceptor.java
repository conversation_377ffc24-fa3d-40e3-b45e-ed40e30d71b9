package org.springframework.cloud.tlb.extension;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.HandlerInterceptor;
import skynet.boot.tlb.TlbRequestContextHolder;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

/**
 * MVC拦截器，从http header中获取TLB标签值并存入上下文中
 *
 * @author: leitong
 * @date: 2023/4/28 11:35
 * @description:
 **/
@Slf4j
public class TlbWebHandlerInterceptor implements HandlerInterceptor {

    private final TlbSelectHeaderProperties tlbSelectHeaderProperties;

    public TlbWebHandlerInterceptor(TlbSelectHeaderProperties tlbSelectHeaderProperties) {
        this.tlbSelectHeaderProperties = tlbSelectHeaderProperties;
        log.info("TlbWebInterceptor initialized. tlbSelectHeaderProperties={}", tlbSelectHeaderProperties);
    }

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        log.debug("TlbWebHandlerInterceptor preHandle ...");
        if (tlbSelectHeaderProperties.isEnabled()) {
            String chain = request.getHeader(tlbSelectHeaderProperties.getChainKey());
            String selector = request.getHeader(tlbSelectHeaderProperties.getTagSelectorKey());
            String bizId = request.getHeader(tlbSelectHeaderProperties.getBizIdKey());
            TlbRequestContextHolder.setHeader(chain, selector, bizId);
        }
        return true;
    }
}