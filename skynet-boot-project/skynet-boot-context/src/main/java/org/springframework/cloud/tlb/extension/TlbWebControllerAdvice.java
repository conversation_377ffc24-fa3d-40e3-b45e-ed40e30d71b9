package org.springframework.cloud.tlb.extension;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

/**
 * 为RestController返回结果统一添加http header
 *
 * @author: leitong
 * @date: 2023/5/17 22:23
 * @description:
 **/
@Slf4j
@ControllerAdvice(annotations = TlbAdviceEnabled.class)
public class TlbWebControllerAdvice implements ResponseBodyAdvice<Object> {

    private final TlbSelectHeaderProperties tlbSelectHeaderProperties;
    private final TlbChainBuilder tlbChainBuilder;

    public TlbWebControllerAdvice(TlbSelectHeaderProperties tlbSelectHeaderProperties, TlbChainBuilder tlbChainBuilder) {
        log.info("Build TlbWebControllerAdvice");
        this.tlbSelectHeaderProperties = tlbSelectHeaderProperties;
        this.tlbChainBuilder = tlbChainBuilder;
    }

    @Override
    public Object beforeBodyWrite(Object body, @NotNull MethodParameter returnType, @NotNull MediaType selectedContentType,
                                  @NotNull Class selectedConverterType, @NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response) {
        if (response instanceof ServletServerHttpResponse && tlbSelectHeaderProperties.isEnabled()) {
            ServletServerHttpResponse res = (ServletServerHttpResponse) (response);
            res.getHeaders().set(tlbSelectHeaderProperties.getChainKey(), tlbChainBuilder.build());
        }

        return body;
    }

    @Override
    public boolean supports(@NotNull MethodParameter returnType, @NotNull Class converterType) {
        return true;
    }
}