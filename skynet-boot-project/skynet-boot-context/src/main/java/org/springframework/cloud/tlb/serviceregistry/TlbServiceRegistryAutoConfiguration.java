/*
 * Copyright 2015-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.springframework.cloud.tlb.serviceregistry;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration;
import org.springframework.cloud.tlb.discovery.ConditionalOnTlbDiscoveryEnabled;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.SkynetProperties;
import skynet.boot.annotation.EnableSkynetTlbDiscoveryClient;
import skynet.boot.tlb.TlbClientRegister;
import skynet.boot.tlb.config.TlbAutoConfiguration;

/**
 * TlbServiceRegistry AutoConfiguration
 *
 * <AUTHOR>
 * @since 4.0.9
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnTlbDiscoveryEnabled
@ConditionalOnProperty(value = TlbDiscoveryProperties.PREFIX + ".register")
@ConditionalOnBean(annotation = EnableSkynetTlbDiscoveryClient.class, value = {TlbClientRegister.class})
@AutoConfigureAfter(TlbAutoConfiguration.class)
@AutoConfigureBefore(ServiceRegistryAutoConfiguration.class)
public class TlbServiceRegistryAutoConfiguration {

    @Bean
    public TlbServiceRegistry tlbServiceRegistry(TlbClientRegister tlbClientRegister, TlbDiscoveryProperties tlbDiscoveryProperties, SkynetProperties skynetProperties) {
        return new TlbServiceRegistry(tlbClientRegister, tlbDiscoveryProperties, skynetProperties);
    }
}
