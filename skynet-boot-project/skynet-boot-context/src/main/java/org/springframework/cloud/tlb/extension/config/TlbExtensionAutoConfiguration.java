package org.springframework.cloud.tlb.extension.config;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.cloud.tlb.discovery.ConditionalOnTlbDiscoveryEnabled;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryAutoConfiguration;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.cloud.tlb.extension.TlbChainBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetTlbExtension;

/**
 * @author: leitong
 * @date: 2023/5/17 21:18
 * @description:
 **/
@Configuration(proxyBeanMethods = false)
@ConditionalOnTlbDiscoveryEnabled
@ConditionalOnBean(annotation = EnableSkynetTlbExtension.class, value = {TlbDiscoveryProperties.class})
@AutoConfigureAfter(TlbDiscoveryAutoConfiguration.class)
public class TlbExtensionAutoConfiguration {

    @Bean
    public TlbChainBuilder tlbChainBuilder(TlbDiscoveryProperties properties) {
        return new TlbChainBuilder(properties);
    }

}