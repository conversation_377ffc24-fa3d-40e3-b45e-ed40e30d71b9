package org.springframework.cloud.tlb.extension;

import feign.InvocationContext;
import feign.ResponseInterceptor;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.tlb.TlbRequestContextHolder;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

import java.util.Collection;

/**
 * Feign响应拦截器，用于提取TLB链路信息
 *
 * @author: leitong
 * @date: 2023/5/17 17:21
 * @description:
 **/
@Slf4j
public class TlbFeignResponseInterceptor implements ResponseInterceptor {

    private final TlbSelectHeaderProperties tlbSelectHeaderProperties;

    public TlbFeignResponseInterceptor(TlbSelectHeaderProperties tlbSelectHeaderProperties) {
        this.tlbSelectHeaderProperties = tlbSelectHeaderProperties;
    }

    //TODO: 需要测试

    @Override
    public Object intercept(InvocationContext invocationContext, Chain chain) throws Exception {
        if (tlbSelectHeaderProperties.isEnabled()) {
            log.debug("updateChain ...");
            Collection<String> chainHeader = invocationContext.response().headers().get(tlbSelectHeaderProperties.getChainKey());
            if (chainHeader != null && !chainHeader.isEmpty()) {
                TlbRequestContextHolder.updateChain(chainHeader.iterator().next());
            }
        }
        return invocationContext.proceed();
    }
}