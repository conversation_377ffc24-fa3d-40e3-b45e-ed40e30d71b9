package org.springframework.cloud.openfeign.loadbalancer;

import feign.Client;
import feign.Request;
import feign.Response;
import skynet.boot.security.auth.AuthUtils;
import skynet.boot.security.config.SkynetAuthClientProperties;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.*;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.cloud.tlb.discovery.TlbServiceInstance;
import org.springframework.cloud.tlb.discovery.TlbServiceInstanceSelector;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

public class TlbFeignBlockingLoadBalancerClient extends FeignBlockingLoadBalancerClient {
    private static final Log LOG = LogFactory.getLog(TlbFeignBlockingLoadBalancerClient.class);
    private final Client delegate;
    private final LoadBalancerClient loadBalancerClient;
    private final LoadBalancerClientFactory loadBalancerClientFactory;
//    private final List<LoadBalancerFeignRequestTransformer> transformers;
    private final TlbServiceInstanceSelector tlbServiceInstanceSelector;
    private final SkynetAuthClientProperties authProperties;
    private final Random random = new Random();

    public TlbFeignBlockingLoadBalancerClient(Client delegate, LoadBalancerClient loadBalancerClient, LoadBalancerClientFactory loadBalancerClientFactory, List<LoadBalancerFeignRequestTransformer> transformers, TlbServiceInstanceSelector tlbServiceInstanceSelector, SkynetAuthClientProperties authProperties) {
        super(delegate, loadBalancerClient, loadBalancerClientFactory, transformers);
        this.delegate = delegate;
        this.loadBalancerClient = loadBalancerClient;
        this.loadBalancerClientFactory = loadBalancerClientFactory;
//        this.transformers = transformers;
        this.tlbServiceInstanceSelector = tlbServiceInstanceSelector;
        this.authProperties = authProperties;
    }

    @Override
    public Response execute(Request request, Request.Options options) throws IOException {
        Collection<String> serviceNameCollection = request.headers().get("x-forword-svc");
        ServiceInstance instance = null;
        URI originalUri = URI.create(request.url());
        String serviceId = CollectionUtils.isEmpty(serviceNameCollection) ? originalUri.getHost() : serviceNameCollection.iterator().next();
        Assert.state(serviceId != null, "Request URI does not contain a valid hostname: " + originalUri);

        String hint = getHint(serviceId);
        DefaultRequest<RequestDataContext> lbRequest = new DefaultRequest(new RequestDataContext(LoadBalancerUtils.buildRequestData(request), hint));
        Set<LoadBalancerLifecycle> supportedLifecycleProcessors = LoadBalancerLifecycleValidator.getSupportedLifecycleProcessors(this.loadBalancerClientFactory.getInstances(serviceId, LoadBalancerLifecycle.class), RequestDataContext.class, ResponseData.class, ServiceInstance.class);
        supportedLifecycleProcessors.forEach((lifecycle) -> {
            lifecycle.onStart(lbRequest);
        });
        LOG.debug("TlbFeignBlockingLoadBalancerClient execute uri :" + originalUri);
        if (CollectionUtils.isEmpty(serviceNameCollection) && !isValidIP(originalUri.getHost())) {
            LOG.debug("loadBalancerClient choose serviceId :" + serviceId);
            List<ServiceInstance> instances = tlbServiceInstanceSelector.getInstances(serviceId);
            if (!CollectionUtils.isEmpty(instances)) {
                int index = random.nextInt(instances.size());
                instance = instances.get(index);
            } else {
                LOG.debug("loadBalancerClient choose serviceId " + serviceId + " not found:");
            }
        } else {
            skynet.boot.tlb.client.data.TlbServiceInstance tlbServiceInstance = new skynet.boot.tlb.client.data.TlbServiceInstance();
            tlbServiceInstance.setServiceName(serviceId);
            tlbServiceInstance.setServiceID(serviceId);
            tlbServiceInstance.setServiceIP(originalUri.getHost());
            tlbServiceInstance.setServicePort(originalUri.getPort());
            tlbServiceInstance.setServiceName(originalUri.getScheme());
            instance = new TlbServiceInstance(tlbServiceInstance);
        }

        org.springframework.cloud.client.loadbalancer.Response<ServiceInstance> lbResponse = new DefaultResponse(instance);
        String message;
        if (instance == null) {
            message = "Load balancer does not contain an instance for the service " + serviceId;
            if (LOG.isWarnEnabled()) {
                LOG.warn(message);
            }

            supportedLifecycleProcessors.forEach((lifecycle) -> {
                lifecycle.onComplete(new CompletionContext(CompletionContext.Status.DISCARD, lbRequest, lbResponse));
            });
            return Response.builder().request(request).status(HttpStatus.SERVICE_UNAVAILABLE.value()).body(message, StandardCharsets.UTF_8).build();
        } else {
            message = this.loadBalancerClient.reconstructURI(instance, originalUri).toString();
            Request newRequest = this.buildRequest(request, message, instance);
            return LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(this.delegate, options, newRequest, lbRequest, lbResponse, supportedLifecycleProcessors);
        }
    }

    /**
     * 注入认证头信息
     * 
     * 虽然 @see SignAuthFeignClientConfiguration 也实现了 @see RequestInterceptor 接口注入了认证信息，但是注入时机过早，
     * 注入时 URL 还没有经过 TLB 转换，导致请求头中的 Host 是 TLB 服务名，而不是真实的 IP 地址，进而导致签名错误
     */
    @Override
    protected Request buildRequest(Request request, String url, ServiceInstance instance) {
        if (StringUtils.isNotBlank(authProperties.getApiKey()) && StringUtils.isNotBlank(authProperties.getApiSecret())) {
            Map<String, String> headers = AuthUtils.assembleAuthorizationHeaders(
                HttpMethod.valueOf(request.httpMethod().name()),
                url,
                authProperties.getApiKey(), authProperties.getApiSecret(),
                request.body() == null ? null : new String(request.body(), StandardCharsets.UTF_8)
            );

            for (Map.Entry<String, String> entry : headers.entrySet()) {
                request.header(entry.getKey(), entry.getValue());
            }
        }
        return super.buildRequest(request, url, instance);
    }

    private String getHint(String serviceId) {
        LoadBalancerProperties properties = this.loadBalancerClientFactory.getProperties(serviceId);
        String defaultHint = (String) properties.getHint().getOrDefault("default", "default");
        String hintPropertyValue = (String) properties.getHint().get(serviceId);
        return hintPropertyValue != null ? hintPropertyValue : defaultHint;
    }

    private boolean isValidIPv4(String ip) {
        String ipv4Pattern = "^([0-9]{1,3}\\.){3}[0-9]{1,3}$";
        if (ip.matches(ipv4Pattern)) {
            String[] parts = ip.split("\\.");
            for (String part : parts) {
                int value = Integer.parseInt(part);
                if (value < 0 || value > 255) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    private boolean isValidIPv6(String ip) {
        String ipv6Pattern = "([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}";
        return ip.matches(ipv6Pattern);
    }

    private boolean isValidIP(String ip) {
        return isValidIPv4(ip) || isValidIPv6(ip);
    }

}
