package org.springframework.cloud.tlb.extension.config;

import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.tlb.discovery.ConditionalOnTlbDiscoveryEnabled;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryAutoConfiguration;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.cloud.tlb.extension.TlbChainBuilder;
import org.springframework.cloud.tlb.extension.TlbWebControllerAdvice;
import org.springframework.cloud.tlb.extension.TlbWebHandlerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import skynet.boot.annotation.EnableSkynetTlbExtension;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

/**
 * TLB扩展配置类，用于生成各类拦截器/过滤器
 *
 * @author: leitong
 * @date: 2023/5/10 16:43
 * @description:
 **/
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(WebMvcConfigurer.class)
@ConditionalOnTlbDiscoveryEnabled
@ConditionalOnBean(annotation = EnableSkynetTlbExtension.class, value = {TlbDiscoveryProperties.class, TlbSelectHeaderProperties.class})
@AutoConfigureAfter(TlbDiscoveryAutoConfiguration.class)
public class TlbWebMvcAutoConfiguration {

    @Bean
    public WebMvcConfigurer tlbWebMvcConfigurer(TlbSelectHeaderProperties tlbSelectHeaderProperties) {
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(@NotNull InterceptorRegistry registry) {
                registry.addInterceptor(new TlbWebHandlerInterceptor(tlbSelectHeaderProperties));
            }
        };
    }

    @Bean
    public TlbWebControllerAdvice tlbWebControllerAdvice(TlbSelectHeaderProperties tlbSelectHeaderProperties, TlbChainBuilder tlbChainBuilder) {
        return new TlbWebControllerAdvice(tlbSelectHeaderProperties, tlbChainBuilder);
    }
}