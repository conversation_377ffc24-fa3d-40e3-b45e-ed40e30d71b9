package org.springframework.cloud.tlb.extension;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.tlb.TlbRequestContextHolder;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;
import skynet.boot.tlb.domain.TlbRequestHeader;

/**
 * Feign请求拦截器，用于设置TLB相关的 http header
 *
 * @author: leitong
 * @date: 2023/5/17 17:20
 * @description:
 **/
@Slf4j
public class TlbFeignRequestInterceptor implements RequestInterceptor {

    private final TlbSelectHeaderProperties tlbSelectHeaderProperties;

    public TlbFeignRequestInterceptor(TlbSelectHeaderProperties tlbSelectHeaderProperties) {
        log.info("Build TlbFeignRequestInterceptor tlbSelectHeaderProperties={}", tlbSelectHeaderProperties);
        this.tlbSelectHeaderProperties = tlbSelectHeaderProperties;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {

        if (this.tlbSelectHeaderProperties.isEnabled()) {
            log.debug("TlbFeignRequestInterceptor apply...");
            TlbRequestHeader tlbRequestHeader = TlbRequestContextHolder.getHeader();
            if (tlbRequestHeader != null) {
                if (StringUtils.isNotBlank(tlbRequestHeader.getBizId())) {
                    requestTemplate.header(tlbSelectHeaderProperties.getBizIdKey(), tlbRequestHeader.getBizId());
                }
                // 将 如果没有传递 并且 本地设置了 服务标签，就设置头信息
                if (StringUtils.isNotBlank(tlbRequestHeader.getSelector())) {
                    requestTemplate.header(tlbSelectHeaderProperties.getTagSelectorKey(), tlbRequestHeader.getSelector());
                }
                // 如果请求头中，没有设置 selector，下个请求不 接力传递 请求头。 by lyhu  2025年01月06日20:34:50

//                else if (StringUtils.isNotBlank(tlbDiscoveryProperties.getDefaultServiceTagSelector())) {
//                    requestTemplate.header(tlbSelectHeaderProperties.getTagSelectorKey(), tlbDiscoveryProperties.getDefaultServiceTagSelector());
//                }
                if (StringUtils.isNotBlank(tlbRequestHeader.getChain())) {
                    requestTemplate.header(tlbSelectHeaderProperties.getChainKey(), tlbRequestHeader.getChain());
                }
            }
        }
    }
}