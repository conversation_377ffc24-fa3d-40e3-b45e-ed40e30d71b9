package org.springframework.cloud.tlb.extension;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import skynet.boot.tlb.TlbRequestContextHolder;

/**
 * 构建调用链
 *
 * @author: leitong
 * @date: 2023/5/17 16:59
 * @description:
 **/
public class TlbChainBuilder {

    private final TlbDiscoveryProperties tlbDiscoveryProperties;

    public TlbChainBuilder(TlbDiscoveryProperties tlbDiscoveryProperties) {
        this.tlbDiscoveryProperties = tlbDiscoveryProperties;
    }

    /**
     * 将自身添加进调用链中并返回Json Array 字符串格式的调用链信息
     *
     * @return
     */
    public String build() {
        String chain = TlbRequestContextHolder.getHeader().getChain();
        JSONArray chainJSONArray = (StringUtils.isBlank(chain)) ? new JSONArray() : JSONArray.parse(chain);
        String endpoint = tlbDiscoveryProperties.getServiceHost() + ":" + tlbDiscoveryProperties.getServicePort();
        chainJSONArray.add(new JSONObject().fluentPut("name", tlbDiscoveryProperties.getServiceName())
                .fluentPut("endpoint", endpoint));
        return chainJSONArray.toJSONString();
    }
}