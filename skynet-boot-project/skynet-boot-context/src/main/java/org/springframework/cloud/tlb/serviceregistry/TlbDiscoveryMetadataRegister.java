package org.springframework.cloud.tlb.serviceregistry;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.util.StringUtils;
import skynet.boot.SkynetConsts;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;

/**
 * 注册 PluginCode 到 spring.cloud.tlb.discovery.metadata["SKYNET_PLUGIN_CODE"]=skynet-stream
 * <p>
 * <p>
 * 注册时机：TlbDiscoveryProperties 初始化后，
 * <p>
 *
 * <AUTHOR>
 * @date 2022/5/1 08:28
 */
@Slf4j
public class TlbDiscoveryMetadataRegister {

    public TlbDiscoveryMetadataRegister(SkynetProperties skynetProperties,
                                        TlbDiscoveryProperties tlbDiscoveryProperties) {
        if (StringUtils.hasText(skynetProperties.getPlugin())) {
            log.info("Register metadata of tlbDiscoveryProperties: {} = {}",
                    SkynetConsts.DISCOVER_METADATA_SKYNET_PLUGIN_CODE_KEY, skynetProperties.getPlugin());
            tlbDiscoveryProperties.getMetadata().put(SkynetConsts.DISCOVER_METADATA_SKYNET_PLUGIN_CODE_KEY,
                    skynetProperties.getPlugin());

            log.info("Register metadata of tlbDiscoveryProperties: {} = {}",
                    SkynetConsts.DISCOVER_METADATA_SKYNET_ACTION_PID, OsUtil.getCurrentPid());
            tlbDiscoveryProperties.getMetadata().put(SkynetConsts.DISCOVER_METADATA_SKYNET_ACTION_PID,
                    String.valueOf(OsUtil.getCurrentPid()));

        } else {
            log.warn("Current action plugin is blank.[actionPoint = {}]", skynetProperties.getActionPoint());
        }
    }
}
