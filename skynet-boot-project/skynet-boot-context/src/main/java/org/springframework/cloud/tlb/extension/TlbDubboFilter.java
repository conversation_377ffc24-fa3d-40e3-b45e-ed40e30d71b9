package org.springframework.cloud.tlb.extension;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.tlb.TlbRequestContextHolder;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

/**
 * Dubbo拦截器，从dubbo请求的隐式参数中获取TLB标签并存入上下文中
 *
 * @author: leitong
 * @date: 2023/5/9 17:58
 * @description:
 **/
@Slf4j
@Getter
@Setter
@Activate(group = Constants.PROVIDER)
public class TlbDubboFilter implements Filter {

    private TlbSelectHeaderProperties tlbSelectHeaderProperties;

    private TlbChainBuilder tlbChainBuilder;

    public TlbDubboFilter() {
        log.info("TlbDubboFilter initialized");
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        boolean isTlbEnabled = tlbSelectHeaderProperties.isEnabled();
        if (isTlbEnabled) {
            String chain = invocation.getAttachment(tlbSelectHeaderProperties.getChainKey(), null);
            String selector = invocation.getAttachment(tlbSelectHeaderProperties.getTagSelectorKey(), null);
            String bizId = invocation.getAttachment(tlbSelectHeaderProperties.getBizIdKey(), null);
            TlbRequestContextHolder.setHeader(chain, selector, bizId);
        }
        Result result = null;
        try {
            result = invoker.invoke(invocation);
        } finally {
            if (isTlbEnabled && result != null) {
                try {
                    result.getAttachments().put(tlbSelectHeaderProperties.getChainKey(), tlbChainBuilder.build());
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        return result;
    }

}