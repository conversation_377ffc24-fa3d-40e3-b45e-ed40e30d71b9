package org.springframework.cloud.tlb.serviceregistry;

import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.tlb.discovery.ConditionalOnTlbDiscoveryEnabled;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.SkynetProperties;

@ConditionalOnTlbDiscoveryEnabled
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({TlbDiscoveryProperties.class})
@ConditionalOnBean(value = {TlbDiscoveryProperties.class})
@AutoConfigureBefore(TlbServiceRegistryAutoConfiguration.class)
public class TlbDiscoveryMetadataRegisterAutoConfiguration {

    @Bean
    public TlbDiscoveryMetadataRegister tlbDiscoveryMetadataRegister(SkynetProperties skynetProperties,
                                                                     TlbDiscoveryProperties tlbDiscoveryProperties) {
        return new TlbDiscoveryMetadataRegister(skynetProperties, tlbDiscoveryProperties);
    }
}
