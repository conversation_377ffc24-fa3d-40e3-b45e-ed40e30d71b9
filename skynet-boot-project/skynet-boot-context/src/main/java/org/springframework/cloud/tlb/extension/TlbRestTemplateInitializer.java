package org.springframework.cloud.tlb.extension;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * 为RestTemplate Bean初始化TLB拦截器
 *
 * @author: leitong
 * @date: 2023/5/9 18:14
 * @description:
 **/
@Slf4j
public class TlbRestTemplateInitializer implements ApplicationListener<ApplicationStartedEvent> {

    private final TlbRestTemplateInterceptor tlbRestTemplateInterceptor;

    public TlbRestTemplateInitializer(TlbRestTemplateInterceptor tlbRestTemplateInterceptor) {
        log.info("Build TlbRestTemplateInitializer.");
        this.tlbRestTemplateInterceptor = tlbRestTemplateInterceptor;
    }

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.debug("TlbRestTemplateInitializer...");
        try {
            // 获取容器中所有RestTemplate类型的bean
            Map<String, RestTemplate> beans = event.getApplicationContext().getBeansOfType(RestTemplate.class);
            for (Map.Entry<String, RestTemplate> entry : beans.entrySet()) {
                RestTemplate bean = entry.getValue();
                List<ClientHttpRequestInterceptor> interceptors = bean.getInterceptors();
                // 为bean添加拦截器
                interceptors.add(0, this.tlbRestTemplateInterceptor);
                bean.setInterceptors(interceptors);
                log.info("Add tlb-interceptor for RestTemplate : {}", entry.getKey());
            }
        } catch (BeansException e) {
            // do nothing
        }
    }
}