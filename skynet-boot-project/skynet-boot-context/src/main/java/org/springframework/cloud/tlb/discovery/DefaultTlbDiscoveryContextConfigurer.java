package org.springframework.cloud.tlb.discovery;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import skynet.boot.discovery.TlbDiscoveryContext;
import skynet.boot.discovery.TlbDiscoveryContextConfigurer;
import skynet.boot.tlb.TlbRequestContextHolder;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;
import skynet.boot.tlb.domain.TlbRequestHeader;

import java.util.Optional;

/**
 * 根据HTTP请求头，动态修改 TLB 服务发现参数，优先级最高
 */
@Slf4j
public class DefaultTlbDiscoveryContextConfigurer implements TlbDiscoveryContextConfigurer {

    private final TlbSelectHeaderProperties tlbSelectHeaderProperties;

    public final static int DEFAULT_ORDER = LOWEST_PRECEDENCE - 100;

    public DefaultTlbDiscoveryContextConfigurer(TlbSelectHeaderProperties tlbSelectHeaderProperties) {
        log.info("Build DefaultTlbDiscoveryContextConfigurer tlbSelectHeaderProperties={}", tlbSelectHeaderProperties);
        this.tlbSelectHeaderProperties = tlbSelectHeaderProperties;
    }

    @Override
    public void apply(TlbDiscoveryContext tlbDiscoveryContext) {
        if (!tlbSelectHeaderProperties.isEnabled()) {
            return;
        }
        log.trace("tlbDiscoveryContext={}", tlbDiscoveryContext);
        TlbRequestHeader header = TlbRequestContextHolder.getHeader();
        String bizId = Optional.ofNullable(header).map(TlbRequestHeader::getBizId).orElse(null);
        String tagSelector = Optional.ofNullable(header).map(TlbRequestHeader::getSelector).orElse(null);
        if (StringUtils.hasText(bizId)) {
            tlbDiscoveryContext.setBizId(bizId);
        }
        if (StringUtils.hasText(tagSelector)) {
            tlbDiscoveryContext.setTagSelector(tagSelector);
        }
        log.trace("tlbDiscoveryContext={}", tlbDiscoveryContext);
    }

    /**
     * 可以通过 order 修改 多个 TlbDiscoveryContextConfigurer 实现的顺序，可以达到覆盖的效果
     *
     * @return
     */
    @Override
    public int getOrder() {
        return DEFAULT_ORDER;
    }
}
