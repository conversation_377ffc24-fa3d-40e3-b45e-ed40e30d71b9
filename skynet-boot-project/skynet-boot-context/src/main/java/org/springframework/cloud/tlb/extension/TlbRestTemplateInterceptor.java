package org.springframework.cloud.tlb.extension;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import skynet.boot.tlb.TlbRequestContextHolder;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;
import skynet.boot.tlb.domain.TlbRequestHeader;

import java.io.IOException;
import java.util.List;

/**
 * RestTemplate拦截器，从上下文获取标签值并给http header赋值
 *
 * @author: leitong
 * @date: 2023/5/9 18:21
 * @description:
 **/
@Slf4j
public class TlbRestTemplateInterceptor implements ClientHttpRequestInterceptor {

    private final TlbDiscoveryProperties tlbDiscoveryProperties;
    private final TlbSelectHeaderProperties tlbSelectHeaderProperties;

    public TlbRestTemplateInterceptor(TlbDiscoveryProperties properties, TlbSelectHeaderProperties tlbSelectHeaderProperties) {
        log.info("Build TlbRestTemplateInterceptor tlbDiscoveryProperties={}", properties);
        this.tlbDiscoveryProperties = properties;
        this.tlbSelectHeaderProperties = tlbSelectHeaderProperties;
    }

    @NotNull
    @Override
    public ClientHttpResponse intercept(@NotNull HttpRequest request, @NotNull byte[] body, @NotNull ClientHttpRequestExecution execution) throws IOException {
        if (tlbDiscoveryProperties.isEnabled()) {
            log.debug("intercept...");
            TlbRequestHeader tlbRequestHeader = TlbRequestContextHolder.getHeader();
            if (tlbRequestHeader != null) {
                log.debug("tlbRequestHeader={}", tlbRequestHeader);
                if (StringUtils.isNotBlank(tlbRequestHeader.getBizId())) {
                    request.getHeaders().set(tlbSelectHeaderProperties.getBizIdKey(), tlbRequestHeader.getBizId());
                }
                if (StringUtils.isNotBlank(tlbRequestHeader.getSelector())) {
                    request.getHeaders().set(tlbSelectHeaderProperties.getTagSelectorKey(), tlbRequestHeader.getSelector());
                }
//                如果没有传递 并且 本地设置了 服务标签，就设置头信息
                if (StringUtils.isNotBlank(tlbRequestHeader.getSelector())) {
                    request.getHeaders().set(tlbSelectHeaderProperties.getTagSelectorKey(), tlbRequestHeader.getSelector());
                }
                // 如果请求头中，没有设置 selector，下个请求不 接力传递 请求头。 by lyhu  2025年01月06日20:34:50
//                else if (StringUtils.isNotBlank(tlbDiscoveryProperties.getDefaultServiceTagSelector())) {
//                    request.getHeaders().set(tlbSelectHeaderProperties.getTagSelectorKey(), tlbDiscoveryProperties.getDefaultServiceTagSelector());
//                }
                if (StringUtils.isNotBlank(tlbRequestHeader.getChain())) {
                    request.getHeaders().set(tlbSelectHeaderProperties.getChainKey(), tlbRequestHeader.getChain());
                }
            }
        }
        ClientHttpResponse ret = null;
        try {
            ret = execution.execute(request, body);
        } finally {
            if (ret != null && tlbDiscoveryProperties.isEnabled()) {
                List<String> chainHeader = ret.getHeaders().get(tlbSelectHeaderProperties.getChainKey());
                log.debug("chainHeader={}", chainHeader);
                if (chainHeader != null && !chainHeader.isEmpty()) {
                    TlbRequestContextHolder.updateChain(chainHeader.get(0));
                }
            }
        }
        return ret;
    }
}