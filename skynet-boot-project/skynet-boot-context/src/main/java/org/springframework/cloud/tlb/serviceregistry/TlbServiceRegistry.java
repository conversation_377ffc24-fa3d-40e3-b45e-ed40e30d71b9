package org.springframework.cloud.tlb.serviceregistry;

import jakarta.annotation.PreDestroy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryProperties;
import org.springframework.util.StringUtils;
import skynet.boot.SkynetProperties;
import skynet.boot.tlb.TlbClientRegister;

import java.io.Closeable;
import java.util.HashMap;
import java.util.Map;

/**
 * tlb 服务发现自动注册、此方法一般http服务
 */
@Slf4j
public class TlbServiceRegistry implements SmartInitializingSingleton, Closeable {

    public final TlbClientRegister tlbClientRegister;
    public final TlbDiscoveryProperties tlbDiscoveryProperties;

    public final SkynetProperties skynetProperties;

    public TlbServiceRegistry(TlbClientRegister tlbClientRegister, TlbDiscoveryProperties tlbDiscoveryProperties, SkynetProperties skynetProperties) {
        this.tlbClientRegister = tlbClientRegister;
        this.tlbDiscoveryProperties = tlbDiscoveryProperties;
        this.skynetProperties = skynetProperties;
    }

    @Override
    public void afterSingletonsInstantiated() {
        if (tlbDiscoveryProperties.isRegister()) {
            log.debug("TlbServiceRegistry...");
            //serviceId = 默认为 getActionId+IpAddress+port
            String serviceId = StringUtils.hasText(tlbDiscoveryProperties.getServiceId()) ? tlbDiscoveryProperties.getServiceId() :
                    String.format("%s_%s_%s", skynetProperties.getActionId(), skynetProperties.getIpAddress(), skynetProperties.getPort());
            String serviceName = StringUtils.hasText(tlbDiscoveryProperties.getServiceName()) ? tlbDiscoveryProperties.getServiceName() : skynetProperties.getActionCode();
            String ip = StringUtils.hasText(tlbDiscoveryProperties.getServiceHost()) ? tlbDiscoveryProperties.getServiceHost() : skynetProperties.getIpAddress();
            int port = tlbDiscoveryProperties.getServicePort() > 0 ? tlbDiscoveryProperties.getServicePort() : skynetProperties.getPort();
            int maxLic = tlbDiscoveryProperties.getMaxLic() > 0 ? tlbDiscoveryProperties.getMaxLic() : Runtime.getRuntime().availableProcessors();
            log.debug("TlbServiceRegistry serviceId={};serviceName={} ...", serviceId, serviceName);
            Map<String, String> metadata = new HashMap<>(tlbDiscoveryProperties.getMetadata());
            metadata.put("register.from", TlbDiscoveryProperties.PREFIX);
            tlbClientRegister.initialize(serviceId, serviceName, ip, port, maxLic, metadata);
        }
    }

    @SneakyThrows
    @Override
    @PreDestroy
    public void close() {
        tlbClientRegister.close();
    }

}
