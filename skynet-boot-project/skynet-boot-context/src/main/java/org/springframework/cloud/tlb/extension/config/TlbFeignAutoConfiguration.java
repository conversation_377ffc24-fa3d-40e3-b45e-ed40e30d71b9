package org.springframework.cloud.tlb.extension.config;

import feign.Feign;
import feign.RequestInterceptor;
import feign.ResponseInterceptor;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.tlb.discovery.ConditionalOnTlbDiscoveryEnabled;
import org.springframework.cloud.tlb.discovery.TlbDiscoveryAutoConfiguration;
import org.springframework.cloud.tlb.extension.TlbFeignRequestInterceptor;
import org.springframework.cloud.tlb.extension.TlbFeignResponseInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.boot.annotation.EnableSkynetTlbExtension;
import skynet.boot.tlb.config.TlbSelectHeaderProperties;

/**
 * Feign拦截器配置类
 *
 * @author: leitong
 * @date: 2023/5/17 21:09
 * @description:
 **/
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({Feign.class, feign.RequestInterceptor.class, feign.ResponseInterceptor.class})
@ConditionalOnTlbDiscoveryEnabled
@ConditionalOnBean(annotation = EnableSkynetTlbExtension.class, value = TlbSelectHeaderProperties.class)
@AutoConfigureAfter(TlbDiscoveryAutoConfiguration.class)
public class TlbFeignAutoConfiguration {

    @Bean
    public RequestInterceptor requestInterceptor(TlbSelectHeaderProperties properties) {
        return new TlbFeignRequestInterceptor(properties);
    }

    @Bean
    public ResponseInterceptor responseInterceptor(TlbSelectHeaderProperties properties) {
        return new TlbFeignResponseInterceptor(properties);
    }

}