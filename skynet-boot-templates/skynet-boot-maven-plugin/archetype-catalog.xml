<?xml version="1.0" encoding="UTF-8"?>
<archetype-catalog xsi:schemaLocation="http://maven.apache.org/plugins/maven-archetype-plugin/archetype-catalog/1.0.0 http://maven.apache.org/xsd/archetype-catalog-1.0.0.xsd"
    xmlns="http://maven.apache.org/plugins/maven-archetype-plugin/archetype-catalog/1.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <archetypes>
    <archetype>
      <groupId>com.iflytek.skynet</groupId>
      <artifactId>skynet-boot-template-mongo</artifactId>
      <version>4.3.3-SNAPSHOT</version>
      <description>Archetype for creating Skynet Boot application with Mongo</description>
	  <repository>https://depend.iflytek.com/artifactory/mvn-repo/</repository>
    </archetype>
    <archetype>
      <groupId>com.iflytek.skynet</groupId>
      <artifactId>skynet-boot-template-mysql</artifactId>
      <version>4.3.3-SNAPSHOT</version>
      <description>Archetype for creating Skynet Boot application with MySQL</description>
	  <repository>https://depend.iflytek.com/artifactory/mvn-repo/</repository>
    </archetype>
  </archetypes>
</archetype-catalog>
