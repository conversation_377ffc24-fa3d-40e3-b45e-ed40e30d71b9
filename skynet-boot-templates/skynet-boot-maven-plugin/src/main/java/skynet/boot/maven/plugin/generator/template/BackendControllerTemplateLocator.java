package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;
import skynet.boot.maven.plugin.generator.utils.StringUtils;

/**
 * Controller.ftl
 */
public class BackendControllerTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return true;
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "backend",
                "Controller.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getModuleName(),
                "src", "main", "java",
                genConfig.getPack().replace(".", "/"),
                "web", "rest",
                StringUtils.toCapitalizeCamelCase(domainInfo.getDomainEnName()) + "Controller.java"
        );
        return new TemplateLocation(key, value);
    }

}
