package skynet.boot.maven.plugin.generator.template;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TemplateLocation {

    public TemplateLocation(String templatePath, String targetPath) {
        this(templatePath, targetPath, false);
    }

    public TemplateLocation(String templatePath, String targetPath, boolean canOverwrite) {
        this.templatePath = templatePath;
        this.targetPath = targetPath;
        this.canOverwrite = canOverwrite;
    }

    /**
     * 模板路径
     */
    private String templatePath;

    /**
     * 生成文件路径
     */
    private String targetPath;

    /**
     * 目标文件是否能覆盖重新生成
     */
    private boolean canOverwrite;
}
