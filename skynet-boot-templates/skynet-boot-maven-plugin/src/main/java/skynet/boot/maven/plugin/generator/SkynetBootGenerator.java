package skynet.boot.maven.plugin.generator;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.maven.plugin.logging.Log;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ClassUtil;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;
import skynet.boot.maven.plugin.generator.model.GeneratorDTO;
import skynet.boot.maven.plugin.generator.template.TemplateFactory;
import skynet.boot.maven.plugin.generator.template.TemplateLocation;
import skynet.boot.maven.plugin.generator.utils.freemarker.CamelCase;
import skynet.boot.maven.plugin.generator.utils.freemarker.CapitalizeCamelCase;
import skynet.boot.maven.plugin.generator.utils.freemarker.Pluralize;

public class SkynetBootGenerator {

    private TemplateFactory templateFactory;
    private Configuration freemarkerConfig;
    private Log log;

    public SkynetBootGenerator(Log log) {
        this.templateFactory = new TemplateFactory();
        this.freemarkerConfig = new Configuration(Configuration.VERSION_2_3_31);
        this.freemarkerConfig.setLocalizedLookup(false);
        this.freemarkerConfig.setDefaultEncoding("utf-8");
        this.freemarkerConfig.setTemplateLoader(new ClassTemplateLoader(ClassUtil.getClassLoader(), "template"));
        this.freemarkerConfig.setSharedVariable("camelCase", new CamelCase());
        this.freemarkerConfig.setSharedVariable("capitalizeCamelCase", new CapitalizeCamelCase());
        this.freemarkerConfig.setSharedVariable("pluralize", new Pluralize());
        this.log = log;
    }

    /**
     * 生成代码
     *
     * @param generatorDTO 代码生成配置
     */
    public void generateCode(GeneratorDTO generatorDTO) throws Exception {

        GenConfig genConfig = generatorDTO.getGenConfig();
        for (DomainInfo domainInfo : generatorDTO.getDomainInfos()) {

            // 组装参数
            Map<String, Object> templateParamMap = new HashMap<>();
            templateParamMap.put("genConfig", genConfig);
            templateParamMap.put("domainInfo", domainInfo);

            // 获取模板列表，并生成模板文件
            List<TemplateLocation> templateLocations = templateFactory.getAllTemplateLocations(genConfig, domainInfo);
            for (TemplateLocation templateLocation : templateLocations) {
                generateTemplate(templateLocation, templateParamMap, generatorDTO);
            }
        }
    }

    /**
     * 生成模板文件
     */
    private void generateTemplate(TemplateLocation templateLocation, Map<String, Object> templateParamMap, GeneratorDTO generatorDTO) throws Exception {
        File generateFile = new File(templateLocation.getTargetPath());

        // 文件已存在，是否强制生成
        if (generateFile.exists() && !templateLocation.isCanOverwrite()) {
            if (generatorDTO.getGenConfig().getCanOverwrite() != null && generatorDTO.getGenConfig().getCanOverwrite()) {
                log.info(String.format("File %s exists, overwrite.", generateFile.getName()));
            } else {
                log.info(String.format("File %s exists, ignore.", generateFile.getName()));
                return;
            }
        }

        FileUtil.touch(generateFile);
        Template template = freemarkerConfig.getTemplate(templateLocation.getTemplatePath());
        try (Writer writer = new BufferedWriter(
                new OutputStreamWriter(
                        new FileOutputStream(generateFile), "UTF-8"))) {
            template.process(templateParamMap, writer);
        }
    }
}
