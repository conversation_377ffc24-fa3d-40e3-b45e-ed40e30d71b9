package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * 模板文件定位器
 */
public interface TemplateLocator {

    /**
     * 根据生成器参数决定是否要生成该模板文件
     *
     * @param genConfig  生成器配置
     * @param domainInfo 领域模型配置
     * @return
     */
    boolean match(GenConfig genConfig, DomainInfo domainInfo);

    /**
     * 获取模板文件位置信息
     */
    TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo);
}
