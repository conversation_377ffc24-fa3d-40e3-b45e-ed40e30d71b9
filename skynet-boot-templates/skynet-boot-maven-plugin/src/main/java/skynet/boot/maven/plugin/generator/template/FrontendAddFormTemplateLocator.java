package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.utils.StringUtils;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * addForm.ftl
 */
public class FrontendAddFormTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return StringUtils.isNotBlank(genConfig.getUiModuleName()) && domainInfo.getPageInfo() != null;
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "front",
                "addForm.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getUiModuleName(),
                "src",
                "views",
                StringUtils.toCamelCase(domainInfo.getDomainEnName()),
                "form.vue"
        );
        return new TemplateLocation(key, value);
    }

}
