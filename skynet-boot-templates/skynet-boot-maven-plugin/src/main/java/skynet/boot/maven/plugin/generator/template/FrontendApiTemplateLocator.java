package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.utils.StringUtils;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * api.ftl
 */
public class FrontendApiTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return StringUtils.isNotBlank(genConfig.getUiModuleName());
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "front",
                "api.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getUiModuleName(),
                "src",
                "axios", "api",
                StringUtils.toCamelCase(domainInfo.getDomainEnName()) + ".js"
        );
        return new TemplateLocation(key, value);
    }

}
