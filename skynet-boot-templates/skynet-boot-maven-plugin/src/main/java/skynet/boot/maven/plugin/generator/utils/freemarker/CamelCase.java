package skynet.boot.maven.plugin.generator.utils.freemarker;

import java.util.List;

import freemarker.template.SimpleScalar;
import freemarker.template.TemplateMethodModelEx;
import freemarker.template.TemplateModelException;
import skynet.boot.maven.plugin.generator.utils.StringUtils;

/**
 * "hello_world" => "helloWorld"
 */
public class CamelCase implements TemplateMethodModelEx {

    @Override
    public Object exec(@SuppressWarnings("rawtypes") List args) throws TemplateModelException {
        if (args == null || args.isEmpty() || args.get(0) == null) {
            return "";
        }
        SimpleScalar simpleScalar = (SimpleScalar) args.get(0);
        return StringUtils.toCamelCase(simpleScalar.getAsString());
    }
}
