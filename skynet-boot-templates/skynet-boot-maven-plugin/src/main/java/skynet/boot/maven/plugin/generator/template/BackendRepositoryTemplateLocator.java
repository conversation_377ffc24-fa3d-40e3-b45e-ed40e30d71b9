package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.utils.StringUtils;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * Repository.ftl
 */
public class BackendRepositoryTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return true;
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "backend",
                "Repository.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getModuleName(),
                "src", "main", "java",
                genConfig.getPack().replace(".", "/"),
                "repository",
                StringUtils.toCapitalizeCamelCase(domainInfo.getDomainEnName()) + "Repository.java"
        );
        return new TemplateLocation(key, value);
    }

}
