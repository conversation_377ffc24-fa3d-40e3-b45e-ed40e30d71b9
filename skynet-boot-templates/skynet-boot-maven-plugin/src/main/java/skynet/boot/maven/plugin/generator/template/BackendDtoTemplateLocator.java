package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.utils.StringUtils;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * Dto.ftl
 */
public class BackendDtoTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return true;
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "backend",
                "Dto.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getModuleName(),
                "src", "main", "java",
                genConfig.getPack().replace(".", "/"),
                "service", "dto",
                StringUtils.toCapitalizeCamelCase(domainInfo.getDomainEnName()) + "DTO.java"
        );
        return new TemplateLocation(key, value);
    }

}
