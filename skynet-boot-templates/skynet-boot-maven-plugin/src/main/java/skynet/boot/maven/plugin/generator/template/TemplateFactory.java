package skynet.boot.maven.plugin.generator.template;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

public class TemplateFactory {

    private List<TemplateLocator> templateLocators = Arrays.asList(
            new BackendControllerTemplateLocator(),
            new BackendCriteriaTemplateLocator(),
            new BackendDtoTemplateLocator(),
            new BackendGeneratedControllerTemplateLocator(),
            new BackendGeneratedCriteriaTemplateLocator(),
            new BackendGeneratedDtoTemplateLocator(),
            new BackendGeneratedEntityTemplateLocator(),
            new BackendGeneratedMapperTemplateLocator(),
            new BackendGeneratedPatchDtoTemplateLocator(),
            new BackendGeneratedRepositoryTemplateLocator(),
            new BackendGeneratedServiceImplTemplateLocator(),
            new BackendGeneratedServiceTemplateLocator(),
            new BackendLiquibaseTemplateLocator(),
            new BackendPatchDtoTemplateLocator(),
            new BackendRepositoryTemplateLocator(),
            new BackendServiceImplTemplateLocator(),
            new BackendServiceTemplateLocator(),
            new FrontendAddFormTemplateLocator(),
            new FrontendApiTemplateLocator(),
            new FrontendIndexTemplateLocator(),
            new FrontendRouterTemplateLocator()
    );

    /**
     * 根据生成器配置获取所有待生成的模板路径
     */
    public List<TemplateLocation> getAllTemplateLocations(GenConfig genConfig, DomainInfo domainInfo) {
        List<TemplateLocation> templates = new ArrayList<>();
        for (TemplateLocator templateLocator : templateLocators) {
            if (templateLocator.match(genConfig, domainInfo)) {
                templates.add(templateLocator.getLocation(genConfig, domainInfo));
            }
        }
        return templates;
    }
}
