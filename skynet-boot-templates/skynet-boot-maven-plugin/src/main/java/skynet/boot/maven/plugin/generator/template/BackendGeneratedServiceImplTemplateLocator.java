package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.utils.StringUtils;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * GeneratedServiceImpl.ftl
 */
public class BackendGeneratedServiceImplTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return true;
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "backend",
                genConfig.getDbType(),
                "GeneratedServiceImpl.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getModuleName(),
                "src", "main", "java",
                genConfig.getPack().replace(".", "/"),
                "generated", "service", "impl",
                "Generated" + StringUtils.toCapitalizeCamelCase(domainInfo.getDomainEnName()) + "ServiceImpl.java"
        );
        return new TemplateLocation(key, value, true);
    }

}
