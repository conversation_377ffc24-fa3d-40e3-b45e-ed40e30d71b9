package skynet.boot.maven.plugin;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.lang3.StringUtils;
import org.apache.maven.plugin.AbstractMojo;
import org.apache.maven.plugin.MojoExecutionException;
import org.apache.maven.plugins.annotations.Mojo;
import org.apache.maven.plugins.annotations.Parameter;

import cn.hutool.core.io.FileUtil;
import skynet.boot.maven.plugin.generator.SkynetBootGenerator;
import skynet.boot.maven.plugin.generator.model.DBType;
import skynet.boot.maven.plugin.generator.model.GenConfig;
import skynet.boot.maven.plugin.generator.model.GeneratorDTO;

/**
 * Skynet 代码生成插件
 */
@Mojo(name = "generate")
public class GeneratorMojo extends AbstractMojo {
    /**
     * 代码生成配置文件，可以是文件或目录，如果是目录，自动遍历目录下所有文件
     */
    @Parameter(property = "generate.configFile", defaultValue = ".generator")
    private String configFile;

    /**
     * 是否覆盖已生成的用户代码
     */
    @Parameter(property = "generate.canOverwrite", defaultValue = "false")
    private Boolean canOverwrite;

    /**
     * 后端代码生成到哪个模块下，默认是当前目录
     */
    @Parameter(property = "generate.moduleName", defaultValue = ".")
    private String moduleName;

    /**
     * 后端代码生成到哪个包下
     */
    @Parameter(property = "generate.packageName")
    private String packageName;

    /**
     * 生成代码使用的数据库类型
     */
    @Parameter(property = "generate.dbType")
    private String dbType;

    /**
     * 前端代码生成到哪个模块下，默认为空，表示不生成
     */
    @Parameter(property = "generate.uiModuleName")
    private String uiModuleName;

    /**
     * 插件入口
     */
    public void execute() throws MojoExecutionException {
        getLog().info("Generating code using config file: " + configFile);

        File file = new File(configFile);
        if (!file.exists()) {
            getLog().info("Generating code ignored: config file not exists.");
            return;
        }

        if (file.isDirectory()) {
            // config file 是目录，遍历目录下所有文件
            File[] files = file.listFiles();
            if (files != null) {
                for (File child : files) {
                    generateFile(child);
                }
            }
        } else {
            // config file 是文件
            generateFile(file);
        }

        getLog().info("Generating code done!");
    }

    /**
     * 根据某个配置文件生成代码
     *
     * @param file
     */
    private void generateFile(File file) throws MojoExecutionException {
        try {
            getLog().info("Generating ... " + file.getName());
            GeneratorDTO generatorDTO = formatGeneratorDTO(file);
            if (generatorDTO != null) {
                SkynetBootGenerator generator = new SkynetBootGenerator(getLog());
                generator.generateCode(generatorDTO);
            }
        } catch (Exception e) {
            throw new MojoExecutionException("Generate error: " + file.getName(), e);
        }
    }

    /**
     * 读取代码生成配置
     *
     * @param file
     * @return
     */
    private GeneratorDTO formatGeneratorDTO(File file) throws MojoExecutionException {
        String configContent = FileUtil.readString(file, StandardCharsets.UTF_8);
        GeneratorDTO generatorDTO = JSON.parseObject(configContent, GeneratorDTO.class);
        if (generatorDTO.getGenConfig() == null) {
            generatorDTO.setGenConfig(new GenConfig());
        }
        GenConfig genConfig = generatorDTO.getGenConfig();
        formatGenConfig(genConfig);

        // 根据 dbType 设置主键类型
        if ( Arrays.asList(DBType.MYSQL, DBType.DM).stream()
                .anyMatch(s->s.equals(genConfig.getDbType()))) {
            generatorDTO.getDomainInfos().forEach(domain -> domain.setPkColumnType("Long"));
        } else if (DBType.MONGODB.equals(genConfig.getDbType())) {
            generatorDTO.getDomainInfos().forEach(domain -> domain.setPkColumnType("String"));
        } else {
            throw new MojoExecutionException("DbType not supported: " + genConfig.getDbType());
        }
        return generatorDTO;
    }

    /**
     * 优先使用配置文件中的配置，如果配置文件中没有配置，则使用插件配置。
     * 插件配置可以通过 <configuration> 设置：
     *
     * <plugin>
     * <groupId>com.iflytek.skynet</groupId>
     * <artifactId>skynet-boot-maven-plugin</artifactId>
     * <version>${parent.version}</version>
     * <configuration>
     * <moduleName>.</moduleName>
     * <packageName>com.example.demo</packageName>
     * <dbType>mongodb</dbType>
     * <uiModuleName></uiModuleName>
     * </configuration>
     * </plugin>
     * <p>
     * 或通过命令行参数：
     * <p>
     * mvn skynet-boot:generate \
     * -Dgenerate.moduleName=. \
     * -Dgenerate.packageName=com.example.demo \
     * -Dgenerate.dbType=mongo \
     * -Dgenerate.uiModuleName=
     *
     * @param genConfig
     */
    private void formatGenConfig(GenConfig genConfig) {
        if (StringUtils.isBlank(genConfig.getModuleName())) {
            genConfig.setModuleName(moduleName);
        }
        if (StringUtils.isBlank(genConfig.getPack())) {
            genConfig.setPack(packageName);
        }
        if (StringUtils.isBlank(genConfig.getDbType())) {
            genConfig.setDbType(dbType);
        }
        if (StringUtils.isBlank(genConfig.getUiModuleName())) {
            genConfig.setUiModuleName(uiModuleName);
        }
        if (genConfig.getCanOverwrite() == null) {
            genConfig.setCanOverwrite(canOverwrite);
        }
    }
}