package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.model.DBType;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;
import skynet.boot.maven.plugin.generator.utils.StringUtils;

import java.util.Arrays;

/**
 * Liquibase.ftl
 */
public class BackendLiquibaseTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return Arrays.asList(DBType.MYSQL, DBType.DM).stream()
                .anyMatch(s->s.equals(genConfig.getDbType()));
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "backend",
                genConfig.getDbType(),
                "Liquibase.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getModuleName(),
                "src", "main", "resources", "liquibase", "changelog", genConfig.getDbType(),
                domainInfo.getDomainEnName() + ".sql"
        );
        return new TemplateLocation(key, value, true);
    }

}
