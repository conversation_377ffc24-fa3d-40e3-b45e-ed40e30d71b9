package skynet.boot.maven.plugin.generator.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import skynet.boot.maven.plugin.generator.utils.ColUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

/**
 * 表的数据信息
 *
 * <AUTHOR> Bin
 * @date 2019-11-20
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DomainInfo {

    private static final List<String> DOMAIN_API_LIST = Arrays.asList(
            "insert", "delete", "update", "patch", "listPage", "listUnPage", "detail", "copy"
    );

    /**
     * 表英文名称
     **/
    private String domainEnName;

    /**
     * 表中文名称
     **/
    private String domainCnName;

    /**
     * 不生成某个接口，默认值为空，表示不限制
     */
    private String domainApiExclude = "";

    /**
     * 只生成某个接口，默认值为 *，表示生成所有接口
     */
    private String domainApiInclude = "*";

    /**
     * 导出配置
     */
    private ExportInfo exportInfo;

    /**
     * 页面配置信息
     */
    private PageInfo pageInfo;

    /**
     * 列信息
     */
    private List<ColumnInfo> columnInfos;

    /**
     * 系统列信息
     */
    private List<ColumnInfo> systemColumnInfos = new ArrayList<>();

    /**
     * 主键列类型
     */
    private String pkColumnType = "String";

    /**
     * 主键列名称
     */
    private String pkColumnEnName = "id";

    /**
     * 判断是否含有 filterable = true 的字段
     *
     * @return
     */
    public boolean hasFilter() {
        return this.columnInfos.stream().anyMatch(ColumnInfo::isFilterable);
    }

    /**
     * 判断是否含有 patchable = true 的字段
     *
     * @return
     */
    public boolean hasPatch() {
        return this.columnInfos.stream().anyMatch(ColumnInfo::isPatchable);
    }

    /**
     * 判断是否含有 uploadable = true 的字段
     *
     * @return
     */
    public boolean hasUploadable() {
        return this.columnInfos.stream().anyMatch(ColumnInfo::isUploadable);
    }

    /**
     * 判断是否含有 Instant 类型的字段
     *
     * @return
     */
    public boolean hasInstant() {
        return this.columnInfos.stream().anyMatch(c -> "Instant".equals(ColUtil.cloToJava(c.getColumnType())));
    }

    /**
     * 判断是否含有 BigDecimal 类型的字段
     *
     * @return
     */
    public boolean hasBigDecimal() {
        return this.columnInfos.stream().anyMatch(c -> "BigDecimal".equals(ColUtil.cloToJava(c.getColumnType())));
    }

    /**
     * 判断是否含有 JSONObject 类型的字段
     *
     * @return
     */
    public boolean hasJson() {
        return this.columnInfos.stream().anyMatch(c -> "JSONObject".equals(ColUtil.cloToJava(c.getColumnType())));
    }

    /**
     * 判断是否含有 JSONArray 类型的字段
     *
     * @return
     */
    public boolean hasArray() {
        return this.columnInfos.stream().anyMatch(c -> "JSONArray".equals(ColUtil.cloToJava(c.getColumnType())));
    }

    /**
     * 获取要生成的 API 清单
     *
     * @return
     */
    public Map<String, Boolean> getDomainApiMap() {

        List<String> includes = getDomainApis(domainApiInclude);
        List<String> excludes = getDomainApis(domainApiExclude);
        includes.removeAll(excludes);

        Map<String, Boolean> result = new HashMap<>();
        for (String api : DOMAIN_API_LIST) {
            result.put(api, includes.contains(api));
        }
        return result;
    }

    /**
     * 将逗号分隔的 API 清单转换为列表，支持通配符 * 表示所有
     *
     * @param domainApi
     * @return
     */
    private List<String> getDomainApis(String domainApi) {
        List<String> apis = new ArrayList<>();
        if (StringUtils.isNotBlank(domainApi)) {
            if ("*".equals(domainApi)) {
                apis.addAll(DOMAIN_API_LIST);
            } else {
                apis.addAll(Arrays.stream(domainApi.split(",")).map(String::trim).collect(Collectors.toList()));
            }
        }
        return apis;
    }

    @Getter
    @Setter
    public static class ExportInfo {
        private boolean exportable = false;
        private String exportType = "";
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PageInfo {

        /**
         * 页面名称
         **/
        private String pageTitle = "";

        /**
         * 列表形式（list 或 card）
         */

        private String listType = "list";

        /**
         * 菜单图标
         */
        private String icon = "";

        /**
         * 菜单排序
         */
        private Integer order;

        /**
         * 操作按钮
         */
        private OperationInfo operationInfo = new OperationInfo();

        /**
         * 父页面配置信息
         */
        private ParentInfo parentInfo;

        @Getter
        @Setter
        public static class OperationInfo {
            private boolean addable = true;
            private boolean editable = true;
            private boolean deletable = true;
            private boolean copyable = false;
            private List<PageButton> extendButtons = new ArrayList<>();
        }

        @Getter
        @Setter
        public static class ParentInfo {
            private String foreignKey;
            private String domain;
            private String pk = "id";
        }

        @Getter
        @Setter
        public static class PageButton {
            private String name;

            /**
             * 子页面管理按钮
             */
            private String domain;

            /**
             * 状态管理按钮
             */
            private String column;
            private String current;
            private String next;
            private String icon;
        }
    }
}
