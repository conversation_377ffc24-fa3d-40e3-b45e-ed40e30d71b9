package skynet.boot.maven.plugin.generator.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GenConfig {

    private Long id;

    /**
     * 包路径
     **/
    private String pack;

    /**
     * 模块名
     **/
    private String moduleName;

    /**
     * 前端模块名
     **/
    private String uiModuleName;


    /**
     * 作者
     **/
    private String author;

    /**
     * 表前缀
     **/
    private String prefix;

    /**
     * 数据库类型  mysql|mongodb
     **/
    private String dbType;

    /**
     * 是否覆盖已生成的用户代码
     */
    private Boolean canOverwrite;

    /**
     * common package
     *
     * @return
     */
    public String getCommonPackage() {
        return pack.substring(0, pack.lastIndexOf("."));
    }
}
