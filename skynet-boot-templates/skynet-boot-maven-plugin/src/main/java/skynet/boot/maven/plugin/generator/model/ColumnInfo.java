package skynet.boot.maven.plugin.generator.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import skynet.boot.maven.plugin.generator.utils.ColUtil;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 数据库定义
 *
 * <AUTHOR> Bin
 * @date 2019-11-20
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ColumnInfo {

    /**
     * 系统内置字段
     */
    private static List<String> SYSTEM_FIELDS = Arrays.asList(
            "id", "created_by", "created_date", "last_modified_by", "last_modified_date", "tenant_id");

    /**
     * 数据库字段中文名称
     **/
    private String columnCnName = "";

    /**
     * 数据库字段英文名称
     **/
    private String columnEnName = "";

    /**
     * 数据库字段类型
     **/
    private String columnType = "";

    /**
     * 数据库字段长度
     **/
    private Integer columnLength = 100;

    /**
     * 数据库字段注释
     **/
    private String columnComment = "";

    /**
     * 数据库字段索引类型 PRI-主键
     **/
    private String indexType = "";

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 允许空值
     **/
    private boolean nullable = true;

    /**
     * 允许过滤条件
     **/
    private boolean filterable = false;

    /**
     * 允许部分更新
     **/
    private boolean patchable = false;

    /**
     * 是否为上传类字段
     */
    private boolean uploadable = false;

    /**
     * 是否唯一
     */
    private boolean unique = false;

    /**
     * 字段关联其他表
     */
    private ColumnRefer refer;

    /**
     * 页面配置信息
     */
    private PageInfo pageInfo;

    /**
     * 校验规则
     **/
    private List<ValidateRule> validateRules = new ArrayList<>();

    public String getColumnComment() {
        return StringUtils.isEmpty(this.columnComment) ?
                this.columnCnName :
                this.columnCnName + "（" + this.columnComment + "）";
    }

    /**
     * 将 columnType 转换为 Java 类型
     *
     * @return
     */
    public String getColumnJavaType() {
        return ColUtil.cloToJava(this.columnType);
    }

    /**
     * 判断是否为系统内置字段
     *
     * @return
     */
    public boolean isSystemField() {
        return SYSTEM_FIELDS.contains(this.columnEnName);
    }

    @Getter
    @Setter
    public static class ColumnRefer {
        private String domain = "";
        private String pk = "id";
        private List<ColumnReferInfo> columns;
    }

    @Getter
    @Setter
    public static class ColumnReferInfo {
        private String name = "name";
        private String columnEnName = "";
        private String columnCnName = "";
    }

    @Getter
    @Setter
    public static class PageInfo {

        /**
         * 页面字段校验规则
         **/
        private List<ColumnRule> columnRules = new ArrayList<>();

        /**
         * 字段取值枚举
         */
        private List<ColumnEnums> columnEnums = new ArrayList<>();

        /**
         * 输入类型，如单行输入（text）、多行输入（textarea）、单选（radio）、多选（checkbox）等组件
         */
        private String inputType = "";

        /**
         * 是否显示在列表
         **/
        private boolean columnShowList = true;

        /**
         * 是否显示在创建表单
         **/
        private boolean columnShowAdd = true;

        /**
         * 该字段是否支持筛选
         */
        private boolean filterable = false;

        /**
         * 是否为上传类字段
         */
        private boolean uploadable = false;

        @Getter
        @Setter
        public static class ColumnRule {
            private String message = "";
            private String trigger = "blur";

            private Integer max;
            private Integer min;
            private Boolean required;
            private String regex;
        }

        @Getter
        @Setter
        public static class ColumnEnums {
            private String key = "";
            private String value = "";
            private String style = "";
        }
    }

    @Getter
    @Setter
    public static class ValidateRule {

        private String message;
        private String trigger = "blur";

        // 字符串   @Size(min = 3, max = 20)
        // 其他类型 @Max @Min
        private Integer max;
        private Integer min;

        // 字符串   @NotBlank
        // 其他类型 @NotNull
        private Boolean required;

        // 字符串 @Pattern(regexp = "xx")
        private String regex;
    }
}
