package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.utils.StringUtils;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * router.ftl
 */
public class FrontendRouterTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return StringUtils.isNotBlank(genConfig.getUiModuleName()) && domainInfo.getPageInfo() != null;
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "front",
                "router.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getUiModuleName(),
                "src",
                "router", "modules",
                StringUtils.toCamelCase(domainInfo.getDomainEnName()) + ".js"
        );
        return new TemplateLocation(key, value);
    }

}
