package skynet.boot.maven.plugin.generator.template;

import skynet.boot.maven.plugin.generator.utils.StringUtils;
import skynet.boot.maven.plugin.generator.model.DomainInfo;
import skynet.boot.maven.plugin.generator.model.GenConfig;

/**
 * index.ftl
 */
public class FrontendIndexTemplateLocator implements TemplateLocator {

    @Override
    public boolean match(GenConfig genConfig, DomainInfo domainInfo) {
        return StringUtils.isNotBlank(genConfig.getUiModuleName()) && domainInfo.getPageInfo() != null;
    }

    @Override
    public TemplateLocation getLocation(GenConfig genConfig, DomainInfo domainInfo) {
        String key = StringUtils.joinFilePath(
                "front",
                "index.ftl"
        );
        String value = StringUtils.joinFilePath(
                genConfig.getUiModuleName(),
                "src",
                "views",
                StringUtils.toCamelCase(domainInfo.getDomainEnName()),
                "index.vue"
        );
        return new TemplateLocation(key, value);
    }

}
