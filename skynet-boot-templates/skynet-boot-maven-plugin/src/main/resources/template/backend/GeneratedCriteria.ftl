package ${genConfig.pack}.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria implements Serializable, Criteria  {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if column.filterable>

    private ${column.columnJavaType}Filter ${camelCase(column.columnEnName)};
    </#if>
    </#list>
</#if>
<#if domainInfo.systemColumnInfos??>
    <#list domainInfo.systemColumnInfos as column>
    <#if column.filterable && column.isSystemField()>

    private ${column.columnJavaType}Filter ${camelCase(column.columnEnName)};
    </#if>
    </#list>
</#if>

    public Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria() {}

    public Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria(Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria other) {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if column.filterable>
        this.${camelCase(column.columnEnName)} = other.${camelCase(column.columnEnName)} == null ? null : other.${camelCase(column.columnEnName)}.copy();
    </#if>
    </#list>
</#if>
<#if domainInfo.systemColumnInfos??>
    <#list domainInfo.systemColumnInfos as column>
    <#if column.filterable && column.isSystemField()>
        this.${camelCase(column.columnEnName)} = other.${camelCase(column.columnEnName)} == null ? null : other.${camelCase(column.columnEnName)}.copy();
    </#if>
    </#list>
</#if>
    }

    @Override
    public Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria copy() {
        return new Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria(this);
    }
}
