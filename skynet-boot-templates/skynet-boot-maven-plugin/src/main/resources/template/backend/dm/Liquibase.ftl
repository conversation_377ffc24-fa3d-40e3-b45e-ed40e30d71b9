--liquibase formatted sql
--changeset skynet:create_table_${domainInfo.domainEnName}
CREATE TABLE ${domainInfo.domainEnName}( 
    id int NOT NULL AUTO_INCREMENT,
    <#list domainInfo.columnInfos as column>
    <#if column.columnType = 'varchar'>
    ${column.columnEnName} ${column.columnType}(${column.columnLength?c}) COMMENT '${column.columnComment}', 
    <#else>
    ${column.columnEnName} ${column.columnType} COMMENT '${column.columnComment}', 
    </#if>
    </#list>
    created_by varchar(100) COMMENT '创建人',
    created_date timestamp DEFAULT SYSDATE COMMENT '创建时间',
    last_modified_by varchar(100) COMMENT '更新人',
    last_modified_date timestamp DEFAULT SYSDATE COMMENT '更新时间',
    tenant_id varchar(100) COMMENT '租户',
    PRIMARY KEY (id)
);

CREATE INDEX idx_created_by ON ${domainInfo.domainEnName} (created_by);
CREATE INDEX idx_created_date ON ${domainInfo.domainEnName} (created_date);
CREATE INDEX idx_last_modified_by ON ${domainInfo.domainEnName} (last_modified_by);
CREATE INDEX idx_last_modified_date ON ${domainInfo.domainEnName} (last_modified_date);
CREATE INDEX idx_tenant_id ON ${domainInfo.domainEnName} (tenant_id);

<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if column.unique>
--changeset skynet:alter_table_add_unique_index_${column.columnEnName}
CREATE UNIQUE INDEX idx_${column.columnEnName} ON ${domainInfo.domainEnName} (${column.columnEnName});
    </#if>
    </#list>
</#if>
