package ${genConfig.pack}.generated.service.mapper;

import ${genConfig.pack}.generated.domain.Generated${capitalizeCamelCase(domainInfo.domainEnName)};
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}DTO;
import skynet.boot.common.mapper.EntityMapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;

@Mapper(
    componentModel = "spring",
    uses = {},
    builder = @Builder(disableBuilder = true))
public interface Generated${capitalizeCamelCase(domainInfo.domainEnName)}Mapper extends EntityMapper<${capitalizeCamelCase(domainInfo.domainEnName)}DTO, Generated${capitalizeCamelCase(domainInfo.domainEnName)}> {

}