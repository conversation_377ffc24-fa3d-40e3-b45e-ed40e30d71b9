package ${genConfig.pack}.generated.repository;

import ${genConfig.pack}.generated.domain.Generated${capitalizeCamelCase(domainInfo.domainEnName)};
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

@NoRepositoryBean
public interface Generated${capitalizeCamelCase(domainInfo.domainEnName)}Repository extends JpaRepository<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, ${domainInfo.pkColumnType}>, JpaSpecificationExecutor<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.unique>

    /**
     * findBy${capitalizeCamelCase(column.columnEnName)}
     *
     * @param ${camelCase(column.columnEnName)} ${column.columnCnName}
     * @return ${domainInfo.domainCnName}
     */
    Optional<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> findBy${capitalizeCamelCase(column.columnEnName)}(${column.columnJavaType} ${camelCase(column.columnEnName)});
        </#if>
    </#list>
</#if>
}