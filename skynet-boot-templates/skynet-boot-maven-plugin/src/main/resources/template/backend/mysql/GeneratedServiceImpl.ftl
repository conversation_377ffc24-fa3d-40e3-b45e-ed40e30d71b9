package ${genConfig.pack}.generated.service.impl;

import ${genConfig.pack}.generated.domain.*; // for static metamodels
import ${genConfig.pack}.repository.${capitalizeCamelCase(domainInfo.domainEnName)}Repository;
<#if domainInfo.columnInfos??>
<#assign seen_domains = []>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
<#if seen_domains?seq_contains(column.refer.domain)>
<#else>
<#assign seen_domains = seen_domains + [column.refer.domain]>
import ${genConfig.pack}.repository.${capitalizeCamelCase(column.refer.domain)}Repository;
</#if>
</#if>
</#list>
</#if>
import ${genConfig.pack}.generated.service.Generated${capitalizeCamelCase(domainInfo.domainEnName)}Service;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}Criteria;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}DTO;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO;
import ${genConfig.pack}.generated.service.mapper.Generated${capitalizeCamelCase(domainInfo.domainEnName)}Mapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl extends MysqlTemplateServiceImpl<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, ${capitalizeCamelCase(domainInfo.domainEnName)}DTO, ${domainInfo.pkColumnType}> implements Generated${capitalizeCamelCase(domainInfo.domainEnName)}Service {

    @Resource
    protected ${capitalizeCamelCase(domainInfo.domainEnName)}Repository ${camelCase(domainInfo.domainEnName)}Repository;

    @Resource
    protected Generated${capitalizeCamelCase(domainInfo.domainEnName)}Mapper ${camelCase(domainInfo.domainEnName)}Mapper;

<#if domainInfo.columnInfos??>
<#assign seen_domains = []>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
<#if seen_domains?seq_contains(column.refer.domain)>
<#else>
<#assign seen_domains = seen_domains + [column.refer.domain]>
    @Resource
    protected ${capitalizeCamelCase(column.refer.domain)}Repository ${camelCase(column.refer.domain)}Repository;
</#if>
</#if>
</#list>
</#if>

    public Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl(EntityMapper<${capitalizeCamelCase(domainInfo.domainEnName)}DTO, Generated${capitalizeCamelCase(domainInfo.domainEnName)}> entityMapper, JpaSpecificationExecutor<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> jpaSpecificationExecutor, JpaRepository<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, ${domainInfo.pkColumnType}> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllByCriteria(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllByCriteria(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> createSpecification(Criteria criteria) {
        ${capitalizeCamelCase(domainInfo.domainEnName)}Criteria ${camelCase(domainInfo.domainEnName)}Criteria = (${capitalizeCamelCase(domainInfo.domainEnName)}Criteria) criteria;
        Specification<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> specification = Specification.where(null);
        if (${camelCase(domainInfo.domainEnName)}Criteria != null) {
    <#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if column.filterable>
            if (${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}() != null) {
                <#if column.columnJavaType == 'String'>
                specification = specification.and(buildStringSpecification(${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}(), Generated${capitalizeCamelCase(domainInfo.domainEnName)}_.${camelCase(column.columnEnName)}));
                <#elseif column.columnJavaType == 'Boolean'>
                specification = specification.and(buildSpecification(${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}(), Generated${capitalizeCamelCase(domainInfo.domainEnName)}_.${camelCase(column.columnEnName)}));
                <#else>
                specification = specification.and(buildRangeSpecification(${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}(), Generated${capitalizeCamelCase(domainInfo.domainEnName)}_.${camelCase(column.columnEnName)}));
                </#if>
            }
    </#if>
    </#list>
    </#if>
    <#if domainInfo.systemColumnInfos??>
    <#list domainInfo.systemColumnInfos as column>
    <#if column.filterable && column.isSystemField()>
            if (${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}() != null) {
                <#if column.columnJavaType == 'String'>
                specification = specification.and(buildStringSpecification(${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}(), Generated${capitalizeCamelCase(domainInfo.domainEnName)}_.${camelCase(column.columnEnName)}));
                <#elseif column.columnJavaType == 'Boolean'>
                specification = specification.and(buildSpecification(${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}(), Generated${capitalizeCamelCase(domainInfo.domainEnName)}_.${camelCase(column.columnEnName)}));
                <#else>
                specification = specification.and(buildRangeSpecification(${camelCase(domainInfo.domainEnName)}Criteria.get${capitalizeCamelCase(column.columnEnName)}(), Generated${capitalizeCamelCase(domainInfo.domainEnName)}_.${camelCase(column.columnEnName)}));
                </#if>
            }
    </#if>
    </#list>
    </#if>
        }
        return specification;
    }

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO findById(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        ${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO = super.findById(id);
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
        convert${capitalizeCamelCase(column.refer.domain)}(${camelCase(domainInfo.domainEnName)}DTO);
</#if>
</#list>
</#if>
        return ${camelCase(domainInfo.domainEnName)}DTO;
    }
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>

    private void convert${capitalizeCamelCase(column.refer.domain)}(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
        if (StringUtils.isNotBlank(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}())) {
            ${camelCase(column.refer.domain)}Repository.findBy${capitalizeCamelCase(column.refer.pk)}(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}()).ifPresent(${camelCase(column.refer.domain)} -> {
<#list column.refer.columns as referColumn>
                ${camelCase(domainInfo.domainEnName)}DTO.set${capitalizeCamelCase(referColumn.columnEnName)}(${camelCase(column.refer.domain)}.get${capitalizeCamelCase(referColumn.name)}());
</#list>
            });
        }
    }
</#if>
</#list>
</#if>

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO save(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if column.unique>
        if (${camelCase(domainInfo.domainEnName)}Repository.findBy${capitalizeCamelCase(column.columnEnName)}(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}()).isPresent()) {
            throw new DuplicateNameException("${column.columnCnName}");
        }
    </#if>
    </#list>
</#if>
        return super.save(${camelCase(domainInfo.domainEnName)}DTO);
    }

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO update(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.unique>
        Optional<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> ${camelCase(domainInfo.domainEnName)}${column_index} = ${camelCase(domainInfo.domainEnName)}Repository.findBy${capitalizeCamelCase(column.columnEnName)}(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}());
        if (${camelCase(domainInfo.domainEnName)}${column_index}.isPresent() && !${camelCase(domainInfo.domainEnName)}${column_index}.get().get${capitalizeCamelCase(domainInfo.pkColumnEnName)}().equals(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(domainInfo.pkColumnEnName)}())) {
            throw new DuplicateNameException("${column.columnCnName}");
        }
        </#if>
    </#list>
</#if>

        return super.update(${camelCase(domainInfo.domainEnName)}DTO.getId(),${camelCase(domainInfo.domainEnName)}DTO);
    }

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO patch(${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO ${camelCase(domainInfo.domainEnName)}PatchDTO) {
        return super.patch(${camelCase(domainInfo.domainEnName)}PatchDTO.getId(), ${camelCase(domainInfo.domainEnName)}PatchDTO);
    }

    @Override
    public void delete(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        super.delete(${camelCase(domainInfo.pkColumnEnName)});
    }


    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO copy(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        List<String> renameFields = new ArrayList<>();
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.unique>
        renameFields.add("${camelCase(column.columnEnName)}");
        </#if>
    </#list>
</#if>
        return super.copy(id, renameFields, Generated${capitalizeCamelCase(domainInfo.domainEnName)}.class);
    }
}