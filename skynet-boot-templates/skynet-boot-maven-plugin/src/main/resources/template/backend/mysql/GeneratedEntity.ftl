package ${genConfig.pack}.generated.domain;

import skynet.boot.mysql.domain.AbstractAuditingEntity;
import skynet.boot.common.utils.IdUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

<#if domainInfo.hasInstant()>
import java.time.Instant;
</#if>
<#if domainInfo.hasBigDecimal()>
import java.math.BigDecimal;
</#if>
<#if domainInfo.hasArray()>
import com.alibaba.fastjson2.JSONArray;
</#if>
<#if domainInfo.hasJson()>
import com.alibaba.fastjson2.JSONObject;
</#if>

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;

/**
 * ${domainInfo.domainCnName}
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "${domainInfo.domainEnName}")
public class Generated${capitalizeCamelCase(domainInfo.domainEnName)} extends AbstractAuditingEntity<${domainInfo.pkColumnType}> {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if !column.isSystemField()>
    /**
     * ${column.columnComment}
     */
    @Column(name = "${column.columnEnName}"<#if column.unique>, unique = true</#if><#if !column.nullable && column.indexType != 'PRI'>, nullable = false</#if>)
    private ${column.columnJavaType} ${camelCase(column.columnEnName)};
    </#if>
    </#list>

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
<#if domainInfo.pkColumnType == 'Long'>
            this.setId(IdUtil.getSnowflakeNextId());
<#else>
            this.setId(IdUtil.getSnowflakeNextIdStr());
</#if>
        } 
    }

</#if>

}