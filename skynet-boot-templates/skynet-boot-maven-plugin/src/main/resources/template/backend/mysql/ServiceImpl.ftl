package ${genConfig.pack}.service.impl;

import ${genConfig.pack}.service.${capitalizeCamelCase(domainInfo.domainEnName)}Service;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}DTO;
import skynet.boot.common.mapper.EntityMapper;
import ${genConfig.pack}.generated.domain.Generated${capitalizeCamelCase(domainInfo.domainEnName)};
import ${genConfig.pack}.generated.service.impl.Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class ${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl extends Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl implements ${capitalizeCamelCase(domainInfo.domainEnName)}Service {

    public ${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl(EntityMapper<${capitalizeCamelCase(domainInfo.domainEnName)}DTO, Generated${capitalizeCamelCase(domainInfo.domainEnName)}> entityMapper, JpaSpecificationExecutor<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> jpaSpecificationExecutor, JpaRepository<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, ${domainInfo.pkColumnType}> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }
}