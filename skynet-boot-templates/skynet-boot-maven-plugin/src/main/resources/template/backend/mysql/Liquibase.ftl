--liquibase formatted sql
--changeset skynet:create_table_${domainInfo.domainEnName}
CREATE TABLE `${domainInfo.domainEnName}`( 
    `id` int NOT NULL AUTO_INCREMENT,
    <#list domainInfo.columnInfos as column>
    <#if column.columnType = 'varchar'>
    `${column.columnEnName}` ${column.columnType}(${column.columnLength?c}) COMMENT '${column.columnComment}', 
    <#else>
    `${column.columnEnName}` ${column.columnType} COMMENT '${column.columnComment}', 
    </#if>
    </#list>
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;

<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if column.unique>
--changeset skynet:alter_table_add_unique_index_${column.columnEnName}
ALTER TABLE `${domainInfo.domainEnName}` ADD UNIQUE `idx_${column.columnEnName}` (`${column.columnEnName}`);
    </#if>
    </#list>
</#if>
