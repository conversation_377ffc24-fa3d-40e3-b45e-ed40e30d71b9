package ${genConfig.pack}.service.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import ${genConfig.pack}.generated.service.dto.Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class ${capitalizeCamelCase(domainInfo.domainEnName)}Criteria extends Generated${capitalizeCamelCase(domainInfo.domainEnName)}Criteria {
    
}
