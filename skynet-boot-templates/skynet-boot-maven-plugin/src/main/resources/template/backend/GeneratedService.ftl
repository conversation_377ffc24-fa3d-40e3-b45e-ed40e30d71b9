package ${genConfig.pack}.generated.service;

import ${genConfig.pack}.generated.domain.Generated${capitalizeCamelCase(domainInfo.domainEnName)};
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}Criteria;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}DTO;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
<#if domainInfo.hasUploadable() || (domainInfo.exportInfo?? && domainInfo.exportInfo.exportable)>
import org.springframework.web.multipart.MultipartFile;
</#if>

import java.util.List;

public interface Generated${capitalizeCamelCase(domainInfo.domainEnName)}Service extends TemplateService<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, ${capitalizeCamelCase(domainInfo.domainEnName)}DTO, ${domainInfo.pkColumnType}> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllByCriteria(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Pageable pageable);
    Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllByCriteria(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Sort sort);
    List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param ${camelCase(domainInfo.pkColumnEnName)}
     * @return
     */
    ${capitalizeCamelCase(domainInfo.domainEnName)}DTO findById(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)});

    /**
     * 创建
     * @param ${camelCase(domainInfo.domainEnName)}DTO
     * @return
     */
    ${capitalizeCamelCase(domainInfo.domainEnName)}DTO save(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO);

    /**
     * 修改
     * @param ${camelCase(domainInfo.domainEnName)}DTO
     */
    ${capitalizeCamelCase(domainInfo.domainEnName)}DTO update(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO);

    /**
     * 更新
     * @param ${camelCase(domainInfo.domainEnName)}PatchDTO
     */
    ${capitalizeCamelCase(domainInfo.domainEnName)}DTO patch(${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO ${camelCase(domainInfo.domainEnName)}PatchDTO);

    /**
     * 删除单条
     * @param ${camelCase(domainInfo.pkColumnEnName)}
     */
    void delete(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)});

    /**
    * 复制
    * @param ${camelCase(domainInfo.pkColumnEnName)}
    */
    ${capitalizeCamelCase(domainInfo.domainEnName)}DTO copy(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)});
<#if domainInfo.hasUploadable()>

    /**
     * 上传
     */
    String upload(MultipartFile file);

    /**
     * 下载
     */
    void download(String filePath);
</#if>
<#if domainInfo.exportInfo?? && domainInfo.exportInfo.exportable>

    /**
     * 导出
     * @param ids
     */
    void exportAll(List<String> ids);

    /**
     * 导入
     * @param file
     */
    void importAll(MultipartFile file);
</#if>
}