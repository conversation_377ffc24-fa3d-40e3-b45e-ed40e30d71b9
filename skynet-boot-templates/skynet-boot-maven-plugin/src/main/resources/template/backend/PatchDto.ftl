package ${genConfig.pack}.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import ${genConfig.pack}.generated.service.dto.Generated${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@AllArgsConstructor
public class ${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO extends Generated${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO {

}