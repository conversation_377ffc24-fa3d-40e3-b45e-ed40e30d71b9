package ${genConfig.pack}.generated.controller;

import ${genConfig.pack}.service.${capitalizeCamelCase(domainInfo.domainEnName)}Service;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}Criteria;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}DTO;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
<#if domainInfo.hasUploadable() || (domainInfo.exportInfo?? && domainInfo.exportInfo.exportable)>
import org.springframework.web.multipart.MultipartFile;
</#if>

import jakarta.annotation.Resource;
import java.util.List;

/**
 * ${domainInfo.domainCnName}管理
 */
public class Generated${capitalizeCamelCase(domainInfo.domainEnName)}Controller {

    @Resource
    protected ${capitalizeCamelCase(domainInfo.domainEnName)}Service ${camelCase(domainInfo.domainEnName)}Service;
<#if domainInfo.domainApiMap["listPage"]>

    @Operation(summary = "分页查询${domainInfo.domainCnName}")
    @GetMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}")
    public Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllByPage(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Pageable pageable) {
        return ${camelCase(domainInfo.domainEnName)}Service.findAllByCriteria(criteria, pageable);
    }
</#if>
<#if domainInfo.domainApiMap["listUnPage"]>

    @Operation(summary = "不分页查询${domainInfo.domainCnName}")
    @GetMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/unPage")
    public List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllUnPage(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Sort sort) {
        return ${camelCase(domainInfo.domainEnName)}Service.findAllByCriteria(criteria, sort);
    }
</#if>
<#if domainInfo.domainApiMap["insert"]>

    @Operation(summary = "新增${domainInfo.domainCnName}")
    @PostMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}")
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO save(@Validated @RequestBody ${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
        return ${camelCase(domainInfo.domainEnName)}Service.save(${camelCase(domainInfo.domainEnName)}DTO);
    }
</#if>
<#if domainInfo.domainApiMap["update"]>

    @Operation(summary = "修改${domainInfo.domainCnName}")
    @PutMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}")
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO update(@Validated @RequestBody ${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
        return ${camelCase(domainInfo.domainEnName)}Service.update(${camelCase(domainInfo.domainEnName)}DTO);
    }
</#if>
<#if domainInfo.domainApiMap["patch"]>

    @Operation(summary = "更新${domainInfo.domainCnName}")
    @PatchMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}")
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO patch(@Validated @RequestBody ${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO ${camelCase(domainInfo.domainEnName)}PatchDTO) {
        return ${camelCase(domainInfo.domainEnName)}Service.patch(${camelCase(domainInfo.domainEnName)}PatchDTO);
    }
</#if>
<#if domainInfo.domainApiMap["detail"]>

    @Operation(summary = "查找${domainInfo.domainCnName}")
    @GetMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/{${camelCase(domainInfo.pkColumnEnName)}}")
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO get(@PathVariable ${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        return ${camelCase(domainInfo.domainEnName)}Service.findById(${camelCase(domainInfo.pkColumnEnName)});
    }
</#if>
<#if domainInfo.domainApiMap["delete"]>

    @Operation(summary = "删除${domainInfo.domainCnName}")
    @DeleteMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/{${camelCase(domainInfo.pkColumnEnName)}}")
    public void delete(@PathVariable ${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        ${camelCase(domainInfo.domainEnName)}Service.delete(${camelCase(domainInfo.pkColumnEnName)});
    }
</#if>
<#if domainInfo.domainApiMap["copy"]>

    @Operation(summary = "复制${domainInfo.domainCnName}")
    @PostMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/{id}/copy")
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO copy(@PathVariable ${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        return ${camelCase(domainInfo.domainEnName)}Service.copy(${camelCase(domainInfo.pkColumnEnName)});
    }
</#if>
<#if domainInfo.hasUploadable()>

    @Operation(summary = "上传${domainInfo.domainCnName}")
    @PostMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/upload")
    public String upload(MultipartFile file) {
        return ${camelCase(domainInfo.domainEnName)}Service.upload(file);
    }

    @Operation(summary = "下载${domainInfo.domainCnName}")
    @GetMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/download")
    public void download(@RequestParam(required = true, name = "filePath") String filePath) {
        ${camelCase(domainInfo.domainEnName)}Service.download(filePath);
    }
</#if>
<#if domainInfo.exportInfo?? && domainInfo.exportInfo.exportable>

    @Operation(summary = "导出${domainInfo.domainCnName}")
    @GetMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/export")
    public void exportAll(@RequestParam(required = false) List<String> ids) {
        ${camelCase(domainInfo.domainEnName)}Service.exportAll(ids);
    }

    @Operation(summary = "导入${domainInfo.domainCnName}")
    @PostMapping(value = "/${pluralize(camelCase(domainInfo.domainEnName))}/import")
    public void importAll(MultipartFile file) {
        ${camelCase(domainInfo.domainEnName)}Service.importAll(file);
    }
</#if>
}