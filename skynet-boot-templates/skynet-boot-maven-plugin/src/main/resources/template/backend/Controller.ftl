package ${genConfig.pack}.web.rest;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import skynet.boot.annotation.EnableSkynetSwagger2;
import ${genConfig.pack}.generated.controller.Generated${capitalizeCamelCase(domainInfo.domainEnName)}Controller;

/**
 * ${domainInfo.domainCnName}管理
 */
@Tag(name = "${domainInfo.domainCnName}管理")
@RestController
@RequestMapping("api")
@EnableSkynetSwagger2
public class ${capitalizeCamelCase(domainInfo.domainEnName)}Controller extends Generated${capitalizeCamelCase(domainInfo.domainEnName)}Controller {

}
