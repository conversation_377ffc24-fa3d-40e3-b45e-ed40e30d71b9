package ${genConfig.pack}.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
<#if domainInfo.hasInstant()>
import java.time.Instant;
</#if>
<#if domainInfo.hasBigDecimal()>
import java.math.BigDecimal;
</#if>
<#if domainInfo.hasArray()>
import com.alibaba.fastjson2.JSONArray;
</#if>
<#if domainInfo.hasJson()>
import com.alibaba.fastjson2.JSONObject;
</#if>
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class Generated${capitalizeCamelCase(domainInfo.domainEnName)}DTO extends AbstractAuditingEntity<${domainInfo.pkColumnType}> {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>

    /**
     * ${column.columnComment}
     */
    @Schema(title = "${column.columnComment}")
    <#list column.validateRules as rule>
    <#if rule.required??>
    <#if column.columnJavaType == 'String'>
    @NotBlank(message = "<#if rule.message??>${rule.message}<#else>不能为空</#if>")
    <#else>
    @NotNull(message = "<#if rule.message??>${rule.message}<#else>不能为空</#if>")
    </#if>
    </#if>
    <#if rule.min?? && rule.max??>
    <#if column.columnJavaType == 'String'>
    @Size(max = ${rule.max?c}, min = ${rule.min?c}, message = "<#if rule.message??>${rule.message}<#else>长度必须大于${rule.min?c}且小于${rule.max?c}</#if>")
    <#else>
    @Max(value = ${rule.max?c}, message = "<#if rule.message??>${rule.message}<#else>数值不能大于${rule.max?c}</#if>")
    @Min(value = ${rule.min?c}, message = "<#if rule.message??>${rule.message}<#else>数值不能小于${rule.min?c}</#if>")
    </#if>
    <#elseif rule.min??>
    <#if column.columnJavaType == 'String'>
    @Size(min = ${rule.min?c}, message = "<#if rule.message??>${rule.message}<#else>长度不能小于${rule.min?c}</#if>")
    <#else>
    @Min(value = ${rule.min?c}, message = "<#if rule.message??>${rule.message}<#else>数值不能小于${rule.min?c}</#if>")
    </#if>
    <#elseif rule.max??>
    <#if column.columnJavaType == 'String'>
    @Size(max = ${rule.max?c}, message = "<#if rule.message??>${rule.message}<#else>长度不能大于${rule.max?c}</#if>")
    <#else>
    @Max(value = ${rule.max?c}, message = "<#if rule.message??>${rule.message}<#else>数值不能大于${rule.max?c}</#if>")
    </#if>
    </#if>
    <#if rule.regex??>
    @Pattern(regexp = "${rule.regex}", message = "<#if rule.message??>${rule.message}<#else>格式不正确</#if>")
    </#if>
    </#list>
    <#if column.defaultValue??>
    private ${column.columnJavaType} ${camelCase(column.columnEnName)} = ${column.defaultValue};
    <#else>
    private ${column.columnJavaType} ${camelCase(column.columnEnName)};
    </#if>
    <#if column.refer??>
    <#list column.refer.columns as referColumn>

    /**
     * ${referColumn.columnCnName}
     */
    private String ${camelCase(referColumn.columnEnName)};
    </#list>
    </#if>
    </#list>
</#if>
}