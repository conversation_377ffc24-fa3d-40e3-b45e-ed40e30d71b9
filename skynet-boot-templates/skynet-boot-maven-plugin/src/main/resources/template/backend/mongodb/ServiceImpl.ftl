package ${genConfig.pack}.service.impl;

import ${genConfig.pack}.generated.domain.*; // for static metamodels

import ${genConfig.pack}.service.${capitalizeCamelCase(domainInfo.domainEnName)}Service;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}DTO;
import skynet.boot.common.mapper.EntityMapper;
import ${genConfig.pack}.generated.domain.Generated${capitalizeCamelCase(domainInfo.domainEnName)};
import ${genConfig.pack}.generated.service.impl.Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl;

import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class ${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl extends Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl implements ${capitalizeCamelCase(domainInfo.domainEnName)}Service {

    public ${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl(MongoRepository<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, String> mongoRepository, EntityMapper<${capitalizeCamelCase(domainInfo.domainEnName)}DTO, Generated${capitalizeCamelCase(domainInfo.domainEnName)}> entityMapper, MongoOperations mongoOperations) {
        super(mongoRepository, entityMapper, mongoOperations);
    }
}