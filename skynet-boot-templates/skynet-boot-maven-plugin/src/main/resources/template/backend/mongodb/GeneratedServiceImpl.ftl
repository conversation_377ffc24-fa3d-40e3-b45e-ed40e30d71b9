package ${genConfig.pack}.generated.service.impl;

import com.alibaba.fastjson2.JSON;
<#if domainInfo.hasUploadable() || (domainInfo.exportInfo?? && domainInfo.exportInfo.exportable)>
import skynet.boot.web.HttpServletUtils;
import org.apache.commons.io.FileUtils;
</#if>
<#if (domainInfo.exportInfo?? && domainInfo.exportInfo.exportable)>
import skynet.boot.common.FileZipUtil;
</#if>
import ${genConfig.pack}.generated.domain.*; // for static metamodels
import ${genConfig.pack}.repository.${capitalizeCamelCase(domainInfo.domainEnName)}Repository;
<#if domainInfo.columnInfos??>
<#assign seen_domains = []>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
<#if seen_domains?seq_contains(column.refer.domain)>
<#else>
<#assign seen_domains = seen_domains + [column.refer.domain]>
import ${genConfig.pack}.repository.${capitalizeCamelCase(column.refer.domain)}Repository;
</#if>
</#if>
</#list>
</#if>
import ${genConfig.pack}.generated.service.Generated${capitalizeCamelCase(domainInfo.domainEnName)}Service;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.mongo.service.MongoTemplateServiceImpl;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}Criteria;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}DTO;
import ${genConfig.pack}.service.dto.${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO;
import ${genConfig.pack}.generated.domain.Generated${capitalizeCamelCase(domainInfo.domainEnName)};
import ${genConfig.pack}.generated.service.mapper.Generated${capitalizeCamelCase(domainInfo.domainEnName)}Mapper;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.web.multipart.MultipartFile;

import lombok.extern.slf4j.Slf4j;

import jakarta.annotation.Resource;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@Slf4j
public class Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl extends MongoTemplateServiceImpl<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, ${capitalizeCamelCase(domainInfo.domainEnName)}DTO, String> implements Generated${capitalizeCamelCase(domainInfo.domainEnName)}Service {

    @Resource
    protected ${capitalizeCamelCase(domainInfo.domainEnName)}Repository ${camelCase(domainInfo.domainEnName)}Repository;

    @Resource
    protected Generated${capitalizeCamelCase(domainInfo.domainEnName)}Mapper ${camelCase(domainInfo.domainEnName)}Mapper;

<#if domainInfo.columnInfos??>
<#assign seen_domains = []>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
<#if seen_domains?seq_contains(column.refer.domain)>
<#else>
<#assign seen_domains = seen_domains + [column.refer.domain]>
    @Resource
    protected ${capitalizeCamelCase(column.refer.domain)}Repository ${camelCase(column.refer.domain)}Repository;
</#if>
</#if>
</#list>
</#if>
    @Resource
    protected MongoOperations mongoOperations;

<#if domainInfo.hasUploadable()>
    @Value("${r'$'}{application.${camelCase(domainInfo.domainEnName)}.dir-path:./${camelCase(domainInfo.domainEnName)}/}")
    protected String ${camelCase(domainInfo.domainEnName)}DirPath;
</#if>
<#if domainInfo.hasUploadable() || (domainInfo.exportInfo?? && domainInfo.exportInfo.exportable)>

    @Resource
    protected HttpServletUtils httpServletUtils;
</#if>

    public Generated${capitalizeCamelCase(domainInfo.domainEnName)}ServiceImpl(MongoRepository<Generated${capitalizeCamelCase(domainInfo.domainEnName)}, String> mongoRepository, EntityMapper<${capitalizeCamelCase(domainInfo.domainEnName)}DTO, Generated${capitalizeCamelCase(domainInfo.domainEnName)}> entityMapper, MongoOperations mongoOperations) {
        super(mongoRepository, entityMapper, mongoOperations);
    }

    @Override
    public Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllByCriteria(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Pageable pageable) {
        Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> list = super.findAllByCriteria(criteria, Generated${capitalizeCamelCase(domainInfo.domainEnName)}.class, pageable);
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
        list.forEach(this::convert${capitalizeCamelCase(column.refer.domain)});
</#if>
</#list>
</#if>
        return list;
    }

    @Override
    public Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAll(Pageable pageable) {
        Page<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> list = super.findAllByCriteria(null, Generated${capitalizeCamelCase(domainInfo.domainEnName)}.class, pageable);
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
        list.forEach(this::convert${capitalizeCamelCase(column.refer.domain)});
</#if>
</#list>
</#if>
        return list;
    }

    @Override
    public List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAllByCriteria(${capitalizeCamelCase(domainInfo.domainEnName)}Criteria criteria, Sort sort) {
        List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> list = super.findAllByCriteria(criteria, Generated${capitalizeCamelCase(domainInfo.domainEnName)}.class, sort);
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
        list.forEach(this::convert${capitalizeCamelCase(column.refer.domain)});
</#if>
</#list>
</#if>
        return list;
    }

    @Override
    public List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> findAll(Sort sort) {
        List<${capitalizeCamelCase(domainInfo.domainEnName)}DTO> list = super.findAllByCriteria(null, Generated${capitalizeCamelCase(domainInfo.domainEnName)}.class, sort);
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
        list.forEach(this::convert${capitalizeCamelCase(column.refer.domain)});
</#if>
</#list>
</#if>
        return list;
    }

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO findById(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        ${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO = super.findById(id);
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>
        convert${capitalizeCamelCase(column.refer.domain)}(${camelCase(domainInfo.domainEnName)}DTO);
</#if>
</#list>
</#if>
        return ${camelCase(domainInfo.domainEnName)}DTO;
    }
<#if domainInfo.columnInfos??>
<#list domainInfo.columnInfos as column>
<#if column.refer??>

    private void convert${capitalizeCamelCase(column.refer.domain)}(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
        if (StringUtils.isNotBlank(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}())) {
            ${camelCase(column.refer.domain)}Repository.findBy${capitalizeCamelCase(column.refer.pk)}(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}()).ifPresent(${camelCase(column.refer.domain)} -> {
<#list column.refer.columns as referColumn>
                ${camelCase(domainInfo.domainEnName)}DTO.set${capitalizeCamelCase(referColumn.columnEnName)}(${camelCase(column.refer.domain)}.get${capitalizeCamelCase(referColumn.name)}());
</#list>
            });
        }
    }
</#if>
</#list>
</#if>

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO save(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if column.unique>
        if (${camelCase(domainInfo.domainEnName)}Repository.findBy${capitalizeCamelCase(column.columnEnName)}(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}()).isPresent()) {
            throw new DuplicateNameException("${column.columnCnName}");
        }
    </#if>
    </#list>
</#if>
        return super.save(${camelCase(domainInfo.domainEnName)}DTO);
    }

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO update(${capitalizeCamelCase(domainInfo.domainEnName)}DTO ${camelCase(domainInfo.domainEnName)}DTO) {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.unique>
        Optional<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> ${camelCase(domainInfo.domainEnName)}${column_index} = ${camelCase(domainInfo.domainEnName)}Repository.findBy${capitalizeCamelCase(column.columnEnName)}(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(column.columnEnName)}());
        if (${camelCase(domainInfo.domainEnName)}${column_index}.isPresent() && !${camelCase(domainInfo.domainEnName)}${column_index}.get().get${capitalizeCamelCase(domainInfo.pkColumnEnName)}().equals(${camelCase(domainInfo.domainEnName)}DTO.get${capitalizeCamelCase(domainInfo.pkColumnEnName)}())) {
            throw new DuplicateNameException("${column.columnCnName}");
        }
        </#if>
    </#list>
</#if>

        return super.update(${camelCase(domainInfo.domainEnName)}DTO.getId(),${camelCase(domainInfo.domainEnName)}DTO);
    }

    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO patch(${capitalizeCamelCase(domainInfo.domainEnName)}PatchDTO ${camelCase(domainInfo.domainEnName)}PatchDTO) {
        return super.patch(${camelCase(domainInfo.domainEnName)}PatchDTO.getId(), ${camelCase(domainInfo.domainEnName)}PatchDTO);
    }

    @Override
    public void delete(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        super.delete(${camelCase(domainInfo.pkColumnEnName)});
    }


    @Override
    public ${capitalizeCamelCase(domainInfo.domainEnName)}DTO copy(${domainInfo.pkColumnType} ${camelCase(domainInfo.pkColumnEnName)}) {
        List<String> renameFields = new ArrayList<>();
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.unique>
        renameFields.add("${camelCase(column.columnEnName)}");
        </#if>
    </#list>
</#if>
        return super.copy(id, renameFields, Generated${capitalizeCamelCase(domainInfo.domainEnName)}.class);
    }
<#if domainInfo.hasUploadable()>

    @Override
    public String upload(MultipartFile file) {
        String filePath = System.currentTimeMillis() + File.separator + file.getOriginalFilename();
        try {
            FileUtils.copyInputStreamToFile(file, new File(${camelCase(domainInfo.domainEnName)}DirPath + filePath));
            return filePath;
        } catch (Exception e) {
            log.error(String.format("上传文件[%s]出现异常！", filePath), e);
            throw new BusinessException("上传文件异常");
        }
    }

    @Override
    public void download(String filePath) {
        try {
            httpServletUtils.copyFileToResponse(new File(${camelCase(domainInfo.domainEnName)}DirPath + filePath));
        } catch (Exception e) {
            log.error(String.format("下载文件[%s]出现异常！", filePath), e);
            throw new BusinessException("下载文件异常");
        }
    }
</#if>
<#if domainInfo.exportInfo?? && domainInfo.exportInfo.exportable>

    @Override
    public void exportAll(List<String> ids) {
        File tempDir = new File(System.getProperty("java.io.tmpdir"), "export-" + System.currentTimeMillis());
        tempDir.mkdirs();
        try {
            // 将数据导出到临时目录
            exportAllToDir(ids, tempDir);

            // 组装导出的压缩包
            File zipFile = new File(tempDir, "export-" + System.currentTimeMillis() + ".zip");
            String[] folders = tempDir.list();
            if (folders != null) {
                // 转换成绝对路径
                String[] foldersPath = new String[folders.length];
                for (int i = 0; i < folders.length; i++) {
                    foldersPath[i] = tempDir.getAbsolutePath() + File.separator + folders[i];
                }
                // 压缩成zip包
                FileZipUtil.zip(foldersPath, zipFile.getAbsolutePath());
            }
            httpServletUtils.copyFileToResponse(zipFile);
        } catch (Exception e) {
            log.error("导出异常：" + e.getMessage(), e);
        } finally {
            FileUtils.deleteQuietly(tempDir);
        }
    }

    /**
     * 将数据导出到临时目录
     * @param ids 要导出的数据，如果为空，导出所有数据
     * @param tempDir 临时目录
     */
    protected void exportAllToDir(List<String> ids, File tempDir) {
        List<Generated${capitalizeCamelCase(domainInfo.domainEnName)}> ${pluralize(camelCase(domainInfo.domainEnName))} = ${camelCase(domainInfo.domainEnName)}Repository.findAll();
        exportEntity(new File(tempDir, "${pluralize(camelCase(domainInfo.domainEnName))}"), ${pluralize(camelCase(domainInfo.domainEnName))}, Generated${capitalizeCamelCase(domainInfo.domainEnName)}::getId);
    }

    /**
     * 导出某个实体
     */
    protected <T> void exportEntity(File dir, List<T> entities, Function<T, Object> keySelector) {
        dir.mkdirs();
        for (T entity : entities) {
            String fileName = keySelector.apply(entity) + ".json";
            try {
                FileUtils.writeStringToFile(new File(dir, fileName), JSON.toJSONString(entity), StandardCharsets.UTF_8);
            } catch (Exception e) {
                log.error("导出 zip 文件时出现异常，文件名：{} {}", fileName, e.getMessage());
            }
        }
    }

    @Override
    public void importAll(MultipartFile file) {
        File tempDir = new File(System.getProperty("java.io.tmpdir"), "import-" + System.currentTimeMillis());
        tempDir.mkdirs();
        try {
            // 将zip包解压到临时目录
            File zipFile = new File(tempDir, file.getOriginalFilename());
            file.transferTo(zipFile);
            FileZipUtil.unZip(zipFile, tempDir);
            FileUtils.deleteQuietly(zipFile);

            // 导入数据
            importAllToDb(tempDir);
        } catch (Exception e) {
            log.error("导入异常：" + e.getMessage(), e);
        } finally {
            FileUtils.deleteQuietly(tempDir);
        }
    }

    /**
     * 将临时目录中的数据导入数据库
     * @param tempDir 临时目录
     */
    protected void importAllToDb(File tempDir) {
        importEntity(new File(tempDir, "${pluralize(camelCase(domainInfo.domainEnName))}"), Generated${capitalizeCamelCase(domainInfo.domainEnName)}.class);
    }

    /**
     * 导入某个实体
     */
    protected <T> void importEntity(File dir, Class<T> entityClass) {
        if (!dir.exists()) {
            return;
        }
        for (String fileName : dir.list()) {
            try {
                File jsonFile = new File(dir, fileName);
                String content = FileUtils.readFileToString(jsonFile, StandardCharsets.UTF_8);
                T entity = JSON.parseObject(content, entityClass);
                mongoOperations.save(entity);
            } catch (Exception e) {
                log.error("导入 zip 文件时出现异常，文件名：{} {}", fileName, e.getMessage());
            }
        }
    }
</#if>
}