package ${genConfig.pack}.generated.domain;

import skynet.boot.mongo.domain.AbstractAuditingEntity;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

<#if domainInfo.hasInstant()>
import java.time.Instant;
</#if>
<#if domainInfo.hasBigDecimal()>
import java.math.BigDecimal;
</#if>
<#if domainInfo.hasArray()>
import com.alibaba.fastjson2.JSONArray;
</#if>
<#if domainInfo.hasJson()>
import com.alibaba.fastjson2.JSONObject;
</#if>

/**
 * ${domainInfo.domainCnName}
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "${domainInfo.domainEnName}")
public class Generated${capitalizeCamelCase(domainInfo.domainEnName)} extends AbstractAuditingEntity<${domainInfo.pkColumnType}> {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
    <#if !column.isSystemField()>
    /**
     * ${column.columnComment}
     */
    @Field(name = "${column.columnEnName}")
    private ${column.columnJavaType} ${camelCase(column.columnEnName)};
    </#if>
    </#list>
</#if>

}