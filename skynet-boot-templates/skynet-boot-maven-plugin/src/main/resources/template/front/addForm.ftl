<template>
  <el-dialog :append-to-body="true" :visible.sync="dialog" :title="isAdd ? '新增' : '编辑'" width="600px">
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
<#if domainInfo.columnInfos??>
  <#list domainInfo.columnInfos as column>
  <#if column.pageInfo?? && column.pageInfo.columnShowAdd>
      <#if domainInfo.pageInfo.parentInfo?? && domainInfo.pageInfo.parentInfo.foreignKey == column.columnEnName>
      <el-form-item label="${column.columnCnName}：" prop="${camelCase(column.columnEnName)}" style="display:none;">
      <#elseif column.refer??>
      <el-form-item label="${column.refer.columns[0].columnCnName}：" prop="${camelCase(column.columnEnName)}">
      <#else>
      <el-form-item label="${column.columnCnName}：" prop="${camelCase(column.columnEnName)}">
      </#if>
        <#if column.pageInfo?? && column.pageInfo.columnEnums?? && column.pageInfo.columnEnums?size gt 0>
            <el-select placeholder="请选择${domainInfo.domainCnName}${column.columnCnName}"  v-model="form.${camelCase(column.columnEnName)}" clearable size="small" style="width: 360px;">
                <#list column.pageInfo.columnEnums as enum>
                <el-option label="${enum.value}" value="${enum.key}"></el-option>
            </#list>
            </el-select>
        <#elseif column.refer??>
        <el-select placeholder="请选择${column.refer.columns[0].columnCnName}" v-model="form.${camelCase(column.columnEnName)}" clearable size="small" style="width: 360px;">
            <el-option v-for="item in ${camelCase(column.refer.domain)}List" :key="item.${camelCase(column.refer.pk)}" :label="item.${camelCase(column.refer.columns[0].name)}" :value="item.${camelCase(column.refer.pk)}"></el-option>
        </el-select>
        <#else>
        <#switch column.pageInfo.inputType>
        <#case 'textarea'>
          <el-input placeholder="请输入${column.columnCnName}" v-model="form.${camelCase(column.columnEnName)}" style="width: 360px;" :rows="2" type="textarea"/>
        <#break>
        <#case 'date'>
          <el-date-picker v-model="form.${camelCase(column.columnEnName)}" type="date" placeholder="请选择${column.columnCnName}" style="width: 360px;"/>
        <#break>
        <#case 'datetime'>
          <el-date-picker v-model="form.${camelCase(column.columnEnName)}" type="datetime" placeholder="请选择${column.columnCnName}" style="width: 360px;"/>
        <#break>
        <#case 'upload'>
          <el-upload ref="upload_${camelCase(column.columnEnName)}"
              :on-exceed="onExceed_${camelCase(column.columnEnName)}"
              :on-error="onError_${camelCase(column.columnEnName)}"
              :on-progress="onProgress_${camelCase(column.columnEnName)}"
              :on-success="onSuccess_${camelCase(column.columnEnName)}"
              :before-upload="onBeforeUpload_${camelCase(column.columnEnName)}"
              :limit="1"
              action="api/${pluralize(camelCase(domainInfo.domainEnName))}/upload"
              class="upload-wrapper"
              drag
              element-loading-spinner="el-icon-loading"
              element-loading-text="文件正在上传中..."
              v-loading="loading">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div class="el-upload__tip" slot="tip">上传文件最大不超过500M</div>
          </el-upload>
        <#break>
        <#default>
          <el-input placeholder="请输入${column.columnCnName}" v-model="form.${camelCase(column.columnEnName)}" style="width: 360px;"/>
        </#switch>
        </#if>
      </el-form-item>
   </#if>
  </#list>
</#if>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { add, edit } from '@/axios/api/${camelCase(domainInfo.domainEnName)}'
export default {
  props: {
<#if domainInfo.columnInfos??>
  <#list domainInfo.columnInfos as column>
    <#if column.refer??>
    ${camelCase(column.refer.domain)}List: {
      type: Array,
      required: true
    },
    </#if>
  </#list>
</#if>
    isAdd: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      loading: false, dialog: false,
      form: {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.pageInfo?? && column.pageInfo.columnShowAdd>
        ${camelCase(column.columnEnName)}: <#if column.columnType = "json">{}<#else>''</#if><#if column_has_next>,</#if>
        </#if>
    </#list>
</#if>
      },
      rules: {
<#list domainInfo.columnInfos as column>
<#if column.pageInfo?? && column.pageInfo.columnShowAdd>
<#if column.pageInfo.columnRules?? && column.pageInfo.columnRules?size gt 0>
        ${camelCase(column.columnEnName)}: [
    <#list column.pageInfo.columnRules as rule>
        <#if rule.required??>
          { required: ${rule.required?c}, message: '${rule.message}', trigger: '${rule.trigger}' }<#if (rule_has_next)>,</#if>
        </#if>
        <#if rule.max??>
          { max: ${rule.max}, message: '${rule.message}', trigger: '${rule.trigger}' }<#if (rule_has_next)>,</#if>
        </#if>
        <#if rule.min??>
          { min: ${rule.min}, message: '${rule.message}', trigger: '${rule.trigger}' }<#if (rule_has_next)>,</#if>
        </#if>
        <#if rule.regex??>
          {
            validator: function(rule, value, callback) {
              if (value && /${rule.regex}/.test(value) == false) {
                callback(new Error("${rule.message}"));
              } else {
                callback();
              }
            },
            trigger: "${rule.trigger}"
          }<#if (rule_has_next)>,</#if>
        </#if>
    </#list>
        ]<#if (column_has_next)>,</#if>
</#if>
</#if>
</#list>
      }
    }
  },
  methods: {
    cancel() {
      this.resetForm()
    },
    doSubmit() {
      if (this.isAdd) {
        this.doAdd()
      } else this.doEdit()
    },
    doAdd() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          add(this.form).then(res => {
            this.resetForm()
            this.$message.success({
              title: '添加成功',
              type: 'success',
              duration: 2500
            })
            this.loading = false
            this.$emit("query")
          }).catch(err => {
            this.loading = false
            console.log(err.response.data.message)
          })
        }
      })
    },
    doEdit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          edit(this.form).then(res => {
            this.resetForm()
            this.$message.success({
              title: '修改成功',
              type: 'success',
              duration: 2500
            })
            this.loading = false
            this.$emit("query")
          }).catch(err => {
            this.loading = false
            console.log(err.response.data.message)
          })
        }
      });
    },
    resetForm() {
      this.dialog = false
      this.$refs['form'].resetFields()
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.pageInfo?? && column.pageInfo.inputType == "upload">
      this.$refs['upload_${camelCase(column.columnEnName)}'].clearFiles()
        </#if>
    </#list>
</#if>
      this.form = {
<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.pageInfo?? && column.pageInfo.columnShowAdd>
        ${camelCase(column.columnEnName)}: <#if column.columnType = "json">{}<#else>''</#if><#if column_has_next>,</#if>
        </#if>
    </#list>
</#if>
      }
    },

<#if domainInfo.columnInfos??>
    <#list domainInfo.columnInfos as column>
        <#if column.pageInfo?? && column.pageInfo.inputType == "upload">
    onExceed_${camelCase(column.columnEnName)}(files, fileList) {
        this.$message.error("只支持同时上传一个文件，请先移除后再上传！");
    },
    onError_${camelCase(column.columnEnName)}(err) {
        this.loading = false;
        this.$message.error(JSON.parse(err.message).message);
    },
    onProgress_${camelCase(column.columnEnName)}(event) {
        this.loading = true;
    },
    onSuccess_${camelCase(column.columnEnName)}(res) {
        this.loading = false;
        this.form.${camelCase(column.columnEnName)} = res;
    },
    onBeforeUpload_${camelCase(column.columnEnName)}(file, fileList) {
        const fileSize = file.size / 1024 / 1024;
        if (fileSize > 500) {
            this.$message.error("上传文件大小不能超过500MB!");
            return false;
        }
    },
        </#if>
    </#list>
</#if>
  }
}
</script>

<style scoped>

</style>
