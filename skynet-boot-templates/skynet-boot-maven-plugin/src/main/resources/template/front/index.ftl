<#--noinspection ALL-->
<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!-- 新增 -->
      <div style="position: absolute;right: 0">
<#if domainInfo.pageInfo.operationInfo.addable>
        <el-button class="filter-item" size="mini" type="primary" icon="el-icon-plus" @click="add">新增${domainInfo.domainCnName}</el-button>
</#if>
      </div>
      <el-form :inline="true" :model="queryForm" style="position: absolute;left: 0">
      <#if domainInfo.columnInfos??>
          <#list domainInfo.columnInfos as column>
          <#if column.pageInfo?? && column.pageInfo.filterable>
            <#if column.refer??>
        <el-form-item label="${column.refer.columns[0].columnCnName}：">
            <#else>
        <el-form-item label="${column.columnCnName}：">
            </#if>
            <#if column.pageInfo?? && column.pageInfo.columnEnums?? && column.pageInfo.columnEnums?size gt 0>
            <el-select placeholder="请选择${domainInfo.domainCnName}${column.columnCnName}"  v-model="queryForm.${camelCase(column.columnEnName)}" clearable size="small" @change="query(true)">
                <el-option label="全部" value=""></el-option>
                <#list column.pageInfo.columnEnums as enum>
                <el-option label="${enum.value}" value="${enum.key}"></el-option>
                </#list>
            </el-select>
            <#elseif column.refer??>
            <el-select placeholder="请选择${column.refer.columns[0].columnCnName}" v-model="queryForm.${camelCase(column.columnEnName)}" clearable size="small" @change="query(true)">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in ${camelCase(column.refer.domain)}List" :key="item.${camelCase(column.refer.pk)}" :label="item.${camelCase(column.refer.columns[0].name)}" :value="item.${camelCase(column.refer.pk)}"></el-option>
            </el-select>
            <#elseif column.columnJavaType == "Instant">
            <el-date-picker start-placeholder="开始时间" end-placeholder="结束时间" v-model="queryForm.${camelCase(column.columnEnName)}" type="datetimerange" :default-time="['00:00:00','23:59:59']" clearable size="small" @change="query(true)"/>
            <#else>
            <el-input placeholder="请输入${domainInfo.domainCnName}${column.columnCnName}" v-model="queryForm.${camelCase(column.columnEnName)}" @input="query(true)" clearable size="small"></el-input>
            </#if>
        </el-form-item>
          </#if>
          </#list>
      </#if>
      </el-form>
    </div>
    <!--表单组件-->
    <eForm ref="form"
        :is-add="isAdd"
<#if domainInfo.columnInfos??>
  <#list domainInfo.columnInfos as column>
    <#if column.refer??>
        :${camelCase(column.refer.domain)}List="${camelCase(column.refer.domain)}List"
    </#if>
  </#list>
</#if>
        @query="query"/>
    <!--表格渲染-->
<#if domainInfo.pageInfo.listType = "list">
    <el-table v-loading="loading" :data="data" size="small" style="width: 100%;" class="table" header-row-class-name="headbg">
     <el-table-column label="序号" type="index" width="70" align="center"/>
      <#if domainInfo.columnInfos??>
          <#list domainInfo.columnInfos as column>
          <#if column.pageInfo?? && column.pageInfo.columnShowList>
          <#if column.refer??>
          <#list column.refer.columns as referColumn>
          <el-table-column prop="${camelCase(referColumn.columnEnName)}" min-width="100" label="${referColumn.columnCnName}">
            <template slot-scope="scope"> {{ scope.row.${camelCase(referColumn.columnEnName)} }} </template>
          </el-table-column>
          </#list>
          <#else>
          <el-table-column prop="${camelCase(column.columnEnName)}" min-width="100" label="${column.columnCnName}">
          <template slot-scope="scope">
              <#if column.pageInfo?? && column.pageInfo.columnEnums?? && column.pageInfo.columnEnums?size gt 0>
                  <#list column.pageInfo.columnEnums as enum>
              <span class="status ${enum.style}" v-if="scope.row.${camelCase(column.columnEnName)} == '${enum.key}'"><#if enum.style = 'running'><svg-icon icon-class="icon-jiazai"></svg-icon> </#if>${enum.value}</span>
                  </#list>
              <#elseif column.pageInfo?? && column.pageInfo.inputType == "date">
              {{ scope.row.${camelCase(column.columnEnName)} ? scope.row.${camelCase(column.columnEnName)}.substring(0,10) : '' }}
              <#else>
              {{ scope.row.${camelCase(column.columnEnName)} }}
              </#if>
          </template>
          </el-table-column>
          </#if>
          </#if>
          </#list>
      </#if>
      <el-table-column label="操作" min-width="150px" align="center">
        <template slot-scope="scope">
<#if domainInfo.pageInfo.operationInfo.editable>
          <el-button @click="edit(scope.row)" size="mini">编辑</el-button>
</#if>
<#if domainInfo.pageInfo.operationInfo.deletable>
          <el-button @click="remove(scope.row.${camelCase(domainInfo.pkColumnEnName)})" size="mini" >删除</el-button>
</#if>
<#if domainInfo.pageInfo.operationInfo.copyable>
          <el-button @click="copy(scope.row.${camelCase(domainInfo.pkColumnEnName)})" size="mini" >复制</el-button>
</#if>
<#if domainInfo.pageInfo.operationInfo.extendButtons?size gt 0>
    <#list domainInfo.pageInfo.operationInfo.extendButtons as button>
        <#if button.column??>
          <el-button @click="patch(scope.row.${camelCase(domainInfo.pkColumnEnName)}, '${button.column}', ${button.next}, '${button.name}')" v-if="scope.row.${button.column} == ${button.current}" size="mini">${button.name}</el-button>
        <#else>
          <el-button @click="${camelCase(button.domain)}List(scope.row)" size="mini">${button.name}</el-button>
        </#if>
    </#list>
</#if>
        </template>
      </el-table-column>
    </el-table>
<#else>
    <div class="template-cards">
        <el-row v-for="(items, i) in tableChunked" :gutter="12" :key="i">
            <el-col v-for="item in items" :span="6" :key="item.id">
                <el-card shadow="hover" class="template-card">
                    <div class="title">
                        <div class="left">
                            <h2>{{item.name}}</h2>
                            <span>{{item.createdDate}}</span>
                        </div>
                    </div>
                    <div class="detail">
                        <ul>
                    <#list domainInfo.columnInfos as column>
                        <#if column.pageInfo?? && column.pageInfo.columnShowList>
                            <li>${column.columnCnName}：{{item.${camelCase(column.columnEnName)}}}</li>
                        </#if>
                    </#list>
                        </ul>
                    </div>
                    <div class="ops">
<#if domainInfo.pageInfo.operationInfo.editable>
                        <el-tooltip content="编辑" effect="light" placement="bottom" :open-delay=300>
                            <el-button :disabled="item.status == 10" @click.stop.prevent="edit(item)" icon="el-icon-edit-outline" plain size="mini"></el-button>
                        </el-tooltip>
                        <el-divider direction="vertical"></el-divider>
</#if>
<#if domainInfo.pageInfo.operationInfo.copyable>
                        <el-tooltip content="复制" effect="light" placement="bottom" :open-delay=300>
                            <el-button @click.stop.prevent="copy(item.id)" icon="el-icon-copy-document" plain size="mini"></el-button>
                        </el-tooltip>
                        <el-divider direction="vertical"></el-divider>
</#if>
<#if domainInfo.pageInfo.operationInfo.deletable>
                        <el-tooltip content="删除" effect="light" placement="bottom" :open-delay=300>
                            <el-button :disabled="item.status == 10" @click.stop.prevent="remove(item.id)" icon="el-icon-delete" plain size="mini"></el-button>
                        </el-tooltip>
                        <el-divider direction="vertical"></el-divider>
</#if>
<#if domainInfo.pageInfo.operationInfo.extendButtons?size gt 0>
    <#list domainInfo.pageInfo.operationInfo.extendButtons as button>
        <#if button.column??>
                        <el-tooltip content="${button.name}" effect="light" placement="bottom" :open-delay=300 v-if="item.${button.column} == ${button.current}" >
                            <el-button icon="${button.icon}" @click.stop.prevent="patch(item.${camelCase(domainInfo.pkColumnEnName)}, '${button.column}', ${button.next}, '${button.name}')" plain size="mini"></el-button>
                        </el-tooltip>
        <#else>          
                        <el-tooltip content="${button.name}" effect="light" placement="bottom" :open-delay=300 >
                            <el-button @click="${camelCase(button.domain)}List(scope.row)" size="mini">${button.name}</el-button>
                        </el-tooltip>
        </#if>
    </#list>
</#if>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</#if>
    <!--分页组件-->
    <el-pagination
      :total="total"
      :current-page.sync="page"
      :page-size.sync="size"
      style="margin-top: 8px;"
      align="right"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="query(true)"
      @current-change="query(false)"/>
  </div>
</template>

<script>
import { del,list,copy,patch } from '@/axios/api/${camelCase(domainInfo.domainEnName)}'
<#if domainInfo.columnInfos??>
  <#list domainInfo.columnInfos as column>
    <#if column.refer??>
import { listUnPage as query${capitalizeCamelCase(column.refer.domain)}ListUnPage } from '@/axios/api/${camelCase(column.refer.domain)}'
    </#if>
  </#list>
</#if>
<#if domainInfo.hasInstant()>
import { parseTime } from '@/utils/index'
</#if>
import eForm from './form'
export default {
  components: { eForm },
  data() {
    return {
      loading: false,
      total: 0,
      page: 1,
      size: 10,
      isAdd: true,
<#if domainInfo.columnInfos??>
  <#list domainInfo.columnInfos as column>
    <#if column.refer??>
      ${camelCase(column.refer.domain)}List: [],
    </#if>
  </#list>
</#if>
      tableChunked:[],
      queryForm: {
      <#if domainInfo.columnInfos??>
          <#list domainInfo.columnInfos as column>
          <#if column.pageInfo?? && column.pageInfo.filterable>
          ${camelCase(column.columnEnName)}: "",
          </#if>
          </#list>
      </#if>
      },
      data: []
    }
  },
  created() {
    this.$nextTick(() => {
<#if domainInfo.columnInfos??>
  <#list domainInfo.columnInfos as column>
    <#if column.refer??>
      this.query${capitalizeCamelCase(column.refer.domain)}List()
    </#if>
  </#list>
</#if>
<#if domainInfo.pageInfo.parentInfo??>
      if (sessionStorage.${camelCase(domainInfo.pageInfo.parentInfo.domain)}) {
        this.queryForm.${camelCase(domainInfo.pageInfo.parentInfo.foreignKey)} = JSON.parse(sessionStorage.${camelCase(domainInfo.pageInfo.parentInfo.domain)}).${camelCase(domainInfo.pageInfo.parentInfo.pk)}
        this.query(true)
      }
<#else>
      this.query(true)
</#if>
    })
  },
  methods: {
<#if domainInfo.columnInfos??>
  <#list domainInfo.columnInfos as column>
    <#if column.refer??>
    query${capitalizeCamelCase(column.refer.domain)}List() {
      query${capitalizeCamelCase(column.refer.domain)}ListUnPage({}).then(res=>{
        this.${camelCase(column.refer.domain)}List  = res
      })
    },
    </#if>
  </#list>
</#if>
    query(reset) {
      if (reset == true){
        this.page = 1
      }
      const sort = '${camelCase(domainInfo.pkColumnEnName)},desc'
      this.params = {
<#if domainInfo.pageInfo.parentInfo??>
          "${camelCase(domainInfo.pageInfo.parentInfo.foreignKey)}.equals": this.queryForm.${camelCase(domainInfo.pageInfo.parentInfo.foreignKey)},
</#if>
      <#if domainInfo.columnInfos??>
          <#list domainInfo.columnInfos as column>
          <#if column.pageInfo?? && column.pageInfo.filterable>
          <#if column.columnJavaType = 'Long' || column.columnJavaType = 'Integer' || column.refer??>
          "${camelCase(column.columnEnName)}.equals": this.queryForm.${camelCase(column.columnEnName)},
          <#elseif column.columnJavaType = 'Instant'>
          "${camelCase(column.columnEnName)}.greaterThanOrEqual": this.queryForm.${camelCase(column.columnEnName)} ? this.queryForm.${camelCase(column.columnEnName)}[0] : null,
          "${camelCase(column.columnEnName)}.lessThanOrEqual": this.queryForm.${camelCase(column.columnEnName)} ? this.queryForm.${camelCase(column.columnEnName)}[1] : null,
          <#else>
          "${camelCase(column.columnEnName)}.contains": this.queryForm.${camelCase(column.columnEnName)},
          </#if>
          </#if>
          </#list>
      </#if>
          page: this.page,
          size: this.size,
          sort: sort
        }
      list(this.params).then(res=>{
        this.data  = res.content
        this.total = res.totalElements
        this.tableChunked = [];
        for (let i = 0; i < this.data.length; i+=4) {
            this.tableChunked.push(this.data.slice(i, i+4));
        }
      })
    },

    remove(${camelCase(domainInfo.pkColumnEnName)}) {
      this.$confirm("确认删除该${domainInfo.domainCnName}吗？", "提示")
        .then(() => {
          this.loading = true
          del(${camelCase(domainInfo.pkColumnEnName)}).then((res) => {
            this.loading = false
            this.$message.success("删除成功！");
            this.query();
          }).catch(() => {
              this.loading = false
          });
        })
        .catch(() => {
          this.loading = false
        });
    },

    copy(${camelCase(domainInfo.pkColumnEnName)}) {
      this.$confirm("确认复制该${domainInfo.domainCnName}吗？", "提示")
        .then(() => {
          this.loading = true
          copy(${camelCase(domainInfo.pkColumnEnName)}).then((res) => {
            this.loading = false
            this.$message.success("复制成功！");
            this.query();
          }).catch(() => {
              this.loading = false
          });
        })
        .catch(() => {
          this.loading = false
        });
    },

    add() {
      this.isAdd = true
      const _this = this.$refs.form
      _this.form = {
<#if domainInfo.pageInfo.parentInfo??>
        ${camelCase(domainInfo.pageInfo.parentInfo.foreignKey)}: this.queryForm.${camelCase(domainInfo.pageInfo.parentInfo.foreignKey)}
</#if>
      }
      _this.dialog = true
    },
    goDetail(id){
      this.$router.push("/${camelCase(domainInfo.domainEnName)}/info/"+id)
    },
<#if domainInfo.pageInfo.operationInfo.extendButtons?size gt 0>
    <#list domainInfo.pageInfo.operationInfo.extendButtons as button>
        <#if button.domain??>
    ${camelCase(button.domain)}List(data) {
        sessionStorage.${camelCase(domainInfo.domainEnName)} = JSON.stringify(data)
        this.$router.push("/${camelCase(button.domain)}")
    },
        </#if>
    </#list>
</#if>
    edit(data) {
      this.isAdd = false
      const _this = this.$refs.form
      _this.form = {
        <#if domainInfo.columnInfos??>
        <#list domainInfo.columnInfos as column>
        ${camelCase(column.columnEnName)}: data.${camelCase(column.columnEnName)}<#if column_has_next>,</#if>
        </#list>
        </#if>
      }
      _this.dialog = true
    },
    patch(id, key, value, operateName) {
        this.$confirm("确认"+operateName+"该${domainInfo.domainCnName}吗？", "提示")
        .then(() => {
            this.loading = true
            let data = {${camelCase(domainInfo.pkColumnEnName)}:id}
            data[key] = value
            patch(data).then((res) => {
                this.loading = false
                this.$message.success(operateName+"成功！");
                this.query();
            }).catch(() => {
                this.loading = false
            });
        })
        .catch(() => {
            this.loading = false
        });
    },
  }
}
</script>

<style lang="less" scoped>
.template-cards {
    .template-card {
        color: #77859B;
        cursor: pointer;
        margin-bottom: 20px;
        .title {
            position: relative;
            height: 50px;
            .left {
                position: absolute;
                left: 0;
                width: 70%;
                h2 {
                    font-size: 1.2em;
                    margin: 0;
                    font-weight: 550;
                    width: 100%;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
            .right {
                position: absolute;
                right: 0;
                span {
                    display: block;
                    text-align: right;
                }
            }

            .status {
                position: relative;
                padding-left: 20px
            }
            .status:before {
                content: "";
                position: absolute;
                left: 0;
                width: 10px;
                height: 10px;
                border-radius: 5px;
                top: 5px
            }
            .status.success {
                color: #56cd75
            }
            .status.success:before {
                background: #56cd75
            }
            .status.info {
                color: #3D6FFF
            }
            .status.info:before {
                background: #3D6FFF
            }
        }
        .detail {
            background: #F5F6FA;
            border-radius: 4px;
            opacity: 0.6;
            margin: 10px 0;
            padding: 10px 0;
            ul {
                margin-bottom: 0;
                li {
                    list-style-type: none;
                    margin-left: -20px;
                    padding-right: 20px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
        .ops {
            text-align: center;
            button {
                border: 0;
                font-size: 20px;
                padding: 5px 5px;
            }
        }
    }
}
</style>
