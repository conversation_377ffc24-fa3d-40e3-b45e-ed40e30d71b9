import {
    $get,
    $post,
    $put,
    $delete,
    $patch,
} from "../http";

export function add(data) {
    return $post("/api/${pluralize(camelCase(domainInfo.domainEnName))}", data);
};

export function edit(data) {
    return $put("/api/${pluralize(camelCase(domainInfo.domainEnName))}", data);
};

export function patch(data) {
    return $patch("/api/${pluralize(camelCase(domainInfo.domainEnName))}", data);
};

export function list(data) {
     return $get("/api/${pluralize(camelCase(domainInfo.domainEnName))}", data);
};

export function listUnPage(data) {
    return $get("/api/${pluralize(camelCase(domainInfo.domainEnName))}/unPage", data);
};

export function get(${camelCase(domainInfo.pkColumnEnName)}) {
    return $get("api/${pluralize(camelCase(domainInfo.domainEnName))}/" + ${camelCase(domainInfo.pkColumnEnName)});
};

export function del(${camelCase(domainInfo.pkColumnEnName)}) {
    return $delete("api/${pluralize(camelCase(domainInfo.domainEnName))}/" + ${camelCase(domainInfo.pkColumnEnName)});
};

export function copy(${camelCase(domainInfo.pkColumnEnName)}) {
    return $post("api/${pluralize(camelCase(domainInfo.domainEnName))}/copy/" + ${camelCase(domainInfo.pkColumnEnName)});
};

