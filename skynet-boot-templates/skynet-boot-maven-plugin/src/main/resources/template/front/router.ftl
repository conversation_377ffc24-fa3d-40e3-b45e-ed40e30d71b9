import Layout from "@/layout";

export default [
  {
    path: "/${camelCase(domainInfo.domainEnName)}",
    component: Layout,
    name: "${domainInfo.pageInfo.pageTitle}",
<#if domainInfo.pageInfo.parentInfo??>
    hidden:true,
</#if>
    meta: { title: "${domainInfo.pageInfo.pageTitle}", icon: "${domainInfo.pageInfo.icon}", affix: false<#if domainInfo.pageInfo.order??>, order: ${domainInfo.pageInfo.order}</#if> },
    children: [
      {
        path: "/${camelCase(domainInfo.domainEnName)}",
        component: () => import("@/views/${camelCase(domainInfo.domainEnName)}/index"),
        meta: {
          title: "${domainInfo.pageInfo.pageTitle}",
          icon: "${domainInfo.pageInfo.icon}",
          affix: false,
          breadcrumb: false
        }
      },
      {
        path: "/${camelCase(domainInfo.domainEnName)}/info/:id",
        name: "${domainInfo.domainCnName}详情",
        hidden:true,
        component: () => import("@/views/${camelCase(domainInfo.domainEnName)}/index"),
        meta: {
          title: "${domainInfo.domainCnName}详情",
          affix: true,
          activeMenu: "/${camelCase(domainInfo.domainEnName)}"
        }
      }
    ]
  }
];
