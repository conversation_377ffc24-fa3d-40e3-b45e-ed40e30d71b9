# skynet-boot-maven-plugin

Skynet Boot 代码生成插件：

* 支持自动生成独立业务表的逻辑增删改查后端 `Controller`、`Service`、`Dao` 的代码
* 支持自动生成代码和用户开发代码分离，重复生成不影响已修改代码
* 支持两个表之前字段关联，如A表依赖B表中某个字段，需要在A业务列表中显示B表
* 支持生成前端列表查询和单条数据的增删改查的 `vue` 代码

## 生成 Skynet Boot 代码模板

如果是已有项目，这一步可以省略。

首先下载 [`archetype-catalog.xml`](./archetype-catalog.xml) 文件，将该文件放在你的本地 Maven 仓库中（默认路径为：`${user.home}/.m2/repository`，如果该路径下已经有 `archetype-catalog.xml` 文件，需要手工将内容拷贝进去），然后执行下面的命令生成项目模板：

```
$ mvn archetype:generate \
  -DarchetypeGroupId=com.iflytek.skynet \
  -DarchetypeArtifactId=skynet-boot-template-mongo \
  -DarchetypeVersion=4.3.3-SNAPSHOT \
  -DgroupId=com.example \
  -DartifactId=demo \
  -Dversion=1.0.0-SNAPSHOT \
  -DinteractiveMode=false
```

目前支持的代码模板有：

* skynet-boot-template-mongo
* skynet-boot-template-mysql
* skynet-boot-template-dm

## 快速使用

首先在 `pom.xml` 文件中添加 `skynet-boot-maven-plugin` 插件（使用 `skynet-boot-templates` 生成的项目已经内置该插件）：

```
<build>
    <plugins>
        <plugin>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-maven-plugin</artifactId>
            <version>4.3.3-SNAPSHOT</version>
            <configuration>
                <moduleName>.</moduleName>
                <packageName>com.example.demo</packageName>
                <dbType>mysql</dbType>
                <uiModuleName></uiModuleName>
            </configuration>
        </plugin>
    </plugins>
</build>
```

在项目根目录创建一个文件夹 `.generator`，并在文件夹下创建一个文件 `category.json`，文件内容如下：

```
{
    "domainInfos": [
        {
            "domainEnName": "category",
            "domainCnName": "分类",
            "columnInfos": [
                {
                    "columnEnName": "name",
                    "columnCnName": "名称",
                    "columnType": "varchar",
                    "columnLength": "100"
                }
            ]
        }
    ]
}
```

执行如下命令生成代码：

```
$ mvn skynet-boot:generate
```

或者使用下面的命令指定配置生成：

```
$ mvn skynet-boot:generate -Dgenerate.configFile=.generator/task.json
```

## 代码结构说明

通过插件生成代码之后，项目结构如下：

```
|   pom.xml
|   README.md
+---.generator
|       category.json
+---src
|   +---main
|   |   +---java
|   |   |   \---com
|   |   |       \---example
|   |   |           \---demo
|   |   |                |   Application.java
|   |   |                +---generated
|   |   |                |   +---controller
|   |   |                |   |       GeneratedCategoryController.java
|   |   |                |   +---domain
|   |   |                |   |       GeneratedCategory.java
|   |   |                |   +---repository
|   |   |                |   |       GeneratedCategoryRepository.java
|   |   |                |   \---service
|   |   |                |       |   GeneratedCategoryService.java
|   |   |                |       +---dto
|   |   |                |       |       GeneratedCategoryDTO.java
|   |   |                |       |       GeneratedCategoryPatchDTO.java
|   |   |                |       +---impl
|   |   |                |       |       GeneratedCategoryServiceImpl.java
|   |   |                |       \---mapper
|   |   |                |               GeneratedCategoryMapper.java
|   |   |                +---repository
|   |   |                |       CategoryRepository.java
|   |   |                +---service
|   |   |                |   |   CategoryService.java
|   |   |                |   +---dto
|   |   |                |   |       CategoryDTO.java
|   |   |                |   |       CategoryPatchDTO.java
|   |   |                |   \---impl
|   |   |                |           CategoryServiceImpl.java
|   |   |                \---web
|   |   |                    \---rest
|   |   |                            CategoryController.java
|   |   \---resources
|   |       |   application.properties
|   |       \---liquibase
|   |           |   master.xml
|   |           \---changelog
|   |                   category.sql
```

其中 `generated` 目录下是自动生成的增删改查的代码，外面的 `XXXController`、`XXXService`、`XXXRepository` 等都是继承自 `generated` 目录下的类，如果需要新增方法，直接在外面的类中添加即可，如果自动生成的代码逻辑不满足你的需求，可以通过在外面 Override 覆写父类的方法。注意不要修改 `generated` 目录下的文件，该目录下的文件每次都会重新生成，外面的类只会在第一次执行时生成，后面不会再生成，这样可以保护你的修改不会被代码生成器覆盖掉。不过，你也可以通过参数 `-Dgenerate.canOverwrite=true` 强制将外面的类再生成一遍：

```
$ mvn skynet-boot:generate -Dgenerate.canOverwrite=true
```

默认情况，生成器会生成以下几个接口：

| 接口编码 | 接口方法 | 接口地址                                  | 接口说明                                            |
| ------- | -------- | ----------------------------------------- | --------------------------------------------------- |
| insert | POST     | /api/entities                             | 新增                                                |
| delete | DELETE   | /api/entities/${entityId}                 | 删除                                                |
| update | PUT     | /api/entities                 | 修改，和 PATCH 接口的区别在于，会修改对象的所有字段 |
| patch | PATCH    | /api/entities                 | 部分更新，只修改对象的某些字段                      |
| listPage | GET      | /api/entities?page=1&size=20&sort=id,desc | 查询分页列表，支持通过 sort 参数按某个字段排序      |
| listUnPage | GET      | /api/entities/unPage?sort=id,desc         | 查询不分页列表，支持通过 sort 参数按某个字段排序    |
| detail | GET      | /api/entities/${entityId}                 | 查询详情                                            |
| copy | POST      | /api/entities/${entityId}/copy           | 复制                                              |

如果你不想生成这些接口，可以通过 `domainApiExclude` 和 `domainApiInclude` 参数进行控制。

另外值得注意的是，生成器生成的领域模型内置了如下几个字段：

* id - 主键
* createdDate - 创建时间
* createdBy - 创建人
* lastModifiedDate - 更新时间
* lastModifiedBy - 更新人

我们在 JSON 文件中定义字段的时候不要再重复定义。新增或编辑该实体时，框架使用了 JPA Auditing 机制自动对这些字段进行赋值，其中 `createdBy` 和 `lastModifiedBy` 这两个字段的赋值，需要在你的代码中实现 `AuditorAware` 接口，下面是一个简单的示例：

```
import org.springframework.data.domain.AuditorAware;

public class UsernameAuditorAware implements AuditorAware<String> {
    
    @Override
    public Optional<String> getCurrentAuditor() {
        return Optional.of("demo");
    }
}
```

## 配置文件格式说明

下面是一个完整的配置示例，仅供参考，后面会对每个配置项进行说明。

```
{
    "domainInfos": [
        {
            "domainEnName": "task",
            "domainCnName": "任务",
            "columnInfos": [
                {
                    "columnEnName": "name",
                    "columnCnName": "名称",
                    "columnType": "varchar",
                    "columnLength": "100",
                    "filterable": true,
                    "validateRules": [
                        { "required": true, "message": "名称不能为空" },
                        { "min": 3, "message": "名称最少3个字符" },
                        { "max": 100, "message": "名称最多100个字符" },
                        { "regex": "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", "message": "名称格式错误，请输入中文、英文（大小写）、数字和下划线（_）" }
                    ]
                },
                {
                    "columnEnName": "code",
                    "columnCnName": "编码",
                    "columnType": "varchar",
                    "columnLength": "100",
                    "filterable": true,
                    "unique": true,
                    "validateRules": [
                        { "required": true },
                        { "min": 3, "max": 100 },
                        { "regex": "^[a-zA-Z0-9_]+$" }
                    ]
                },
                {
                    "columnEnName": "start_date",
                    "columnCnName": "开始时间",
                    "columnType": "datetime",
                    "filterable": true
                },
                {
                    "columnEnName": "status",
                    "columnCnName": "状态",
                    "columnComment": "0 未开始，1 进行中，2 已完成",
                    "columnType": "varchar",
                    "columnLength": "10",
                    "patchable": true
                }
            ],
            "systemColumnInfos": [
                {
                    "columnEnName": "created_date",
                    "columnType": "datetime",
                    "filterable": true
                },
                {
                    "columnEnName": "last_modified_date",
                    "columnType": "datetime",
                    "filterable": true
                },
                {
                    "columnEnName": "id",
                    "columnType": "bigint",
                    "filterable": true
                }
            ]
        }
    ]
}
```

### domainInfos

领域类信息。一个 JSON 文件中可以定义多个领域类，推荐将相关的领域类统一放在一个 JSON 文件中方便管理。

#### domainEnName

领域类的英文名，必须是小写带下划线格式，如 `user_role`。

#### domainCnName

领域类的中文名。

#### domainApiExclude

让生成器不生成某些接口，默认值为空，表示不限制接口的生成，这时生成器会以 `domainApiInclude` 参数配置为准。参数值为接口编码，多个接口编码以逗号分隔，各个接口的编码参见上面的接口列表。如果不想生成器生成任何接口，可以使用通配符 `*`：

```
{
    "domainApiExclude": "*"
}
```

#### domainApiInclude

让生成器只生成某些接口，默认值为 `*`，表示生成所有的接口，生成器会优先使用 `domainApiInclude` 参数配置，得到要生成的接口清单，然后使用 `domainApiExclude` 参数进行过滤。如果只想生成查询列表接口，可以这样配置：

```
{
    "domainApiInclude": "listPage, listUnPage"
}
```

#### columnInfos

领域类的字段配置。

##### columnEnName

字段的英文名，必须是小写带下划线格式，如 `user_id`。

##### columnCnName

字段的中文名。

##### columnComment

字段的注释信息。选填。如果为空，则使用 `columnCnName` 作为注释信息。注释信息会生成到 DTO 类对应字段的注释和Swagger 说明上，以及数据库 SQL 的 COMMNET 上。

##### columnType

字段的数据类型。支持的类型和其对应的 Java 类型如下所示：

```
tinyint=Boolean
smallint=Integer
mediumint=Integer
int=Integer
integer=Integer
bigint=Long
float=Float
double=Double
decimal=BigDecimal
bit=Boolean
char=String
varchar=String
tinytext=String
text=String
mediumtext=String
longtext=String
date=Instant
datetime=Instant
timestamp=Instant
objectId=String
json=JSONObject
array=JSONArray
```

##### columnLength

字段的长度。

##### filterable

是否可以使用该字段过滤。对于列表接口来说，我们经常需要按某个字段过滤检索，譬如：检索某个学校的学生，查询某个用户的角色，等等。我们可以将需要过滤的字段设置为 `filterable`，生成器会自动生成按该字段检索的接口，类似于下面这种格式：

```
GET /api/students?schoolId.equals=1
```

在上面的例子中，`schoolId` 就是一个 `filterable` 的字段，`equals=1` 表示检索 `schoolId == 1` 的学生。除了 `equals`，基本的检索语法还包括：`notEquals`、`in` 和 `notIn`：

* `schoolId.equals=1`
* `schoolId.notEquals=1`
* `schoolId.in[]=1&schoolId.in[]=2`
* `schoolId.notIn[]=1&schoolId.notIn[]=2`

对于字符串类型的字段，还可以使用 `contains` 和 `doesNotContain`:

* `name.contains=zhang`
* `name.doesNotContain=zhang`

对于具有范围性质的字段（数值、时间等），还可以使用 `greaterThan` 和 `lessThan` 等：

* `age.greaterThan=18`
* `age.greaterThanOrEqual=18`
* `age.lessThan=18`
* `age.lessThanOrEqual=18`

##### unique

该字段是否唯一。在新增和编辑时，程序会自动校验值是否已存在。如果配置的数据库类型为 MySQL，生成器还会自动为该字段生成唯一索引。另外在复制时，也会自动为该字段生成唯一值，保证值不重复。

> 注意：虽然在新增、编辑和复制接口里做了重复性判断，但在并发情况下还是可能会出现重复值。绝对的唯一性需要通过唯一索引来保证。

##### patchable

该字段是否可部分更新。最常见的一个场景是状态字段的更新。比如开始任务时，任务状态需要更新为运行中，那么就可以将 `status` 字段标记为 `patchable`，并通过 `PATCH` 接口来更新：

```
PATCH /api/tasks

{
    "id": 1,
    "status": "running"
}
```

可以同时有多个 `patchable` 的字段，但是接口会根据调用时传过来的参数决定要更新哪个字段的，比如：`name` 和 `status` 两个字段是 `patchable` 的，但是调用时只传了 `name` 参数：

```
PATCH /api/tasks

{
    "id": 1,
    "name": "test"
}
```

那么只会更新 `name` 字段的值，其他字段值保持不变。

> 很显然，无法通过 `patch` 接口将字段更新为 `null`，如果你确实需要将某个字段更新成 `null`，可以使用 update 接口（`PUT`）。

##### validateRules

字段的校验规则，每个字段可以有多个校验规则。下面是一个配置示例：

```
"validateRules": [
  { "required": true, "message": "名称不能为空" },
  { "min": 3, "message": "名称最少3个字符" },
  { "max": 256, "message": "名称最多256个字符" },
  { "regex": "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", "message": "名称格式错误，请输入中文、英文（大小写）、数字和下划线（_）" }
]
```

也可以省略 message 使用默认的提示信息：

```
"validateRules": [
  { "required": true },
  { "min": 3, "max": 256 },
  { "regex": "^[a-zA-Z0-9_]+$" }
]
```

###### required

该字段为必填字段。如果是字符串类型，生成 `@NotBlank` 注解；如果是其他类型，生成 `@NotNull` 注解。

###### min

如果该字段为字符串类型，表示长度不要小于 `min` 值，对应注解为 `@Size(min = 3)`；如果是数值类型，则表示数值不要小于 `min` 值，对应注解为 `@Min(value = 3)`。

###### max

如果该字段为字符串类型，表示长度不要大于 `max` 值，对应注解为 `@Size(max = 20)`；如果是数值类型，则表示数值不要大于 `max` 值，对应注解为 `@Max(value = 3)`。

> 注意：`min` 和 `max` 规则可以同时使用，比如：`{ "min": 3, "max": 256 }`。

###### regex

该规则只对字符串类型有效，使用正则表达式校验字符串是否满足规则，注意 `\` 符号要写成 `\\` 转义形式。

###### message

当校验不通过时，返回的提示信息。该字段可以为空，默认生成的提示信息如下：

| 校验规则  | 字段类型 | 默认提示信息                    |
| --------- | -------- | ------------------------------- |
| required  | 任意类型 | 不能为空                        |
| min       | 字符串   | 长度不能小于 `min`              |
| max       | 字符串   | 长度不能大于 `max`              |
| min & max | 字符串   | 长度必须大于 `min` 且小于 `max` |
| min       | 数值     | 数值不能小于 `min`              |
| max       | 数值     | 数值不能大于 `max`              |
| regex     | 字符串   | 格式不正确                      |

#### systemColumnInfos

针对系统字段的配置。由于 `columnInfos` 里只能定义非系统字段，对于系统字段的配置，需要放在 `systemColumnInfos` 里，配置内容和 `columnInfos` 类似，只是支持的配置项较少。下面是一个示例，支持按 `createdDate` 字段检索：

```
"systemColumnInfos": [
    {
        "columnEnName": "created_date",
        "columnType": "datetime",
        "filterable": true
    }
]
```

### genConfig

生成器配置。

#### moduleName

在哪个模块下生成代码。默认值为 `.`，表示在当前模块下生成代码。当你的项目为多模块时，譬如下面这样的项目结构：

```
+ pom.xml
|
+-- module-a
+-- module-b
+-- module-c
```

`skynet-boot-maven-plugin` 插件配置在项目根目录的 `pom.xml` 文件中，则可以通过 `moduleName` 参数将指定的领域代码生成到对应的模块下，也可以通过命令行参数指定该参数：

```
$ mvn skynet-boot:generate -Dgenerate.moduleName=module-a
```

#### uiModuleName

在哪个模块下生成前端 UI 代码。默认值为空，表示不生成前端代码。当你的项目为多模块时，譬如下面这样的项目结构：

```
+ pom.xml
|
+-- module-a
+-- module-b
+-- module-c
+-- module-ui
```

`skynet-boot-maven-plugin` 插件配置在项目根目录的 `pom.xml` 文件中，则可以通过 `uiModuleName` 参数将指定领域的前端代码生成到对应的模块下，也可以通过命令行参数指定该参数：

```
$ mvn skynet-boot:generate -Dgenerate.moduleName=module-a -Dgenerate.uiModuleName=module-ui
```

#### packageName

生成代码所在的包名，也就是你的 Application 入口类所在的包名。

#### dbType

数据库类型，目前插件支持 mysql / mongo / dm 三种数据库，插件会根据该配置自动生成对应数据库的增删改查代码。

#### prefix

TODO
