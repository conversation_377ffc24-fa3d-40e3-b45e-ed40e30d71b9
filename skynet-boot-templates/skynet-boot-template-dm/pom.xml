<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.iflytek.skynet</groupId>
    <artifactId>skynet-boot-template-dm</artifactId>
    <version>4.3.3-SNAPSHOT</version>
    <packaging>maven-archetype</packaging>

    <name>skynet-boot-template-dm</name>
    <description>Archetype for creating Skynet Boot application with DM Database</description>

    <!-- Properties -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.archetype-packaging.version>3.0.1</maven.archetype-packaging.version>
        <maven.maven-archetype-plugin.version>3.0.1</maven.maven-archetype-plugin.version>
        <maven.maven-resources-plugin.version>3.0.2</maven.maven-resources-plugin.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>mvn-releases</id>
            <name>iflytek-nexus</name>
            <url>https://depend.iflytek.com/artifactory/hy-mvn-release-private</url>
        </repository>
        <snapshotRepository>
            <id>mvn-snapshots</id>
            <name>iflytek-snapshots</name>
            <url>https://depend.iflytek.com/artifactory/hy-mvn-snapshot-private</url>
        </snapshotRepository>
    </distributionManagement>

    <!-- Build -->
    <build>
        <extensions>
            <extension>
                <groupId>org.apache.maven.archetype</groupId>
                <artifactId>archetype-packaging</artifactId>
                <version>${maven.archetype-packaging.version}</version>
            </extension>
        </extensions>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>${maven.maven-archetype-plugin.version}</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven.maven-resources-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>