<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-starter-parent</artifactId>
        <version>4.3.3-SNAPSHOT</version>
    </parent>

    <groupId>${groupId}</groupId>
    <artifactId>${artifactId}</artifactId>
    <name>${artifactId}</name>
    <version>${version}</version>
    <packaging>jar</packaging>

    <properties>

        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>
        <buildSid>${maven.build.timestamp}</buildSid>
    </properties>

    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <dependencies>
        <!-- 达梦驱动 -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.2.192</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmDialect-for-hibernate5.0</artifactId>
            <version>8.1.2.192</version>
        </dependency>

        <!-- liquibase集成达梦 -->
        <dependency>
            <groupId>com.github.mengweijin</groupId>
            <artifactId>db-migration-dm</artifactId>
            <version>1.1.6</version>
        </dependency>

        <!-- 为兼容达梦修改的liquibse-core -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>4.9.1-dm-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}-Build${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.hibernate</groupId>
                            <artifactId>hibernate-jpamodelgen</artifactId>
                            <version>${hibernate.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.iflytek.skynet</groupId>
                <artifactId>skynet-boot-maven-plugin</artifactId>
                <configuration>
                    <moduleName>.</moduleName>
                    <packageName>${groupId}.${artifactId}</packageName>
                    <dbType>mysql</dbType>
                    <uiModuleName></uiModuleName>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
