#if ($propertyExtension == 'properties')
#set($comment = '######################################################')
server.port=8080
spring.application.name=${artifactId}
${comment}
logging.level.root=INFO
logging.level.org.springframework=WARN
logging.level.com.iflytek=INFO
${comment}
springfox.documentation.enabled=true
${comment}
spring.datasource.url=jdbc:dm://127.0.0.1:5236?schema=demo&compatibleMode=oracle
spring.datasource.username=root
spring.datasource.password=root
${comment}
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:/liquibase/master.xml
#else
logging:
  level:
    com:
      iflytek: INFO
    org:
      springframework: WARN
    root: INFO
server:
  port: 8080
skynet:
  api:
    swagger2:
      enabled: true
spring:
  application:
    name: ${artifactId}
  datasource:
    url: jdbc:dm://127.0.0.1:5236?schema=demo&compatibleMode=oracle
    username: root
    password: root
  liquibase:
    enabled: true
    change-log: classpath:/liquibase/master.xml
#end
