<archetype-descriptor name="Skynet Boot Mysql Template"
                      xmlns="http://maven.apache.org/plugins/maven-archetype-plugin/archetype-descriptor/1.0.0"
                      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                      xsi:schemaLocation="http://maven.apache.org/plugins/maven-archetype-plugin/archetype-descriptor/1.0.0 http://maven.apache.org/xsd/archetype-descriptor-1.0.0.xsd">
    <requiredProperties>
        <requiredProperty key="propertyExtension">
            <defaultValue>properties</defaultValue>
        </requiredProperty>
    </requiredProperties>
    <fileSets>
        <fileSet filtered="true" encoding="UTF-8">
            <directory></directory>
            <includes>
                <include>.gitignore</include>
                <include>README.md</include>
            </includes>
        </fileSet>
        <fileSet filtered="true">
            <directory>src/main/java</directory>
            <includes>
                <include>**/*.java</include>
            </includes>
        </fileSet>
        <fileSet filtered="true" encoding="UTF-8">
            <directory>src/main/resources</directory>
        </fileSet>
        <fileSet filtered="true">
            <directory>src/test/java</directory>
            <includes>
                <include>**/*.java</include>
            </includes>
        </fileSet>
        <fileSet filtered="true">
            <directory>src/test/resources</directory>
        </fileSet>
    </fileSets>
</archetype-descriptor>