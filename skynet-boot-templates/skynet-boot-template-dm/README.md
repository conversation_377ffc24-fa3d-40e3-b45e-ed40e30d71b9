# skynet-boot-template-dm

这是基于 Skynet Boot 开发 达梦数据库 项目的模板。

## 安装到本地仓库

```bash
$ mvn install
```

## 部署到中央仓库

```bash
$ mvn deploy
```

## 如何使用

```bash
$ mvn archetype:generate \
  -DarchetypeGroupId=com.iflytek.skynet \
  -DarchetypeArtifactId=skynet-boot-template-dm \
  -DarchetypeVersion=4.3.3-SNAPSHOT \
  -DgroupId=com.example \
  -DartifactId=demo \
  -Dversion=1.0.0-SNAPSHOT \
  -DinteractiveMode=false
```

通过上面的命令生成的项目，配置文件默认是 properties 格式，可以通过下面的参数改成 yaml 格式：

```
  -DpropertyExtension=yaml
```
