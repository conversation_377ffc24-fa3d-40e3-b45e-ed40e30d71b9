#if ($propertyExtension == 'properties')
#set($comment = '######################################################')
server.port=8080
spring.application.name=${artifactId}
${comment}
logging.level.root=INFO
logging.level.org.springframework=WARN
logging.level.com.iflytek=INFO
${comment}
springfox.documentation.enabled=true
${comment}
spring.data.mongodb.uri=mongodb://127.0.0.1:27017/demo
${comment}
#else
logging:
  level:
    com:
      iflytek: INFO
    org:
      springframework: WARN
    root: INFO
server:
  port: 8080
skynet:
  api:
    swagger2:
      enabled: true
spring:
  application:
    name: ${artifactId}
  data:
    mongodb:
      uri: mongodb://127.0.0.1:27017/demo
#end
