package skynet.sample.exception.extend;

import org.springframework.stereotype.Component;
import skynet.boot.exception.handler.ExceptionDescriptor;
import skynet.boot.exception.handler.SkynetExceptionHandler;
import skynet.boot.exception.message.ExceptionMessageFormatter;

/**
 * 自定义一个异常处理器
 *
 * <AUTHOR>
 * @date 2022/7/1 17:33
 */
@Component
public class ExtendExceptionHandler extends SkynetExceptionHandler<ExtendException> {

    public ExtendExceptionHandler(ExceptionMessageFormatter formatter) {
        super(formatter, new ExtendExceptionDescriptor());
    }


    /**
     * 自定义定义异常码、异常描述
     */
    static class ExtendExceptionDescriptor implements ExceptionDescriptor {

        @Override
        public ExceptionInfo getInfo() {
            return new ExceptionInfo(-888, "扩展使用的异常");
        }
    }
}
