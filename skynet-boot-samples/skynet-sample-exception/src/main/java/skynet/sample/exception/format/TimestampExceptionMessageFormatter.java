package skynet.sample.exception.format;

import org.springframework.stereotype.Component;
import skynet.boot.exception.SkynetException;
import skynet.boot.exception.message.ErrorMessage;
import skynet.boot.exception.message.ExceptionMessageFormatter;

/**
 * 组装自定义返回消息格式
 *
 * <AUTHOR>
 * @date 2022/7/1 17:18
 */
@Component
public class TimestampExceptionMessageFormatter implements ExceptionMessageFormatter {
    @Override
    public ErrorMessage format(Throwable e, int code) {
        return new TimestampErrorMessage(getCurrentTraceId(), code, e.getMessage());
    }

    @Override
    public ErrorMessage format(SkynetException e) {
        return format(e, e.getCode());
    }

}
