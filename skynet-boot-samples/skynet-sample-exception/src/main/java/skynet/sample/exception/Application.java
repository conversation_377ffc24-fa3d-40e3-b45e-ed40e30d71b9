package skynet.sample.exception;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.AppUtils;
import skynet.boot.annotation.EnableSkynetException;
import skynet.sample.exception.extend.ExtendException;
import skynet.sample.exception.usual.UsualException;

/**
 * <AUTHOR>
 * @date 2022/7/1 15:58
 */
@RestController
@EnableSkynetException
@SpringBootApplication
public class Application {

    public static void main(String[] args) {
        AppUtils.run(Application.class, args);
    }

    @GetMapping("/usual")
    public Object usual() throws Exception {
        throw new UsualException();
    }

    @GetMapping("/extend")
    public Object extend() throws Exception {
        throw new ExtendException();
    }
}
