package skynet.sample.exception;

import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import skynet.boot.exception.handler.SkynetExceptionAdvice;
import skynet.boot.exception.handler.SkynetExceptionHandlerManager;
import skynet.boot.exception.message.ErrorMessage;

import java.io.IOException;

@RestControllerAdvice
public class CustomExceptionAdvice extends SkynetExceptionAdvice {

    private final SkynetExceptionHandlerManager exceptionHandlerManager;

    public CustomExceptionAdvice(SkynetExceptionHandlerManager exceptionHandlerManager) {
        super(exceptionHandlerManager);
        this.exceptionHandlerManager = exceptionHandlerManager;
    }

    // 将 isFilterBefore 设置为 true，可以让 HTTP 状态码返回 500，否则返回的是 200
    @ExceptionHandler(Exception.class)
    public ErrorMessage handlerException(Exception e) throws IOException {
        return exceptionHandlerManager.format(e, true);
    }
}
