package skynet.sample.exception.format;

import lombok.Getter;
import skynet.boot.exception.message.ErrorMessage;

/**
 * 自定义返回的信息格式
 *
 * <AUTHOR>
 * @date 2022/7/1 17:08
 */
public class TimestampErrorMessage implements ErrorMessage {

    private final String traceId;
    private final Integer code;
    private final String message;
    @Getter
    private final long timestamp;

    public TimestampErrorMessage(String traceId, Integer code, String message) {
        this.traceId = traceId;
        this.code = code;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }

    @Override
    public String getTraceId() {
        return traceId;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getPath() {
        return "";
    }
}
