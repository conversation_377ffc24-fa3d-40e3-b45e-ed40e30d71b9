package skynet.sample.exception;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
class ApplicationTest {

    @Autowired
    MockMvc mockMvc;

    @Test
    public void invokeTest() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.get("/invoke"))
                .andDo(result -> System.out.println("response body: " + result.getResponse().getContentAsString()));
    }
}