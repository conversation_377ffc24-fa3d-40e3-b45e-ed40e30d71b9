# skynet-framework统一异常处理机制

## 1. 快速开始

maven parent引入 skynet-boot-starter-parent

```xml
<parent>
    <groupId>com.iflytek.skynet</groupId>
    <artifactId>skynet-boot-starter-parent</artifactId>
    <version>4.3.3-SNAPSHOT</version>
    <relativePath/>
</parent>
```

并在项目启动类上添加`@EnableSkynetException`注解，即可开启skynet-framework统一异常处理机制。

```java
@RestController
@EnableSkynetException
@SpringBootApplication
public class Application {


    public static void main(String[] args) {
        AppUtils.run(Application.class, args);
    }


    
    @GetMapping("/usual")
    public Object usual() throws Exception {
        throw new UsualException();
    }

    @GetMapping("/extend")
    public Object extend() throws Exception {
        throw new ExtendException();
    }
}
```

*注意：请不要自定义`@RestControllerAdvice`，否则将使用您自定义的异常处理，而使得skynet-framework统一异常处理机制失效。*

假如有一个自定义的异常UsualException

```java
public class UsualException extends Exception {
    public UsualException() {
        super("this is a usual exception");
    }
}
```

我们请求 `/usual `时，controller抛出了UsualException()，skynet-framework统一异常处理机制即会捕获这个异常并返回response。

```json
{
  "header": {
    "traceId": null,
    "code": -1,
    "message": "this is a usual exception"
  }
}
```

## 2. 自定义错误码

默认情况下，异常的错误吗返回都是-1，如何自定义异常的错误码呢？可以通过自定义一个异常处理器来解决。

以ExtendException为例

```java
public class ExtendException extends Exception {
    public ExtendException() {
        super("this is a extend exception");
    }
}
```

我们为其定义一个异常处理器，ExtendExceptionHandler。注意**异常处理器需是抽象类SkynetExceptionHandler的子类**
。并通过构造方法为父类SkynetExceptionHandler传入一个ExtendExceptionDescriptor对象。

```java
@Component
public class ExtendExceptionHandler extends SkynetExceptionHandler<ExtendException> {

    public ExtendExceptionHandler(ExceptionMessageFormatter formatter) {
        super(formatter, new ExtendExceptionDescriptor());
    }


    /**
     * 自定义定义异常码、异常名称。 异常名称一般在error日志中打印，不在返回信息中体现
     */
    static class ExtendExceptionDescriptor implements ExceptionDescriptor {

        @Override
        public ExceptionInfo getInfo() {
            return new ExceptionInfo(-888, "扩展的异常");
        }
    }
}
```

然后请求 `/extend `时，controller抛出了ExtendException()，返回的错误信息如下，返回的错误码即为

```json
{
  "header": {
    "traceId": null,
    "code": -888,
    "message": "this is a extend exception"
  }
}
```

日志中异常信息。

```text
2022-07-04 11:41:30.542 ERROR 21196 --- [nio-8080-exec-1] s.b.e.handler.SkynetExceptionHandler     : 扩展使用的异常: code=-888; url=/extend; this is a extend exception
```

## 3. 自定义异常返回格式

也可以通过自定义ExceptionMessageFormatter实现类，来实现自定义格式的返回response。需要注意的是，自定义的ExceptionMessageFormatter是对整个应用生效的，所有的异常信息都会以自定义的格式返回。

仍以ExtendException为例，*
*自定义一个消息格式化器TimestampExceptionMessageFormatter，实现ExceptionMessageFormatter接口；并定义一个消息对象TimestampErrorMessage，实现ErrorMessage接口
**。

```java
@Component
public class TimestampExceptionMessageFormatter implements ExceptionMessageFormatter {
    @Override
    public ErrorMessage format(Throwable e, int code) {
        // 返回自定义的消息对象
        return new TimestampErrorMessage(MDC.get("SKYNET_CURRENT_TRACE_ID"), code, e.getMessage());
    }

    @Override
    public ErrorMessage format(SkynetException e) {
        return format(e, e.getCode());
    }

}
```

```java
public class TimestampErrorMessage implements ErrorMessage {

    private final String traceId;
    private final Integer code;
    private final String message;
    private final String timestamp;// 在返回消息中增加时间戳字段

    public TimestampErrorMessage(String traceId, Integer code, String message) {
        this.traceId = traceId;
        this.code = code;
        this.message = message;
        this.timestamp = String.valueOf(System.currentTimeMillis());
    }

    @Override
    public String getTraceId() {
        return traceId;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public String getTimestamp() {
        return timestamp;
    }
}
```

此时请求 `/extend `时，controller抛出了ExtendException()，返回的错误信息如下

```java
{
  "traceId": null,
  "code": -888,
  "message": "this is a extend exception",
  "timestamp": "1656905418321"
}
```

