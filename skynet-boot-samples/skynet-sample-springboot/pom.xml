<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-samples</artifactId>
        <version>1.1.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>skynet.boot</groupId>
    <artifactId>skynet-sample-springboot</artifactId>
    <packaging>jar</packaging>

    <name>skynet-sample-springboot</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.source.skip>true</maven.source.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-tlb</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.iflytek.skynet</groupId>-->
        <!--            <artifactId>skynet-boot-starter-zookeeper</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-metrics</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-logging</artifactId>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>commons-httpclient</groupId>-->
        <!--            <artifactId>commons-httpclient</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy.version}</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-security</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
