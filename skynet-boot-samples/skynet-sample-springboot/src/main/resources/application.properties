server.port=7878
#spring.application.name=@project.artifactId@
######################################################
logging.level.root=INFO
logging.level.org.springframework=WARN
logging.level.com.iflytek=INFO
######################################################
springfox.documentation.enabled=true
######################################################
management.endpoints.web.exposure.include=*
management.endpoints.enabled-by-default=true
management.health.defaults.enabled=false
management.metrics.distribution.percentilesHistogram.all=false
######################################################
skynet.zookeeper.enabled=false
skynet.zookeeper.server_list=*************:2181
######################################################
spring.application.name=skynet-sample-v10
######################################################
#spring.cloud.zookeeper.enabled=false
#spring.cloud.zookeeper.connect-string=*************:2181
#spring.cloud.zookeeper.discovery.root=/test/discovery
#spring.cloud.zookeeper.discovery.instance-host=${skynet.ipAddress}
#spring.cloud.zookeeper.discovery.enabled=true
#spring.cloud.zookeeper.discovery.register=true
######################################################
######################################################
skynet.logging.enabled=false
skynet.logging.debug.enabled=false
skynet.logging.debug.head-key=x-skynet-debugging
skynet.logging.debug.expression=$.user=="lyhu"
skynet.logging.debug.dubbo.enabled=false
# ---------------------------------------------------------#
skynet.logging.debug.springmvc.enabled=true
skynet.logging.debug.springmvc.uri-pattern=/**/api/**
# ---------------------------------------------------------#
skynet.logging.debug.enable.root=true
skynet.logging.debug.enable.com.iflytek=true
skynet.logging.debug.enable.skynet=false
skynet.logging.debug.enable.skynet.boot=true
# ---------------------------------------------------------#
skynet.tlb.enabled=true
skynet.tlb.endpoints=**************:33001
# \u662F\u5426\u5F00\u542F\u670D\u52A1\u53D1\u73B0\uFF1A \u9ED8\u8BA4 true
spring.cloud.tlb.discovery.enabled=true
# \u662F\u5426\u81EA\u52A8\u6CE8\u518C\uFF0C\u9ED8\u8BA4false
spring.cloud.tlb.discovery.register=true
# \u670D\u52A1\u6CE8\u518C\u7684\u4E00\u4E9B\u5143\u6570\u636E
spring.cloud.tlb.discovery.max-lic=100
spring.cloud.tlb.discovery.service-id=
#spring.cloud.tlb.discovery.service-name=${spring.application.name}
spring.cloud.tlb.discovery.service-host=
spring.cloud.tlb.discovery.service-port=0
spring.cloud.tlb.discovery.metadata.key1=value
spring.cloud.tlb.discovery.metadata.key2=value
# \u670D\u52A1\u53D1\u73B0\uFF0C\u4ECElb\u4E2D\u83B7\u53D6\u7684\u9ED8\u8BA4tag\u6807\u7B7E
spring.cloud.tlb.discovery.default-service-tag-selector=


skynet.security.enabled=true
skynet.security.form-auth.enabled=true
skynet.security.form-auth.user.name=admin
skynet.security.form-auth.user.password=admin
#skynet.security.form-auth.ignore-patterns=/actuator/mappings,/login,/hello
skynet.security.form-auth.path-patterns=/**

skynet.security.base-auth.enabled=true
skynet.security.base-auth.user.name=admin
skynet.security.base-auth.user.password=admin