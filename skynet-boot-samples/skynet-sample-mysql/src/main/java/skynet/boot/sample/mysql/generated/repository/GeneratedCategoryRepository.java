package skynet.boot.sample.mysql.generated.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;
import skynet.boot.sample.mysql.generated.domain.GeneratedCategory;

@NoRepositoryBean
public interface GeneratedCategoryRepository extends JpaRepository<GeneratedCategory, Long>, JpaSpecificationExecutor<GeneratedCategory> {
}