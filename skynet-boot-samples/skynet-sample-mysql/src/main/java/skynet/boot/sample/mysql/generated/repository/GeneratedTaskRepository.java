package skynet.boot.sample.mysql.generated.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;
import skynet.boot.sample.mysql.generated.domain.GeneratedTask;

import java.util.Optional;

@NoRepositoryBean
public interface GeneratedTaskRepository extends JpaRepository<GeneratedTask, Long>, JpaSpecificationExecutor<GeneratedTask> {

    /**
     * findByCode
     *
     * @param code 编码
     * @return 任务
     */
    Optional<GeneratedTask> findByCode(String code);
}