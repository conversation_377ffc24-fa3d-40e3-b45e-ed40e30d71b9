package skynet.boot.sample.mysql.generated.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import skynet.boot.sample.mysql.service.CategoryService;
import skynet.boot.sample.mysql.service.dto.CategoryCriteria;
import skynet.boot.sample.mysql.service.dto.CategoryDTO;
import skynet.boot.sample.mysql.service.dto.CategoryPatchDTO;

import java.util.List;

/**
 * 分类管理
 */
public class GeneratedCategoryController {

    @Resource
    protected CategoryService categoryService;

    @Operation(summary = "分页查询分类")
    @GetMapping(value = "/categories")
    public Page<CategoryDTO> findAllByPage(CategoryCriteria criteria, Pageable pageable) {
        return categoryService.findAllByCriteria(criteria, pageable);
    }

    @Operation(summary = "不分页查询分类")
    @GetMapping(value = "/categories/unPage")
    public List<CategoryDTO> findAllUnPage(CategoryCriteria criteria, Sort sort) {
        return categoryService.findAllByCriteria(criteria, sort);
    }

    @Operation(summary = "新增分类")
    @PostMapping(value = "/categories")
    public CategoryDTO save(@Validated @RequestBody CategoryDTO categoryDTO) {
        return categoryService.save(categoryDTO);
    }

    @Operation(summary = "修改分类")
    @PutMapping(value = "/categories")
    public CategoryDTO update(@Validated @RequestBody CategoryDTO categoryDTO) {
        return categoryService.update(categoryDTO);
    }

    @Operation(summary = "更新分类")
    @PatchMapping(value = "/categories")
    public CategoryDTO patch(@Validated @RequestBody CategoryPatchDTO categoryPatchDTO) {
        return categoryService.patch(categoryPatchDTO);
    }

    @Operation(summary = "查找分类")
    @GetMapping(value = "/categories/{id}")
    public CategoryDTO get(@PathVariable Long id) {
        return categoryService.findById(id);
    }

    @Operation(summary = "删除分类")
    @DeleteMapping(value = "/categories/{id}")
    public void delete(@PathVariable Long id) {
        categoryService.delete(id);
    }

    @Operation(summary = "复制分类")
    @PostMapping(value = "/categories/{id}/copy")
    public CategoryDTO copy(@PathVariable Long id) {
        return categoryService.copy(id);
    }
}