package skynet.boot.sample.mysql.generated.service.impl;

import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import skynet.boot.sample.mysql.generated.domain.GeneratedCategory;
import skynet.boot.sample.mysql.generated.service.GeneratedCategoryService;
import skynet.boot.sample.mysql.generated.service.mapper.GeneratedCategoryMapper;
import skynet.boot.sample.mysql.repository.CategoryRepository;
import skynet.boot.sample.mysql.service.dto.CategoryCriteria;
import skynet.boot.sample.mysql.service.dto.CategoryDTO;
import skynet.boot.sample.mysql.service.dto.CategoryPatchDTO;

import java.util.ArrayList;
import java.util.List;

public class GeneratedCategoryServiceImpl extends MysqlTemplateServiceImpl<GeneratedCategory, CategoryDTO, Long> implements GeneratedCategoryService {

    @Resource
    protected CategoryRepository categoryRepository;

    @Resource
    protected GeneratedCategoryMapper categoryMapper;


    public GeneratedCategoryServiceImpl(EntityMapper<CategoryDTO, GeneratedCategory> entityMapper, JpaSpecificationExecutor<GeneratedCategory> jpaSpecificationExecutor, JpaRepository<GeneratedCategory, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<CategoryDTO> findAllByCriteria(CategoryCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<CategoryDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<CategoryDTO> findAllByCriteria(CategoryCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<CategoryDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedCategory> createSpecification(Criteria criteria) {
        CategoryCriteria categoryCriteria = (CategoryCriteria) criteria;
        Specification<GeneratedCategory> specification = Specification.where(null);
        if (categoryCriteria != null) {
        }
        return specification;
    }

    @Override
    public CategoryDTO findById(Long id) {
        CategoryDTO categoryDTO = super.findById(id);
        return categoryDTO;
    }

    @Override
    public CategoryDTO save(CategoryDTO categoryDTO) {
        return super.save(categoryDTO);
    }

    @Override
    public CategoryDTO update(CategoryDTO categoryDTO) {

        return super.update(categoryDTO.getId(), categoryDTO);
    }

    @Override
    public CategoryDTO patch(CategoryPatchDTO categoryPatchDTO) {
        return super.patch(categoryPatchDTO.getId(), categoryPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public CategoryDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedCategory.class);
    }
}