package skynet.boot.sample.mysql.generated.service.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.sample.mysql.generated.domain.GeneratedTask;
import skynet.boot.sample.mysql.service.dto.TaskDTO;

@Mapper(
        componentModel = "spring",
        uses = {},
        builder = @Builder(disableBuilder = true))
public interface GeneratedTaskMapper extends EntityMapper<TaskDTO, GeneratedTask> {

}