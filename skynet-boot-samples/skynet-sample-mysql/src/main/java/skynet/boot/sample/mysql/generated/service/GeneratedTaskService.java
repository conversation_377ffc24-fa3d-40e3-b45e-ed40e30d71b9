package skynet.boot.sample.mysql.generated.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import skynet.boot.common.service.TemplateService;
import skynet.boot.sample.mysql.generated.domain.GeneratedTask;
import skynet.boot.sample.mysql.service.dto.TaskCriteria;
import skynet.boot.sample.mysql.service.dto.TaskDTO;
import skynet.boot.sample.mysql.service.dto.TaskPatchDTO;

import java.util.List;

public interface GeneratedTaskService extends TemplateService<GeneratedTask, TaskDTO, Long> {

    /**
     * 查找列表-分页
     *
     * @param pageable
     * @return
     */
    Page<TaskDTO> findAllByCriteria(TaskCriteria criteria, Pageable pageable);

    Page<TaskDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     *
     * @return
     */
    List<TaskDTO> findAllByCriteria(TaskCriteria criteria, Sort sort);

    List<TaskDTO> findAll(Sort sort);

    /**
     * 查找单条
     *
     * @param id
     * @return
     */
    TaskDTO findById(Long id);

    /**
     * 创建
     *
     * @param taskDTO
     * @return
     */
    TaskDTO save(TaskDTO taskDTO);

    /**
     * 修改
     *
     * @param taskDTO
     */
    TaskDTO update(TaskDTO taskDTO);

    /**
     * 更新
     *
     * @param taskPatchDTO
     */
    TaskDTO patch(TaskPatchDTO taskPatchDTO);

    /**
     * 删除单条
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 复制
     *
     * @param id
     */
    TaskDTO copy(Long id);
}