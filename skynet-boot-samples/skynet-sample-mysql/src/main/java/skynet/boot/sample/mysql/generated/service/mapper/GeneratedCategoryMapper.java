package skynet.boot.sample.mysql.generated.service.mapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.sample.mysql.generated.domain.GeneratedCategory;
import skynet.boot.sample.mysql.service.dto.CategoryDTO;

@Mapper(
        componentModel = "spring",
        uses = {},
        builder = @Builder(disableBuilder = true))
public interface GeneratedCategoryMapper extends EntityMapper<CategoryDTO, GeneratedCategory> {

}