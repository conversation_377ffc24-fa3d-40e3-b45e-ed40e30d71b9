package skynet.boot.sample.mysql.generated.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import skynet.boot.sample.mysql.service.TaskService;
import skynet.boot.sample.mysql.service.dto.TaskCriteria;
import skynet.boot.sample.mysql.service.dto.TaskDTO;
import skynet.boot.sample.mysql.service.dto.TaskPatchDTO;

import java.util.List;

/**
 * 任务管理
 */
public class GeneratedTaskController {

    @Resource
    protected TaskService taskService;

    @Operation(summary = "分页查询任务")
    @GetMapping(value = "/tasks")
    public Page<TaskDTO> findAllByPage(TaskCriteria criteria, Pageable pageable) {
        return taskService.findAllByCriteria(criteria, pageable);
    }

    @Operation(summary = "不分页查询任务")
    @GetMapping(value = "/tasks/unPage")
    public List<TaskDTO> findAllUnPage(TaskCriteria criteria, Sort sort) {
        return taskService.findAllByCriteria(criteria, sort);
    }

    @Operation(summary = "新增任务")
    @PostMapping(value = "/tasks")
    public TaskDTO save(@Validated @RequestBody TaskDTO taskDTO) {
        return taskService.save(taskDTO);
    }

    @Operation(summary = "修改任务")
    @PutMapping(value = "/tasks")
    public TaskDTO update(@Validated @RequestBody TaskDTO taskDTO) {
        return taskService.update(taskDTO);
    }

    @Operation(summary = "更新任务")
    @PatchMapping(value = "/tasks")
    public TaskDTO patch(@Validated @RequestBody TaskPatchDTO taskPatchDTO) {
        return taskService.patch(taskPatchDTO);
    }

    @Operation(summary = "查找任务")
    @GetMapping(value = "/tasks/{id}")
    public TaskDTO get(@PathVariable Long id) {
        return taskService.findById(id);
    }

    @Operation(summary = "删除任务")
    @DeleteMapping(value = "/tasks/{id}")
    public void delete(@PathVariable Long id) {
        taskService.delete(id);
    }

    @Operation(summary = "复制任务")
    @PostMapping(value = "/tasks/{id}/copy")
    public TaskDTO copy(@PathVariable Long id) {
        return taskService.copy(id);
    }
}