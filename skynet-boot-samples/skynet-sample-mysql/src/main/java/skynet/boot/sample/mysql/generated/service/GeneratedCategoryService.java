package skynet.boot.sample.mysql.generated.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import skynet.boot.common.service.TemplateService;
import skynet.boot.sample.mysql.generated.domain.GeneratedCategory;
import skynet.boot.sample.mysql.service.dto.CategoryCriteria;
import skynet.boot.sample.mysql.service.dto.CategoryDTO;
import skynet.boot.sample.mysql.service.dto.CategoryPatchDTO;

import java.util.List;

public interface GeneratedCategoryService extends TemplateService<GeneratedCategory, CategoryDTO, Long> {

    /**
     * 查找列表-分页
     *
     * @param pageable
     * @return
     */
    Page<CategoryDTO> findAllByCriteria(CategoryCriteria criteria, Pageable pageable);

    Page<CategoryDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     *
     * @return
     */
    List<CategoryDTO> findAllByCriteria(CategoryCriteria criteria, Sort sort);

    List<CategoryDTO> findAll(Sort sort);

    /**
     * 查找单条
     *
     * @param id
     * @return
     */
    CategoryDTO findById(Long id);

    /**
     * 创建
     *
     * @param categoryDTO
     * @return
     */
    CategoryDTO save(CategoryDTO categoryDTO);

    /**
     * 修改
     *
     * @param categoryDTO
     */
    CategoryDTO update(CategoryDTO categoryDTO);

    /**
     * 更新
     *
     * @param categoryPatchDTO
     */
    CategoryDTO patch(CategoryPatchDTO categoryPatchDTO);

    /**
     * 删除单条
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 复制
     *
     * @param id
     */
    CategoryDTO copy(Long id);
}