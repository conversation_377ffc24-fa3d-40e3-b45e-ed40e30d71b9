package skynet.boot.sample.mysql.generated.service.impl;

import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import skynet.boot.sample.mysql.generated.domain.GeneratedTask;
import skynet.boot.sample.mysql.generated.domain.GeneratedTask_;
import skynet.boot.sample.mysql.generated.service.GeneratedTaskService;
import skynet.boot.sample.mysql.generated.service.mapper.GeneratedTaskMapper;
import skynet.boot.sample.mysql.repository.TaskRepository;
import skynet.boot.sample.mysql.service.dto.TaskCriteria;
import skynet.boot.sample.mysql.service.dto.TaskDTO;
import skynet.boot.sample.mysql.service.dto.TaskPatchDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedTaskServiceImpl extends MysqlTemplateServiceImpl<GeneratedTask, TaskDTO, Long> implements GeneratedTaskService {

    @Resource
    protected TaskRepository taskRepository;

    @Resource
    protected GeneratedTaskMapper taskMapper;


    public GeneratedTaskServiceImpl(EntityMapper<TaskDTO, GeneratedTask> entityMapper, JpaSpecificationExecutor<GeneratedTask> jpaSpecificationExecutor, JpaRepository<GeneratedTask, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<TaskDTO> findAllByCriteria(TaskCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<TaskDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<TaskDTO> findAllByCriteria(TaskCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<TaskDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedTask> createSpecification(Criteria criteria) {
        TaskCriteria taskCriteria = (TaskCriteria) criteria;
        Specification<GeneratedTask> specification = Specification.where(null);
        if (taskCriteria != null) {
            if (taskCriteria.getName() != null) {
                specification = specification.and(buildStringSpecification(taskCriteria.getName(), GeneratedTask_.name));
            }
            if (taskCriteria.getCode() != null) {
                specification = specification.and(buildStringSpecification(taskCriteria.getCode(), GeneratedTask_.code));
            }
            if (taskCriteria.getStartDate() != null) {
                specification = specification.and(buildRangeSpecification(taskCriteria.getStartDate(), GeneratedTask_.startDate));
            }
            if (taskCriteria.getCreatedDate() != null) {
                specification = specification.and(buildRangeSpecification(taskCriteria.getCreatedDate(), GeneratedTask_.createdDate));
            }
            if (taskCriteria.getLastModifiedDate() != null) {
                specification = specification.and(buildRangeSpecification(taskCriteria.getLastModifiedDate(), GeneratedTask_.lastModifiedDate));
            }
            if (taskCriteria.getId() != null) {
                specification = specification.and(buildRangeSpecification(taskCriteria.getId(), GeneratedTask_.id));
            }
        }
        return specification;
    }

    @Override
    public TaskDTO findById(Long id) {
        TaskDTO taskDTO = super.findById(id);
        return taskDTO;
    }

    @Override
    public TaskDTO save(TaskDTO taskDTO) {
        if (taskRepository.findByCode(taskDTO.getCode()).isPresent()) {
            throw new DuplicateNameException("编码");
        }
        return super.save(taskDTO);
    }

    @Override
    public TaskDTO update(TaskDTO taskDTO) {
        Optional<GeneratedTask> task1 = taskRepository.findByCode(taskDTO.getCode());
        if (task1.isPresent() && !task1.get().getId().equals(taskDTO.getId())) {
            throw new DuplicateNameException("编码");
        }

        return super.update(taskDTO.getId(), taskDTO);
    }

    @Override
    public TaskDTO patch(TaskPatchDTO taskPatchDTO) {
        return super.patch(taskPatchDTO.getId(), taskPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public TaskDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        renameFields.add("code");
        return super.copy(id, renameFields, GeneratedTask.class);
    }
}