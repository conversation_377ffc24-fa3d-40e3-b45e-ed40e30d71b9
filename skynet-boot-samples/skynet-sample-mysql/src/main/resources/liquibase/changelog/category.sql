--liquibase formatted sql
--changeset skynet:create_table_category
CREATE TABLE `category`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COMMENT '名称', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Compact;

