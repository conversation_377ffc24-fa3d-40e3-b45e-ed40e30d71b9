server.port=8080
spring.application.name=skynet-boot-sample-mysql
logging.level.root=INFO
logging.level.org.springframework=WARN
logging.level.com.iflytek=INFO 
springfox.documentation.enabled=true
skynet.fastjson.http.message.convert.enabled=false
spring.datasource.url=*************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=cloud1234
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:/liquibase/master.xml
spring.jpa.show-sql=true
