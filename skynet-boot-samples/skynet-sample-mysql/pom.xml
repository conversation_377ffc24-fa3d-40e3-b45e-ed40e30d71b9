<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-samples</artifactId>
        <version>1.1.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>skynet-sample-mysql</artifactId>
    <name>skynet-sample-mysql</name>
    <packaging>jar</packaging>

    <properties>
        <maven.source.skip>true</maven.source.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
    </properties>

    <dependencies>
        <!-- MySQL -->
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-mysql</artifactId>
        </dependency>
        <!-- Swagger -->
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-swagger</artifactId>
        </dependency>
        <!-- Metrics -->
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-metrics</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}-Build${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.hibernate</groupId>
                            <artifactId>hibernate-jpamodelgen</artifactId>
                            <version>${hibernate.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.iflytek.skynet</groupId>
                <artifactId>skynet-boot-maven-plugin</artifactId>
                <configuration>
                    <moduleName>.</moduleName>
                    <packageName>skynet.boot.sample.mysql</packageName>
                    <dbType>mysql</dbType>
                    <uiModuleName></uiModuleName>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
