<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-starter</artifactId>
        <version>${skynet-revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>skynet-boot-starter-swagger</artifactId>
    <name>skynet-boot-starter-swagger</name>
    <packaging>jar</packaging>

    <description>swagger pom providing dependency for applications built with Maven</description>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
    </dependencies>
</project>