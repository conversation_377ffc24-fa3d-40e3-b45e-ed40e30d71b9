<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-starter</artifactId>
        <version>${skynet-revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>skynet-boot-starter-metrics</artifactId>
    <name>skynet-boot-starter-metrics</name>
    <packaging>jar</packaging>

    <description>metrics pom providing dependency for applications built with Maven</description>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-context</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>
</project>