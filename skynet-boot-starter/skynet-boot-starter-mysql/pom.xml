<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-boot-starter</artifactId>
        <version>${skynet-revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>skynet-boot-starter-mysql</artifactId>
    <name>skynet-boot-starter-mysql</name>
    <packaging>jar</packaging>

    <description>mysql pom providing dependency for applications built with Maven</description>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-context</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

    </dependencies>
</project>